[{"id": 1, "calculation_definition_id": 1, "locale": "fi", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 2, "calculation_definition_id": 2, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 3, "calculation_definition_id": 3, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON> (vähärikkinen)", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 4, "calculation_definition_id": 4, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 5, "calculation_definition_id": 5, "locale": "fi", "data_name": "Raskas polttoöljy", "emission_factor_name": "<PERSON><PERSON><PERSON> p<PERSON> (rikkipitoisuus ≥1%)", "result_name": "Raskas polttoöljy", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 6, "calculation_definition_id": 6, "locale": "fi", "data_name": "Neste My Diesel", "emission_factor_name": "Neste My Diesel", "result_name": "Neste MyDiesel", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 7, "calculation_definition_id": 7, "locale": "fi", "data_name": "(R134A) Veden- ja nest<PERSON>, lämpöpumput", "emission_factor_name": "(R134A) Veden- ja nest<PERSON>, lämpöpumput", "result_name": "(R134A) Veden- ja nest<PERSON>, lämpöpumput", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-07-23 06:22:01"}, {"id": 8, "calculation_definition_id": 8, "locale": "fi", "data_name": "(R410A) Veden- ja nest<PERSON>, lämpöpumput ", "emission_factor_name": "(R410A) Veden- ja nest<PERSON>, lämpöpumput ", "result_name": "(R410A) Veden- ja nest<PERSON>, lämpöpumput ", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-07-23 06:22:20"}, {"id": 9, "calculation_definition_id": 9, "locale": "fi", "data_name": "(R404A) Kaupan kylmälaitteet, ammattikylmälaitteet ", "emission_factor_name": "(R404A) Kaupan kylmälaitteet, ammattikylmälaitteet ", "result_name": "(R404A) Kaupan kylmälaitteet, ammattikylmälaitteet", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-07-23 06:22:53"}, {"id": 10, "calculation_definition_id": 10, "locale": "fi", "data_name": "(R507A) Kaupan kylmälaitteet, ammattikylmälaitteet ", "emission_factor_name": "(R507A) Kaupan kylmälaitteet, ammattikylmälaitteet ", "result_name": "(R507A) Kaupan kylmälaitteet, ammattikylmälaitteet ", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-07-23 06:23:10"}, {"id": 11, "calculation_definition_id": 11, "locale": "fi", "data_name": "(R134a) Kaupan k<PERSON>mälaitteet, ammattikylmälaitteet ", "emission_factor_name": "(R134a) Kaupan k<PERSON>mälaitteet, ammattikylmälaitteet ", "result_name": "(R134a) Kaupan k<PERSON>mälaitteet, ammattikylmälaitteet ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:23:28"}, {"id": 12, "calculation_definition_id": 12, "locale": "fi", "data_name": "(R407A) Kaupan kylmälaitteet, ammattikylmälaitteet", "emission_factor_name": "(R407A) Kaupan kylmälaitteet, ammattikylmälaitteet", "result_name": "(R407A) Kaupan kylmälaitteet, ammattikylmälaitteet", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:23:45"}, {"id": 13, "calculation_definition_id": 13, "locale": "fi", "data_name": "(R407F) Kaupan kylmälaitteet, ammattikylmälaitteet ", "emission_factor_name": "(R407F) Kaupan kylmälaitteet, ammattikylmälaitteet ", "result_name": "(R407F) Kaupan kylmälaitteet, ammattikylmälaitteet ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:24:01"}, {"id": 14, "calculation_definition_id": 14, "locale": "fi", "data_name": "(R404A) Lauhdutinyksiköt ", "emission_factor_name": "(R404A) Lauhdutinyksiköt ", "result_name": "(R404A) Lauhdutinyksiköt ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:24:14"}, {"id": 15, "calculation_definition_id": 15, "locale": "fi", "data_name": "(R410A) Ilmastointi ", "emission_factor_name": "(R410A) Ilmastointi ", "result_name": "(R410A) Ilmastointi ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:24:28"}, {"id": 16, "calculation_definition_id": 16, "locale": "fi", "data_name": "(R407C) Ilmastointi ", "emission_factor_name": "(R407C) Ilmastointi ", "result_name": "(R407C) Ilmastointi ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:24:44"}, {"id": 17, "calculation_definition_id": 17, "locale": "fi", "data_name": "(R404A) Jäähdytetty kuljetus ", "emission_factor_name": "(R404A) Jäähdytetty kuljetus ", "result_name": "(R404A) Jäähdytetty kuljetus ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:24:57"}, {"id": 18, "calculation_definition_id": 18, "locale": "fi", "data_name": "(R134a) Jäähdytetty kuljetus ", "emission_factor_name": "(R134a) Jäähdytetty kuljetus ", "result_name": "(R134a) Jäähdytetty kuljetus ", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:25:10"}, {"id": 19, "calculation_definition_id": 19, "locale": "fi", "data_name": "Suorat typpioksiduulipäästöt", "emission_factor_name": "Suorat typpioksiduulipäästöt", "result_name": "Suorat typpioksiduulipäästöt", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 20, "calculation_definition_id": 20, "locale": "fi", "data_name": "Suorat otsonipäästöt", "emission_factor_name": "Suorat otsonipäästöt", "result_name": "Suorat otsonipäästöt", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 21, "calculation_definition_id": 21, "locale": "fi", "data_name": "Suorat metaanipäästöt", "emission_factor_name": "Suorat metaanipäästöt", "result_name": "Suorat metaanipäästöt", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 22, "calculation_definition_id": 22, "locale": "fi", "data_name": "Suorat hiilidioksipäästöt", "emission_factor_name": "Suorat hiilidioksipäästöt", "result_name": "Suorat hiilidioksipäästöt", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 23, "calculation_definition_id": 23, "locale": "fi", "data_name": "<PERSON><PERSON>ä täytä tätä", "emission_factor_name": "Sähkö, Suomen keskiarvo", "result_name": "Sijaintiperusteinen sähkö", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 09:57:55"}, {"id": 24, "calculation_definition_id": 24, "locale": "fi", "data_name": "Sähkönkulutus (päästötön sähkö)", "emission_factor_name": "Sähkö (päästötön)", "result_name": "Markkinaperusteinen sähkö", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-22 11:08:51"}, {"id": 25, "calculation_definition_id": 25, "locale": "fi", "data_name": "Kaukolämpö", "emission_factor_name": "Kaukolämpö, valitse kunta", "result_name": "Kaukolämpö", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-07-23 06:32:49"}, {"id": 26, "calculation_definition_id": 26, "locale": "fi", "data_name": "Päästötön kaukolämpö, alkuperätakuu", "emission_factor_name": "Päästötön kaukolämpö", "result_name": "Päästötön kaukolämpö, alkuperätakuu", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-08-12 07:44:36"}, {"id": 27, "calculation_definition_id": 27, "locale": "fi", "data_name": "Kaukojäähdytyksen kulutus (Suomi)", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Suomen keskiarvo", "result_name": "Kaukojäähdytys (Suomi)", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 28, "calculation_definition_id": 28, "locale": "fi", "data_name": "Oste<PERSON> hö<PERSON> kulu<PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>, oma energia<PERSON>ht<PERSON>ö", "result_name": "Höyry", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 29, "calculation_definition_id": 29, "locale": "fi", "data_name": "Kannettava tietokone", "emission_factor_name": "Kannettava tietokone", "result_name": "Kannettava tietokone", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 30, "calculation_definition_id": 30, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 31, "calculation_definition_id": 31, "locale": "fi", "data_name": "Tablet-laite", "emission_factor_name": "Tablet-laite", "result_name": "Tablet-laite", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 32, "calculation_definition_id": 32, "locale": "fi", "data_name": "<PERSON><PERSON>t sähkölaitteet", "emission_factor_name": "<PERSON><PERSON>t sähkölaitteet", "result_name": "<PERSON><PERSON>t sähkölaitteet", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 33, "calculation_definition_id": 33, "locale": "fi", "data_name": "Pienet sähkölaitteet", "emission_factor_name": "Pienet sähkölaitteet", "result_name": "Pienet sähkölaitteet", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 34, "calculation_definition_id": 34, "locale": "fi", "data_name": "Puuvillavaate", "emission_factor_name": "Puuvillavaate", "result_name": "Puuvillavaate", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 35, "calculation_definition_id": 35, "locale": "fi", "data_name": "Tekokuituvaate", "emission_factor_name": "Tekokuituvaate", "result_name": "Tekokuituvaate", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 36, "calculation_definition_id": 36, "locale": "fi", "data_name": "Terästuotteet", "emission_factor_name": "Terästuotteet", "result_name": "Terästuotteet", "created_at": "2025-06-25 10:43:01", "updated_at": "2025-06-25 10:43:01"}, {"id": 37, "calculation_definition_id": 37, "locale": "fi", "data_name": "Alumiinituotteet", "emission_factor_name": "Alumiinituotteet", "result_name": "Alumiinituotteet", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 38, "calculation_definition_id": 38, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 39, "calculation_definition_id": 39, "locale": "fi", "data_name": "Muovituotteet", "emission_factor_name": "Muovituotteet", "result_name": "Muovituotteet", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 40, "calculation_definition_id": 40, "locale": "fi", "data_name": "Paperituotteet", "emission_factor_name": "Paperituotteet", "result_name": "Paperituotteet", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 41, "calculation_definition_id": 41, "locale": "fi", "data_name": "Kartonkituotteet", "emission_factor_name": "Kartonkituotteet", "result_name": "Kartonkituotteet", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 42, "calculation_definition_id": 42, "locale": "fi", "data_name": "Lasituotteet", "emission_factor_name": "Lasituotteet", "result_name": "Lasituotteet", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 43, "calculation_definition_id": 43, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (lypsykarja)", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (lypsykarja)", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (lypsykarja)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 44, "calculation_definition_id": 44, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 45, "calculation_definition_id": 45, "locale": "fi", "data_name": "<PERSON><PERSON> (kirjolohi)", "emission_factor_name": "<PERSON><PERSON> (kirjolohi)", "result_name": "<PERSON><PERSON> (kirjolohi)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 46, "calculation_definition_id": 46, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 47, "calculation_definition_id": 47, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 48, "calculation_definition_id": 48, "locale": "fi", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 49, "calculation_definition_id": 49, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "emission_factor_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "result_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 50, "calculation_definition_id": 50, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> (vähärikkinen)", "emission_factor_name": "<PERSON><PERSON><PERSON> (vähärikkinen)", "result_name": "<PERSON><PERSON><PERSON> (vähärikkinen)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 51, "calculation_definition_id": 51, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 52, "calculation_definition_id": 52, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> p<PERSON> (rikkipitoisuus ≥1%)", "emission_factor_name": "<PERSON><PERSON><PERSON> p<PERSON> (rikkipitoisuus ≥1%)", "result_name": "<PERSON><PERSON><PERSON> p<PERSON> (rikkipitoisuus ≥1%)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 53, "calculation_definition_id": 53, "locale": "fi", "data_name": "Neste MyDiesel", "emission_factor_name": "Neste MyDiesel", "result_name": "Neste MyDiesel", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 54, "calculation_definition_id": 54, "locale": "fi", "data_name": "Ydinvoima", "emission_factor_name": "Ydinvoima", "result_name": "Ydinvoima", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 55, "calculation_definition_id": 55, "locale": "fi", "data_name": "Tuulivoima", "emission_factor_name": "Tuulivoima", "result_name": "Tuulivoima", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 56, "calculation_definition_id": 56, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 57, "calculation_definition_id": 57, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 58, "calculation_definition_id": 58, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 59, "calculation_definition_id": 59, "locale": "fi", "data_name": "Biomassa", "emission_factor_name": "Biomassa", "result_name": "Biomassa", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 60, "calculation_definition_id": 60, "locale": "fi", "data_name": "Siirtohäviön o<PERSON> s<PERSON>köntuotannossa", "emission_factor_name": "Siirtohäviön o<PERSON> s<PERSON>köntuotannossa", "result_name": "Siirtohäviön o<PERSON> s<PERSON>köntuotannossa", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 61, "calculation_definition_id": 61, "locale": "fi", "data_name": "Puoliperävaunurekka (3,5-33t) (diesel)", "emission_factor_name": "Puoliperävaunurekka (3,5-33t) (diesel)  ", "result_name": "Puoliperävaunurekka (3,5-33t) (diesel)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 62, "calculation_definition_id": 62, "locale": "fi", "data_name": "Kuorma-auto (7,5-17t) (diesel)", "emission_factor_name": "Kuorma-auto (7,5-17t) (diesel)  ", "result_name": "Kuorma-auto (7,5-17t) (diesel)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 63, "calculation_definition_id": 63, "locale": "fi", "data_name": "Pakettiauto (diesel)", "emission_factor_name": "Pakettiauto (diesel)  ", "result_name": "Pakettiauto (diesel)", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 64, "calculation_definition_id": 64, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen  ", "result_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen", "created_at": "2025-06-25 10:43:02", "updated_at": "2025-06-25 10:43:02"}, {"id": 65, "calculation_definition_id": 65, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>  ", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 66, "calculation_definition_id": 66, "locale": "fi", "data_name": "<PERSON><PERSON> autokuljetus, dieselin kulutus yhteens<PERSON>", "emission_factor_name": "Muu autokuljetus, diesel  ", "result_name": "<PERSON><PERSON> autokuljetus, dieselin kulutus yhteens<PERSON>  ", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 67, "calculation_definition_id": 67, "locale": "fi", "data_name": "<PERSON><PERSON>l<PERSON>, ben<PERSON><PERSON>n kulutus", "emission_factor_name": "<PERSON><PERSON> autokuljetus, ben<PERSON><PERSON>  ", "result_name": "<PERSON><PERSON>l<PERSON>, ben<PERSON><PERSON>n kulutus", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 68, "calculation_definition_id": 68, "locale": "fi", "data_name": "Muu autokuljetus, Neste MY dieselin kulutus", "emission_factor_name": "Muu autokuljetus, Neste MY diesel  ", "result_name": "Muu autokuljetus, Neste MY dieselin kulutus", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 69, "calculation_definition_id": 69, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 70, "calculation_definition_id": 70, "locale": "fi", "data_name": "Ajokilometrit, diesel-auto", "emission_factor_name": "Ajokilometrit, diesel-auto", "result_name": "Ajokilometrit, diesel-auto", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 71, "calculation_definition_id": 71, "locale": "fi", "data_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "emission_factor_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "result_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 72, "calculation_definition_id": 72, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 73, "calculation_definition_id": 73, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> ja pahvi", "emission_factor_name": "<PERSON><PERSON><PERSON> ja pahvi", "result_name": "<PERSON><PERSON><PERSON> ja pahvi", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 74, "calculation_definition_id": 74, "locale": "fi", "data_name": "Biojäte", "emission_factor_name": "Biojäte", "result_name": "Biojäte", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 75, "calculation_definition_id": 75, "locale": "fi", "data_name": "Linja-auto", "emission_factor_name": "Linja-auto  ", "result_name": "Linja-auto", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 76, "calculation_definition_id": 76, "locale": "fi", "data_name": "Henkilöauto", "emission_factor_name": "Henkilöauto  ", "result_name": "Henkilöauto", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 77, "calculation_definition_id": 77, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>  ", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 78, "calculation_definition_id": 78, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON> / ly<PERSON>et lennot", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON> / ly<PERSON>et lennot  ", "result_name": "<PERSON><PERSON><PERSON><PERSON> / ly<PERSON>et lennot", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 79, "calculation_definition_id": 79, "locale": "fi", "data_name": "Eurooppa / keskipitkät lennot", "emission_factor_name": "Eurooppa / keskipitkät lennot  ", "result_name": "Eurooppa / keskipitkät lennot", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 80, "calculation_definition_id": 80, "locale": "fi", "data_name": "Kaukolennot / pitkät lennot", "emission_factor_name": "Kaukolennot / pitkät lennot  ", "result_name": "Kaukolennot / pitkät lennot", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 81, "calculation_definition_id": 81, "locale": "fi", "data_name": "Raitiovaunu, metro ja lähijuna", "emission_factor_name": "Raitiovaunu, metro ja lähijuna  ", "result_name": "Raitiovaunu, metro ja lähijuna", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 82, "calculation_definition_id": 82, "locale": "fi", "data_name": "Hotelliyö Suomessa", "emission_factor_name": "Hotelliyö Suomessa", "result_name": "Hotelliyö Suomessa", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 83, "calculation_definition_id": 83, "locale": "fi", "data_name": "Työpäivien lukumäärä vuodessa", "emission_factor_name": "Työpäivien lukumäärä vuodessa", "result_name": "Työpäivien lukumäärä vuodessa", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 84, "calculation_definition_id": 84, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee autolla", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee autolla", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee autolla", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 85, "calculation_definition_id": 85, "locale": "fi", "data_name": "Keskimääräiset ajokilometrit päivässä", "emission_factor_name": "Keskimääräinen henkilöauto  ", "result_name": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>lla", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 86, "calculation_definition_id": 86, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee kaupunkibussilla", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee kaupunkibussilla", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka kulkee kaupunkibussilla", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 87, "calculation_definition_id": 87, "locale": "fi", "data_name": "Keskimääräinen linja-automatka päivässä", "emission_factor_name": "Linja-auto  ", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 88, "calculation_definition_id": 88, "locale": "fi", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 89, "calculation_definition_id": 89, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "emission_factor_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "result_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 90, "calculation_definition_id": 90, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 91, "calculation_definition_id": 91, "locale": "fi", "data_name": "Ajokilometrit, diesel-auto", "emission_factor_name": "Ajokilometrit, diesel-auto", "result_name": "Ajokilometrit, diesel-auto", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 92, "calculation_definition_id": 92, "locale": "fi", "data_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "emission_factor_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "result_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:03", "updated_at": "2025-06-25 10:43:03"}, {"id": 93, "calculation_definition_id": 93, "locale": "fi", "data_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo", "emission_factor_name": "Sähkö, Suomen keskiarvo", "result_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-07-24 11:18:04"}, {"id": 94, "calculation_definition_id": 94, "locale": "fi", "data_name": "Sähkönkulutus, päästötön sähkö", "emission_factor_name": "Sähkö, vihreä/päästötön sähkö", "result_name": "Sähkönkulutus, päästötön sähkö", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 95, "calculation_definition_id": 95, "locale": "fi", "data_name": "Kaukolämmö<PERSON> kulu<PERSON>, Suomen keskiarvo", "emission_factor_name": "Kaukolämpö, Suomen keskiarvo", "result_name": "Kaukolämmö<PERSON> kulu<PERSON>, Suomen keskiarvo", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-08-05 09:25:01"}, {"id": 96, "calculation_definition_id": 96, "locale": "fi", "data_name": "Kaukojäähdytyksen kulutus, Suomen keskiarvo", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Suomen keskiarvo", "result_name": "Kaukojäähdytyksen kulutus, Suomen keskiarvo", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-08-05 09:27:20"}, {"id": 97, "calculation_definition_id": 97, "locale": "fi", "data_name": "Puoliperävaunurekka (3,5-33t) (diesel)", "emission_factor_name": "Puoliperävaunurekka (3,5-33t) (diesel)  ", "result_name": "Puoliperävaunurekka (3,5-33t) (diesel)", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 98, "calculation_definition_id": 98, "locale": "fi", "data_name": "Kuorma-auto (7,5-17t) (diesel)", "emission_factor_name": "Kuorma-auto (7,5-17t) (diesel)  ", "result_name": "Kuorma-auto (7,5-17t) (diesel)", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 99, "calculation_definition_id": 99, "locale": "fi", "data_name": "Pakettiauto (diesel)", "emission_factor_name": "Pakettiauto (diesel)  ", "result_name": "Pakettiauto (diesel)", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 100, "calculation_definition_id": 100, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen  ", "result_name": "<PERSON><PERSON><PERSON><PERSON>, keskimääräinen", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 101, "calculation_definition_id": 101, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>  ", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 102, "calculation_definition_id": 102, "locale": "fi", "data_name": "<PERSON><PERSON> autokuljetus, dieselin kulutus yhteens<PERSON>", "emission_factor_name": "Muu autokuljetus, diesel  ", "result_name": "<PERSON><PERSON> autokuljetus, dieselin kulutus yhteens<PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 103, "calculation_definition_id": 103, "locale": "fi", "data_name": "<PERSON><PERSON>l<PERSON>, ben<PERSON><PERSON>n kulutus", "emission_factor_name": "<PERSON><PERSON> autokuljetus, ben<PERSON><PERSON>  ", "result_name": "<PERSON><PERSON>l<PERSON>, ben<PERSON><PERSON>n kulutus", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 104, "calculation_definition_id": 104, "locale": "fi", "data_name": "Muu autokuljetus, Neste MY dieselin kulutus", "emission_factor_name": "Muu autokuljetus, Neste MY diesel  ", "result_name": "Muu autokuljetus, Neste MY dieselin kulutus", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 105, "calculation_definition_id": 105, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 106, "calculation_definition_id": 106, "locale": "fi", "data_name": "Ajokilometrit, diesel-auto", "emission_factor_name": "Ajokilometrit, diesel-auto", "result_name": "Ajokilometrit, diesel-auto", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 107, "calculation_definition_id": 107, "locale": "fi", "data_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "emission_factor_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "result_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 108, "calculation_definition_id": 108, "locale": "fi", "data_name": "Prosessointiin kuluva sähkö", "emission_factor_name": "Prosessointiin kuluva sähkö (Suomen keskiarvo)", "result_name": "Prosessointiin kuluva sähkö", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-07-24 11:19:19"}, {"id": 109, "calculation_definition_id": 109, "locale": "fi", "data_name": "Käyttöön kuluva sähkö", "emission_factor_name": "Käyttöön kuluva sähkö (Suomen keskiarvo)", "result_name": "Käyttöön kuluva sähkö", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-07-24 11:20:19"}, {"id": 110, "calculation_definition_id": 110, "locale": "fi", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 111, "calculation_definition_id": 111, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> ja pahvi", "emission_factor_name": "<PERSON><PERSON><PERSON> ja pahvi", "result_name": "<PERSON><PERSON><PERSON> ja pahvi", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 112, "calculation_definition_id": 112, "locale": "fi", "data_name": "Biojäte", "emission_factor_name": "Biojäte", "result_name": "Biojäte", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 113, "calculation_definition_id": 113, "locale": "fi", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 114, "calculation_definition_id": 114, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "emission_factor_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "result_name": "<PERSON><PERSON><PERSON> (moottoribensiini)", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 115, "calculation_definition_id": 115, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON> tunt<PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 116, "calculation_definition_id": 116, "locale": "fi", "data_name": "Ajokilometrit, diesel-auto", "emission_factor_name": "Ajokilometrit, diesel-auto", "result_name": "Ajokilometrit, diesel-auto", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 117, "calculation_definition_id": 117, "locale": "fi", "data_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "emission_factor_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "result_name": "Ajokilometrit, ben<PERSON><PERSON><PERSON>", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 118, "calculation_definition_id": 118, "locale": "fi", "data_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo ", "emission_factor_name": "Sähkö, Suomen keskiarvo ", "result_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo ", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-07-24 11:21:36"}, {"id": 119, "calculation_definition_id": 119, "locale": "fi", "data_name": "Sähkönkulutus, päästötön sähkö", "emission_factor_name": "Sähkö, vihreä/päästötön sähkö", "result_name": "Sähkönkulutus, päästötön sähkö", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-06-25 10:43:04"}, {"id": 120, "calculation_definition_id": 120, "locale": "fi", "data_name": "Kaukolämmö<PERSON> kulu<PERSON>, Suomen keskiarvo", "emission_factor_name": "Kaukolämpö, Suomen keskiarvo", "result_name": "Kaukolämmö<PERSON> kulu<PERSON>, Suomen keskiarvo", "created_at": "2025-06-25 10:43:04", "updated_at": "2025-08-05 10:15:27"}, {"id": 121, "calculation_definition_id": 121, "locale": "fi", "data_name": "Kaukojäähdytyksen kulutus, Suomen keskiarvo", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Suomen keskiarvo", "result_name": "Kaukojäähdytyksen kulutus, Suomen keskiarvo", "created_at": "2025-06-25 10:43:05", "updated_at": "2025-08-05 10:16:01"}, {"id": 122, "calculation_definition_id": 130, "locale": "fi", "data_name": "JUSSIN TESTI: <PERSON><PERSON><PERSON><PERSON><PERSON>, Suomen jäännösjakauma", "emission_factor_name": "JUSSIN TESTI:  <PERSON><PERSON><PERSON><PERSON><PERSON>, Suomen jäännösjakauma", "result_name": "JUSSIN TESTI:  markkinaperusteinen sähkö", "created_at": "2025-06-27 05:31:11", "updated_at": "2025-06-27 05:31:11"}, {"id": 123, "calculation_definition_id": 131, "locale": "fi", "data_name": "TESTI SÄHKÖ SUOMEN KESKIARVO", "emission_factor_name": "TESTI SÄHKÖ SUOMEN KESKIARVO", "result_name": "TESTI SÄHKÖ SUOMEN KESKIARVO, MARKKINAPERUSTEINEN", "created_at": "2025-06-27 06:02:51", "updated_at": "2025-06-27 06:02:51"}, {"id": 124, "calculation_definition_id": 133, "locale": "fi", "data_name": "2029 testilasku", "emission_factor_name": "2029 testi ", "result_name": "2029 testi", "created_at": "2025-06-30 07:32:52", "updated_at": "2025-06-30 07:32:52"}, {"id": 125, "calculation_definition_id": 1, "locale": "en", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-07-22 06:09:03", "updated_at": "2025-07-22 06:09:03"}, {"id": 126, "calculation_definition_id": 2, "locale": "en", "data_name": "Petrol", "emission_factor_name": "Petrol (motor petrol)", "result_name": "Petrol", "created_at": "2025-07-22 07:27:07", "updated_at": "2025-07-22 07:27:07"}, {"id": 127, "calculation_definition_id": 3, "locale": "en", "data_name": "Light fuel oil", "emission_factor_name": "Light fuel oil (low sulphur)", "result_name": "Light fuel oil", "created_at": "2025-07-22 07:27:41", "updated_at": "2025-07-22 07:27:41"}, {"id": 128, "calculation_definition_id": 4, "locale": "en", "data_name": "Natural gas", "emission_factor_name": "Natural gas", "result_name": "Natural gas", "created_at": "2025-07-22 07:29:11", "updated_at": "2025-07-22 07:29:11"}, {"id": 129, "calculation_definition_id": 5, "locale": "en", "data_name": "Heavy fuel oil", "emission_factor_name": "Heavy fuel oil (sulphur content ≥ 1 %)", "result_name": "Heavy fuel oil", "created_at": "2025-07-22 07:29:48", "updated_at": "2025-07-22 07:29:48"}, {"id": 130, "calculation_definition_id": 6, "locale": "en", "data_name": "Neste My Diesel", "emission_factor_name": "Neste My Diesel", "result_name": "Neste MyDiesel", "created_at": "2025-07-22 07:29:59", "updated_at": "2025-07-22 07:29:59"}, {"id": 131, "calculation_definition_id": 7, "locale": "en", "data_name": "(R134A) Water and liquid coolers, heat pumps", "emission_factor_name": "(R134A) Water and liquid coolers, heat pumps", "result_name": "(R134A) Water and liquid coolers, heat pumps", "created_at": "2025-07-22 07:30:29", "updated_at": "2025-07-23 06:25:52"}, {"id": 132, "calculation_definition_id": 8, "locale": "en", "data_name": "(R410A) Water and liquid coolers, heat pumps ", "emission_factor_name": "(R410A) Water and liquid coolers, heat pumps ", "result_name": "(R410A) Water and liquid coolers, heat pumps ", "created_at": "2025-07-22 07:30:58", "updated_at": "2025-07-23 06:26:11"}, {"id": 133, "calculation_definition_id": 9, "locale": "en", "data_name": "(R404A) Retail refrigeration equipment, commercial refrigeration equipment ", "emission_factor_name": "(R404A) Retail refrigeration equipment, commercial refrigeration equipment ", "result_name": "(R404A) Retail refrigeration equipment, commercial refrigeration equipment ", "created_at": "2025-07-22 07:31:31", "updated_at": "2025-07-23 06:26:24"}, {"id": 134, "calculation_definition_id": 10, "locale": "en", "data_name": "(R507A) Retail refrigeration equipment, commercial refrigeration equipment ", "emission_factor_name": "(R507A) Retail refrigeration equipment, commercial refrigeration equipment ", "result_name": "(R507A) Retail refrigeration equipment, commercial refrigeration equipment ", "created_at": "2025-07-22 07:31:54", "updated_at": "2025-07-23 06:26:38"}, {"id": 135, "calculation_definition_id": 11, "locale": "en", "data_name": "(R134a) Retail refrigeration equipment, commercial refrigeration equipment ", "emission_factor_name": "(R134a) Retail refrigeration equipment, commercial refrigeration equipment ", "result_name": "(R134a) Retail refrigeration equipment, commercial refrigeration equipment ", "created_at": "2025-07-22 07:32:31", "updated_at": "2025-07-23 06:26:52"}, {"id": 136, "calculation_definition_id": 12, "locale": "en", "data_name": "(R407A) Retail refrigeration equipment, commercial refrigeration equipment ", "emission_factor_name": "(R407A) Retail refrigeration equipment, commercial refrigeration equipment ", "result_name": "(R407A) Retail refrigeration equipment, commercial refrigeration equipment ", "created_at": "2025-07-22 07:32:51", "updated_at": "2025-07-23 06:27:08"}, {"id": 137, "calculation_definition_id": 13, "locale": "en", "data_name": "(R407F) Retail refrigeration equipment, commercial refrigeration equipment ", "emission_factor_name": "(R407F) Retail refrigeration equipment, commercial refrigeration equipment", "result_name": "(R407F) Retail refrigeration equipment, commercial refrigeration equipment", "created_at": "2025-07-22 07:33:21", "updated_at": "2025-07-23 06:27:22"}, {"id": 138, "calculation_definition_id": 14, "locale": "en", "data_name": "(R404A) Condensing units ", "emission_factor_name": "(R404A) Condensing units ", "result_name": "(R404A) Condensing units ", "created_at": "2025-07-22 07:33:49", "updated_at": "2025-07-23 06:27:36"}, {"id": 139, "calculation_definition_id": 15, "locale": "en", "data_name": "(R410A) Air conditioning ", "emission_factor_name": "(R410A) Air conditioning ", "result_name": "(R410A) Air conditioning ", "created_at": "2025-07-22 07:34:16", "updated_at": "2025-07-23 06:28:02"}, {"id": 140, "calculation_definition_id": 16, "locale": "en", "data_name": "(R407C) Air conditioning ", "emission_factor_name": "(R407C) Air conditioning ", "result_name": "(R407C) Air conditioning ", "created_at": "2025-07-22 07:34:38", "updated_at": "2025-07-23 06:28:13"}, {"id": 141, "calculation_definition_id": 17, "locale": "en", "data_name": "(R404A) Refrigerated transport ", "emission_factor_name": "(R404A) Refrigerated transport ", "result_name": "(R404A) Refrigerated transport ", "created_at": "2025-07-22 07:35:00", "updated_at": "2025-07-23 06:28:24"}, {"id": 142, "calculation_definition_id": 18, "locale": "en", "data_name": "(R134a) Refrigerated transport ", "emission_factor_name": "(R134a) Refrigerated transport ", "result_name": "(R134a) Refrigerated transport ", "created_at": "2025-07-22 07:35:20", "updated_at": "2025-07-23 06:28:36"}, {"id": 143, "calculation_definition_id": 19, "locale": "en", "data_name": "Direct nitrous oxide emissions", "emission_factor_name": "Direct nitrous oxide emissions", "result_name": "Direct nitrous oxide emissions", "created_at": "2025-07-22 07:35:48", "updated_at": "2025-07-22 07:35:48"}, {"id": 144, "calculation_definition_id": 21, "locale": "en", "data_name": "Direct methane emissions", "emission_factor_name": "Direct methane emissions", "result_name": "Direct methane emissions", "created_at": "2025-07-22 07:36:15", "updated_at": "2025-07-22 07:36:15"}, {"id": 145, "calculation_definition_id": 22, "locale": "en", "data_name": "Direct carbon dioxide emissions", "emission_factor_name": "Direct carbon dioxide emissions", "result_name": "Direct carbon dioxide emissions", "created_at": "2025-07-22 07:36:41", "updated_at": "2025-07-22 07:36:41"}, {"id": 146, "calculation_definition_id": 23, "locale": "en", "data_name": "Leave this empty ", "emission_factor_name": "Electricity (average consumption in Finland)", "result_name": "Location based electricity emissions", "created_at": "2025-07-22 07:48:48", "updated_at": "2025-07-23 10:02:48"}, {"id": 147, "calculation_definition_id": 24, "locale": "en", "data_name": "Electricity consumption (emission-free electricity)", "emission_factor_name": "Emission free electricity ", "result_name": "Market based electricity emissions", "created_at": "2025-07-22 07:52:53", "updated_at": "2025-07-22 07:52:53"}, {"id": 148, "calculation_definition_id": 25, "locale": "en", "data_name": "District heating consumption ", "emission_factor_name": "District heating, choose area", "result_name": "District heating", "created_at": "2025-07-22 08:00:54", "updated_at": "2025-07-23 06:31:48"}, {"id": 149, "calculation_definition_id": 26, "locale": "en", "data_name": "District heating consumption (emission-free heating)", "emission_factor_name": "District heating (emission-free heating), choose area", "result_name": "District heating consumption (emission-free heating)", "created_at": "2025-07-22 08:02:05", "updated_at": "2025-07-24 05:44:10"}, {"id": 150, "calculation_definition_id": 27, "locale": "en", "data_name": "District cooling (average district cooling)", "emission_factor_name": "District cooling (Finland average)", "result_name": "District cooling (Finland)", "created_at": "2025-07-22 08:43:18", "updated_at": "2025-07-22 08:43:18"}, {"id": 151, "calculation_definition_id": 28, "locale": "en", "data_name": "Purchased steam consumption", "emission_factor_name": "Steam, own energy company", "result_name": "Steam", "created_at": "2025-07-22 08:48:08", "updated_at": "2025-07-22 08:48:08"}, {"id": 152, "calculation_definition_id": 29, "locale": "en", "data_name": "Laptop computers", "emission_factor_name": "Laptop computers", "result_name": "Laptop computers", "created_at": "2025-07-22 08:49:36", "updated_at": "2025-07-22 08:49:36"}, {"id": 153, "calculation_definition_id": 30, "locale": "en", "data_name": "Mobile phones", "emission_factor_name": "Mobile phones", "result_name": "Mobile phones", "created_at": "2025-07-22 08:50:54", "updated_at": "2025-07-22 08:50:54"}, {"id": 154, "calculation_definition_id": 31, "locale": "en", "data_name": "Tablet devices", "emission_factor_name": "Tablet devices", "result_name": "Tablet devices", "created_at": "2025-07-22 08:51:33", "updated_at": "2025-07-22 08:51:33"}, {"id": 155, "calculation_definition_id": 32, "locale": "en", "data_name": "Large electrical appliances", "emission_factor_name": "Large electrical appliances", "result_name": "Large electrical appliances", "created_at": "2025-07-22 08:52:12", "updated_at": "2025-07-22 08:52:12"}, {"id": 156, "calculation_definition_id": 33, "locale": "en", "data_name": "Small electrical appliances", "emission_factor_name": "Small electrical appliances", "result_name": "Small electrical appliances", "created_at": "2025-07-22 08:53:34", "updated_at": "2025-07-22 08:53:34"}, {"id": 157, "calculation_definition_id": 34, "locale": "en", "data_name": "Cotton garments", "emission_factor_name": "Cotton garments", "result_name": "Cotton garments", "created_at": "2025-07-22 08:54:23", "updated_at": "2025-07-22 08:54:23"}, {"id": 158, "calculation_definition_id": 35, "locale": "en", "data_name": "Synthetic fibre garments", "emission_factor_name": "Synthetic fibre garments", "result_name": "Synthetic fibre garments", "created_at": "2025-07-22 08:54:46", "updated_at": "2025-07-22 08:54:46"}, {"id": 159, "calculation_definition_id": 36, "locale": "en", "data_name": "Steel products", "emission_factor_name": "Steel products", "result_name": "Steel products", "created_at": "2025-07-22 08:55:07", "updated_at": "2025-07-22 08:55:07"}, {"id": 160, "calculation_definition_id": 37, "locale": "en", "data_name": "Aluminium products", "emission_factor_name": "Aluminium products", "result_name": "Aluminium products", "created_at": "2025-07-22 08:55:31", "updated_at": "2025-07-22 08:55:31"}, {"id": 161, "calculation_definition_id": 38, "locale": "en", "data_name": "Timber products", "emission_factor_name": "Timber products", "result_name": "Timber products", "created_at": "2025-07-22 08:56:12", "updated_at": "2025-07-22 08:56:12"}, {"id": 162, "calculation_definition_id": 39, "locale": "en", "data_name": "Plastic products", "emission_factor_name": "Plastic products", "result_name": "Plastic products", "created_at": "2025-07-22 08:56:32", "updated_at": "2025-07-22 08:56:32"}, {"id": 163, "calculation_definition_id": 40, "locale": "en", "data_name": "Paper products", "emission_factor_name": "Paper products", "result_name": "Paper products", "created_at": "2025-07-22 08:56:48", "updated_at": "2025-07-22 08:56:48"}, {"id": 164, "calculation_definition_id": 41, "locale": "en", "data_name": "Cardboard products", "emission_factor_name": "Cardboard products", "result_name": "Cardboard products", "created_at": "2025-07-22 08:57:08", "updated_at": "2025-07-22 08:57:08"}, {"id": 165, "calculation_definition_id": 42, "locale": "en", "data_name": "Glass products", "emission_factor_name": "Glass products", "result_name": "Glass products", "created_at": "2025-07-22 08:57:33", "updated_at": "2025-07-22 08:57:33"}, {"id": 166, "calculation_definition_id": 43, "locale": "en", "data_name": "Beef (from dairy cattle)", "emission_factor_name": "Beef (from dairy cattle)", "result_name": "Beef (from dairy cattle)", "created_at": "2025-07-22 08:57:56", "updated_at": "2025-07-22 08:57:56"}, {"id": 167, "calculation_definition_id": 44, "locale": "en", "data_name": "Pork", "emission_factor_name": "Pork", "result_name": "Pork", "created_at": "2025-07-22 08:58:13", "updated_at": "2025-07-22 08:58:13"}, {"id": 168, "calculation_definition_id": 45, "locale": "en", "data_name": "Rainbow trout", "emission_factor_name": "Rainbow trout", "result_name": "Rainbow trout", "created_at": "2025-07-22 08:58:30", "updated_at": "2025-07-22 08:58:30"}, {"id": 169, "calculation_definition_id": 46, "locale": "en", "data_name": "Poultry", "emission_factor_name": "Poultry", "result_name": "Poultry", "created_at": "2025-07-22 08:59:00", "updated_at": "2025-07-22 08:59:00"}, {"id": 170, "calculation_definition_id": 47, "locale": "en", "data_name": "Milk", "emission_factor_name": "Milk", "result_name": "Milk", "created_at": "2025-07-22 08:59:32", "updated_at": "2025-07-22 08:59:32"}, {"id": 171, "calculation_definition_id": 49, "locale": "en", "data_name": "Petrol (motor petrol)", "emission_factor_name": "Petrol (motor petrol)", "result_name": "Petrol (motor petrol)", "created_at": "2025-07-22 09:16:27", "updated_at": "2025-07-22 09:16:27"}, {"id": 172, "calculation_definition_id": 48, "locale": "en", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-07-22 09:16:52", "updated_at": "2025-07-22 09:16:52"}, {"id": 173, "calculation_definition_id": 50, "locale": "en", "data_name": "Light fuel oil (low sulphur)", "emission_factor_name": "Light fuel oil (low sulphur)", "result_name": "Light fuel oil (low sulphur)", "created_at": "2025-07-22 09:17:35", "updated_at": "2025-07-22 09:17:35"}, {"id": 174, "calculation_definition_id": 51, "locale": "en", "data_name": "Natural gas", "emission_factor_name": "Natural gas", "result_name": "Natural gas", "created_at": "2025-07-22 09:19:01", "updated_at": "2025-07-22 09:19:01"}, {"id": 175, "calculation_definition_id": 52, "locale": "en", "data_name": "Heavy fuel oil (sulphur content ≥ 1 %)", "emission_factor_name": "Heavy fuel oil (sulphur content ≥ 1 %)", "result_name": "Heavy fuel oil (sulphur content ≥ 1 %)", "created_at": "2025-07-22 09:19:26", "updated_at": "2025-07-22 09:19:26"}, {"id": 176, "calculation_definition_id": 53, "locale": "en", "data_name": "Neste MyDiesel", "emission_factor_name": "Neste MyDiesel", "result_name": "Neste MyDiesel", "created_at": "2025-07-22 09:20:02", "updated_at": "2025-07-22 09:20:02"}, {"id": 177, "calculation_definition_id": 54, "locale": "en", "data_name": "Nuclear ", "emission_factor_name": "Nuclear production", "result_name": "Nuclear production emissions", "created_at": "2025-07-22 09:23:50", "updated_at": "2025-07-22 09:23:50"}, {"id": 178, "calculation_definition_id": 55, "locale": "en", "data_name": "Wind power", "emission_factor_name": "Wind power production", "result_name": "Wind power production emissions", "created_at": "2025-07-22 09:24:33", "updated_at": "2025-07-22 09:24:33"}, {"id": 179, "calculation_definition_id": 56, "locale": "en", "data_name": "Coal", "emission_factor_name": "Coal", "result_name": "Coal", "created_at": "2025-07-22 09:25:59", "updated_at": "2025-07-22 09:25:59"}, {"id": 180, "calculation_definition_id": 57, "locale": "en", "data_name": "Oil", "emission_factor_name": "Oil", "result_name": "Oil", "created_at": "2025-07-22 09:26:25", "updated_at": "2025-07-22 09:26:25"}, {"id": 181, "calculation_definition_id": 58, "locale": "en", "data_name": "Peat", "emission_factor_name": "Peat", "result_name": "Peat", "created_at": "2025-07-22 09:26:50", "updated_at": "2025-07-22 09:26:50"}, {"id": 182, "calculation_definition_id": 59, "locale": "en", "data_name": "Biomass", "emission_factor_name": "Biomass", "result_name": "Biomass", "created_at": "2025-07-22 09:27:25", "updated_at": "2025-07-22 09:27:25"}, {"id": 183, "calculation_definition_id": 60, "locale": "en", "data_name": "Share of transmission losses in electricity production", "emission_factor_name": "Share of transmission losses in electricity production", "result_name": "Share of transmission losses in electricity production", "created_at": "2025-07-22 09:49:16", "updated_at": "2025-07-23 10:06:12"}, {"id": 184, "calculation_definition_id": 61, "locale": "en", "data_name": "Semi-trailer truck (3.5-33t) (diesel)", "emission_factor_name": "Semi-trailer truck (3.5-33t) (diesel)", "result_name": "Semi-trailer truck (3.5-33t) (diesel)", "created_at": "2025-07-22 10:04:28", "updated_at": "2025-07-22 10:04:28"}, {"id": 185, "calculation_definition_id": 62, "locale": "en", "data_name": "Truck (7.5-17t) (diesel)", "emission_factor_name": "Truck (7.5-17t) (diesel)", "result_name": "Truck (7.5-17t) (diesel)", "created_at": "2025-07-22 10:04:54", "updated_at": "2025-07-22 10:04:54"}, {"id": 186, "calculation_definition_id": 63, "locale": "en", "data_name": "<PERSON> (Diesel)", "emission_factor_name": "<PERSON> (Diesel)", "result_name": "<PERSON> (Diesel)", "created_at": "2025-07-22 10:05:32", "updated_at": "2025-07-22 10:05:32"}, {"id": 187, "calculation_definition_id": 64, "locale": "en", "data_name": "Container ship, average", "emission_factor_name": "Container ship, average", "result_name": "Container ship, average", "created_at": "2025-07-22 10:05:49", "updated_at": "2025-07-22 10:05:49"}, {"id": 188, "calculation_definition_id": 65, "locale": "en", "data_name": "Train", "emission_factor_name": "Train", "result_name": "Train", "created_at": "2025-07-22 10:06:11", "updated_at": "2025-07-22 10:06:11"}, {"id": 189, "calculation_definition_id": 66, "locale": "en", "data_name": "Other car transport, total diesel consumption", "emission_factor_name": "diesel  ", "result_name": "Other car transport, diesel", "created_at": "2025-07-22 10:08:38", "updated_at": "2025-07-22 10:11:12"}, {"id": 190, "calculation_definition_id": 67, "locale": "en", "data_name": "Other car transport, gasoline consumption", "emission_factor_name": "Petrol (motor petrol)", "result_name": "Other car transport, gasoline", "created_at": "2025-07-22 10:10:09", "updated_at": "2025-07-22 10:11:56"}, {"id": 191, "calculation_definition_id": 68, "locale": "en", "data_name": "Other car transport, Neste MY diesel consumption", "emission_factor_name": "Neste MY diesel", "result_name": "Other car transport, Neste MY diesel ", "created_at": "2025-07-22 10:12:49", "updated_at": "2025-07-22 10:12:49"}, {"id": 192, "calculation_definition_id": 69, "locale": "en", "data_name": "Vehicle kilometeres, fuel type unknown", "emission_factor_name": "Vehicle kilometers, fuel type unknown", "result_name": "Vehicle kilometers, fuel type unknown", "created_at": "2025-07-22 10:14:08", "updated_at": "2025-07-22 10:14:08"}, {"id": 193, "calculation_definition_id": 70, "locale": "en", "data_name": "Vehicle kilometers, diesel vehicle", "emission_factor_name": "Vehicle kilometers, diesel vehicle", "result_name": "Vehicle kilometers, diesel vehicle", "created_at": "2025-07-22 10:15:54", "updated_at": "2025-07-22 10:15:54"}, {"id": 194, "calculation_definition_id": 71, "locale": "en", "data_name": "Vehicle kilometers, petrol vehicle", "emission_factor_name": "Vehicle kilometers, petrol vehicle", "result_name": "Vehicle kilometers, petrol vehicle", "created_at": "2025-07-22 10:16:36", "updated_at": "2025-07-22 10:16:36"}, {"id": 195, "calculation_definition_id": 72, "locale": "en", "data_name": "Paper", "emission_factor_name": "Paper", "result_name": "Paper", "created_at": "2025-07-22 10:17:20", "updated_at": "2025-07-22 10:17:20"}, {"id": 196, "calculation_definition_id": 73, "locale": "en", "data_name": "Cardboard", "emission_factor_name": "Cardboard", "result_name": "Cardboard", "created_at": "2025-07-22 10:17:48", "updated_at": "2025-07-22 10:17:48"}, {"id": 197, "calculation_definition_id": 74, "locale": "en", "data_name": "Biowaste", "emission_factor_name": "Biowaste", "result_name": "Biowaste", "created_at": "2025-07-22 10:18:07", "updated_at": "2025-07-22 10:18:07"}, {"id": 198, "calculation_definition_id": 75, "locale": "en", "data_name": "Bus", "emission_factor_name": "Bus", "result_name": "Bus", "created_at": "2025-07-22 10:18:38", "updated_at": "2025-07-22 10:18:38"}, {"id": 199, "calculation_definition_id": 76, "locale": "en", "data_name": "Car", "emission_factor_name": "Car", "result_name": "Car", "created_at": "2025-07-22 10:19:11", "updated_at": "2025-07-22 10:19:11"}, {"id": 200, "calculation_definition_id": 77, "locale": "en", "data_name": "Long-distance train", "emission_factor_name": "Long-distance train", "result_name": "Long-distance train", "created_at": "2025-07-22 10:23:19", "updated_at": "2025-07-22 10:23:19"}, {"id": 201, "calculation_definition_id": 78, "locale": "en", "data_name": "Domestic / short flights", "emission_factor_name": "Domestic / short flights", "result_name": "Domestic / short flights", "created_at": "2025-07-22 10:23:57", "updated_at": "2025-07-22 10:23:57"}, {"id": 202, "calculation_definition_id": 79, "locale": "en", "data_name": "Europe / Medium length flights", "emission_factor_name": "Europe / Medium length flights", "result_name": "Europe / Medium length flights", "created_at": "2025-07-22 10:25:00", "updated_at": "2025-07-22 10:25:00"}, {"id": 203, "calculation_definition_id": 80, "locale": "en", "data_name": "Long flights", "emission_factor_name": "Long flights", "result_name": "Long flights", "created_at": "2025-07-22 10:25:35", "updated_at": "2025-07-22 10:25:35"}, {"id": 204, "calculation_definition_id": 81, "locale": "en", "data_name": "Tram, subway and commuter train", "emission_factor_name": "Tram, subway and commuter train", "result_name": "Tram, subway and commuter train", "created_at": "2025-07-22 10:25:56", "updated_at": "2025-07-22 10:25:56"}, {"id": 205, "calculation_definition_id": 82, "locale": "en", "data_name": "Hotel stay ", "emission_factor_name": "Hotel stay ", "result_name": "Hotel stay ", "created_at": "2025-07-22 10:26:52", "updated_at": "2025-07-22 10:26:52"}, {"id": 206, "calculation_definition_id": 83, "locale": "en", "data_name": "Number of working days per year", "emission_factor_name": "Number of working days per year", "result_name": "Number of working days per year", "created_at": "2025-07-22 10:29:14", "updated_at": "2025-07-22 10:29:14"}, {"id": 207, "calculation_definition_id": 84, "locale": "en", "data_name": "Personnel who travel by car", "emission_factor_name": "Personnel who travel by car", "result_name": "Personnel who travel by car", "created_at": "2025-07-22 10:30:14", "updated_at": "2025-07-22 10:30:14"}, {"id": 208, "calculation_definition_id": 85, "locale": "en", "data_name": "Average mileage per day by car", "emission_factor_name": "Average car", "result_name": "Employee commuting by car", "created_at": "2025-07-22 10:33:55", "updated_at": "2025-07-22 10:38:43"}, {"id": 209, "calculation_definition_id": 86, "locale": "en", "data_name": "Personnel who travel by city bus", "emission_factor_name": "Personnel who travel by city bus", "result_name": "Personnel who travel by city bus", "created_at": "2025-07-22 10:36:15", "updated_at": "2025-07-22 10:36:15"}, {"id": 210, "calculation_definition_id": 87, "locale": "en", "data_name": "Average bus journey per day", "emission_factor_name": "Bus ", "result_name": "Employee commuting by bus", "created_at": "2025-07-22 10:37:41", "updated_at": "2025-07-22 10:37:41"}, {"id": 211, "calculation_definition_id": 88, "locale": "en", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-07-22 10:40:57", "updated_at": "2025-07-22 10:40:57"}, {"id": 212, "calculation_definition_id": 89, "locale": "en", "data_name": "Petrol (motor petrol)", "emission_factor_name": "Petrol (motor petrol)", "result_name": "Petrol (motor petrol)", "created_at": "2025-07-22 10:41:32", "updated_at": "2025-07-22 10:41:32"}, {"id": 213, "calculation_definition_id": 90, "locale": "en", "data_name": "Vehicle kilometers, fuel type unknown", "emission_factor_name": "Vehicle kilometers, fuel type unknown", "result_name": "Vehicle kilometers, fuel type unknown", "created_at": "2025-07-22 10:42:17", "updated_at": "2025-07-22 10:42:17"}, {"id": 214, "calculation_definition_id": 91, "locale": "en", "data_name": "Vehicle kilometers, diesel vehicle", "emission_factor_name": "Vehicle kilometers, diesel vehicle", "result_name": "Vehicle kilometers, diesel vehicle", "created_at": "2025-07-22 10:42:52", "updated_at": "2025-07-22 10:42:52"}, {"id": 215, "calculation_definition_id": 92, "locale": "en", "data_name": "Vehicle kilometers, petrol vehicle", "emission_factor_name": "Vehicle kilometers, petrol vehicle", "result_name": "Vehicle kilometers, petrol vehicle", "created_at": "2025-07-22 10:43:23", "updated_at": "2025-07-22 10:43:23"}, {"id": 216, "calculation_definition_id": 93, "locale": "en", "data_name": "Electricity consumption (average consumption in Finland)", "emission_factor_name": "Electricity (average in Finland)", "result_name": "Electricity consumption (average in Finland)", "created_at": "2025-07-22 10:44:08", "updated_at": "2025-07-24 11:23:59"}, {"id": 217, "calculation_definition_id": 94, "locale": "en", "data_name": "Electricity consumption (emission-free electricity)", "emission_factor_name": "Electricity (emission-free electricity)", "result_name": "Electricity consumption (emission-free electricity)", "created_at": "2025-07-22 10:44:58", "updated_at": "2025-07-22 10:44:58"}, {"id": 218, "calculation_definition_id": 95, "locale": "en", "data_name": "District heating consumption (Average in Finland)", "emission_factor_name": "District heating (Average in Finland)", "result_name": "District heating consumption (Average in Finland)", "created_at": "2025-07-22 10:45:44", "updated_at": "2025-07-22 10:46:40"}, {"id": 219, "calculation_definition_id": 96, "locale": "en", "data_name": "District cooling consumption (average in Finland)", "emission_factor_name": "District cooling (average in Finland)", "result_name": "District cooling consumption (average in Finland)", "created_at": "2025-07-22 10:47:47", "updated_at": "2025-07-22 10:47:47"}, {"id": 220, "calculation_definition_id": 97, "locale": "en", "data_name": "Semi-trailer truck (3.5-33t) (diesel)", "emission_factor_name": "Semi-trailer truck (3.5-33t) (diesel)", "result_name": "Semi-trailer truck (3.5-33t) (diesel)", "created_at": "2025-07-22 10:49:21", "updated_at": "2025-07-22 10:49:21"}, {"id": 221, "calculation_definition_id": 98, "locale": "en", "data_name": "Truck (7.5-17t) (diesel)", "emission_factor_name": "Truck (7.5-17t) (diesel)", "result_name": "Truck (7.5-17t) (diesel)", "created_at": "2025-07-22 10:53:07", "updated_at": "2025-07-22 10:53:07"}, {"id": 222, "calculation_definition_id": 99, "locale": "en", "data_name": "<PERSON> (Diesel)", "emission_factor_name": "<PERSON> (Diesel)", "result_name": "<PERSON> (Diesel)", "created_at": "2025-07-22 10:53:24", "updated_at": "2025-07-22 10:53:24"}, {"id": 223, "calculation_definition_id": 100, "locale": "en", "data_name": "Container ship, average", "emission_factor_name": "Container ship, average", "result_name": "Container ship, average", "created_at": "2025-07-22 10:54:26", "updated_at": "2025-07-22 10:54:26"}, {"id": 224, "calculation_definition_id": 101, "locale": "en", "data_name": "Train", "emission_factor_name": "Train", "result_name": "Train", "created_at": "2025-07-22 10:54:46", "updated_at": "2025-07-22 10:54:46"}, {"id": 225, "calculation_definition_id": 102, "locale": "en", "data_name": "Other car transport, total diesel consumption", "emission_factor_name": "Other car transport, diesel ", "result_name": "Other car transport, diesel ", "created_at": "2025-07-22 10:55:35", "updated_at": "2025-07-22 10:55:35"}, {"id": 226, "calculation_definition_id": 103, "locale": "en", "data_name": "Other car transport, gasoline consumption", "emission_factor_name": "Other car transport, gasoline", "result_name": "Other car transport, gasoline", "created_at": "2025-07-22 10:56:02", "updated_at": "2025-07-22 10:56:02"}, {"id": 227, "calculation_definition_id": 104, "locale": "en", "data_name": "Other car transport, Neste MY diesel consumption", "emission_factor_name": "Other car transport, Neste MY diesel", "result_name": "Other car transport, Neste MY diesel", "created_at": "2025-07-22 10:56:33", "updated_at": "2025-07-22 10:56:33"}, {"id": 228, "calculation_definition_id": 105, "locale": "en", "data_name": "Vehicle kilometers, fuel type unknown", "emission_factor_name": "Vehicle kilometers, fuel type unknown", "result_name": "Vehicle kilometers, fuel type unknown", "created_at": "2025-07-22 10:57:20", "updated_at": "2025-07-22 10:57:20"}, {"id": 229, "calculation_definition_id": 106, "locale": "en", "data_name": "Vehicle kilometers, diesel vehicle", "emission_factor_name": "Vehicle kilometers, diesel vehicle", "result_name": "Vehicle kilometers, diesel vehicle", "created_at": "2025-07-22 10:57:43", "updated_at": "2025-07-22 10:57:43"}, {"id": 230, "calculation_definition_id": 107, "locale": "en", "data_name": "Vehicle kilometers, petrol vehicle", "emission_factor_name": "Vehicle kilometers, petrol vehicle", "result_name": "Vehicle kilometers, petrol vehicle", "created_at": "2025-07-22 10:58:05", "updated_at": "2025-07-22 10:58:05"}, {"id": 231, "calculation_definition_id": 108, "locale": "en", "data_name": "Electricity used for processing", "emission_factor_name": "Electricity (average in Finland)", "result_name": "Electricity used for processing", "created_at": "2025-07-22 10:59:22", "updated_at": "2025-07-24 11:24:46"}, {"id": 232, "calculation_definition_id": 109, "locale": "en", "data_name": "Electricity used during use phase", "emission_factor_name": "Electricity (average in Finland)", "result_name": "Electricity used during use phase", "created_at": "2025-07-22 11:01:25", "updated_at": "2025-07-24 11:25:30"}, {"id": 233, "calculation_definition_id": 110, "locale": "en", "data_name": "Paper", "emission_factor_name": "Paper", "result_name": "Paper", "created_at": "2025-07-22 11:01:49", "updated_at": "2025-07-22 11:01:49"}, {"id": 234, "calculation_definition_id": 111, "locale": "en", "data_name": "Cardboard", "emission_factor_name": "Cardboard", "result_name": "Cardboard", "created_at": "2025-07-22 11:02:10", "updated_at": "2025-07-22 11:02:10"}, {"id": 235, "calculation_definition_id": 112, "locale": "en", "data_name": "Biowaste", "emission_factor_name": "Biowaste", "result_name": "Biowaste", "created_at": "2025-07-22 11:02:27", "updated_at": "2025-07-22 11:02:27"}, {"id": 236, "calculation_definition_id": 113, "locale": "en", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-07-22 11:02:40", "updated_at": "2025-07-22 11:02:40"}, {"id": 237, "calculation_definition_id": 114, "locale": "en", "data_name": "Petrol (motor petrol)", "emission_factor_name": "Petrol (motor petrol)", "result_name": "Petrol (motor petrol)", "created_at": "2025-07-22 11:03:02", "updated_at": "2025-07-22 11:03:02"}, {"id": 238, "calculation_definition_id": 115, "locale": "en", "data_name": "Vehicle kilometers, fuel type unknown", "emission_factor_name": "Vehicle kilometers, fuel type unknown", "result_name": "Vehicle kilometers, fuel type unknown", "created_at": "2025-07-22 11:03:26", "updated_at": "2025-07-22 11:03:26"}, {"id": 239, "calculation_definition_id": 116, "locale": "en", "data_name": "Vehicle kilometers, diesel vehicle", "emission_factor_name": "Vehicle kilometers, diesel vehicle", "result_name": "Vehicle kilometers, diesel vehicle", "created_at": "2025-07-22 11:03:59", "updated_at": "2025-07-22 11:03:59"}, {"id": 240, "calculation_definition_id": 117, "locale": "en", "data_name": "Vehicle kilometers, petrol vehicle", "emission_factor_name": "Vehicle kilometers, petrol vehicle", "result_name": "Vehicle kilometers, petrol vehicle", "created_at": "2025-07-22 11:04:27", "updated_at": "2025-07-22 11:04:27"}, {"id": 241, "calculation_definition_id": 118, "locale": "en", "data_name": "Electricity consumption (average consumption in Finland)", "emission_factor_name": "Electricity (average in Finland)", "result_name": "Electricity (average in Finland)", "created_at": "2025-07-22 11:04:58", "updated_at": "2025-07-24 11:26:15"}, {"id": 242, "calculation_definition_id": 119, "locale": "en", "data_name": "Electricity consumption (emission-free electricity)", "emission_factor_name": "Electricity (emission-free electricity)", "result_name": "Electricity (emission-free electricity)", "created_at": "2025-07-22 11:05:37", "updated_at": "2025-07-22 11:05:37"}, {"id": 243, "calculation_definition_id": 120, "locale": "en", "data_name": "District heating consumption (average in Finland)", "emission_factor_name": "District heating (average in Finland)", "result_name": "District heating consumption (average in Finland)", "created_at": "2025-07-22 11:06:21", "updated_at": "2025-07-22 11:06:21"}, {"id": 244, "calculation_definition_id": 121, "locale": "en", "data_name": "District cooling consumption (average in Finland)", "emission_factor_name": "District cooling (average in Finland)", "result_name": "District cooling consumption (average in Finland)", "created_at": "2025-07-22 11:06:53", "updated_at": "2025-07-22 11:06:53"}, {"id": 245, "calculation_definition_id": 145, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-23 07:38:57", "updated_at": "2025-07-23 07:38:57"}, {"id": 246, "calculation_definition_id": 147, "locale": "fi", "data_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo ", "emission_factor_name": "Sähkö, Suomen keskiarvo", "result_name": "Sähkö<PERSON>ulutus, Suomen keskiarvo", "created_at": "2025-07-23 07:44:16", "updated_at": "2025-07-24 11:22:18"}, {"id": 247, "calculation_definition_id": 148, "locale": "fi", "data_name": "Sähkönkulutus, päästötön sähkö", "emission_factor_name": "Sähkö, vihreä/päästötön sähkö", "result_name": "Sähkönkulutus, päästötön sähkö", "created_at": "2025-07-23 07:46:15", "updated_at": "2025-07-23 07:46:15"}, {"id": 248, "calculation_definition_id": 145, "locale": "en", "data_name": "Light Industrial building ", "emission_factor_name": "Light Industrial building ", "result_name": "Light Industrial building ", "created_at": "2025-07-23 07:52:47", "updated_at": "2025-07-23 07:52:47"}, {"id": 249, "calculation_definition_id": 147, "locale": "en", "data_name": "Electricity consumption (average consumption in Finland)", "emission_factor_name": "Electricity (average in Finland)", "result_name": "Electricity (average in Finland)", "created_at": "2025-07-23 07:53:23", "updated_at": "2025-07-24 11:26:54"}, {"id": 250, "calculation_definition_id": 148, "locale": "en", "data_name": "Electricity consumption (emission-free electricity)", "emission_factor_name": "Electricity (emission-free electricity)", "result_name": "Electricity consumption (emission-free electricity)", "created_at": "2025-07-23 07:53:49", "updated_at": "2025-07-23 07:53:49"}, {"id": 251, "calculation_definition_id": 149, "locale": "fi", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "Sijoituksen päästöintensiteetti ", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-07-23 08:01:36", "updated_at": "2025-07-23 08:01:36"}, {"id": 252, "calculation_definition_id": 149, "locale": "en", "data_name": "Investments", "emission_factor_name": "Investments emission-intensity ", "result_name": "Investments", "created_at": "2025-07-23 08:03:28", "updated_at": "2025-07-23 08:03:28"}, {"id": 253, "calculation_definition_id": 150, "locale": "fi", "data_name": "Sähkönk<PERSON>tus (varmentamaton alkuperä)", "emission_factor_name": "Sähkö (Suomen jäännösjakauma)", "result_name": "Markkinaperustainen sähkö", "created_at": "2025-07-23 09:07:24", "updated_at": "2025-07-23 09:12:10"}, {"id": 254, "calculation_definition_id": 150, "locale": "en", "data_name": "Electricity consumption (Uncertified electricity)", "emission_factor_name": "Electricity (Finland Residual mix)", "result_name": "Market based electricity emissions", "created_at": "2025-07-23 10:05:14", "updated_at": "2025-07-23 10:05:14"}, {"id": 255, "calculation_definition_id": 1, "locale": "sv", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-07-29 06:57:50", "updated_at": "2025-07-29 06:57:50"}, {"id": 256, "calculation_definition_id": 2, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON> (motorbensin)", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 06:58:26", "updated_at": "2025-07-29 06:58:26"}, {"id": 257, "calculation_definition_id": 3, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON> br<PERSON> (l<PERSON><PERSON> svavelinnehåll)", "emission_factor_name": "<PERSON><PERSON><PERSON> br<PERSON> (l<PERSON><PERSON> svavelinnehåll)", "result_name": "<PERSON><PERSON>t brännolja", "created_at": "2025-07-29 06:58:57", "updated_at": "2025-07-29 06:58:57"}, {"id": 258, "calculation_definition_id": 4, "locale": "sv", "data_name": "Naturgas", "emission_factor_name": "Naturgas", "result_name": "Naturgas", "created_at": "2025-07-29 06:59:34", "updated_at": "2025-07-29 06:59:34"}, {"id": 259, "calculation_definition_id": 5, "locale": "sv", "data_name": "<PERSON>ng brä<PERSON>ol<PERSON>", "emission_factor_name": "Tung brännolja (svavelhalt ≥ 1 %)", "result_name": "<PERSON>ng brä<PERSON>ol<PERSON>", "created_at": "2025-07-29 07:00:06", "updated_at": "2025-07-29 07:00:06"}, {"id": 260, "calculation_definition_id": 6, "locale": "sv", "data_name": "Neste My Diesel", "emission_factor_name": "Neste My Diesel", "result_name": "Neste MyDiesel", "created_at": "2025-07-29 07:00:23", "updated_at": "2025-07-29 07:00:23"}, {"id": 261, "calculation_definition_id": 7, "locale": "sv", "data_name": "(R134A) Vatten- och vätskekylare, värmepumpar", "emission_factor_name": "(R134A) Vatten- och vätskekylare, värmepumpar", "result_name": "(R134A) Vatten- och vätskekylare, värmepumpar", "created_at": "2025-07-29 07:01:03", "updated_at": "2025-07-29 07:01:03"}, {"id": 262, "calculation_definition_id": 8, "locale": "sv", "data_name": "(R410A) Vatten- och vätskekylare, värmepumpar", "emission_factor_name": "(R410A) Vatten- och vätskekylare, värmepumpar", "result_name": "(R410A) Vatten- och vätskekylare, värmepumpar", "created_at": "2025-07-29 07:01:38", "updated_at": "2025-07-29 07:01:38"}, {"id": 263, "calculation_definition_id": 9, "locale": "sv", "data_name": "(R404A) Kommersiell kylutrustning", "emission_factor_name": "(R404A) Kommersiell kylutrustning", "result_name": "(R404A) Kommersiell kylutrustning", "created_at": "2025-07-29 07:02:07", "updated_at": "2025-07-29 07:02:07"}, {"id": 264, "calculation_definition_id": 10, "locale": "sv", "data_name": "(R507A) Kommersiell kylutrustning", "emission_factor_name": "(R507A) Kommersiell kylutrustning", "result_name": "(R507A) Kommersiell kylutrustning", "created_at": "2025-07-29 07:02:36", "updated_at": "2025-07-29 07:02:36"}, {"id": 265, "calculation_definition_id": 11, "locale": "sv", "data_name": "(R134a) Kommersiell kylutrustning", "emission_factor_name": "(R134a) Kommersiell kylutrustning", "result_name": "(R134a) Kommersiell kylutrustning", "created_at": "2025-07-29 07:04:20", "updated_at": "2025-07-29 07:04:20"}, {"id": 266, "calculation_definition_id": 12, "locale": "sv", "data_name": "(R407A) Kommersiell kylutrustning", "emission_factor_name": "(R407A) Kommersiell kylutrustning", "result_name": "(R407A) Kommersiell kylutrustning", "created_at": "2025-07-29 07:04:56", "updated_at": "2025-07-29 07:04:56"}, {"id": 267, "calculation_definition_id": 13, "locale": "sv", "data_name": "(R407F) Kommersiell kylutrustning", "emission_factor_name": "(R407F) Kommersiell kylutrustning", "result_name": "(R407F) Kommersiell kylutrustning", "created_at": "2025-07-29 07:05:29", "updated_at": "2025-07-29 07:05:29"}, {"id": 268, "calculation_definition_id": 14, "locale": "sv", "data_name": "(R404A) Kondenseringsaggregat", "emission_factor_name": "(R404A) Kondenseringsaggregat", "result_name": "(R404A) Kondenseringsaggregat ", "created_at": "2025-07-29 07:05:52", "updated_at": "2025-07-29 07:05:52"}, {"id": 269, "calculation_definition_id": 15, "locale": "sv", "data_name": "(R410A) Luftkonditionering", "emission_factor_name": "(R410A) Luftkonditionering", "result_name": "(R410A) Luftkonditionering", "created_at": "2025-07-29 07:06:14", "updated_at": "2025-07-29 07:06:14"}, {"id": 270, "calculation_definition_id": 16, "locale": "sv", "data_name": "(R407C) Luftkonditionering", "emission_factor_name": "(R407C) Luftkonditionering", "result_name": "(R407C) Luftkonditionering", "created_at": "2025-07-29 07:06:38", "updated_at": "2025-07-29 07:06:38"}, {"id": 271, "calculation_definition_id": 17, "locale": "sv", "data_name": "(R404A) Kyltransporter", "emission_factor_name": "(R404A) Kyltransporter", "result_name": "(R404A) Kyltransporter", "created_at": "2025-07-29 07:07:06", "updated_at": "2025-07-29 07:07:06"}, {"id": 272, "calculation_definition_id": 18, "locale": "sv", "data_name": "(R134a) Kyltransporter", "emission_factor_name": "(R134a) Kyltransporter", "result_name": "(R134a) Kyltransporter", "created_at": "2025-07-29 07:09:03", "updated_at": "2025-07-29 07:09:03"}, {"id": 273, "calculation_definition_id": 19, "locale": "sv", "data_name": "Direkta lustgasutsläpp", "emission_factor_name": "Direkta lustgasutsläpp", "result_name": "Direkta lustgasutsläpp", "created_at": "2025-07-29 07:09:44", "updated_at": "2025-07-29 07:09:44"}, {"id": 274, "calculation_definition_id": 21, "locale": "sv", "data_name": "Direkta meta<PERSON>", "emission_factor_name": "Direkta meta<PERSON>", "result_name": "Direkta meta<PERSON>", "created_at": "2025-07-29 07:12:16", "updated_at": "2025-07-29 07:12:16"}, {"id": 275, "calculation_definition_id": 22, "locale": "sv", "data_name": "Direkta koldioxidutsläpp", "emission_factor_name": "Direkta koldioxidutsläpp", "result_name": "Direkta koldioxidutsläpp", "created_at": "2025-07-29 07:12:52", "updated_at": "2025-07-29 07:12:52"}, {"id": 276, "calculation_definition_id": 23, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> tomt", "emission_factor_name": "El (genomsnittlig konsumtion i Finland)", "result_name": "Platsbaserad el", "created_at": "2025-07-29 07:15:56", "updated_at": "2025-07-29 07:15:56"}, {"id": 277, "calculation_definition_id": 24, "locale": "sv", "data_name": "Förbrukning av utsläppsfri el", "emission_factor_name": "Utsläppsfri el", "result_name": "Marknadsbaserad el", "created_at": "2025-07-29 07:17:55", "updated_at": "2025-07-29 07:17:55"}, {"id": 278, "calculation_definition_id": 150, "locale": "sv", "data_name": "Elförbrukning (obevisad ursprungsgaranti)", "emission_factor_name": "El (Finlands residualmix)", "result_name": "Marknadsbaserad el", "created_at": "2025-07-29 07:19:20", "updated_at": "2025-07-29 07:19:20"}, {"id": 279, "calculation_definition_id": 25, "locale": "sv", "data_name": "Fjärrvärme", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, välj område", "result_name": "Fjärrvärme", "created_at": "2025-07-29 07:20:19", "updated_at": "2025-07-29 07:20:19"}, {"id": 280, "calculation_definition_id": 26, "locale": "sv", "data_name": "Utsläppsfri fjärrvärme med ursprungsgaranti", "emission_factor_name": "F<PERSON>ärr<PERSON><PERSON><PERSON><PERSON> (utsläppsfri), välj område", "result_name": "Utsläppsfri fjärrvärme med ursprungsgaranti", "created_at": "2025-07-29 07:21:18", "updated_at": "2025-07-29 07:21:18"}, {"id": 281, "calculation_definition_id": 27, "locale": "sv", "data_name": "Förbrukning av fjärrkyla (Finland)", "emission_factor_name": "Fjärrkyla (genomsnitt i Finland)", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Finland)", "created_at": "2025-07-29 07:23:04", "updated_at": "2025-07-29 07:23:04"}, {"id": 282, "calculation_definition_id": 28, "locale": "sv", "data_name": "Förbrukning av inköpt ånga", "emission_factor_name": "Ånga, eget energibolag", "result_name": "Ånga", "created_at": "2025-07-29 07:24:12", "updated_at": "2025-07-29 07:24:12"}, {"id": 283, "calculation_definition_id": 29, "locale": "sv", "data_name": "Bärbara datorer", "emission_factor_name": "Bärbara datorer", "result_name": "Bärbara datorer", "created_at": "2025-07-29 07:25:13", "updated_at": "2025-07-29 07:25:13"}, {"id": 284, "calculation_definition_id": 30, "locale": "sv", "data_name": "Mobiltelefoner", "emission_factor_name": "Mobiltelefoner", "result_name": "Mobiltelefoner", "created_at": "2025-07-29 07:25:38", "updated_at": "2025-07-29 07:25:38"}, {"id": 285, "calculation_definition_id": 31, "locale": "sv", "data_name": "Surfplattor", "emission_factor_name": "Surfplattor", "result_name": "Surfplattor", "created_at": "2025-07-29 07:26:08", "updated_at": "2025-07-29 07:26:08"}, {"id": 286, "calculation_definition_id": 32, "locale": "sv", "data_name": "Stora elapparater", "emission_factor_name": "Stora elapparater", "result_name": "Stora elapparater", "created_at": "2025-07-29 07:26:51", "updated_at": "2025-07-29 07:26:51"}, {"id": 287, "calculation_definition_id": 33, "locale": "sv", "data_name": "Små elapparater", "emission_factor_name": "Små elapparater", "result_name": "Små elapparater", "created_at": "2025-07-29 07:27:21", "updated_at": "2025-07-29 07:27:21"}, {"id": 288, "calculation_definition_id": 34, "locale": "sv", "data_name": "Bomullskläder", "emission_factor_name": "Bomullskläder", "result_name": "Bomullskläder", "created_at": "2025-07-29 07:27:47", "updated_at": "2025-07-29 07:27:47"}, {"id": 289, "calculation_definition_id": 35, "locale": "sv", "data_name": "Kläder av syntetfibrer", "emission_factor_name": "Kläder av syntetfibrer", "result_name": "Kläder av syntetfibrer", "created_at": "2025-07-29 07:28:52", "updated_at": "2025-07-29 07:28:52"}, {"id": 290, "calculation_definition_id": 36, "locale": "sv", "data_name": "St<PERSON>lprodukter", "emission_factor_name": "St<PERSON>lprodukter", "result_name": "St<PERSON>lprodukter", "created_at": "2025-07-29 07:29:12", "updated_at": "2025-07-29 07:29:12"}, {"id": 291, "calculation_definition_id": 37, "locale": "sv", "data_name": "Aluminiumprodukter", "emission_factor_name": "Aluminiumprodukter", "result_name": "Aluminiumprodukter", "created_at": "2025-07-29 07:29:36", "updated_at": "2025-07-29 07:29:36"}, {"id": 292, "calculation_definition_id": 38, "locale": "sv", "data_name": "Träprodukter", "emission_factor_name": "Träprodukter", "result_name": "Träprodukter", "created_at": "2025-07-29 07:29:55", "updated_at": "2025-07-29 07:29:55"}, {"id": 293, "calculation_definition_id": 39, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "created_at": "2025-07-29 07:30:18", "updated_at": "2025-07-29 07:30:18"}, {"id": 294, "calculation_definition_id": 40, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>rod<PERSON><PERSON>", "created_at": "2025-07-29 07:42:28", "updated_at": "2025-07-29 07:42:28"}, {"id": 295, "calculation_definition_id": 41, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-07-29 07:42:50", "updated_at": "2025-07-29 07:42:50"}, {"id": 296, "calculation_definition_id": 42, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-07-29 07:43:08", "updated_at": "2025-07-29 07:43:08"}, {"id": 297, "calculation_definition_id": 43, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (från mjölk<PERSON>)", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (från mjölk<PERSON>)", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (från mjölk<PERSON>)", "created_at": "2025-07-29 07:43:33", "updated_at": "2025-07-29 07:43:33"}, {"id": 298, "calculation_definition_id": 44, "locale": "sv", "data_name": "Fläskkött", "emission_factor_name": "Fläskkött", "result_name": "Fläskkött", "created_at": "2025-07-29 07:44:10", "updated_at": "2025-07-29 07:44:10"}, {"id": 299, "calculation_definition_id": 45, "locale": "sv", "data_name": "Regnbågslax", "emission_factor_name": "Regnbågslax", "result_name": "Regnbågslax", "created_at": "2025-07-29 07:44:30", "updated_at": "2025-07-29 07:44:30"}, {"id": 300, "calculation_definition_id": 46, "locale": "sv", "data_name": "Fjäderfä", "emission_factor_name": "Fjäderfä", "result_name": "Fjäderfä", "created_at": "2025-07-29 07:44:56", "updated_at": "2025-07-29 07:44:56"}, {"id": 301, "calculation_definition_id": 47, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-07-29 07:45:21", "updated_at": "2025-07-29 07:45:21"}, {"id": 302, "calculation_definition_id": 48, "locale": "sv", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel  ", "created_at": "2025-07-29 07:45:40", "updated_at": "2025-07-29 07:45:40"}, {"id": 303, "calculation_definition_id": 49, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON> (motorbensin)", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 07:46:15", "updated_at": "2025-07-29 07:46:15"}, {"id": 304, "calculation_definition_id": 50, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON> br<PERSON> (l<PERSON><PERSON> svavelinnehåll)", "emission_factor_name": "<PERSON><PERSON><PERSON> br<PERSON> (l<PERSON><PERSON> svavelinnehåll)", "result_name": "<PERSON><PERSON>t brännolja", "created_at": "2025-07-29 07:46:50", "updated_at": "2025-07-29 07:46:50"}, {"id": 305, "calculation_definition_id": 51, "locale": "sv", "data_name": "Naturgas", "emission_factor_name": "Naturgas", "result_name": "Naturgas", "created_at": "2025-07-29 07:47:16", "updated_at": "2025-07-29 07:47:16"}, {"id": 306, "calculation_definition_id": 52, "locale": "sv", "data_name": "Tung brännolja (svavelhalt ≥ 1 %)", "emission_factor_name": "Tung brännolja (svavelhalt ≥ 1 %)", "result_name": "Tung brännolja (svavelhalt ≥ 1 %)", "created_at": "2025-07-29 07:47:39", "updated_at": "2025-07-29 07:47:39"}, {"id": 307, "calculation_definition_id": 53, "locale": "sv", "data_name": "Neste MyDiesel", "emission_factor_name": "Neste MyDiesel", "result_name": "Neste MyDiesel", "created_at": "2025-07-29 07:47:55", "updated_at": "2025-07-29 07:47:55"}, {"id": 308, "calculation_definition_id": 54, "locale": "sv", "data_name": "Kärnkraft", "emission_factor_name": "Kärnkraftsproduktion", "result_name": "Kärnkraft", "created_at": "2025-07-29 07:49:33", "updated_at": "2025-07-29 07:49:33"}, {"id": 309, "calculation_definition_id": 55, "locale": "sv", "data_name": "Vindkraft", "emission_factor_name": "Vindkraftsproduktion", "result_name": "Vindkraft", "created_at": "2025-07-29 07:50:05", "updated_at": "2025-07-29 07:50:05"}, {"id": 310, "calculation_definition_id": 56, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 07:50:43", "updated_at": "2025-07-29 07:50:43"}, {"id": 311, "calculation_definition_id": 57, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-29 07:51:07", "updated_at": "2025-07-29 07:51:07"}, {"id": 312, "calculation_definition_id": 58, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 07:51:35", "updated_at": "2025-07-29 07:51:35"}, {"id": 313, "calculation_definition_id": 59, "locale": "sv", "data_name": "Biomassa", "emission_factor_name": "Biomassa", "result_name": "Biomassa", "created_at": "2025-07-29 07:51:51", "updated_at": "2025-07-29 07:51:51"}, {"id": 314, "calculation_definition_id": 60, "locale": "sv", "data_name": "Andel överföringsförluster i elproduktion", "emission_factor_name": "Andel överföringsförluster i elproduktion", "result_name": "Andel överföringsförluster i elproduktion", "created_at": "2025-07-29 07:53:37", "updated_at": "2025-07-29 07:53:37"}, {"id": 315, "calculation_definition_id": 61, "locale": "sv", "data_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "emission_factor_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "result_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "created_at": "2025-07-29 08:03:38", "updated_at": "2025-07-29 08:03:38"}, {"id": 316, "calculation_definition_id": 62, "locale": "sv", "data_name": "Lastbil (7,5–17 t) (diesel)", "emission_factor_name": "Lastbil (7,5–17 t) (diesel)", "result_name": "Lastbil (7,5–17 t) (diesel)", "created_at": "2025-07-29 08:03:59", "updated_at": "2025-07-29 08:03:59"}, {"id": 317, "calculation_definition_id": 63, "locale": "sv", "data_name": "Skåpbil (diesel)", "emission_factor_name": "Skåpbil (diesel)", "result_name": "Skåpbil (diesel)", "created_at": "2025-07-29 08:04:24", "updated_at": "2025-07-29 08:04:24"}, {"id": 318, "calculation_definition_id": 64, "locale": "sv", "data_name": "Containership, genomsnitt", "emission_factor_name": "Containership, genomsnitt", "result_name": "Containership, genomsnitt", "created_at": "2025-07-29 08:04:49", "updated_at": "2025-07-29 08:04:49"}, {"id": 319, "calculation_definition_id": 65, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON> ", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-29 08:05:23", "updated_at": "2025-07-29 08:05:23"}, {"id": 320, "calculation_definition_id": 66, "locale": "sv", "data_name": "Annan biltransport, diesel", "emission_factor_name": "Diesel  ", "result_name": "Annan biltransport, diesel", "created_at": "2025-07-29 08:08:37", "updated_at": "2025-07-29 08:08:37"}, {"id": 321, "calculation_definition_id": 67, "locale": "sv", "data_name": "Annan biltransport, bensin", "emission_factor_name": "<PERSON><PERSON>", "result_name": "Annan biltransport, bensin", "created_at": "2025-07-29 08:09:13", "updated_at": "2025-07-29 08:09:13"}, {"id": 322, "calculation_definition_id": 68, "locale": "sv", "data_name": "Annan biltransport, Neste MY-diesel", "emission_factor_name": "Neste MY-diesel", "result_name": "Annan biltransport, Neste MY-diesel", "created_at": "2025-07-29 08:09:51", "updated_at": "2025-07-29 08:09:51"}, {"id": 323, "calculation_definition_id": 69, "locale": "sv", "data_name": "Fordonskilometer, bränsletyp okänd", "emission_factor_name": "Fordonskilometer, bränsletyp okänd", "result_name": "Fordonskilometer, bränsletyp okänd", "created_at": "2025-07-29 08:10:11", "updated_at": "2025-07-29 08:10:11"}, {"id": 324, "calculation_definition_id": 70, "locale": "sv", "data_name": "Fordonskilometer, dieselfordon", "emission_factor_name": "Fordonskilometer, dieselfordon", "result_name": "Fordonskilometer, dieselfordon", "created_at": "2025-07-29 08:10:34", "updated_at": "2025-07-29 08:10:34"}, {"id": 325, "calculation_definition_id": 71, "locale": "sv", "data_name": "Fordonskilometer, bensinfordon", "emission_factor_name": "Fordonskilometer, bensinfordon", "result_name": "Fordonskilometer, bensinfordon", "created_at": "2025-07-29 08:11:17", "updated_at": "2025-07-29 08:11:17"}, {"id": 326, "calculation_definition_id": 72, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 08:11:39", "updated_at": "2025-07-29 08:11:39"}, {"id": 327, "calculation_definition_id": 73, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-29 08:13:32", "updated_at": "2025-07-29 08:13:32"}, {"id": 328, "calculation_definition_id": 74, "locale": "sv", "data_name": "Bioavfall", "emission_factor_name": "Bioavfall", "result_name": "Bioavfall", "created_at": "2025-07-29 08:13:50", "updated_at": "2025-07-29 08:13:50"}, {"id": 329, "calculation_definition_id": 75, "locale": "sv", "data_name": "Buss", "emission_factor_name": "Buss", "result_name": "Buss", "created_at": "2025-07-29 08:14:10", "updated_at": "2025-07-29 08:14:10"}, {"id": 330, "calculation_definition_id": 76, "locale": "sv", "data_name": "Bil", "emission_factor_name": "Bil", "result_name": "Bil", "created_at": "2025-07-29 08:14:40", "updated_at": "2025-07-29 08:14:40"}, {"id": 331, "calculation_definition_id": 77, "locale": "sv", "data_name": "Långdistans tåg", "emission_factor_name": "Långdistans tåg", "result_name": "Långdistans tåg", "created_at": "2025-07-29 08:15:01", "updated_at": "2025-07-29 08:15:01"}, {"id": 332, "calculation_definition_id": 78, "locale": "sv", "data_name": "In<PERSON>es / korta flygningar", "emission_factor_name": "In<PERSON>es / korta flygningar", "result_name": "In<PERSON>es / korta flygningar", "created_at": "2025-07-29 08:15:20", "updated_at": "2025-07-29 08:15:20"}, {"id": 333, "calculation_definition_id": 79, "locale": "sv", "data_name": "Europa / medellånga flygningar", "emission_factor_name": "Europa / medellånga flygningar ", "result_name": "Europa / medellånga flygningar", "created_at": "2025-07-29 08:15:40", "updated_at": "2025-07-29 08:15:40"}, {"id": 334, "calculation_definition_id": 80, "locale": "sv", "data_name": "Långa flygningar", "emission_factor_name": "Långa flygningar", "result_name": "Långa flygningar", "created_at": "2025-07-29 08:15:59", "updated_at": "2025-07-29 08:15:59"}, {"id": 335, "calculation_definition_id": 81, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tunnelbana och pendel<PERSON>g", "emission_factor_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tunnelbana och pendel<PERSON>g", "result_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tunnelbana och pendel<PERSON>g", "created_at": "2025-07-29 08:16:42", "updated_at": "2025-07-29 08:16:42"}, {"id": 336, "calculation_definition_id": 82, "locale": "sv", "data_name": "Hotellövernattning", "emission_factor_name": "Hotellövernattning", "result_name": "Hotellövernattning", "created_at": "2025-07-29 08:17:21", "updated_at": "2025-07-29 08:17:21"}, {"id": 337, "calculation_definition_id": 83, "locale": "sv", "data_name": "Antal arb<PERSON>gar per år", "emission_factor_name": "Antal arb<PERSON>gar per år", "result_name": "Antal arb<PERSON>gar per år", "created_at": "2025-07-29 08:17:45", "updated_at": "2025-07-29 08:17:45"}, {"id": 338, "calculation_definition_id": 84, "locale": "sv", "data_name": "Personal som reser med bil", "emission_factor_name": "Personal som reser med bil", "result_name": "Personal som reser med bil", "created_at": "2025-07-29 08:18:07", "updated_at": "2025-07-29 08:18:07"}, {"id": 339, "calculation_definition_id": 85, "locale": "sv", "data_name": "Genomsnittlig körsträcka med bil", "emission_factor_name": "Genomsnittlig bil ", "result_name": "<PERSON>ens pendling med bil", "created_at": "2025-07-29 08:18:49", "updated_at": "2025-07-29 08:22:43"}, {"id": 340, "calculation_definition_id": 86, "locale": "sv", "data_name": "Personal som reser med stadsbuss", "emission_factor_name": "Personal som reser med stadsbuss", "result_name": "Personal som reser med stadsbuss", "created_at": "2025-07-29 08:19:34", "updated_at": "2025-07-29 08:19:34"}, {"id": 341, "calculation_definition_id": 87, "locale": "sv", "data_name": "Genomsnittlig körsträcka med buss", "emission_factor_name": "Buss", "result_name": "Personalens pendling med buss", "created_at": "2025-07-29 08:21:39", "updated_at": "2025-07-29 08:21:39"}, {"id": 342, "calculation_definition_id": 88, "locale": "sv", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-07-29 08:23:04", "updated_at": "2025-07-29 08:23:04"}, {"id": 343, "calculation_definition_id": 89, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON> (motorbensin)", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 08:24:00", "updated_at": "2025-07-29 08:24:00"}, {"id": 344, "calculation_definition_id": 90, "locale": "sv", "data_name": "Fordonskilometer, bränsletyp okänd", "emission_factor_name": "Fordonskilometer, bränsletyp okänd", "result_name": "Fordonskilometer, bränsletyp okänd", "created_at": "2025-07-29 08:24:23", "updated_at": "2025-07-29 08:24:23"}, {"id": 345, "calculation_definition_id": 91, "locale": "sv", "data_name": "Fordonskilometer, dieselfordon", "emission_factor_name": "Fordonskilometer, dieselfordon", "result_name": "Fordonskilometer, dieselfordon", "created_at": "2025-07-29 08:24:46", "updated_at": "2025-07-29 08:24:46"}, {"id": 346, "calculation_definition_id": 92, "locale": "sv", "data_name": "Fordonskilometer, bensinfordon", "emission_factor_name": "Fordonskilometer, bensinfordon", "result_name": "Fordonskilometer, bensinfordon", "created_at": "2025-07-29 08:25:04", "updated_at": "2025-07-29 08:25:04"}, {"id": 347, "calculation_definition_id": 93, "locale": "sv", "data_name": "Förbrukning av el (Finland)", "emission_factor_name": "El (genomsnitt i Finland)", "result_name": "Förbrukning av el (Finland)", "created_at": "2025-07-29 08:27:13", "updated_at": "2025-07-29 08:27:13"}, {"id": 348, "calculation_definition_id": 94, "locale": "sv", "data_name": "Förbrukning av utsläppsfri el", "emission_factor_name": "El (utsläppsfri)", "result_name": "Förbrukning av utsläppsfri el", "created_at": "2025-07-29 08:27:58", "updated_at": "2025-07-29 08:27:58"}, {"id": 349, "calculation_definition_id": 95, "locale": "sv", "data_name": "Fjärrvärmeförbrukning", "emission_factor_name": "Fjärrvärme (genomsnitt i Finland)", "result_name": "Fjärrvärmeförbrukning", "created_at": "2025-07-29 08:29:09", "updated_at": "2025-07-29 08:29:09"}, {"id": 350, "calculation_definition_id": 96, "locale": "sv", "data_name": "Fjärrkylaförbrukning", "emission_factor_name": "Fjärrkyla (genomsnitt i Finland)", "result_name": "Fjärrkylaförbrukning", "created_at": "2025-07-29 08:30:35", "updated_at": "2025-07-29 08:30:35"}, {"id": 351, "calculation_definition_id": 97, "locale": "sv", "data_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "emission_factor_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "result_name": "Semitrailerlastbil (3,5–33 t) (diesel)", "created_at": "2025-07-29 08:30:59", "updated_at": "2025-07-29 08:30:59"}, {"id": 352, "calculation_definition_id": 98, "locale": "sv", "data_name": "Lastbil (7,5–17 t) (diesel)", "emission_factor_name": "Lastbil (7,5–17 t) (diesel) ", "result_name": "Lastbil (7,5–17 t) (diesel)", "created_at": "2025-07-29 08:31:25", "updated_at": "2025-07-29 08:31:25"}, {"id": 353, "calculation_definition_id": 99, "locale": "sv", "data_name": "Skåpbil (diesel)", "emission_factor_name": "Skåpbil (diesel)", "result_name": "Skåpbil (diesel)", "created_at": "2025-07-29 08:31:53", "updated_at": "2025-07-29 08:31:53"}, {"id": 354, "calculation_definition_id": 100, "locale": "sv", "data_name": "Containership, genomsnitt", "emission_factor_name": "Containership, genomsnitt", "result_name": "Containership, genomsnitt", "created_at": "2025-07-29 08:32:19", "updated_at": "2025-07-29 08:32:19"}, {"id": 355, "calculation_definition_id": 101, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-29 08:32:40", "updated_at": "2025-07-29 08:32:40"}, {"id": 356, "calculation_definition_id": 102, "locale": "sv", "data_name": "Annan biltransport, diesel", "emission_factor_name": "Diesel  ", "result_name": "Annan biltransport, diesel", "created_at": "2025-07-29 08:41:03", "updated_at": "2025-07-29 08:41:03"}, {"id": 357, "calculation_definition_id": 103, "locale": "sv", "data_name": "Annan biltransport, bensin", "emission_factor_name": "<PERSON><PERSON>", "result_name": "Annan biltransport, bensin", "created_at": "2025-07-29 08:41:29", "updated_at": "2025-07-29 08:41:29"}, {"id": 358, "calculation_definition_id": 104, "locale": "sv", "data_name": "Annan biltransport, Neste MY diesel", "emission_factor_name": "Neste MY diesel  ", "result_name": "Annan biltransport, Neste MY diesel", "created_at": "2025-07-29 08:41:54", "updated_at": "2025-07-29 08:41:54"}, {"id": 359, "calculation_definition_id": 105, "locale": "sv", "data_name": "Fordonskilometer, bränsletyp okänd", "emission_factor_name": "Fordonskilometer, bränsletyp okänd", "result_name": "Fordonskilometer, bränsletyp okänd", "created_at": "2025-07-29 08:42:14", "updated_at": "2025-07-29 08:42:14"}, {"id": 360, "calculation_definition_id": 106, "locale": "sv", "data_name": "Fordonskilometer, dieselfordon", "emission_factor_name": "Fordonskilometer, dieselfordon", "result_name": "Fordonskilometer, dieselfordon", "created_at": "2025-07-29 08:42:40", "updated_at": "2025-07-29 08:42:40"}, {"id": 361, "calculation_definition_id": 107, "locale": "sv", "data_name": "Fordonskilometer, bensinfordon", "emission_factor_name": "Fordonskilometer, bensinfordon", "result_name": "Fordonskilometer, bensinfordon", "created_at": "2025-07-29 08:42:58", "updated_at": "2025-07-29 08:42:58"}, {"id": 362, "calculation_definition_id": 108, "locale": "sv", "data_name": "Elförbrukning för bearbetning", "emission_factor_name": "El (genomsnitt i Finland)", "result_name": "Elförbrukning för bearbetning", "created_at": "2025-07-29 08:44:35", "updated_at": "2025-07-29 08:44:35"}, {"id": 363, "calculation_definition_id": 109, "locale": "sv", "data_name": "Elförbrukning för användning", "emission_factor_name": "El (genomsnitt i Finland)", "result_name": "Elförbrukning för användning", "created_at": "2025-07-29 08:45:21", "updated_at": "2025-07-29 08:45:21"}, {"id": 364, "calculation_definition_id": 110, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON>", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 08:46:04", "updated_at": "2025-07-29 08:46:04"}, {"id": 365, "calculation_definition_id": 111, "locale": "sv", "data_name": "<PERSON><PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON><PERSON>", "result_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-29 08:46:45", "updated_at": "2025-07-29 08:46:45"}, {"id": 366, "calculation_definition_id": 112, "locale": "sv", "data_name": "Bioavfall", "emission_factor_name": "Bioavfall", "result_name": "Bioavfall", "created_at": "2025-07-29 08:47:13", "updated_at": "2025-07-29 08:47:13"}, {"id": 367, "calculation_definition_id": 113, "locale": "sv", "data_name": "Diesel", "emission_factor_name": "Diesel  ", "result_name": "Diesel", "created_at": "2025-07-29 08:47:42", "updated_at": "2025-07-29 08:47:42"}, {"id": 368, "calculation_definition_id": 114, "locale": "sv", "data_name": "<PERSON><PERSON>", "emission_factor_name": "<PERSON><PERSON> (motorbensin)", "result_name": "<PERSON><PERSON>", "created_at": "2025-07-29 08:48:13", "updated_at": "2025-07-29 08:48:13"}, {"id": 369, "calculation_definition_id": 115, "locale": "sv", "data_name": "Fordonskilometer, bränsletyp okänd", "emission_factor_name": "Fordonskilometer, bränsletyp okänd", "result_name": "Fordonskilometer, bränsletyp okänd", "created_at": "2025-07-29 08:48:33", "updated_at": "2025-07-29 08:48:33"}, {"id": 370, "calculation_definition_id": 116, "locale": "sv", "data_name": "Fordonskilometer, dieselfordon", "emission_factor_name": "Fordonskilometer, dieselfordon", "result_name": "Fordonskilometer, dieselfordon", "created_at": "2025-07-29 08:48:50", "updated_at": "2025-07-29 08:48:50"}, {"id": 371, "calculation_definition_id": 117, "locale": "sv", "data_name": "Fordonskilometer, bensinfordon", "emission_factor_name": "Fordonskilometer, bensinfordon", "result_name": "Fordonskilometer, bensinfordon", "created_at": "2025-07-29 08:49:11", "updated_at": "2025-07-29 08:49:11"}, {"id": 372, "calculation_definition_id": 118, "locale": "sv", "data_name": "Elförbrukning", "emission_factor_name": "El (genomsnitt i Finland)", "result_name": "El (genomsnitt i Finland)", "created_at": "2025-07-29 08:50:29", "updated_at": "2025-07-29 08:50:29"}, {"id": 373, "calculation_definition_id": 119, "locale": "sv", "data_name": "Förbrukning av utsläppsfri el", "emission_factor_name": "El (utsläppsfri)", "result_name": "Förbrukning av utsläppsfri el", "created_at": "2025-07-29 08:51:10", "updated_at": "2025-07-29 08:51:10"}, {"id": 374, "calculation_definition_id": 120, "locale": "sv", "data_name": "Fjärrvärmeförbrukning", "emission_factor_name": "Fjärrvärme (genomsnitt i Finland)", "result_name": "Fjärrvärmeförbrukning", "created_at": "2025-07-29 08:51:46", "updated_at": "2025-07-29 08:51:46"}, {"id": 375, "calculation_definition_id": 121, "locale": "sv", "data_name": "Fjärrkylaförbrukning", "emission_factor_name": "Fjärrkyla (genomsnitt i Finland)", "result_name": "Fjärrkylaförbrukning", "created_at": "2025-07-29 08:52:16", "updated_at": "2025-07-29 08:52:16"}, {"id": 376, "calculation_definition_id": 145, "locale": "sv", "data_name": "Lätt industribyggnad", "emission_factor_name": "Lätt industribyggnad", "result_name": "Lätt industribyggnad", "created_at": "2025-07-29 08:52:35", "updated_at": "2025-07-29 08:52:35"}, {"id": 377, "calculation_definition_id": 147, "locale": "sv", "data_name": "Elförbrukning", "emission_factor_name": "El (genomsnitt i Finland)", "result_name": "Elförbrukning", "created_at": "2025-07-29 08:53:24", "updated_at": "2025-07-29 08:53:24"}, {"id": 378, "calculation_definition_id": 148, "locale": "sv", "data_name": "Förbrukning av utsläppsfri el", "emission_factor_name": "El (utsläppsfri)", "result_name": "Förbrukning av utsläppsfri el", "created_at": "2025-07-29 08:53:57", "updated_at": "2025-07-29 08:53:57"}, {"id": 379, "calculation_definition_id": 149, "locale": "sv", "data_name": "Investeringar", "emission_factor_name": "Utsläppsintensitet för investeringar", "result_name": "Investeringar", "created_at": "2025-07-29 08:54:48", "updated_at": "2025-07-29 08:54:48"}, {"id": 380, "calculation_definition_id": 180, "locale": "fi", "data_name": "-", "emission_factor_name": "-", "result_name": "Markkinaperusteinen kaukolämpö", "created_at": "2025-08-14 12:21:18", "updated_at": "2025-08-14 12:21:18"}, {"id": 381, "calculation_definition_id": 181, "locale": "fi", "data_name": "-", "emission_factor_name": "-", "result_name": "Sijaintiperusteinen kaukolämpö", "created_at": "2025-08-14 12:24:40", "updated_at": "2025-08-14 12:24:40"}, {"id": 382, "calculation_definition_id": 180, "locale": "en", "data_name": "-", "emission_factor_name": "-", "result_name": "Market-based district heating", "created_at": "2025-08-14 12:27:53", "updated_at": "2025-08-14 12:27:53"}, {"id": 383, "calculation_definition_id": 181, "locale": "en", "data_name": "-", "emission_factor_name": "-", "result_name": "Location-based district heating", "created_at": "2025-08-14 12:28:03", "updated_at": "2025-08-14 12:28:13"}, {"id": 384, "calculation_definition_id": 181, "locale": "sv", "data_name": "-", "emission_factor_name": "-", "result_name": "Platsbaserad fjärrvärme (or Lägesbaserad fjärrvärme)", "created_at": "2025-08-14 12:28:23", "updated_at": "2025-08-14 12:28:23"}, {"id": 385, "calculation_definition_id": 180, "locale": "sv", "data_name": "-", "emission_factor_name": "-", "result_name": "Marknadsbaserad fjärrvärme", "created_at": "2025-08-14 12:28:40", "updated_at": "2025-08-14 12:28:40"}]