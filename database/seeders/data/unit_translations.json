[{"id": 1, "unit_id": 1, "locale": "fi", "name": "Yö", "symbol": "yö", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 2, "unit_id": 2, "locale": "fi", "name": "Henkilötyövuodet", "symbol": "htv", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 3, "unit_id": 3, "locale": "fi", "name": "Tuhatta euroa", "symbol": "t€", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 4, "unit_id": 4, "locale": "fi", "name": "Henkilö", "symbol": "hlö", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 5, "unit_id": 5, "locale": "fi", "name": "<PERSON><PERSON>attitunt<PERSON>", "symbol": "kWh", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 6, "unit_id": 6, "locale": "fi", "name": "Litra", "symbol": "l", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 7, "unit_id": 7, "locale": "fi", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "m³", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 8, "unit_id": 8, "locale": "fi", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "kg", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 9, "unit_id": 9, "locale": "fi", "name": "<PERSON><PERSON>", "symbol": "kpl", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 10, "unit_id": 10, "locale": "fi", "name": "Tonnikilometri", "symbol": "tkm", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 11, "unit_id": 11, "locale": "fi", "name": "Kilometri", "symbol": "km", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 12, "unit_id": 12, "locale": "fi", "name": "Henkilökilometri", "symbol": "hkm", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 13, "unit_id": 13, "locale": "fi", "name": "Gramma CO2-<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "gCO2e", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 14, "unit_id": 14, "locale": "fi", "name": "Kilogramma CO2-<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "kgCO2e", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 15, "unit_id": 15, "locale": "fi", "name": "Tonni CO2-<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "tCO2e", "created_at": "2025-06-25 10:43:00", "updated_at": "2025-06-25 10:43:00"}, {"id": 16, "unit_id": 16, "locale": "fi", "name": "Työntekijät", "symbol": "työntekijää", "created_at": "2025-06-25 10:43:08", "updated_at": "2025-06-25 10:43:08"}, {"id": 17, "unit_id": 17, "locale": "fi", "name": "Miljoonaa euroa", "symbol": "M€", "created_at": "2025-06-25 10:43:08", "updated_at": "2025-06-25 10:43:08"}, {"id": 18, "unit_id": 18, "locale": "fi", "name": "euro", "symbol": "€", "created_at": "2025-06-26 11:54:08", "updated_at": "2025-06-26 11:54:08"}, {"id": 19, "unit_id": 1, "locale": "en", "name": "Night", "symbol": "night", "created_at": "2025-07-22 06:43:11", "updated_at": "2025-07-22 06:43:11"}, {"id": 20, "unit_id": 2, "locale": "en", "name": "Full-time equivalent", "symbol": "FTE", "created_at": "2025-07-22 06:43:37", "updated_at": "2025-07-22 06:43:37"}, {"id": 21, "unit_id": 3, "locale": "en", "name": "Thousand euros", "symbol": "t€", "created_at": "2025-07-22 06:43:50", "updated_at": "2025-07-22 06:43:50"}, {"id": 22, "unit_id": 4, "locale": "en", "name": "Person", "symbol": "pers.", "created_at": "2025-07-22 06:44:09", "updated_at": "2025-07-22 06:44:09"}, {"id": 23, "unit_id": 5, "locale": "en", "name": "Kilowatt-hour", "symbol": "kWh", "created_at": "2025-07-22 06:44:24", "updated_at": "2025-07-22 06:44:24"}, {"id": 24, "unit_id": 6, "locale": "en", "name": "Litre", "symbol": "l", "created_at": "2025-07-22 06:44:34", "updated_at": "2025-07-22 06:44:34"}, {"id": 25, "unit_id": 7, "locale": "en", "name": "Cubic metre", "symbol": "m³", "created_at": "2025-07-22 06:44:54", "updated_at": "2025-07-22 06:44:54"}, {"id": 26, "unit_id": 8, "locale": "en", "name": "Kilogram", "symbol": "kg", "created_at": "2025-07-22 06:45:09", "updated_at": "2025-07-22 06:45:09"}, {"id": 27, "unit_id": 9, "locale": "en", "name": "Piece / Item / Unit", "symbol": "pcs / items / units", "created_at": "2025-07-22 06:45:37", "updated_at": "2025-07-22 06:45:37"}, {"id": 28, "unit_id": 10, "locale": "en", "name": "Tonne-kilometre", "symbol": "tkm", "created_at": "2025-07-22 06:46:01", "updated_at": "2025-07-22 06:46:01"}, {"id": 29, "unit_id": 11, "locale": "en", "name": "Kilometre", "symbol": "km", "created_at": "2025-07-22 06:46:15", "updated_at": "2025-07-22 06:46:15"}, {"id": 30, "unit_id": 12, "locale": "en", "name": "Passenger-kilometre", "symbol": "pkm", "created_at": "2025-07-22 06:46:40", "updated_at": "2025-07-22 06:46:40"}, {"id": 31, "unit_id": 13, "locale": "en", "name": "Gram of CO₂e", "symbol": "gCO2e", "created_at": "2025-07-22 06:47:11", "updated_at": "2025-07-22 06:47:11"}, {"id": 32, "unit_id": 14, "locale": "en", "name": "Kilogram of CO₂e", "symbol": "kgCO2e", "created_at": "2025-07-22 06:47:25", "updated_at": "2025-07-22 06:47:52"}, {"id": 33, "unit_id": 15, "locale": "en", "name": "<PERSON><PERSON> of CO₂e", "symbol": "tCO2e", "created_at": "2025-07-22 06:48:13", "updated_at": "2025-07-22 06:48:13"}, {"id": 34, "unit_id": 16, "locale": "en", "name": "Employees", "symbol": "employees", "created_at": "2025-07-22 06:48:31", "updated_at": "2025-07-22 06:48:31"}, {"id": 35, "unit_id": 17, "locale": "en", "name": "Million euros", "symbol": "M€", "created_at": "2025-07-22 06:48:50", "updated_at": "2025-07-22 06:48:50"}, {"id": 36, "unit_id": 18, "locale": "en", "name": "euro", "symbol": "€", "created_at": "2025-07-22 06:49:00", "updated_at": "2025-07-22 06:49:00"}, {"id": 37, "unit_id": 19, "locale": "fi", "name": "<PERSON><PERSON><PERSON>", "symbol": "m²", "created_at": "2025-07-23 07:35:40", "updated_at": "2025-07-23 07:35:40"}, {"id": 38, "unit_id": 19, "locale": "en", "name": "square metre", "symbol": "m²", "created_at": "2025-07-23 07:52:05", "updated_at": "2025-07-23 07:52:05"}, {"id": 39, "unit_id": 20, "locale": "en", "name": "percent", "symbol": "%", "created_at": "2025-07-23 08:12:16", "updated_at": "2025-07-29 06:51:56"}, {"id": 40, "unit_id": 22, "locale": "fi", "name": "<PERSON><PERSON><PERSON>", "symbol": "%", "created_at": "2025-07-23 08:12:48", "updated_at": "2025-07-23 08:12:48"}, {"id": 41, "unit_id": 1, "locale": "sv", "name": "Natt", "symbol": "natt", "created_at": "2025-07-29 06:42:10", "updated_at": "2025-07-29 06:42:10"}, {"id": 42, "unit_id": 2, "locale": "sv", "name": "Heltidsekvivalent (FTE) / Personår", "symbol": "FTE", "created_at": "2025-07-29 06:42:54", "updated_at": "2025-07-29 06:42:54"}, {"id": 43, "unit_id": 3, "locale": "sv", "name": "Tusen euro", "symbol": "t€", "created_at": "2025-07-29 06:43:11", "updated_at": "2025-07-29 06:43:11"}, {"id": 44, "unit_id": 4, "locale": "sv", "name": "Person", "symbol": "pers", "created_at": "2025-07-29 06:43:34", "updated_at": "2025-07-29 06:43:34"}, {"id": 45, "unit_id": 5, "locale": "sv", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "kWh", "created_at": "2025-07-29 06:43:55", "updated_at": "2025-07-29 06:43:55"}, {"id": 46, "unit_id": 6, "locale": "sv", "name": "Liter", "symbol": "l", "created_at": "2025-07-29 06:44:09", "updated_at": "2025-07-29 06:44:09"}, {"id": 47, "unit_id": 7, "locale": "sv", "name": "Kubikmeter", "symbol": "m³", "created_at": "2025-07-29 06:44:40", "updated_at": "2025-07-29 06:44:40"}, {"id": 48, "unit_id": 8, "locale": "sv", "name": "Kilogram", "symbol": "kg", "created_at": "2025-07-29 06:44:52", "updated_at": "2025-07-29 06:44:52"}, {"id": 49, "unit_id": 9, "locale": "sv", "name": "Styck / Enhet", "symbol": "st.", "created_at": "2025-07-29 06:45:17", "updated_at": "2025-07-29 06:45:17"}, {"id": 50, "unit_id": 10, "locale": "sv", "name": "Tonkilometer", "symbol": "tkm", "created_at": "2025-07-29 06:45:33", "updated_at": "2025-07-29 06:45:33"}, {"id": 51, "unit_id": 11, "locale": "sv", "name": "Kilometer", "symbol": "km", "created_at": "2025-07-29 06:45:50", "updated_at": "2025-07-29 06:45:50"}, {"id": 52, "unit_id": 12, "locale": "sv", "name": "Passagerarkilometer", "symbol": "pkm", "created_at": "2025-07-29 06:46:24", "updated_at": "2025-07-29 06:46:24"}, {"id": 53, "unit_id": 13, "locale": "sv", "name": "Gram koldioxidekvivalenter", "symbol": "gCO2e", "created_at": "2025-07-29 06:47:04", "updated_at": "2025-07-29 06:53:12"}, {"id": 54, "unit_id": 14, "locale": "sv", "name": "<PERSON><PERSON><PERSON> koldioxidekvivalenter", "symbol": "kgCO₂e", "created_at": "2025-07-29 06:47:27", "updated_at": "2025-07-29 06:47:27"}, {"id": 55, "unit_id": 15, "locale": "sv", "name": "<PERSON><PERSON>", "symbol": "tCO₂e", "created_at": "2025-07-29 06:48:11", "updated_at": "2025-07-29 06:48:11"}, {"id": 56, "unit_id": 16, "locale": "sv", "name": "Anställda", "symbol": "pers.", "created_at": "2025-07-29 06:48:37", "updated_at": "2025-07-29 06:48:37"}, {"id": 57, "unit_id": 17, "locale": "sv", "name": "Miljon euro", "symbol": "M€", "created_at": "2025-07-29 06:48:54", "updated_at": "2025-07-29 06:48:54"}, {"id": 58, "unit_id": 18, "locale": "sv", "name": "euro", "symbol": "€", "created_at": "2025-07-29 06:49:02", "updated_at": "2025-07-29 06:49:02"}, {"id": 59, "unit_id": 19, "locale": "sv", "name": "Kvadratmeter", "symbol": "m²", "created_at": "2025-07-29 06:50:20", "updated_at": "2025-07-29 06:50:20"}, {"id": 60, "unit_id": 22, "locale": "sv", "name": "procent", "symbol": "%", "created_at": "2025-07-29 06:51:08", "updated_at": "2025-07-29 06:51:08"}, {"id": 61, "unit_id": 22, "locale": "en", "name": "percent", "symbol": "%", "created_at": "2025-07-29 06:52:05", "updated_at": "2025-07-29 06:52:05"}]