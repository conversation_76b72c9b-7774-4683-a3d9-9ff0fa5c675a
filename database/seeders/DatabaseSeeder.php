<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\InputMethod;
use App\Enums\SelectOptionType;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use App\Models\Category;
use App\Models\Company;
use App\Models\CompoundUnit;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use App\Models\Grouping;
use App\Models\MetricDefinition;
use App\Models\Scope;
use App\Models\ScopeCalculationVariant;
use App\Models\SelectOption;
use App\Models\Unit;
use App\Models\UnitConversion;
use App\Models\Year;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

final class DatabaseSeeder extends Seeder
{
  // Note: 'data' (lowercase) is fine for JSON files.
  // If this were PHP classes, it would need to be 'Data' for PSR-4 compliance
  private const SEED_DATA_PATH = "database/seeders/data/";

  /**
   * ID mappings for references
   * @var array<string, array<int, int>>
   */
  private array $idMappings = [];

  // Data properties with proper types

  /** @var list<array{id: int, year: int, published: ?string, created_at: ?string, updated_at: ?string, deleted_at: ?string}> */
  private array $yearsData = [];

  /** @var list<array{id: int, number: int, created_at: ?string, updated_at: ?string, allow_grouping_hiding: int}> */
  private array $scopesData = [];

  /** @var list<array{id: int, scope_id: int, predicate: ?string, display_order: int, created_at: ?string, updated_at: ?string}> */
  private array $scopeCalculationVariantsData = [];

  /** @var list<array{id: int, scope_calculation_variant_id: int, locale: string, label: string, created_at: ?string, updated_at: ?string}> */
  private array $scopeCalculationVariantTranslationsData = [];

  /** @var list<array{id: int, created_at: ?string, updated_at: ?string, sort_order: int, hide_total: int}> */
  private array $categoriesData = [];

  /** @var list<array{id: int, category_id: int, locale: string, title: string, description: ?string, created_at: ?string, updated_at: ?string}> */
  private array $categoryTranslationsData = [];

  /** @var list<array{id: int, created_at: ?string, updated_at: ?string, deleted_at: ?string, sort_order: int}> */
  private array $groupingsData = [];

  /** @var list<array{id: int, grouping_id: int, locale: string, title: string, created_at: ?string, updated_at: ?string}> */
  private array $groupingTranslationsData = [];

  /** @var list<array{id: int, created_at: ?string, updated_at: ?string, deleted_at: ?string}> */
  private array $unitsData = [];

  /** @var list<array{id: int, unit_id: int, locale: string, name: string, symbol: string, created_at: ?string, updated_at: ?string}> */
  private array $unitTranslationsData = [];

  /** @var list<array{id: int, numerator_unit_id: int, denominator_unit_id: int, created_at: ?string, updated_at: ?string, deleted_at: ?string}> */
  private array $compoundUnitsData = [];

  /** @var list<array{id: int, from_unit_id: int, to_unit_id: int, conversion_factor: string, created_at: ?string, updated_at: ?string}> */
  private array $unitConversionsData = [];

  /** @var list<array{id: int, name: string, created_at: ?string, updated_at: ?string}> */
  private array $optionSetsData = [];

  /** @var list<array{id: int, company_id: ?int, scope_id: int, category_id: int, data_unit_id: int, emission_factor_compound_unit_id: int, created_at: ?string, updated_at: ?string, deleted_at: ?string, grouping_id: int, custom_name: ?string, input_method: string, sort_order: int, data_formula: ?string, hide_from_data_page: int, hide_from_emission_factor_page: int, hide_from_results_page: int, tag: ?string, link_id: ?string, option_set_id: ?int}> */
  private array $calculationDefinitionsData = [];

  /** @var list<array{id: int, calculation_definition_id: int, locale: string, data_name: string, emission_factor_name: string, result_name: string, created_at: ?string, updated_at: ?string}> */
  private array $calculationDefinitionTranslationsData = [];
  /** @var list<array{id: int, category_id: int, calculation_definition_id: int, sort_order: int, created_at: ?string, updated_at: ?string}> */
  private array $calculationDefinitionCategoryData = [];

  /** @var list<array{id: int, calculation_definition_id: int, year_id: int, created_at: ?string, updated_at: ?string, emission_factor_default_value: ?string, emission_factor_default_source: string}> */
  private array $calculationDefinitionYearData = [];

  /** @var list<array{id: int, calculation_definition_year_id: int, locale: string, data_help_text: string, emission_factor_help_text: string, result_help_text: string, created_at: ?string, updated_at: ?string}> */
  private array $calculationDefinitionYearTranslationsData = [];

  /** @var list<array{id: int, sort_order: int, created_at: ?string, updated_at: ?string, option_set_id: int}> */
  private array $calculationDefinitionOptionsData = [];

  /** @var list<array{id: int, calculation_definition_option_id: int, locale: string, label: string, created_at: ?string, updated_at: ?string}> */
  private array $calculationDefinitionOptionTranslationsData = [];

  /** @var list<array{id: int, calculation_definition_option_id: int, year_id: int, value: string, created_at: ?string, updated_at: ?string}> */
  private array $calculationDefinitionOptionYearValuesData = [];

  /** @var list<array{id: int, unit_id: int, created_at: ?string, updated_at: ?string, deleted_at: ?string}> */
  private array $metricDefinitionsData = [];

  /** @var list<array{id: int, metric_definition_id: int, locale: string, name: string, created_at: ?string, updated_at: ?string}> */
  private array $metricDefinitionTranslationsData = [];

  /** @var list<array{id: int, metric_definition_id: int, year_id: int, created_at: ?string, updated_at: ?string}> */
  private array $metricDefinitionYearData = [];

  /** @var list<array{id: int, metric_definition_year_id: int, locale: string, help_text: string, created_at: ?string, updated_at: ?string}> */
  private array $metricDefinitionYearTranslationsData = [];

  /** @var list<array{id: int, type: string, sort_order: int, created_at: ?string, updated_at: ?string, deleted_at: ?string}> */
  private array $selectOptionsData = [];

  /** @var list<array{id: int, select_option_id: int, locale: string, label: string}> */
  private array $selectOptionTranslationsData = [];

  /**
   * Seed the production database with initial data
   */
  public function run(): void
  {
    $modelsWithAuditing = [
      CalculationDefinition::class,
      Company::class,
      DataValue::class,
      EmissionFactorValue::class,
    ];

    foreach ($modelsWithAuditing as $model) {
      if (method_exists($model, "disableAuditing")) {
        $model::disableAuditing();
      }
    }

    // Load all data
    $this->loadAllData();

    // Seed database
    $this->seedYears();
    $this->seedScopes();
    $this->seedScopeCalculationVariants();
    $this->seedCategories();
    $this->seedGroupings();
    $this->seedUnits();
    $this->seedCompoundUnits();
    $this->seedUnitConversions();
    $this->seedOptionSets();
    $this->seedCalculationDefinitions();
    $this->seedCalculationDefinitionCategory();
    $this->seedCalculationDefinitionOptions();
    $this->seedCalculationDefinitionYearData();
    $this->seedMetricDefinitions();
    $this->seedMetricDefinitionYearData();
    $this->seedSelectOptions();

    // Re-enable audit logging
    foreach ($modelsWithAuditing as $model) {
      if (method_exists($model, "enableAuditing")) {
        $model::enableAuditing();
      }
    }
  }

  /**
   * Check that all required seed data files exist
   */
  private function checkRequiredFiles(): void
  {
    $requiredFiles = [
      "years.json",
      "scopes.json",
      "scope_calculation_variants.json",
      "scope_calculation_variant_translations.json",
      "categories.json",
      "category_translations.json",
      "groupings.json",
      "grouping_translations.json",
      "units.json",
      "unit_translations.json",
      "compound_units.json",
      "unit_conversions.json",
      "option_sets.json",
      "calculation_definitions.json",
      "calculation_definition_translations.json",
      "calculation_definition_category.json",
      "calculation_definition_options.json",
      "calculation_definition_option_translations.json",
      "calculation_definition_option_year_values.json",
      "calculation_definition_year.json",
      "calculation_definition_year_translations.json",
      "metric_definitions.json",
      "metric_definition_translations.json",
      "metric_definition_year.json",
      "metric_definition_year_translations.json",
      "select_options.json",
      "select_option_translations.json",
    ];

    $missingFiles = [];
    foreach ($requiredFiles as $file) {
      $path = base_path(self::SEED_DATA_PATH . $file);
      if (!File::exists($path)) {
        $missingFiles[] = $file;
      }
    }

    if (count($missingFiles) > 0) {
      throw new \RuntimeException(
        "Missing required seed data files:\n" . implode("\n", $missingFiles),
      );
    }
  }

  /**
   * Load all data from JSON files
   */
  private function loadAllData(): void
  {
    $this->checkRequiredFiles();
    $this->loadYearsData();
    $this->loadScopesData();
    $this->loadScopeCalculationVariantsData();
    $this->loadCategoriesData();
    $this->loadGroupingsData();
    $this->loadUnitsData();
    $this->loadCompoundUnitsData();
    $this->loadUnitConversionsData();
    $this->loadOptionSetsData();
    $this->loadCalculationDefinitionsData();
    $this->loadCalculationDefinitionCategoryData();
    $this->loadCalculationDefinitionOptionsData();
    $this->loadCalculationDefinitionYearData();
    $this->loadMetricDefinitionsData();
    $this->loadMetricDefinitionYearData();
    $this->loadSelectOptionsData();
  }

  /**
   * Load and validate years data
   */
  private function loadYearsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "years.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException("Invalid JSON in file years.json: " . json_last_error_msg());
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file years.json must contain an array");
    }

    Assert::list($data, "Data in years.json must be a list");
    Assert::arrayArray($data, "Items in years.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Year must have numeric id");
      Assert::true(
        isset($item["year"]) && is_numeric($item["year"]),
        "Year must have numeric year value",
      );
      $yearNumber = (int) $item["year"];
      Assert::true(
        $yearNumber >= 1900 && $yearNumber <= 2200,
        "Year must be between 1900 and 2200, got {$yearNumber}",
      );

      $published = null;
      if (isset($item["published"])) {
        Assert::string($item["published"], "published must be string");
        $published = $item["published"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "year" => (int) $item["year"],
        "published" => $published,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
      ];
    }

    $this->yearsData = $validatedData;
  }

  /**
   * Load and validate scopes data
   */
  private function loadScopesData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "scopes.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException("Invalid JSON in file scopes.json: " . json_last_error_msg());
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file scopes.json must contain an array");
    }

    Assert::list($data, "Data in scopes.json must be a list");
    Assert::arrayArray($data, "Items in scopes.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Scope must have numeric id");
      Assert::true(
        isset($item["number"]) && is_numeric($item["number"]),
        "Scope must have numeric number",
      );
      $scopeNumber = (int) $item["number"];
      Assert::true(
        in_array($scopeNumber, [1, 2, 3], true),
        "Scope number must be 1, 2, or 3, got {$scopeNumber}",
      );

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $allowGroupingHiding = 0;
      if (isset($item["allow_grouping_hiding"])) {
        Assert::numeric($item["allow_grouping_hiding"], "allow_grouping_hiding must be numeric");
        $allowGroupingHiding = (int) $item["allow_grouping_hiding"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "number" => (int) $item["number"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "allow_grouping_hiding" => $allowGroupingHiding,
      ];
    }

    $this->scopesData = $validatedData;
  }

  /**
   * Load and validate scope calculation variants data
   */
  private function loadScopeCalculationVariantsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "scope_calculation_variants.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file scope_calculation_variants.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file scope_calculation_variants.json must contain an array",
      );
    }

    Assert::list($data, "Data in scope_calculation_variants.json must be a list");
    Assert::arrayArray($data, "Items in scope_calculation_variants.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Variant must have numeric id");
      Assert::true(
        isset($item["scope_id"]) && is_numeric($item["scope_id"]),
        "Variant must have numeric scope_id",
      );
      Assert::true(
        isset($item["display_order"]) && is_numeric($item["display_order"]),
        "Variant must have numeric display_order",
      );

      $predicate = null;
      if (isset($item["predicate"])) {
        Assert::string($item["predicate"], "predicate must be string");
        $predicate = $item["predicate"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "scope_id" => (int) $item["scope_id"],
        "predicate" => $predicate,
        "display_order" => (int) $item["display_order"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->scopeCalculationVariantsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "scope_calculation_variant_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file scope_calculation_variant_translations.json: " .
          json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file scope_calculation_variant_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in scope_calculation_variant_translations.json must be a list");
    Assert::arrayArray(
      $data,
      "Items in scope_calculation_variant_translations.json must be arrays",
    );

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["scope_calculation_variant_id"]) &&
          is_numeric($item["scope_calculation_variant_id"]),
        "Translation must have numeric scope_calculation_variant_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["label"]) && is_string($item["label"]),
        "Translation must have string label",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["label"], "label must be string");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "scope_calculation_variant_id" => (int) $item["scope_calculation_variant_id"],
        "locale" => $item["locale"],
        "label" => $item["label"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->scopeCalculationVariantTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate categories data
   */
  private function loadCategoriesData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "categories.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException("Invalid JSON in file categories.json: " . json_last_error_msg());
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file categories.json must contain an array");
    }

    Assert::list($data, "Data in categories.json must be a list");
    Assert::arrayArray($data, "Items in categories.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Category must have numeric id");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $sortOrder = 0;
      if (isset($item["sort_order"])) {
        Assert::numeric($item["sort_order"], "sort_order must be numeric");
        $sortOrder = (int) $item["sort_order"];
      }

      $hideTotal = 0;
      if (isset($item["hide_total"])) {
        Assert::numeric($item["hide_total"], "hide_total must be numeric");
        $hideTotal = (int) $item["hide_total"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "sort_order" => $sortOrder,
        "hide_total" => $hideTotal,
      ];
    }

    $this->categoriesData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "category_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file category_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file category_translations.json must contain an array");
    }

    Assert::list($data, "Data in category_translations.json must be a list");
    Assert::arrayArray($data, "Items in category_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["category_id"]) && is_numeric($item["category_id"]),
        "Translation must have numeric category_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["title"]) && is_string($item["title"]),
        "Translation must have string title",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["title"], "title must be string");

      $description = null;
      if (isset($item["description"])) {
        Assert::string($item["description"], "description must be string");
        $description = $item["description"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "category_id" => (int) $item["category_id"],
        "locale" => $item["locale"],
        "title" => $item["title"],
        "description" => $description,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->categoryTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate groupings data
   */
  private function loadGroupingsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "groupings.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException("Invalid JSON in file groupings.json: " . json_last_error_msg());
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file groupings.json must contain an array");
    }

    Assert::list($data, "Data in groupings.json must be a list");
    Assert::arrayArray($data, "Items in groupings.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Grouping must have numeric id");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $sortOrder = 0;
      if (isset($item["sort_order"])) {
        Assert::numeric($item["sort_order"], "sort_order must be numeric");
        $sortOrder = (int) $item["sort_order"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
        "sort_order" => $sortOrder,
      ];
    }

    $this->groupingsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "grouping_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file grouping_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file grouping_translations.json must contain an array");
    }

    Assert::list($data, "Data in grouping_translations.json must be a list");
    Assert::arrayArray($data, "Items in grouping_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["grouping_id"]) && is_numeric($item["grouping_id"]),
        "Translation must have numeric grouping_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["title"]) && is_string($item["title"]),
        "Translation must have string title",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["title"], "title must be string");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "grouping_id" => (int) $item["grouping_id"],
        "locale" => $item["locale"],
        "title" => $item["title"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->groupingTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate units data
   */
  private function loadUnitsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "units.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException("Invalid JSON in file units.json: " . json_last_error_msg());
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file units.json must contain an array");
    }

    Assert::list($data, "Data in units.json must be a list");
    Assert::arrayArray($data, "Items in units.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Unit must have numeric id");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
      ];
    }

    $this->unitsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "unit_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file unit_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file unit_translations.json must contain an array");
    }

    Assert::list($data, "Data in unit_translations.json must be a list");
    Assert::arrayArray($data, "Items in unit_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["unit_id"]) && is_numeric($item["unit_id"]),
        "Translation must have numeric unit_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["name"]) && is_string($item["name"]),
        "Translation must have string name",
      );
      Assert::true(
        isset($item["symbol"]) && is_string($item["symbol"]),
        "Translation must have string symbol",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["name"], "name must be string");
      Assert::string($item["symbol"], "symbol must be string");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "unit_id" => (int) $item["unit_id"],
        "locale" => $item["locale"],
        "name" => $item["name"],
        "symbol" => $item["symbol"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->unitTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate compound units data
   */
  private function loadCompoundUnitsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "compound_units.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file compound_units.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file compound_units.json must contain an array");
    }

    Assert::list($data, "Data in compound_units.json must be a list");
    Assert::arrayArray($data, "Items in compound_units.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["id"]) && is_numeric($item["id"]),
        "Compound unit must have numeric id",
      );
      Assert::true(
        isset($item["numerator_unit_id"]) && is_numeric($item["numerator_unit_id"]),
        "Compound unit must have numeric numerator_unit_id",
      );
      Assert::true(
        isset($item["denominator_unit_id"]) && is_numeric($item["denominator_unit_id"]),
        "Compound unit must have numeric denominator_unit_id",
      );

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "numerator_unit_id" => (int) $item["numerator_unit_id"],
        "denominator_unit_id" => (int) $item["denominator_unit_id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
      ];
    }

    $this->compoundUnitsData = $validatedData;
  }

  /**
   * Load and validate unit conversions data
   */
  private function loadUnitConversionsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "unit_conversions.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file unit_conversions.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file unit_conversions.json must contain an array");
    }

    Assert::list($data, "Data in unit_conversions.json must be a list");
    Assert::arrayArray($data, "Items in unit_conversions.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["from_unit_id"]) && is_numeric($item["from_unit_id"]),
        "Unit conversion must have numeric from_unit_id",
      );
      Assert::true(
        isset($item["to_unit_id"]) && is_numeric($item["to_unit_id"]),
        "Unit conversion must have numeric to_unit_id",
      );
      Assert::true(
        isset($item["conversion_factor"]) &&
          (is_numeric($item["conversion_factor"]) || is_string($item["conversion_factor"])),
        "Unit conversion must have numeric conversion_factor",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $conversionFactor = (string) $item["conversion_factor"];

      $validatedData[] = [
        "id" => $id,
        "from_unit_id" => (int) $item["from_unit_id"],
        "to_unit_id" => (int) $item["to_unit_id"],
        "conversion_factor" => $conversionFactor,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->unitConversionsData = $validatedData;
  }

  /**
   * Load and validate option sets data
   */
  private function loadOptionSetsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "option_sets.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file option_sets.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file option_sets.json must contain an array");
    }

    Assert::list($data, "Data in option_sets.json must be a list");
    Assert::arrayArray($data, "Items in option_sets.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["id"]) && is_numeric($item["id"]),
        "Option set must have numeric id",
      );
      Assert::true(
        isset($item["name"]) && is_string($item["name"]),
        "Option set must have string name",
      );

      Assert::string($item["name"], "name must be string");

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "name" => $item["name"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->optionSetsData = $validatedData;
  }

  /**
   * Load and validate calculation definitions data
   */
  private function loadCalculationDefinitionsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "calculation_definitions.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definitions.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file calculation_definitions.json must contain an array");
    }

    Assert::list($data, "Data in calculation_definitions.json must be a list");
    Assert::arrayArray($data, "Items in calculation_definitions.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["id"]) && is_numeric($item["id"]),
        "Definition must have numeric id",
      );
      Assert::true(
        isset($item["scope_id"]) && is_numeric($item["scope_id"]),
        "Definition must have numeric scope_id",
      );
      Assert::true(
        isset($item["category_id"]) && is_numeric($item["category_id"]),
        "Definition must have numeric category_id",
      );
      Assert::true(
        isset($item["grouping_id"]) && is_numeric($item["grouping_id"]),
        "Definition must have numeric grouping_id",
      );
      Assert::true(
        isset($item["data_unit_id"]) && is_numeric($item["data_unit_id"]),
        "Definition must have numeric data_unit_id",
      );
      Assert::true(
        isset($item["emission_factor_compound_unit_id"]) &&
          is_numeric($item["emission_factor_compound_unit_id"]),
        "Definition must have numeric emission_factor_compound_unit_id",
      );

      // Build the validated item
      $validatedItem = [
        "id" => (int) $item["id"],
        "company_id" => null,
        "scope_id" => (int) $item["scope_id"],
        "category_id" => (int) $item["category_id"],
        "data_unit_id" => (int) $item["data_unit_id"],
        "emission_factor_compound_unit_id" => (int) $item["emission_factor_compound_unit_id"],
        "created_at" => null,
        "updated_at" => null,
        "deleted_at" => null,
        "grouping_id" => (int) $item["grouping_id"],
        "custom_name" => null,
        "input_method" => "",
        "sort_order" => 0,
        "data_formula" => null,
        "hide_from_data_page" => 0,
        "hide_from_emission_factor_page" => 0,
        "hide_from_results_page" => 0,
        "tag" => null,
        "link_id" => null,
        "option_set_id" => null,
      ];

      // Override with provided values
      if (isset($item["company_id"]) && is_numeric($item["company_id"])) {
        $validatedItem["company_id"] = (int) $item["company_id"];
      }
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $validatedItem["created_at"] = $item["created_at"];
      }
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $validatedItem["updated_at"] = $item["updated_at"];
      }
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $validatedItem["deleted_at"] = $item["deleted_at"];
      }
      if (isset($item["custom_name"])) {
        Assert::string($item["custom_name"], "custom_name must be string");
        $validatedItem["custom_name"] = $item["custom_name"];
      }
      if (isset($item["input_method"])) {
        Assert::string($item["input_method"], "input_method must be string");
        $validatedItem["input_method"] = $item["input_method"];
      }
      if (isset($item["sort_order"]) && is_numeric($item["sort_order"])) {
        $validatedItem["sort_order"] = (int) $item["sort_order"];
      }
      if (isset($item["data_formula"])) {
        Assert::string($item["data_formula"], "data_formula must be string");
        $validatedItem["data_formula"] = $item["data_formula"];
      }
      if (isset($item["hide_from_data_page"]) && is_numeric($item["hide_from_data_page"])) {
        $validatedItem["hide_from_data_page"] = (int) $item["hide_from_data_page"];
      }
      if (
        isset($item["hide_from_emission_factor_page"]) &&
        is_numeric($item["hide_from_emission_factor_page"])
      ) {
        $validatedItem["hide_from_emission_factor_page"] =
          (int) $item["hide_from_emission_factor_page"];
      }
      if (isset($item["hide_from_results_page"]) && is_numeric($item["hide_from_results_page"])) {
        $validatedItem["hide_from_results_page"] = (int) $item["hide_from_results_page"];
      }
      if (isset($item["tag"])) {
        Assert::string($item["tag"], "tag must be string");
        $validatedItem["tag"] = $item["tag"];
      }
      if (isset($item["link_id"])) {
        Assert::string($item["link_id"], "link_id must be string");
        $validatedItem["link_id"] = $item["link_id"];
      }
      if (isset($item["option_set_id"]) && is_numeric($item["option_set_id"])) {
        $validatedItem["option_set_id"] = (int) $item["option_set_id"];
      }

      $validatedData[] = $validatedItem;
    }

    $this->calculationDefinitionsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_translations.json must be a list");
    Assert::arrayArray($data, "Items in calculation_definition_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["calculation_definition_id"]) && is_numeric($item["calculation_definition_id"]),
        "Translation must have numeric calculation_definition_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["data_name"]) && is_string($item["data_name"]),
        "Translation must have string data_name",
      );
      Assert::true(
        isset($item["emission_factor_name"]) && is_string($item["emission_factor_name"]),
        "Translation must have string emission_factor_name",
      );
      Assert::true(
        isset($item["result_name"]) && is_string($item["result_name"]),
        "Translation must have string result_name",
      );

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["data_name"], "data_name must be string");
      Assert::string($item["emission_factor_name"], "emission_factor_name must be string");
      Assert::string($item["result_name"], "result_name must be string");

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "calculation_definition_id" => (int) $item["calculation_definition_id"],
        "locale" => $item["locale"],
        "data_name" => $item["data_name"],
        "emission_factor_name" => $item["emission_factor_name"],
        "result_name" => $item["result_name"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->calculationDefinitionTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate calculation definition category data
   */
  private function loadCalculationDefinitionCategoryData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_category.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_category.json: " . json_last_error_msg()
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file calculation_definition_category.json must contain an array");
    }

    Assert::list($data, "Data in calculation_definition_category.json must be a list");
    Assert::arrayArray($data, "Items in calculation_definition_category.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["id"]) && is_numeric($item["id"]),
        "Calculation definition category must have numeric id"
      );
      Assert::true(
        isset($item["category_id"]) && is_numeric($item["category_id"]),
        "Calculation definition category must have numeric category_id"
      );
      Assert::true(
        isset($item["calculation_definition_id"]) && is_numeric($item["calculation_definition_id"]),
        "Calculation definition category must have numeric calculation_definition_id"
      );

      $sortOrder = 0;
      if (isset($item["sort_order"])) {
        Assert::numeric($item["sort_order"], "sort_order must be numeric");
        $sortOrder = (int) $item["sort_order"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "category_id" => (int) $item["category_id"],
        "calculation_definition_id" => (int) $item["calculation_definition_id"],
        "sort_order" => $sortOrder,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->calculationDefinitionCategoryData = $validatedData;
  }
  /**
   * Load and validate calculation definition options data
   */
  private function loadCalculationDefinitionOptionsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_options.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_options.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_options.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_options.json must be a list");
    Assert::arrayArray($data, "Items in calculation_definition_options.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Option must have numeric id");
      Assert::true(
        isset($item["option_set_id"]) && is_numeric($item["option_set_id"]),
        "Option must have numeric option_set_id",
      );

      $sortOrder = 0;
      if (isset($item["sort_order"])) {
        Assert::numeric($item["sort_order"], "sort_order must be numeric");
        $sortOrder = (int) $item["sort_order"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "sort_order" => $sortOrder,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "option_set_id" => (int) $item["option_set_id"],
      ];
    }

    $this->calculationDefinitionOptionsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_option_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_option_translations.json: " .
          json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_option_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_option_translations.json must be a list");
    Assert::arrayArray(
      $data,
      "Items in calculation_definition_option_translations.json must be arrays",
    );

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["calculation_definition_option_id"]) &&
          is_numeric($item["calculation_definition_option_id"]),
        "Translation must have numeric calculation_definition_option_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["label"]) && is_string($item["label"]),
        "Translation must have string label",
      );

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["label"], "label must be string");

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "calculation_definition_option_id" => (int) $item["calculation_definition_option_id"],
        "locale" => $item["locale"],
        "label" => $item["label"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->calculationDefinitionOptionTranslationsData = $validatedTranslations;

    // Load year values
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_option_year_values.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_option_year_values.json: " .
          json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_option_year_values.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_option_year_values.json must be a list");
    Assert::arrayArray(
      $data,
      "Items in calculation_definition_option_year_values.json must be arrays",
    );

    $validatedYearValues = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["calculation_definition_option_id"]) &&
          is_numeric($item["calculation_definition_option_id"]),
        "Year value must have numeric calculation_definition_option_id",
      );
      Assert::true(
        isset($item["year_id"]) && is_numeric($item["year_id"]),
        "Year value must have numeric year_id",
      );
      Assert::true(
        isset($item["value"]) && (is_numeric($item["value"]) || is_string($item["value"])),
        "Year value must have numeric value",
      );

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedYearValues[] = [
        "id" => $id,
        "calculation_definition_option_id" => (int) $item["calculation_definition_option_id"],
        "year_id" => (int) $item["year_id"],
        "value" => (string) $item["value"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->calculationDefinitionOptionYearValuesData = $validatedYearValues;
  }

  /**
   * Load and validate calculation definition year data
   */
  private function loadCalculationDefinitionYearData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_year.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_year.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_year.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_year.json must be a list");
    Assert::arrayArray($data, "Items in calculation_definition_year.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Year data must have numeric id");
      Assert::true(
        isset($item["calculation_definition_id"]) && is_numeric($item["calculation_definition_id"]),
        "Year data must have numeric calculation_definition_id",
      );
      Assert::true(
        isset($item["year_id"]) && is_numeric($item["year_id"]),
        "Year data must have numeric year_id",
      );

      $emissionFactorDefaultValue = null;
      if (
        isset($item["emission_factor_default_value"]) &&
        $item["emission_factor_default_value"] !== ""
      ) {
        Assert::true(
          is_numeric($item["emission_factor_default_value"]) ||
            is_string($item["emission_factor_default_value"]),
          "Emission factor default value must be numeric or string",
        );
        $emissionFactorDefaultValue = (string) $item["emission_factor_default_value"];
      }

      $emissionFactorDefaultSource = "";
      if (isset($item["emission_factor_default_source"])) {
        Assert::string(
          $item["emission_factor_default_source"],
          "emission_factor_default_source must be string",
        );
        $emissionFactorDefaultSource = $item["emission_factor_default_source"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "calculation_definition_id" => (int) $item["calculation_definition_id"],
        "year_id" => (int) $item["year_id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "emission_factor_default_value" => $emissionFactorDefaultValue,
        "emission_factor_default_source" => $emissionFactorDefaultSource,
      ];
    }

    $this->calculationDefinitionYearData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "calculation_definition_year_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file calculation_definition_year_translations.json: " .
          json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file calculation_definition_year_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in calculation_definition_year_translations.json must be a list");
    Assert::arrayArray(
      $data,
      "Items in calculation_definition_year_translations.json must be arrays",
    );

    $validatedTranslations = [];
    foreach ($data as $item) {
      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $calcDefYearId = 0;
      if (isset($item["calculation_definition_year_id"])) {
        Assert::numeric(
          $item["calculation_definition_year_id"],
          "calculation_definition_year_id must be numeric",
        );
        $calcDefYearId = (int) $item["calculation_definition_year_id"];
      }

      $locale = "";
      if (isset($item["locale"])) {
        Assert::string($item["locale"], "locale must be string");
        $locale = $item["locale"];
      }

      $dataHelpText = "";
      if (isset($item["data_help_text"])) {
        Assert::string($item["data_help_text"], "data_help_text must be string");
        $dataHelpText = $item["data_help_text"];
      }

      $emissionFactorHelpText = "";
      if (isset($item["emission_factor_help_text"])) {
        Assert::string(
          $item["emission_factor_help_text"],
          "emission_factor_help_text must be string",
        );
        $emissionFactorHelpText = $item["emission_factor_help_text"];
      }

      $resultHelpText = "";
      if (isset($item["result_help_text"])) {
        Assert::string($item["result_help_text"], "result_help_text must be string");
        $resultHelpText = $item["result_help_text"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "calculation_definition_year_id" => $calcDefYearId,
        "locale" => $locale,
        "data_help_text" => $dataHelpText,
        "emission_factor_help_text" => $emissionFactorHelpText,
        "result_help_text" => $resultHelpText,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->calculationDefinitionYearTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate metric definitions data
   */
  private function loadMetricDefinitionsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "metric_definitions.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file metric_definitions.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file metric_definitions.json must contain an array");
    }

    Assert::list($data, "Data in metric_definitions.json must be a list");
    Assert::arrayArray($data, "Items in metric_definitions.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["id"]) && is_numeric($item["id"]),
        "Definition must have numeric id",
      );
      Assert::true(
        isset($item["unit_id"]) && is_numeric($item["unit_id"]),
        "Definition must have numeric unit_id",
      );

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "unit_id" => (int) $item["unit_id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
      ];
    }

    $this->metricDefinitionsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "metric_definition_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file metric_definition_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file metric_definition_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in metric_definition_translations.json must be a list");
    Assert::arrayArray($data, "Items in metric_definition_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["metric_definition_id"]) && is_numeric($item["metric_definition_id"]),
        "Translation must have numeric metric_definition_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["name"]) && is_string($item["name"]),
        "Translation must have string name",
      );

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["name"], "name must be string");

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "metric_definition_id" => (int) $item["metric_definition_id"],
        "locale" => $item["locale"],
        "name" => $item["name"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->metricDefinitionTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate metric definition year data
   */
  private function loadMetricDefinitionYearData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "metric_definition_year.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file metric_definition_year.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file metric_definition_year.json must contain an array");
    }

    Assert::list($data, "Data in metric_definition_year.json must be a list");
    Assert::arrayArray($data, "Items in metric_definition_year.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Year data must have numeric id");
      Assert::true(
        isset($item["metric_definition_id"]) && is_numeric($item["metric_definition_id"]),
        "Year data must have numeric metric_definition_id",
      );
      Assert::true(
        isset($item["year_id"]) && is_numeric($item["year_id"]),
        "Year data must have numeric year_id",
      );

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "metric_definition_id" => (int) $item["metric_definition_id"],
        "year_id" => (int) $item["year_id"],
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->metricDefinitionYearData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "metric_definition_year_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file metric_definition_year_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file metric_definition_year_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in metric_definition_year_translations.json must be a list");
    Assert::arrayArray($data, "Items in metric_definition_year_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $metricDefYearId = 0;
      if (isset($item["metric_definition_year_id"])) {
        Assert::numeric(
          $item["metric_definition_year_id"],
          "metric_definition_year_id must be numeric",
        );
        $metricDefYearId = (int) $item["metric_definition_year_id"];
      }

      $locale = "";
      if (isset($item["locale"])) {
        Assert::string($item["locale"], "locale must be string");
        $locale = $item["locale"];
      }

      $helpText = "";
      if (isset($item["help_text"])) {
        Assert::string($item["help_text"], "help_text must be string");
        $helpText = $item["help_text"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "metric_definition_year_id" => $metricDefYearId,
        "locale" => $locale,
        "help_text" => $helpText,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
      ];
    }

    $this->metricDefinitionYearTranslationsData = $validatedTranslations;
  }

  /**
   * Load and validate select options data
   */
  private function loadSelectOptionsData(): void
  {
    $path = base_path(self::SEED_DATA_PATH . "select_options.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file select_options.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException("JSON file select_options.json must contain an array");
    }

    Assert::list($data, "Data in select_options.json must be a list");
    Assert::arrayArray($data, "Items in select_options.json must be arrays");

    $validatedData = [];
    foreach ($data as $item) {
      Assert::true(isset($item["id"]) && is_numeric($item["id"]), "Option must have numeric id");
      Assert::true(
        isset($item["type"]) && is_string($item["type"]),
        "Option must have string type",
      );

      Assert::string($item["type"], "type must be string");

      $sortOrder = 0;
      if (isset($item["sort_order"])) {
        Assert::numeric($item["sort_order"], "sort_order must be numeric");
        $sortOrder = (int) $item["sort_order"];
      }

      $createdAt = null;
      if (isset($item["created_at"])) {
        Assert::string($item["created_at"], "created_at must be string");
        $createdAt = $item["created_at"];
      }

      $updatedAt = null;
      if (isset($item["updated_at"])) {
        Assert::string($item["updated_at"], "updated_at must be string");
        $updatedAt = $item["updated_at"];
      }

      $deletedAt = null;
      if (isset($item["deleted_at"])) {
        Assert::string($item["deleted_at"], "deleted_at must be string");
        $deletedAt = $item["deleted_at"];
      }

      $validatedData[] = [
        "id" => (int) $item["id"],
        "type" => $item["type"],
        "sort_order" => $sortOrder,
        "created_at" => $createdAt,
        "updated_at" => $updatedAt,
        "deleted_at" => $deletedAt,
      ];
    }

    $this->selectOptionsData = $validatedData;

    // Load translations
    $path = base_path(self::SEED_DATA_PATH . "select_option_translations.json");
    if (!File::exists($path)) {
      throw new \RuntimeException("Required seed data file not found: {$path}");
    }

    $json = File::get($path);
    $data = json_decode($json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new \RuntimeException(
        "Invalid JSON in file select_option_translations.json: " . json_last_error_msg(),
      );
    }

    if (!is_array($data)) {
      throw new \RuntimeException(
        "JSON file select_option_translations.json must contain an array",
      );
    }

    Assert::list($data, "Data in select_option_translations.json must be a list");
    Assert::arrayArray($data, "Items in select_option_translations.json must be arrays");

    $validatedTranslations = [];
    foreach ($data as $item) {
      Assert::true(
        isset($item["select_option_id"]) && is_numeric($item["select_option_id"]),
        "Translation must have numeric select_option_id",
      );
      Assert::true(
        isset($item["locale"]) && is_string($item["locale"]),
        "Translation must have string locale",
      );
      Assert::true(
        isset($item["label"]) && is_string($item["label"]),
        "Translation must have string label",
      );

      Assert::string($item["locale"], "locale must be string");
      Assert::string($item["label"], "label must be string");

      $id = 0;
      if (isset($item["id"])) {
        Assert::numeric($item["id"], "id must be numeric");
        $id = (int) $item["id"];
      }

      $validatedTranslations[] = [
        "id" => $id,
        "select_option_id" => (int) $item["select_option_id"],
        "locale" => $item["locale"],
        "label" => $item["label"],
      ];
    }

    $this->selectOptionTranslationsData = $validatedTranslations;
  }

  /**
   * Seed years
   */
  private function seedYears(): void
  {
    $this->idMappings["years"] = [];

    foreach ($this->yearsData as $yearData) {
      $oldId = $yearData["id"];
      unset($yearData["id"]);

      $year = Year::create($yearData);
      $this->idMappings["years"][$oldId] = $year->id;
    }
  }

  /**
   * Seed scopes
   */
  private function seedScopes(): void
  {
    $this->idMappings["scopes"] = [];

    foreach ($this->scopesData as $scopeData) {
      $oldId = $scopeData["id"];
      unset($scopeData["id"]);

      $scope = Scope::create($scopeData);
      $this->idMappings["scopes"][$oldId] = $scope->id;
    }
  }

  /**
   * Seed scope calculation variants
   */
  private function seedScopeCalculationVariants(): void
  {
    $this->idMappings["scope_calculation_variants"] = [];

    // Group translations by variant ID
    $translationsByVariant = [];
    foreach ($this->scopeCalculationVariantTranslationsData as $translation) {
      $variantId = $translation["scope_calculation_variant_id"];
      if (!isset($translationsByVariant[$variantId])) {
        $translationsByVariant[$variantId] = [];
      }
      $translationsByVariant[$variantId][] = $translation;
    }

    foreach ($this->scopeCalculationVariantsData as $variantData) {
      $oldId = $variantData["id"];
      $oldScopeId = $variantData["scope_id"];

      Assert::true(
        isset($this->idMappings["scopes"][$oldScopeId]),
        "Scope mapping not found for ID {$oldScopeId}",
      );

      $variantData["scope_id"] = $this->idMappings["scopes"][$oldScopeId];
      unset($variantData["id"]);

      $variant = ScopeCalculationVariant::create($variantData);
      $this->idMappings["scope_calculation_variants"][$oldId] = $variant->id;

      // Add translations for all locales with defaults
      $variant->setRelation("translations", collect());

      $locales = ["fi", "sv", "en"];
      foreach ($locales as $locale) {
        $label = "";
        if (isset($translationsByVariant[$oldId])) {
          foreach ($translationsByVariant[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $label = $translation["label"];
              break;
            }
          }
        }
        $variant->translateOrNew($locale)->label = $label;
      }
      $variant->save();
    }
  }

  /**
   * Seed categories
   */
  private function seedCategories(): void
  {
    $this->idMappings["categories"] = [];

    // Group translations by category ID
    $translationsByCategory = [];
    foreach ($this->categoryTranslationsData as $translation) {
      $categoryId = $translation["category_id"];
      if (!isset($translationsByCategory[$categoryId])) {
        $translationsByCategory[$categoryId] = [];
      }
      $translationsByCategory[$categoryId][] = $translation;
    }

    foreach ($this->categoriesData as $categoryData) {
      $oldId = $categoryData["id"];
      unset($categoryData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "title" => "",
          "description" => "",
        ];

        if (isset($translationsByCategory[$oldId])) {
          foreach ($translationsByCategory[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "title" => $translation["title"],
                "description" => $translation["description"] ?? "",
              ];
              break;
            }
          }
        }
      }

      $category = Category::create(array_merge($categoryData, $translations));
      $this->idMappings["categories"][$oldId] = $category->id;
    }
  }

  /**
   * Seed groupings
   */
  private function seedGroupings(): void
  {
    $this->idMappings["groupings"] = [];

    // Group translations by grouping ID
    $translationsByGrouping = [];
    foreach ($this->groupingTranslationsData as $translation) {
      $groupingId = $translation["grouping_id"];
      if (!isset($translationsByGrouping[$groupingId])) {
        $translationsByGrouping[$groupingId] = [];
      }
      $translationsByGrouping[$groupingId][] = $translation;
    }

    foreach ($this->groupingsData as $groupingData) {
      $oldId = $groupingData["id"];
      unset($groupingData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "title" => "",
        ];

        if (isset($translationsByGrouping[$oldId])) {
          foreach ($translationsByGrouping[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "title" => $translation["title"],
              ];
              break;
            }
          }
        }
      }

      $grouping = Grouping::create(array_merge($groupingData, $translations));
      $this->idMappings["groupings"][$oldId] = $grouping->id;
    }
  }

  /**
   * Seed units
   */
  private function seedUnits(): void
  {
    $this->idMappings["units"] = [];

    // Group translations by unit ID
    $translationsByUnit = [];
    foreach ($this->unitTranslationsData as $translation) {
      $unitId = $translation["unit_id"];
      if (!isset($translationsByUnit[$unitId])) {
        $translationsByUnit[$unitId] = [];
      }
      $translationsByUnit[$unitId][] = $translation;
    }

    foreach ($this->unitsData as $unitData) {
      $oldId = $unitData["id"];
      unset($unitData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "name" => "",
          "symbol" => "",
        ];

        if (isset($translationsByUnit[$oldId])) {
          foreach ($translationsByUnit[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "name" => $translation["name"],
                "symbol" => $translation["symbol"],
              ];
              break;
            }
          }
        }
      }

      $unit = Unit::create(array_merge($unitData, $translations));
      $this->idMappings["units"][$oldId] = $unit->id;
    }
  }

  /**
   * Seed compound units
   */
  private function seedCompoundUnits(): void
  {
    $this->idMappings["compound_units"] = [];

    foreach ($this->compoundUnitsData as $data) {
      $oldId = $data["id"];
      $oldNumeratorId = $data["numerator_unit_id"];
      $oldDenominatorId = $data["denominator_unit_id"];

      Assert::true(
        isset($this->idMappings["units"][$oldNumeratorId]),
        "Unit mapping not found for numerator ID {$oldNumeratorId}",
      );
      Assert::true(
        isset($this->idMappings["units"][$oldDenominatorId]),
        "Unit mapping not found for denominator ID {$oldDenominatorId}",
      );

      $data["numerator_unit_id"] = $this->idMappings["units"][$oldNumeratorId];
      $data["denominator_unit_id"] = $this->idMappings["units"][$oldDenominatorId];
      unset($data["id"]);

      $compoundUnit = CompoundUnit::create($data);
      $this->idMappings["compound_units"][$oldId] = $compoundUnit->id;
    }
  }

  /**
   * Seed unit conversions
   */
  private function seedUnitConversions(): void
  {
    foreach ($this->unitConversionsData as $data) {
      $oldFromId = $data["from_unit_id"];
      $oldToId = $data["to_unit_id"];

      Assert::true(
        isset($this->idMappings["units"]) && isset($this->idMappings["units"][$oldFromId]),
        "Unit mapping not found for from_unit_id {$oldFromId}",
      );
      Assert::true(
        isset($this->idMappings["units"][$oldToId]),
        "Unit mapping not found for to_unit_id {$oldToId}",
      );

      $data["from_unit_id"] = $this->idMappings["units"][$oldFromId];
      $data["to_unit_id"] = $this->idMappings["units"][$oldToId];
      unset($data["id"]);

      UnitConversion::create($data);
    }
  }

  /**
   * Seed option sets
   */
  private function seedOptionSets(): void
  {
    $this->idMappings["option_sets"] = [];

    foreach ($this->optionSetsData as $data) {
      $oldId = $data["id"];
      unset($data["id"]);

      $optionSet = \App\Models\OptionSet::create($data);
      $this->idMappings["option_sets"][$oldId] = $optionSet->id;
    }
  }

  /**
   * Seed calculation definitions
   */
  private function seedCalculationDefinitions(): void
  {
    $this->idMappings["calculation_definitions"] = [];

    // Group translations by definition ID
    $translationsByDefinition = [];
    foreach ($this->calculationDefinitionTranslationsData as $translation) {
      $definitionId = $translation["calculation_definition_id"];
      if (!isset($translationsByDefinition[$definitionId])) {
        $translationsByDefinition[$definitionId] = [];
      }
      $translationsByDefinition[$definitionId][] = $translation;
    }

    foreach ($this->calculationDefinitionsData as $definitionData) {
      $oldId = $definitionData["id"];

      // Map foreign keys
      $scopeId = $definitionData["scope_id"];
      $categoryId = $definitionData["category_id"];
      $groupingId = $definitionData["grouping_id"];
      $dataUnitId = $definitionData["data_unit_id"];
      $emissionFactorCompoundUnitId = $definitionData["emission_factor_compound_unit_id"];

      Assert::true(
        isset($this->idMappings["scopes"]) && isset($this->idMappings["scopes"][$scopeId]),
        "Scope mapping not found for ID {$scopeId}",
      );
      Assert::true(
        isset($this->idMappings["categories"]) &&
          isset($this->idMappings["categories"][$categoryId]),
        "Category mapping not found for ID {$categoryId}",
      );
      Assert::true(
        isset($this->idMappings["groupings"]) && isset($this->idMappings["groupings"][$groupingId]),
        "Grouping mapping not found for ID {$groupingId}",
      );
      Assert::true(
        isset($this->idMappings["units"]) && isset($this->idMappings["units"][$dataUnitId]),
        "Unit mapping not found for ID {$dataUnitId}",
      );
      Assert::true(
        isset($this->idMappings["compound_units"]) &&
          isset($this->idMappings["compound_units"][$emissionFactorCompoundUnitId]),
        "Compound unit mapping not found for ID {$emissionFactorCompoundUnitId}",
      );

      $definitionData["scope_id"] = $this->idMappings["scopes"][$scopeId];
      $definitionData["category_id"] = $this->idMappings["categories"][$categoryId];
      $definitionData["grouping_id"] = $this->idMappings["groupings"][$groupingId];
      $definitionData["data_unit_id"] = $this->idMappings["units"][$dataUnitId];
      $definitionData["emission_factor_compound_unit_id"] =
        $this->idMappings["compound_units"][$emissionFactorCompoundUnitId];

      if ($definitionData["option_set_id"] !== null && $definitionData["option_set_id"] !== 0) {
        $optionSetId = $definitionData["option_set_id"];
        Assert::true(
          isset($this->idMappings["option_sets"]) &&
            isset($this->idMappings["option_sets"][$optionSetId]),
          "Option set mapping not found for ID {$optionSetId}",
        );
        $definitionData["option_set_id"] = $this->idMappings["option_sets"][$optionSetId];
      }

      unset($definitionData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "data_name" => "",
          "emission_factor_name" => "",
          "result_name" => "",
        ];

        if (isset($translationsByDefinition[$oldId])) {
          foreach ($translationsByDefinition[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "data_name" => $translation["data_name"],
                "emission_factor_name" => $translation["emission_factor_name"],
                "result_name" => $translation["result_name"],
              ];
              break;
            }
          }
        }
      }

      $definition = CalculationDefinition::create(array_merge($definitionData, $translations));
      $this->idMappings["calculation_definitions"][$oldId] = $definition->id;
    }
  }

  /**
   * Seed calculation definition category (many-to-many pivot)
   */
  private function seedCalculationDefinitionCategory(): void
  {
    foreach ($this->calculationDefinitionCategoryData as $data) {
      $oldCategoryId = $data["category_id"];
      $oldCalculationDefinitionId = $data["calculation_definition_id"];

      Assert::true(
        isset($this->idMappings["categories"][$oldCategoryId]),
        "Category mapping not found for ID {$oldCategoryId}"
      );
      Assert::true(
        isset($this->idMappings["calculation_definitions"][$oldCalculationDefinitionId]),
        "Calculation definition mapping not found for ID {$oldCalculationDefinitionId}"
      );

      $data["category_id"] = $this->idMappings["categories"][$oldCategoryId];
      $data["calculation_definition_id"] = $this->idMappings["calculation_definitions"][$oldCalculationDefinitionId];
      unset($data["id"]);

      DB::table("calculation_definition_category")->insert($data);
    }
  }
  /**
   * Seed calculation definition options
   */
  private function seedCalculationDefinitionOptions(): void
  {
    $this->idMappings["calculation_definition_options"] = [];

    // Group translations by option ID
    $translationsByOption = [];
    foreach ($this->calculationDefinitionOptionTranslationsData as $translation) {
      $optionId = $translation["calculation_definition_option_id"];
      if (!isset($translationsByOption[$optionId])) {
        $translationsByOption[$optionId] = [];
      }
      $translationsByOption[$optionId][] = $translation;
    }

    // Group year values by option ID
    $yearValuesByOption = [];
    foreach ($this->calculationDefinitionOptionYearValuesData as $yearValue) {
      $optionId = $yearValue["calculation_definition_option_id"];
      if (!isset($yearValuesByOption[$optionId])) {
        $yearValuesByOption[$optionId] = [];
      }
      $yearValuesByOption[$optionId][] = $yearValue;
    }

    foreach ($this->calculationDefinitionOptionsData as $optionData) {
      $oldId = $optionData["id"];
      $optionSetId = $optionData["option_set_id"];

      Assert::true(
        isset($this->idMappings["option_sets"]) &&
          isset($this->idMappings["option_sets"][$optionSetId]),
        "Option set mapping not found for ID {$optionSetId}",
      );

      $optionData["option_set_id"] = $this->idMappings["option_sets"][$optionSetId];
      unset($optionData["id"]);

      $option = \App\Models\CalculationDefinitionOption::create($optionData);
      $this->idMappings["calculation_definition_options"][$oldId] = $option->id;

      // Add translations for all locales with defaults
      $option->setRelation("translations", collect());

      $locales = ["fi", "sv", "en"];
      foreach ($locales as $locale) {
        $label = "";
        if (isset($translationsByOption[$oldId])) {
          foreach ($translationsByOption[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $label = $translation["label"];
              break;
            }
          }
        }
        $option->translateOrNew($locale)->label = $label;
      }
      $option->save();

      // Add year values
      if (isset($yearValuesByOption[$oldId])) {
        foreach ($yearValuesByOption[$oldId] as $yearValue) {
          $yearId = $yearValue["year_id"];
          Assert::true(
            isset($this->idMappings["years"]) && isset($this->idMappings["years"][$yearId]),
            "Year mapping not found for ID {$yearId}",
          );

          \App\Models\CalculationDefinitionOptionYearValue::create([
            "calculation_definition_option_id" => $option->id,
            "year_id" => $this->idMappings["years"][$yearId],
            "value" => $yearValue["value"],
          ]);
        }
      }
    }
  }

  /**
   * Seed calculation definition year data
   */
  private function seedCalculationDefinitionYearData(): void
  {
    // Group translations by year record ID
    $translationsByYearRecord = [];
    foreach ($this->calculationDefinitionYearTranslationsData as $translation) {
      $yearRecordId = $translation["calculation_definition_year_id"];
      if (!isset($translationsByYearRecord[$yearRecordId])) {
        $translationsByYearRecord[$yearRecordId] = [];
      }
      $translationsByYearRecord[$yearRecordId][] = $translation;
    }

    foreach ($this->calculationDefinitionYearData as $data) {
      $oldId = $data["id"];
      $calcDefId = $data["calculation_definition_id"];
      $yearId = $data["year_id"];

      Assert::true(
        isset($this->idMappings["calculation_definitions"]) &&
          isset($this->idMappings["calculation_definitions"][$calcDefId]),
        "Calculation definition mapping not found for ID {$calcDefId}",
      );
      Assert::true(
        isset($this->idMappings["years"]) && isset($this->idMappings["years"][$yearId]),
        "Year mapping not found for ID {$yearId}",
      );

      $data["calculation_definition_id"] = $this->idMappings["calculation_definitions"][$calcDefId];
      $data["year_id"] = $this->idMappings["years"][$yearId];
      unset($data["id"]);

      $yearRecordId = DB::table("calculation_definition_year")->insertGetId($data);

      // Insert translations for all locales with defaults
      $locales = ["fi", "sv", "en"];
      foreach ($locales as $locale) {
        $translationData = [
          "calculation_definition_year_id" => $yearRecordId,
          "locale" => $locale,
          "data_help_text" => "",
          "emission_factor_help_text" => "",
          "result_help_text" => "",
          "created_at" => null,
          "updated_at" => null,
        ];

        if (isset($translationsByYearRecord[$oldId])) {
          foreach ($translationsByYearRecord[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translationData = [
                "calculation_definition_year_id" => $yearRecordId,
                "locale" => $locale,
                "data_help_text" => $translation["data_help_text"],
                "emission_factor_help_text" => $translation["emission_factor_help_text"],
                "result_help_text" => $translation["result_help_text"],
                "created_at" => $translation["created_at"] ?? null,
                "updated_at" => $translation["updated_at"] ?? null,
              ];
              break;
            }
          }
        }

        DB::table("calculation_definition_year_translations")->insert($translationData);
      }
    }
  }

  /**
   * Seed metric definitions
   */
  private function seedMetricDefinitions(): void
  {
    $this->idMappings["metric_definitions"] = [];

    // Group translations by definition ID
    $translationsByDefinition = [];
    foreach ($this->metricDefinitionTranslationsData as $translation) {
      $definitionId = $translation["metric_definition_id"];
      if (!isset($translationsByDefinition[$definitionId])) {
        $translationsByDefinition[$definitionId] = [];
      }
      $translationsByDefinition[$definitionId][] = $translation;
    }

    foreach ($this->metricDefinitionsData as $definitionData) {
      $oldId = $definitionData["id"];
      $unitId = $definitionData["unit_id"];

      Assert::true(
        isset($this->idMappings["units"]) && isset($this->idMappings["units"][$unitId]),
        "Unit mapping not found for ID {$unitId}",
      );

      $definitionData["unit_id"] = $this->idMappings["units"][$unitId];
      unset($definitionData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "name" => "",
        ];

        if (isset($translationsByDefinition[$oldId])) {
          foreach ($translationsByDefinition[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "name" => $translation["name"],
              ];
              break;
            }
          }
        }
      }

      $definition = MetricDefinition::create(array_merge($definitionData, $translations));
      $this->idMappings["metric_definitions"][$oldId] = $definition->id;
    }
  }

  /**
   * Seed metric definition year data
   */
  private function seedMetricDefinitionYearData(): void
  {
    // Group translations by year record ID
    $translationsByYearRecord = [];
    foreach ($this->metricDefinitionYearTranslationsData as $translation) {
      $yearRecordId = $translation["metric_definition_year_id"];
      if (!isset($translationsByYearRecord[$yearRecordId])) {
        $translationsByYearRecord[$yearRecordId] = [];
      }
      $translationsByYearRecord[$yearRecordId][] = $translation;
    }

    foreach ($this->metricDefinitionYearData as $data) {
      $oldId = $data["id"];
      $metricDefId = $data["metric_definition_id"];
      $yearId = $data["year_id"];

      Assert::true(
        isset($this->idMappings["metric_definitions"]) &&
          isset($this->idMappings["metric_definitions"][$metricDefId]),
        "Metric definition mapping not found for ID {$metricDefId}",
      );
      Assert::true(
        isset($this->idMappings["years"]) && isset($this->idMappings["years"][$yearId]),
        "Year mapping not found for ID {$yearId}",
      );

      $data["metric_definition_id"] = $this->idMappings["metric_definitions"][$metricDefId];
      $data["year_id"] = $this->idMappings["years"][$yearId];
      unset($data["id"]);

      $yearRecordId = DB::table("metric_definition_year")->insertGetId($data);

      // Insert translations for all locales with defaults
      $locales = ["fi", "sv", "en"];
      foreach ($locales as $locale) {
        $translationData = [
          "metric_definition_year_id" => $yearRecordId,
          "locale" => $locale,
          "help_text" => "",
          "created_at" => null,
          "updated_at" => null,
        ];

        if (isset($translationsByYearRecord[$oldId])) {
          foreach ($translationsByYearRecord[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translationData = [
                "metric_definition_year_id" => $yearRecordId,
                "locale" => $locale,
                "help_text" => $translation["help_text"],
                "created_at" => $translation["created_at"] ?? null,
                "updated_at" => $translation["updated_at"] ?? null,
              ];
              break;
            }
          }
        }

        DB::table("metric_definition_year_translations")->insert($translationData);
      }
    }
  }

  /**
   * Seed select options
   */
  private function seedSelectOptions(): void
  {
    // Group translations by option ID
    $translationsByOption = [];
    foreach ($this->selectOptionTranslationsData as $translation) {
      $optionId = $translation["select_option_id"];
      if (!isset($translationsByOption[$optionId])) {
        $translationsByOption[$optionId] = [];
      }
      $translationsByOption[$optionId][] = $translation;
    }

    foreach ($this->selectOptionsData as $optionData) {
      $oldId = $optionData["id"];
      unset($optionData["id"]);

      // Build translations array for all locales with defaults
      $translations = [];
      $locales = ["fi", "sv", "en"];

      foreach ($locales as $locale) {
        $translations[$locale] = [
          "label" => "",
        ];

        if (isset($translationsByOption[$oldId])) {
          foreach ($translationsByOption[$oldId] as $translation) {
            if ($translation["locale"] === $locale) {
              $translations[$locale] = [
                "label" => $translation["label"],
              ];
              break;
            }
          }
        }
      }

      SelectOption::create(array_merge($optionData, $translations));
    }
  }
}
