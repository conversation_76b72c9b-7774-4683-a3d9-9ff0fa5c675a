<?php

declare(strict_types=1);

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use OverflowException;
use RuntimeException;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
final class UserFactory extends Factory
{
  /**
   * The current password being used by the factory.
   */
  protected static ?string $password;

  /**
   * Define the model's default state.
   *
   * @return array<string, mixed>
   *
   * @throws OverflowException
   * @throws RuntimeException
   */
  public function definition(): array
  {
    return [
      "name" => fake()->name(),
      "email" => fake()->unique()->safeEmail(),
      "email_verified_at" => now(),
      "password" => (self::$password ??= Hash::make("password")),
      "remember_token" => Str::random(10),
    ];
  }

  /**
   * Indicate that the model's email address should be unverified.
   */
  public function unverified(): static
  {
    return $this->state(
      fn(array $attributes) => [
        "email_verified_at" => null,
      ],
    );
  }
}
