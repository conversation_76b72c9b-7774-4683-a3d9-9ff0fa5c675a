<?php

namespace Database\Factories;

use App\Helpers\Assert;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use OverflowException;
use RuntimeException;
use Spatie\Permission\Models\Role;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
final class UserFactory extends Factory
{
  /**
   * The current password being used by the factory.
   */
  protected static ?string $password;

  /**
   * The role to assign to the next created user.
   * @var string|null
   */
  protected static ?string $roleToAssign = null;

  /**
   * Define the model's default state.
   *
   * @return array<string, mixed>
   *
   * @throws OverflowException
   * @throws RuntimeException
   */
  public function definition(): array
  {
    return [
      "name" => fake()->name(),
      "email" => fake()->unique()->safeEmail(),
      "email_verified_at" => now(),
      "password" => (self::$password ??= Hash::make("password")),
      "remember_token" => Str::random(10),
      "selected_company_id" => null,
    ];
  }

  /**
   * Configure the model factory.
   */
  public function configure(): static
  {
    return $this->afterCreating(function ($user) {
      // Use the statically stored role or default to basic
      $roleName = self::$roleToAssign ?? "basic";

      $role = Role::where("name", $roleName)->where("guard_name", "web")->first();
      Assert::notNull($role);

      $user->assignRole($role);

      // Clear the static property after use
      self::$roleToAssign = null;
    });
  }

  /**
   * Indicate that the model's email address should be unverified.
   */
  public function unverified(): static
  {
    return $this->state(
      fn(array $attributes) => [
        "email_verified_at" => null,
      ],
    );
  }

  /**
   * Attach a specific role to the user.
   */
  public function withRole(string $roleName): static
  {
    self::$roleToAssign = $roleName;
    return $this;
  }
}
