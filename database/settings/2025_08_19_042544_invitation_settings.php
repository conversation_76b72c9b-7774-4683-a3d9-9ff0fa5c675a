<?php

declare(strict_types=1);

use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
  public function up(): void
  {
    $this->migrator->add("invitation.tokenExpirationMinutes", 10080); // 7 days
    $this->migrator->add("invitation.maxIpAttempts", 5);
    $this->migrator->add("invitation.ipTimeWindowMinutes", 60);
    $this->migrator->add("invitation.maxEmailAttempts", 3);
    $this->migrator->add("invitation.emailTimeWindowMinutes", 60);
    $this->migrator->add("invitation.maxVerificationAttempts", 10);
    $this->migrator->add("invitation.verificationTimeWindowMinutes", 60);
  }

  public function down(): void
  {
    $this->migrator->delete("invitation.tokenExpirationMinutes");
    $this->migrator->delete("invitation.maxIpAttempts");
    $this->migrator->delete("invitation.ipTimeWindowMinutes");
    $this->migrator->delete("invitation.maxEmailAttempts");
    $this->migrator->delete("invitation.emailTimeWindowMinutes");
    $this->migrator->delete("invitation.maxVerificationAttempts");
    $this->migrator->delete("invitation.verificationTimeWindowMinutes");
  }
};
