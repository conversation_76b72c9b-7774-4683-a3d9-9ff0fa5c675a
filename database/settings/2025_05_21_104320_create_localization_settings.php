<?php

declare(strict_types=1);

namespace App\Settings\Migrations;

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    $this->migrator->add("localization.available_locales", [
      "fi" => "Suomi",
      "en" => "English",
      "sv" => "Svenska",
    ]);

    $this->migrator->add("localization.default_locale", "fi");
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    $this->migrator->delete("localization.available_locales");
    $this->migrator->delete("localization.default_locale");
  }
};
