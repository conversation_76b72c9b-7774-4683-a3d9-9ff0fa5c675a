<?php

declare(strict_types=1);

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
  public function up(): void
  {
    $this->migrator->add("authentication.tokenExpirationMinutes", 15);
    $this->migrator->add("authentication.maxIpAttempts", 5);
    $this->migrator->add("authentication.ipTimeWindowMinutes", 30);
    $this->migrator->add("authentication.maxEmailAttempts", 3);
    $this->migrator->add("authentication.emailTimeWindowMinutes", 15);
    $this->migrator->add("authentication.maxVerificationAttempts", 10);
    $this->migrator->add("authentication.verificationTimeWindowMinutes", 60);
  }
};
