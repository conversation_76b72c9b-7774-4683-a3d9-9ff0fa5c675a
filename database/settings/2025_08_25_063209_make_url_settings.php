<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
  public function up(): void
  {
    $this->migrator->add(
      "url.industry_classification_api_url",
      "https://data.stat.fi/api/classifications/v2/classifications/toimiala_1_20250101/classificationItems",
    );
    $this->migrator->add(
      "url.municipality_api_url",
      "https://data.stat.fi/api/classifications/v2/classifications/kunta_1_20250101/classificationItems",
    );
    $this->migrator->add("url.embedded_app_url", [
      "fi" => "https://kauppakamari.fi/paastolaskuri/",
      "sv" => "https://kauppakamari.fi/sv/emissionsraknare/",
      "en" => "https://kauppakamari.fi/en/emission-calculator/",
    ]);
  }
};
