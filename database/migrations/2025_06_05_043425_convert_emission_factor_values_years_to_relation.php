<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("emission_factor_values", function (Blueprint $table) {
      // Drop foreign keys first
      $table->dropForeign(["emission_factor_definition_id"]);
      $table->dropForeign(["company_id"]);

      // Now we can drop the unique index with its custom name
      $table->dropUnique("company_id_emission_factor_definition_id_year_unique");

      // Drop the year column
      $table->dropColumn("year");

      // Add year_id column
      $table->foreignId("year_id")->after("company_id")->constrained("years");

      // Re-add the foreign keys
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("calculation_definitions");
      $table->foreign("company_id")->references("id")->on("companies");

      // Add new unique constraint with custom name
      $table->unique(
        ["company_id", "emission_factor_definition_id", "year_id"],
        "company_id_emission_factor_definition_id_year_id_unique",
      );
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("emission_factor_values", function (Blueprint $table) {
      // Drop foreign keys
      $table->dropForeign(["emission_factor_definition_id"]);
      $table->dropForeign(["company_id"]);
      $table->dropForeign(["year_id"]);

      // Drop unique constraint with custom name
      $table->dropUnique("company_id_emission_factor_definition_id_year_id_unique");

      // Drop year_id
      $table->dropColumn("year_id");

      // Add back year column
      $table->unsignedSmallInteger("year")->default((int) date("Y"))->after("company_id");

      // Re-add foreign keys
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("calculation_definitions");
      $table->foreign("company_id")->references("id")->on("companies");

      // Add back unique constraint with original custom name
      $table->unique(
        ["company_id", "emission_factor_definition_id", "year"],
        "company_id_emission_factor_definition_id_year_unique",
      );
    });
  }
};
