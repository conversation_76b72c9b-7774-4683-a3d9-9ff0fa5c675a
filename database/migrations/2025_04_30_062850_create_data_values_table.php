<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("data_values", function (Blueprint $table) {
      $table->id();
      $table->decimal("value", 15, 4);
      $table->foreignId("data_definition_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->timestamps();

      $table->index(["data_definition_id", "company_id"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("data_values");
  }
};
