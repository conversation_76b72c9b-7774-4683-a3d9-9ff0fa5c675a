<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("industry_classifications", function (Blueprint $table): void {
      $table->id();
      $table->string("code");
      $table->string("local_id")->unique();
      $table->timestamps();
      $table->softDeletes();

      $table->index("code");
    });

    Schema::create("industry_classification_translations", function (Blueprint $table): void {
      $table->id();
      $table->unsignedBigInteger("industry_classification_id");
      $table->string("locale")->index();
      $table->string("name");

      $table->unique(["industry_classification_id", "locale"], "industry_class_trans_unique");

      $table
        ->foreign("industry_classification_id", "industry_class_trans_foreign")
        ->references("id")
        ->on("industry_classifications")
        ->onDelete("cascade");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("industry_classification_translations");
    Schema::dropIfExists("industry_classifications");
  }
};
