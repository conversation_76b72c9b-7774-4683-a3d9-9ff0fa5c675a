<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->decimal("emission_factor_default_value", 20, 10)->nullable();
      $table->text("data_help_text");
      $table->text("emission_factor_help_text");
      $table->text("result_help_text");
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropColumn(["emission_factor_default_value", "help_text"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->decimal("emission_factor_default_value", 20, 10)->nullable();
      $table->text("help_text");
    });

    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->dropColumn([
        "emission_factor_default_value",
        "data_help_text",
        "emission_factor_help_text",
        "result_help_text",
      ]);
    });
  }
};
