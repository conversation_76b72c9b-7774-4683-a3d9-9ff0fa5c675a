<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->unique(["calculation_definition_id", "year_id"], "calc_def_year_unique");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->dropUnique("calc_def_year_unique");
    });
  }
};
