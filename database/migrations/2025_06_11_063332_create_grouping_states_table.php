<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("grouping_states", function (Blueprint $table) {
      $table->id();
      $table->foreignId("company_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->foreignId("scope_id")->constrained();
      $table->foreignId("grouping_id")->constrained();
      $table->enum("state", ["hidden"]);
      $table->foreignId("select_option_id")->nullable()->constrained();
      $table->text("custom_reason")->nullable();
      $table->timestamps();

      $table->unique(["company_id", "year_id", "scope_id", "grouping_id"], "unique_grouping_state");
      $table->index(["company_id", "year_id"], "idx_company_year");
      $table->index(["scope_id", "grouping_id"], "idx_scope_grouping");
    });

    // Add check constraint to ensure either custom_reason or select_option_id is null
    DB::statement(
      "ALTER TABLE grouping_states ADD CONSTRAINT chk_reason_xor CHECK ((select_option_id IS NULL AND custom_reason IS NOT NULL) OR (select_option_id IS NOT NULL AND custom_reason IS NULL))",
    );
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Drop the check constraint first if it exists
    DB::statement("ALTER TABLE grouping_states DROP CONSTRAINT IF EXISTS chk_reason_xor");

    Schema::dropIfExists("grouping_states");
  }
};
