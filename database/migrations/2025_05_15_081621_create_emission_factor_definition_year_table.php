<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("emission_factor_definition_year", function (Blueprint $table) {
      $table->id();

      // Create column first
      $table->unsignedBigInteger("emission_factor_definition_id");

      // Then add constraint with a custom name
      $table
        ->foreign("emission_factor_definition_id", "ef_def_year_foreign")
        ->references("id")
        ->on("emission_factor_definitions")
        ->onDelete("cascade");

      $table->foreignId("year_id")->constrained();
      $table->timestamps();

      // Also shorten the unique constraint name
      $table->unique(["emission_factor_definition_id", "year_id"], "ef_def_year_unique");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("emission_factor_definition_year");
  }
};
