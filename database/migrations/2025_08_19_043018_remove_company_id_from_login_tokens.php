<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("login_tokens", function (Blueprint $table) {
      $table->dropForeign(["company_id"]);
      $table->dropColumn("company_id");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("login_tokens", function (Blueprint $table) {
      $table->unsignedBigInteger("company_id")->nullable();
      $table->index("company_id");
      $table->foreign("company_id")->references("id")->on("companies");
    });
  }
};
