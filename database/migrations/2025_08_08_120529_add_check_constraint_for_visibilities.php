<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // First, fix any existing data that violates the constraint
    // Company-specific definitions should never be hidden from any page
    DB::table("calculation_definitions")
      ->whereNotNull("company_id")
      ->where(function (Illuminate\Database\Query\Builder $query) {
        $query
          ->where("hide_from_data_page", true)
          ->orWhere("hide_from_emission_factor_page", true)
          ->orWhere("hide_from_results_page", true);
      })
      ->update([
        "hide_from_data_page" => false,
        "hide_from_emission_factor_page" => false,
        "hide_from_results_page" => false,
      ]);

    // This ensures company-specific definitions cannot be hidden from any page
    DB::statement('
            ALTER TABLE calculation_definitions 
            ADD CONSTRAINT chk_company_visibility 
            CHECK (
                company_id IS NULL 
                OR (
                    hide_from_data_page = false 
                    AND hide_from_emission_factor_page = false 
                    AND hide_from_results_page = false
                )
            )
        ');
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    DB::statement("ALTER TABLE calculation_definitions DROP CONSTRAINT chk_company_visibility");
  }
};
