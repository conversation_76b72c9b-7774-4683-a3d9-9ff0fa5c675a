<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("invoice_notification_logs", function (Blueprint $table): void {
      $table->id();
      $table->foreignId("company_id")->unique()->constrained();
      $table->string("last_emailed_hash", 64)->nullable();
      $table->string("pending_hash", 64)->nullable();
      $table->timestamp("pending_marked_at")->nullable();
      $table->timestamp("last_emailed_at")->nullable();
      $table->timestamps();

      $table->index(["pending_hash", "pending_marked_at"]);
      $table->index("company_id");
    });

    DB::statement(
      'ALTER TABLE invoice_notification_logs ADD CONSTRAINT chk_last_emailed_hash_sha256 CHECK (last_emailed_hash IS NULL OR last_emailed_hash REGEXP \'^[a-f0-9]{64}$\')',
    );
    DB::statement(
      'ALTER TABLE invoice_notification_logs ADD CONSTRAINT chk_pending_hash_sha256 CHECK (pending_hash IS NULL OR pending_hash REGEXP \'^[a-f0-9]{64}$\')',
    );
  }

  public function down(): void
  {
    Schema::dropIfExists("invoice_notification_logs");
  }
};
