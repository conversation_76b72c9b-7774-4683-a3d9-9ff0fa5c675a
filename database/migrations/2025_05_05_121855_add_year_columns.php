<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("result_definitions", function (Blueprint $table) {
      $table->unsignedSmallInteger("year")->nullable();

      $table->index(["company_id", "year"]);
    });

    Schema::table("data_values", function (Blueprint $table) {
      $currentYear = (int) date("Y");
      $table->unsignedSmallInteger("year")->default($currentYear);

      $table->unique(["company_id", "data_definition_id", "year"]);
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $currentYear = (int) date("Y");
      $table->unsignedSmallInteger("year")->default($currentYear);

      $table->unique(
        ["company_id", "emission_factor_definition_id", "year"],
        "company_id_emission_factor_definition_id_year_unique",
      );
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropIndex(["company_id", "emission_factor_definition_id", "year"]);
      $table->dropColumn("year");
    });

    Schema::table("data_values", function (Blueprint $table) {
      $table->dropIndex(["company_id", "data_definition_id", "year"]);
      $table->dropColumn("year");
    });

    Schema::table("result_definitions", function (Blueprint $table) {
      $table->dropIndex(["company_id", "year"]);
      $table->dropColumn("year");
    });

    Schema::table("emission_factor_definitions", function (Blueprint $table) {
      $table->dropIndex(["company_id", "year"]);
      $table->dropColumn("year");
    });

    Schema::table("data_definitions", function (Blueprint $table) {
      $table->dropIndex(["company_id", "year"]);
      $table->dropColumn("year");
    });
  }
};
