<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("audit_logs", function (Blueprint $table) {
      $table->id();
      $table->enum("event", ["created", "updated", "deleted"]);
      $table->morphs("auditable");
      $table->foreignId("user_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->json("properties");
      $table->timestamps();

      $table->index("event");
      $table->index("user_id");
      $table->index("company_id");
    });

    // Add JSON validation constraint
    DB::statement("
            ALTER TABLE audit_logs 
            ADD CONSTRAINT check_properties_json_schema
            CHECK (
                properties IS NULL OR 
                JSON_SCHEMA_VALID(
                    '{
                        \"type\": \"object\",
                        \"properties\": {
                            \"changes\": {
                                \"type\": \"object\",
                                \"patternProperties\": {
                                    \"^[a-zA-Z0-9_]+$\": {
                                        \"type\": \"object\",
                                        \"properties\": {
                                            \"old\": {
                                                \"type\": [\"string\", \"number\", \"boolean\", \"null\"]
                                            },
                                            \"new\": {
                                                \"type\": [\"string\", \"number\", \"boolean\", \"null\"]
                                            }
                                        },
                                        \"required\": [\"old\", \"new\"],
                                        \"additionalProperties\": false
                                    }
                                },
                                \"additionalProperties\": false
                            },
                            \"occurred_at\": {
                                \"type\": [\"string\", \"null\"],
                                \"pattern\": \"^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$\"
                            }
                        },
                        \"required\": [\"changes\"],
                        \"additionalProperties\": false
                    }',
                    properties
                )
            )
        ");
  }

  public function down(): void
  {
    Schema::dropIfExists("audit_logs");
  }
};
