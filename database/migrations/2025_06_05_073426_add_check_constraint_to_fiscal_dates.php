<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Add check constraint for fiscal_start date
    DB::statement('
            ALTER TABLE companies 
            ADD CONSTRAINT chk_fiscal_start_date CHECK (
                fiscal_start_month BETWEEN 1 AND 12 
                AND fiscal_start_day BETWEEN 1 AND 31
                AND (
                    (fiscal_start_month IN (1, 3, 5, 7, 8, 10, 12) AND fiscal_start_day <= 31) OR
                    (fiscal_start_month IN (4, 6, 9, 11) AND fiscal_start_day <= 30) OR
                    (fiscal_start_month = 2 AND fiscal_start_day <= 28)
                )
            )
        ');

    // Add check constraint for fiscal_end date
    DB::statement('
            ALTER TABLE companies 
            ADD CONSTRAINT chk_fiscal_end_date CHECK (
                fiscal_end_month BETWEEN 1 AND 12 
                AND fiscal_end_day BETWEEN 1 AND 31
                AND (
                    (fiscal_end_month IN (1, 3, 5, 7, 8, 10, 12) AND fiscal_end_day <= 31) OR
                    (fiscal_end_month IN (4, 6, 9, 11) AND fiscal_end_day <= 30) OR
                    (fiscal_end_month = 2 AND fiscal_end_day <= 28)
                )
            )
        ');
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    DB::statement("ALTER TABLE companies DROP CONSTRAINT chk_fiscal_start_date");
    DB::statement("ALTER TABLE companies DROP CONSTRAINT chk_fiscal_end_date");
  }
};
