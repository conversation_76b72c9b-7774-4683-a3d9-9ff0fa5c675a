<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table): void {
      $table->unsignedBigInteger("municipality_id")->nullable();

      $table->foreign("municipality_id")->references("id")->on("municipalities");
    });
  }

  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table): void {
      $table->dropForeign(["municipality_id"]);
      $table->dropColumn("municipality_id");
    });
  }
};
