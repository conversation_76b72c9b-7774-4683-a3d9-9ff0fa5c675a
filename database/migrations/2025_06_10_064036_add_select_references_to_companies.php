<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->unsignedBigInteger("revenue_range_id")->nullable();
      $table->unsignedBigInteger("employee_count_range_id")->nullable();

      $table->foreign("revenue_range_id")->references("id")->on("select_options");
      $table->foreign("employee_count_range_id")->references("id")->on("select_options");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->dropForeign(["revenue_range_id"]);
      $table->dropForeign(["employee_count_range_id"]);
      $table->dropColumn(["revenue_range_id", "employee_count_range_id"]);
    });
  }
};
