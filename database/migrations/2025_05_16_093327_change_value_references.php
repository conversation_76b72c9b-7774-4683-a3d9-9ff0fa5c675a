<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // First, drop the existing foreign key constraints
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropForeign(["data_definition_id"]);
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign(["emission_factor_definition_id"]);
    });

    // Then add new foreign key constraints pointing to the calculation_definitions table
    Schema::table("data_values", function (Blueprint $table) {
      $table->foreign("data_definition_id")->references("id")->on("calculation_definitions");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("calculation_definitions");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Restore original foreign key constraints
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropForeign(["data_definition_id"]);
      $table->foreign("data_definition_id")->references("id")->on("data_definitions");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign(["emission_factor_definition_id"]);
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("emission_factor_definitions");
    });
  }
};
