<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("company_year", function (Blueprint $table) {
      $table->boolean("awarded_scope1_2_mark")->default(false);
      $table->boolean("awarded_scope1_3_mark")->default(false);
    });

    Schema::table("company_year", function (Blueprint $table) {
      $table->boolean("awarded_scope1_2_mark")->default(null)->change();
      $table->boolean("awarded_scope1_3_mark")->default(null)->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("company_year", function (Blueprint $table) {
      $table->dropColumn(["awarded_scope1_2_mark", "awarded_scope1_3_mark"]);
    });
  }
};
