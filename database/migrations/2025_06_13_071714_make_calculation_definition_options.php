<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("calculation_definition_options", function (Blueprint $table) {
      $table->id();
      $table->foreignId("calculation_definition_id")->constrained();
      $table->decimal("value", 20, 10);
      $table->integer("sort_order")->default(0);
      $table->timestamps();

      $table->index(["calculation_definition_id", "sort_order"], "calc_def_opts_def_id_sort_idx");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("calculation_definition_options");
  }
};
