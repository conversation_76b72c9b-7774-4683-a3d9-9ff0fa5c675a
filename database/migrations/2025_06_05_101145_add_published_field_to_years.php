<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("years", function (Blueprint $table) {
      $table->timestamp("published")->nullable();
      $table->index("published");
      $table->index(["year", "published"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("years", function (Blueprint $table) {
      $table->dropIndex(["year", "published"]);
      $table->dropIndex(["published"]);
      $table->dropColumn("published");
    });
  }
};
