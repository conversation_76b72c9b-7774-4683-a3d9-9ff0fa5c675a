<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  public function up(): void
  {
    DB::statement(
      "ALTER TABLE audit_logs MODIFY COLUMN event ENUM('created', 'updated', 'deleted', 'soft_deleted', 'force_deleted', 'restored') NOT NULL",
    );
  }

  public function down(): void
  {
    // Map the new events back to old ones
    DB::table("audit_logs")
      ->where("event", "soft_deleted")
      ->update(["event" => "deleted"]);

    DB::table("audit_logs")
      ->where("event", "force_deleted")
      ->update(["event" => "deleted"]);

    DB::table("audit_logs")
      ->where("event", "restored")
      ->update(["event" => "updated"]);

    // Revert to the original enum values
    DB::statement(
      "ALTER TABLE audit_logs MODIFY COLUMN event ENUM('created', 'updated', 'deleted') NOT NULL",
    );
  }
};
