<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("login_tokens", function (Blueprint $table): void {
      $table->id();
      $table->foreignId("user_id")->constrained();
      $table->foreignId("company_id")->nullable()->constrained();
      $table->string("token", 64)->unique();
      $table->timestamp("expires_at");
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("login_tokens");
  }
};
