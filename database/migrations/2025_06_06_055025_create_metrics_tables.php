<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Create metric_definitions table
    Schema::create("metric_definitions", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("unit_id");
      $table->timestamps();
      $table->softDeletes();

      $table->foreign("unit_id")->references("id")->on("units");
    });

    // Create metric_definition_translations table
    Schema::create("metric_definition_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("metric_definition_id");
      $table->string("locale");
      $table->string("name");
      $table->timestamps();

      $table
        ->foreign("metric_definition_id")
        ->references("id")
        ->on("metric_definitions")
        ->onDelete("cascade");
      $table->unique(["metric_definition_id", "locale"], "metric_def_trans_unique");
      $table->index("locale");
      $table->index(["locale", "metric_definition_id"], "idx_locale_metric_def");
    });

    // Create metric_definition_year pivot table
    Schema::create("metric_definition_year", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("metric_definition_id");
      $table->unsignedBigInteger("year_id");
      $table->timestamps();

      $table->foreign("metric_definition_id")->references("id")->on("metric_definitions");
      $table->foreign("year_id")->references("id")->on("years");
      $table->unique(["metric_definition_id", "year_id"], "metric_def_year_unique");
      $table->index("year_id");
    });

    // Create metric_definition_year_translations table
    Schema::create("metric_definition_year_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("metric_definition_year_id");
      $table->string("locale");
      $table->text("help_text");
      $table->timestamps();

      $table
        ->foreign("metric_definition_year_id", "metric_def_year_trans_fk")
        ->references("id")
        ->on("metric_definition_year")
        ->onDelete("cascade");
      $table->unique(["metric_definition_year_id", "locale"], "metric_def_year_trans_unique");
      $table->index("locale");
      $table->index(["locale", "metric_definition_year_id"], "idx_locale_metric_def_year");
    });

    // Create metric_values table
    Schema::create("metric_values", function (Blueprint $table) {
      $table->id();
      $table->decimal("value", 15, 4);
      $table->unsignedBigInteger("metric_definition_id");
      $table->unsignedBigInteger("company_id");
      $table->unsignedBigInteger("year_id");
      $table->text("source");
      $table->timestamps();

      $table->foreign("metric_definition_id")->references("id")->on("metric_definitions");
      $table->foreign("company_id")->references("id")->on("companies");
      $table->foreign("year_id")->references("id")->on("years");
      $table->unique(
        ["company_id", "metric_definition_id", "year_id"],
        "company_metric_year_unique",
      );
      $table->index(["metric_definition_id", "company_id"]);
      $table->index(["year_id", "company_id"], "idx_year_company");
    });

    DB::statement(
      "ALTER TABLE metric_values ADD CONSTRAINT chk_metric_values_positive CHECK (value >= 0)",
    );
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("metric_values");
    Schema::dropIfExists("metric_definition_year_translations");
    Schema::dropIfExists("metric_definition_year");
    Schema::dropIfExists("metric_definition_translations");
    Schema::dropIfExists("metric_definitions");
  }
};
