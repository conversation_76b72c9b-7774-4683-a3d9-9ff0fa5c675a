<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("scope_calculation_variants", function (Blueprint $table): void {
      $table->id();
      $table->foreignId("scope_id")->constrained();
      $table->text("predicate")->nullable();
      $table->integer("display_order");
      $table->timestamps();

      $table->index(["scope_id", "display_order"]);
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("scope_calculation_variants");
  }
};
