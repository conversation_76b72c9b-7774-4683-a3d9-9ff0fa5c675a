<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Use datetime instead of timestamp to avoid timezone conversions
    // datetime stores exactly what you give it without timezone adjustments
    Schema::table("companies", function (Blueprint $table) {
      $table->dateTime("terms_of_service_accepted_at")->nullable();
    });

    // Set to Unix epoch - datetime will store this exactly as-is
    DB::statement(
      "UPDATE companies SET terms_of_service_accepted_at = '1970-01-01 00:00:01' WHERE terms_of_service_accepted_at IS NULL",
    );

    // Now make it non-nullable
    Schema::table("companies", function (Blueprint $table) {
      $table->dateTime("terms_of_service_accepted_at")->nullable(false)->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->dropColumn("terms_of_service_accepted_at");
    });
  }
};
