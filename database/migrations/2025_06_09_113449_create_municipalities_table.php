<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("municipalities", function (Blueprint $table): void {
      $table->id();
      $table->string("code", 3)->unique();
      $table->string("local_id")->unique();
      $table->timestamps();
      $table->softDeletes();

      $table->index("code");
    });

    Schema::create("municipality_translations", function (Blueprint $table): void {
      $table->id();
      $table->unsignedBigInteger("municipality_id");
      $table->string("locale")->index();
      $table->string("name");

      $table->unique(["municipality_id", "locale"], "municipality_trans_unique");

      $table
        ->foreign("municipality_id", "municipality_trans_foreign")
        ->references("id")
        ->on("municipalities")
        ->onDelete("cascade");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("municipality_translations");
    Schema::dropIfExists("municipalities");
  }
};
