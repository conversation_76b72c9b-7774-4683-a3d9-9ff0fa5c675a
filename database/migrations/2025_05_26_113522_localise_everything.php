<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("category_translations", function (Blueprint $table) {
      $table->id();
      $table->foreignId("category_id")->constrained("categories")->onDelete("cascade");
      $table->string("locale")->index();
      $table->string("title");
      $table->text("description");
      $table->unique(["category_id", "locale"]);
      $table->timestamps();
    });

    Schema::create("calculation_definition_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("calculation_definition_id");
      $table->string("locale")->index();
      $table->string("data_name");
      $table->string("emission_factor_name");
      $table->string("result_name");
      $table->unique(["calculation_definition_id", "locale"], "calc_def_trans_unique");
      $table
        ->foreign("calculation_definition_id", "calc_def_trans_calc_def_id_foreign")
        ->references("id")
        ->on("calculation_definitions")
        ->onDelete("cascade");
      $table->timestamps();
    });

    Schema::create("calculation_definition_year_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("calculation_definition_year_id");
      $table->string("locale")->index();
      $table->text("data_help_text");
      $table->text("emission_factor_help_text");
      $table->text("result_help_text");
      $table->unique(["calculation_definition_year_id", "locale"], "calc_def_year_trans_unique");
      $table
        ->foreign("calculation_definition_year_id", "calc_def_year_trans_year_id_foreign")
        ->references("id")
        ->on("calculation_definition_year")
        ->onDelete("cascade");
      $table->timestamps();
    });

    Schema::create("grouping_translations", function (Blueprint $table) {
      $table->id();
      $table->foreignId("grouping_id")->constrained("groupings")->onDelete("cascade");
      $table->string("locale")->index();
      $table->string("title");
      $table->unique(["grouping_id", "locale"]);
      $table->timestamps();
    });

    Schema::create("unit_translations", function (Blueprint $table) {
      $table->id();
      $table->foreignId("unit_id")->constrained("units")->onDelete("cascade");
      $table->string("locale")->index();
      $table->string("name");
      $table->string("symbol");
      $table->unique(["unit_id", "locale"]);
      $table->timestamps();
    });

    Schema::table("categories", function (Blueprint $table) {
      $table->dropColumn(["title", "description"]);
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropColumn(["data_name", "emission_factor_name", "result_name"]);
    });

    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->dropColumn(["data_help_text", "emission_factor_help_text", "result_help_text"]);
    });

    Schema::table("groupings", function (Blueprint $table) {
      $table->dropColumn("title");
    });

    Schema::table("units", function (Blueprint $table) {
      $table->dropColumn(["name", "symbol"]);
    });
  }

  public function down(): void
  {
    Schema::table("categories", function (Blueprint $table) {
      $table->string("title");
      $table->text("description");
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->string("data_name");
      $table->string("emission_factor_name");
      $table->string("result_name");
    });

    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->text("data_help_text");
      $table->text("emission_factor_help_text");
      $table->text("result_help_text");
    });

    Schema::table("groupings", function (Blueprint $table) {
      $table->string("title");
    });

    Schema::table("units", function (Blueprint $table) {
      $table->string("name");
      $table->string("symbol");
    });

    Schema::dropIfExists("category_translations");
    Schema::dropIfExists("calculation_definition_translations");
    Schema::dropIfExists("calculation_definition_year_translations");
    Schema::dropIfExists("grouping_translations");
    Schema::dropIfExists("unit_translations");
  }
};
