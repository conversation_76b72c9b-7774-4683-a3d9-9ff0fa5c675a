<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Create option_sets table
    Schema::create("option_sets", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->timestamps();
    });

    // Add option_set_id to calculation_definitions
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->unsignedBigInteger("option_set_id")->nullable()->index();
      $table->foreign("option_set_id")->references("id")->on("option_sets");
    });

    // Migrate existing options to option_sets
    // Each calculation_definition with options gets its own option_set
    DB::statement("
            INSERT INTO option_sets (name, created_at, updated_at)
            SELECT 
                CONCAT('Options for definition ', cd.id),
                NOW(),
                NOW()
            FROM calculation_definitions cd
            WHERE EXISTS (
                SELECT 1 FROM calculation_definition_options cdo 
                WHERE cdo.calculation_definition_id = cd.id
            )
        ");

    // Update calculation_definitions with their option_set_id
    DB::statement("
            UPDATE calculation_definitions cd
            JOIN (
                SELECT 
                    cd2.id as definition_id,
                    os.id as set_id
                FROM calculation_definitions cd2
                JOIN option_sets os ON os.name = CONCAT('Options for definition ', cd2.id)
            ) mapping ON mapping.definition_id = cd.id
            SET cd.option_set_id = mapping.set_id
        ");

    // Add option_set_id to calculation_definition_options as nullable first
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->unsignedBigInteger("option_set_id")->nullable()->index();
    });

    // Copy option_set_id from calculation_definitions to their options
    DB::statement("
            UPDATE calculation_definition_options cdo
            JOIN calculation_definitions cd ON cd.id = cdo.calculation_definition_id
            SET cdo.option_set_id = cd.option_set_id
            WHERE cd.option_set_id IS NOT NULL
        ");

    // Create option sets for any remaining options without them
    DB::statement("
            INSERT INTO option_sets (name, created_at, updated_at)
            SELECT 
                CONCAT('Options for definition ', cd.id),
                NOW(),
                NOW()
            FROM calculation_definition_options cdo
            JOIN calculation_definitions cd ON cd.id = cdo.calculation_definition_id
            WHERE cdo.option_set_id IS NULL
            GROUP BY cd.id
        ");

    // Update remaining options with their new option_set_id
    DB::statement("
            UPDATE calculation_definition_options cdo
            JOIN calculation_definitions cd ON cd.id = cdo.calculation_definition_id
            JOIN option_sets os ON os.name = CONCAT('Options for definition ', cd.id)
            SET cdo.option_set_id = os.id,
                cd.option_set_id = os.id
            WHERE cdo.option_set_id IS NULL
        ");

    // Now make it non-nullable and add the foreign key
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->unsignedBigInteger("option_set_id")->nullable(false)->change();
      $table->foreign("option_set_id")->references("id")->on("option_sets");
    });

    // Drop the compound foreign key constraint from emission_factor_values first
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign("emission_factor_values_option_definition_compound_fk");
    });

    // Add unique compound indexes for the foreign key references
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->unique(["id", "option_set_id"], "idx_definition_optionset");
    });

    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->unique(["id", "option_set_id"], "idx_option_optionset");
    });

    // Add option_set_id to emission_factor_values for constraint enforcement
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->unsignedBigInteger("option_set_id")->nullable()->index();
    });

    // Populate option_set_id in emission_factor_values from the definition
    DB::statement("
            UPDATE emission_factor_values efv
            JOIN calculation_definitions cd ON cd.id = efv.emission_factor_definition_id
            SET efv.option_set_id = cd.option_set_id
            WHERE efv.calculation_definition_option_id IS NOT NULL
        ");

    // Add compound foreign keys to enforce consistency
    Schema::table("emission_factor_values", function (Blueprint $table) {
      // This ensures the definition has this option_set
      $table
        ->foreign(["emission_factor_definition_id", "option_set_id"], "efv_definition_optionset_fk")
        ->references(["id", "option_set_id"])
        ->on("calculation_definitions");

      // This ensures the option belongs to this option_set
      $table
        ->foreign(["calculation_definition_option_id", "option_set_id"], "efv_option_optionset_fk")
        ->references(["id", "option_set_id"])
        ->on("calculation_definition_options");
    });

    // Now we can remove the calculation_definition_id foreign key and column
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->dropForeign(["calculation_definition_id"]);
      $table->dropColumn("calculation_definition_id");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Re-add calculation_definition_id
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->unsignedBigInteger("calculation_definition_id")->nullable()->index();
      $table->foreign("calculation_definition_id")->references("id")->on("calculation_definitions");
    });

    // Restore the relationship
    DB::statement("
            UPDATE calculation_definition_options cdo
            JOIN calculation_definitions cd ON cd.option_set_id = cdo.option_set_id
            SET cdo.calculation_definition_id = cd.id
        ");

    // Remove the new compound foreign keys
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign("efv_definition_optionset_fk");
      $table->dropForeign("efv_option_optionset_fk");
      $table->dropColumn("option_set_id");
    });

    // Remove unique compound indexes
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropUnique("idx_definition_optionset");
    });

    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->dropUnique("idx_option_optionset");
    });

    // Recreate the original compound foreign key
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table
        ->foreign(
          ["calculation_definition_option_id", "emission_factor_definition_id"],
          "emission_factor_values_option_definition_compound_fk",
        )
        ->references(["id", "calculation_definition_id"])
        ->on("calculation_definition_options");
    });

    // Remove option_set_id from tables
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->dropForeign(["option_set_id"]);
      $table->dropColumn("option_set_id");
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropForeign(["option_set_id"]);
      $table->dropColumn("option_set_id");
    });

    // Drop option_sets table
    Schema::dropIfExists("option_sets");
  }
};
