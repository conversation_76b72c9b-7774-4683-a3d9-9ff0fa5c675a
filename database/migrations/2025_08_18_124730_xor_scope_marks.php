<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->renameColumn("applying_for_scope3_mark", "applying_for_scope1_3_mark");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->renameColumn("applying_for_scope1_3_mark", "applying_for_scope3_mark");
    });
  }
};
