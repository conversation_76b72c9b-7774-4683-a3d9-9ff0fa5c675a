<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("scope_calculation_variant_translations", function (Blueprint $table): void {
      $table->id();
      $table->foreignId("scope_calculation_variant_id");
      $table->string("locale")->index();
      $table->string("label");
      $table->timestamps();

      // Add foreign key with custom name to avoid length limit
      $table
        ->foreign("scope_calculation_variant_id", "scv_trans_variant_fk")
        ->references("id")
        ->on("scope_calculation_variants")
        ->cascadeOnDelete();

      $table->unique(["scope_calculation_variant_id", "locale"], "scv_trans_variant_locale_unq");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("scope_calculation_variant_translations");
  }
};
