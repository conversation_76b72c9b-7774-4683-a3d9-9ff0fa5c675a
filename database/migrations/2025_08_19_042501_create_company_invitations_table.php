<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("company_invitations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("company_id");
      $table->unsignedBigInteger("inviter_user_id");
      $table->string("invitee_email");
      $table->uuid("selector")->unique();
      $table->string("token", 64);
      $table->enum("status", ["pending", "accepted", "declined"]);
      $table->timestamp("decided_at")->nullable();
      $table->timestamp("expires_at");
      $table->timestamps();

      $table->index(["company_id", "invitee_email"]);
      $table->index("selector");
      $table->index(["status", "expires_at"]);

      $table->foreign("company_id")->references("id")->on("companies");
      $table->foreign("inviter_user_id")->references("id")->on("users");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("company_invitations");
  }
};
