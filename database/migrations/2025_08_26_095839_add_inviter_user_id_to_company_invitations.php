<?php

use App\Helpers\Assert;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("company_invitations", function (Blueprint $table) {
      $table->bigInteger("invitee_user_id")->unsigned()->nullable();
      $table->index("invitee_user_id");
    });

    // Get the basic role ID once
    $basicRoleId = DB::table("roles")->where("name", "basic")->value("id");
    Assert::notNull($basicRoleId);

    // Process ALL invitations without user IDs (not just accepted ones)
    // Since the new system creates users when invitations are sent, we need to
    // create users for all pending invitations as well
    $invitations = DB::table("company_invitations")->whereNull("invitee_user_id")->get();

    foreach ($invitations as $invitation) {
      $existingUserId = DB::table("users")->where("email", $invitation->invitee_email)->value("id");

      if ($existingUserId === null) {
        // Create user for invitation
        $userId = DB::table("users")->insertGetId([
          "name" => $invitation->invitee_email,
          "email" => $invitation->invitee_email,
          "password" => Hash::make(Str::random(64)),
          "created_at" => now(),
          "updated_at" => now(),
        ]);

        DB::table("model_has_roles")->insert([
          "role_id" => $basicRoleId,
          "model_type" => "App\\Models\\User",
          "model_id" => $userId,
        ]);
      } else {
        $userId = $existingUserId;
      }

      // Update the invitation with the user ID
      DB::table("company_invitations")
        ->where("id", $invitation->id)
        ->update(["invitee_user_id" => $userId]);

      // Note: We do NOT add users to companies here for accepted invitations
      // If they were previously added and then removed, we should not re-add them
      // The accepted status indicates the invitation was processed, not that
      // the user should currently be in the company
    }

    Schema::table("company_invitations", function (Blueprint $table) {
      $table->foreign("invitee_user_id")->references("id")->on("users");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("company_invitations", function (Blueprint $table) {
      $table->dropForeign(["invitee_user_id"]);
      $table->dropIndex(["invitee_user_id"]);
      $table->dropColumn("invitee_user_id");
    });
  }
};
