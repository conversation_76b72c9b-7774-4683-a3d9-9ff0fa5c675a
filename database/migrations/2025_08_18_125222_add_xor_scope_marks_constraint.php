<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // First, fix any existing data that violates the XOR constraint
    // If both are true, keep only scope1_2_mark (you can change this logic if needed)
    DB::table("companies")
      ->where("applying_for_scope1_2_mark", true)
      ->where("applying_for_scope1_3_mark", true)
      ->update(["applying_for_scope1_3_mark" => false]);

    // Now add the check constraint to ensure XOR (exclusive OR) - only one can be true
    DB::statement('
            ALTER TABLE companies 
            ADD CONSTRAINT chk_scope_marks_xor 
            CHECK (
                NOT (applying_for_scope1_2_mark = 1 AND applying_for_scope1_3_mark = 1)
            )
        ');
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Drop the check constraint
    DB::statement("ALTER TABLE companies DROP CONSTRAINT chk_scope_marks_xor");
  }
};
