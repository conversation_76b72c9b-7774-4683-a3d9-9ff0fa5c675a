<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("result_definitions", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->foreignId("data_definition_id")->constrained();
      $table->foreignId("emission_factor_definition_id")->constrained();
      $table->foreignId("result_unit_id")->nullable()->constrained("units");
      $table->foreignId("company_id")->nullable()->constrained();
      $table->timestamps();
      $table->softDeletes();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("result_definitions");
  }
};
