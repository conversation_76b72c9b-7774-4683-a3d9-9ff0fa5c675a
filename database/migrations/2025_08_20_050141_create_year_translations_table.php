<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("year_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("year_id");
      $table->string("locale", 10);
      $table->string("scope1_2_mark_image", 255)->nullable();
      $table->string("scope1_3_mark_image", 255)->nullable();
      $table->string("scope1_2_criteria_procedures_pdf", 255)->nullable();
      $table->string("scope1_3_criteria_procedures_pdf", 255)->nullable();
      $table->timestamps();

      $table->index("year_id");
      $table->index("locale");
      $table->unique(["year_id", "locale"], "year_translations_year_id_locale_unique");

      $table->foreign("year_id")->references("id")->on("years")->onDelete("cascade");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("year_translations");
  }
};
