<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("groupings", function (Blueprint $table) {
      $table->id();
      $table->string("title");
      $table->timestamps();
      $table->softDeletes();
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->foreignId("grouping_id")->constrained();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropForeign(["grouping_id"]);
      $table->dropColumn("grouping_id");
    });

    Schema::dropIfExists("groupings");
  }
};
