<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("company_year", function (Blueprint $table) {
      $table->id();
      $table->foreignId("company_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->boolean("participates_in_climate_program")->default(false);
      $table->boolean("participates_in_climate_community")->default(false);
      $table->timestamps();

      // Ensure a company can only have one entry per year
      $table->unique(["company_id", "year_id"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("company_year");
  }
};
