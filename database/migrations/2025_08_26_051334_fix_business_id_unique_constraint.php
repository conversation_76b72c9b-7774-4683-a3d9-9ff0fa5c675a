<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  public function up(): void
  {
    // Drop the existing unique constraint using the correct index name
    DB::statement("ALTER TABLE companies DROP INDEX idx_business_id");

    // Add a generated column that becomes NULL when a record is soft deleted
    DB::statement('
            ALTER TABLE companies 
            ADD COLUMN active_business_id VARCHAR(9) GENERATED ALWAYS AS 
            (IF(deleted_at IS NULL, business_id, NULL)) STORED
        ');

    // Add unique constraint on the generated column
    DB::statement('
            ALTER TABLE companies 
            ADD UNIQUE INDEX idx_business_id (active_business_id)
        ');
  }

  public function down(): void
  {
    // Remove the unique constraint on active_business_id
    DB::statement("ALTER TABLE companies DROP INDEX idx_business_id");

    // Remove the generated column
    DB::statement("ALTER TABLE companies DROP COLUMN active_business_id");

    // Restore the original unique constraint with the same name
    DB::statement('
            ALTER TABLE companies 
            ADD UNIQUE INDEX idx_business_id (business_id)
        ');
  }
};
