<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("result_definitions", function (Blueprint $table) {
      $table->foreignId("category_id")->constrained();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("result_definitions", function (Blueprint $table) {
      $table->dropForeign(["category_id"]);
      $table->dropColumn("category_id");
    });
  }
};
