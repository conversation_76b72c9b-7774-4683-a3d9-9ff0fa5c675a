<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Add soft deletes to data_values
    Schema::table("data_values", function (Blueprint $table) {
      $table->softDeletes();
    });

    // Add soft deletes to emission_factor_values
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->softDeletes();
    });

    // Temporarily drop foreign keys that are part of the unique constraints
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropForeign("data_values_company_id_foreign");
      $table->dropForeign("data_values_data_definition_id_foreign");
      $table->dropForeign("data_values_year_id_foreign");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign("emission_factor_values_company_id_foreign");
      $table->dropForeign("emission_factor_values_emission_factor_definition_id_foreign");
      $table->dropForeign("emission_factor_values_year_id_foreign");
    });

    // Now drop the unique constraints
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropUnique("data_values_company_id_data_definition_id_year_id_unique");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropUnique("company_id_emission_factor_definition_id_year_id_unique");
    });

    // Re-add the foreign keys
    Schema::table("data_values", function (Blueprint $table) {
      $table->foreign("company_id")->references("id")->on("companies");
      $table->foreign("data_definition_id")->references("id")->on("calculation_definitions");
      $table->foreign("year_id")->references("id")->on("years");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->foreign("company_id")->references("id")->on("companies");
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("calculation_definitions");
      $table->foreign("year_id")->references("id")->on("years");
    });

    // Create generated columns that mirror the key fields only when record is active
    DB::statement('ALTER TABLE data_values 
        ADD COLUMN active_company_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, company_id, NULL)) STORED,
        ADD COLUMN active_data_definition_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, data_definition_id, NULL)) STORED,
        ADD COLUMN active_year_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, year_id, NULL)) STORED');

    DB::statement('ALTER TABLE emission_factor_values 
        ADD COLUMN active_company_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, company_id, NULL)) STORED,
        ADD COLUMN active_emission_factor_definition_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, emission_factor_definition_id, NULL)) STORED,
        ADD COLUMN active_year_id BIGINT UNSIGNED AS (IF(deleted_at IS NULL, year_id, NULL)) STORED');

    // Create unique indexes on the generated columns
    DB::statement('CREATE UNIQUE INDEX unique_data_values_active 
        ON data_values(active_company_id, active_data_definition_id, active_year_id)');

    DB::statement('CREATE UNIQUE INDEX unique_emission_factor_values_active 
        ON emission_factor_values(active_company_id, active_emission_factor_definition_id, active_year_id)');
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Drop unique indexes on generated columns
    DB::statement("DROP INDEX unique_data_values_active ON data_values");
    DB::statement("DROP INDEX unique_emission_factor_values_active ON emission_factor_values");

    // Drop the generated columns
    DB::statement('ALTER TABLE data_values 
        DROP COLUMN active_company_id,
        DROP COLUMN active_data_definition_id,
        DROP COLUMN active_year_id');

    DB::statement('ALTER TABLE emission_factor_values 
        DROP COLUMN active_company_id,
        DROP COLUMN active_emission_factor_definition_id,
        DROP COLUMN active_year_id');

    // Temporarily drop foreign keys
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropForeign("data_values_company_id_foreign");
      $table->dropForeign("data_values_data_definition_id_foreign");
      $table->dropForeign("data_values_year_id_foreign");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign("emission_factor_values_company_id_foreign");
      $table->dropForeign("emission_factor_values_emission_factor_definition_id_foreign");
      $table->dropForeign("emission_factor_values_year_id_foreign");
    });

    // Restore original unique constraints
    Schema::table("data_values", function (Blueprint $table) {
      $table->unique(
        ["company_id", "data_definition_id", "year_id"],
        "data_values_company_id_data_definition_id_year_id_unique",
      );
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->unique(
        ["company_id", "emission_factor_definition_id", "year_id"],
        "company_id_emission_factor_definition_id_year_id_unique",
      );
    });

    // Re-add the foreign keys
    Schema::table("data_values", function (Blueprint $table) {
      $table->foreign("company_id")->references("id")->on("companies");
      $table->foreign("data_definition_id")->references("id")->on("calculation_definitions");
      $table->foreign("year_id")->references("id")->on("years");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->foreign("company_id")->references("id")->on("companies");
      $table
        ->foreign("emission_factor_definition_id")
        ->references("id")
        ->on("calculation_definitions");
      $table->foreign("year_id")->references("id")->on("years");
    });

    // Remove soft deletes
    Schema::table("data_values", function (Blueprint $table) {
      $table->dropSoftDeletes();
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropSoftDeletes();
    });
  }
};
