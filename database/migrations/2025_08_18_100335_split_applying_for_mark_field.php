<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      // Add new fields
      $table->boolean("applying_for_scope1_2_mark")->default(false);
      $table->boolean("applying_for_scope3_mark")->default(false);
      $table->string("e_invoice_contact_name")->nullable();
      $table->string("e_invoice_contact_email")->nullable();

      // Drop the old field
      $table->dropColumn("applying_for_mark");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      // Re-add old field
      $table->boolean("applying_for_mark")->default(false);

      // Drop the new fields
      $table->dropColumn([
        "applying_for_scope1_2_mark",
        "applying_for_scope3_mark",
        "e_invoice_contact_name",
        "e_invoice_contact_email",
      ]);
    });
  }
};
