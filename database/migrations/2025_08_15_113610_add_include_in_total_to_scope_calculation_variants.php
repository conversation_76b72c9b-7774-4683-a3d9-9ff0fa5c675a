<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // First add the column with a default value for existing records
    Schema::table("scope_calculation_variants", function (Blueprint $table) {
      $table->boolean("include_in_total")->default(true);
      $table->index("include_in_total");
    });

    // Then remove the default constraint for future records
    Schema::table("scope_calculation_variants", function (Blueprint $table) {
      $table->boolean("include_in_total")->default(null)->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("scope_calculation_variants", function (Blueprint $table) {
      $table->dropIndex(["include_in_total"]);
      $table->dropColumn("include_in_total");
    });
  }
};
