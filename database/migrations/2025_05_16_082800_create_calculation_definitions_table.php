<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("calculation_definitions", function (Blueprint $table) {
      $table->id();

      // Common fields across models
      $table->foreignId("company_id")->nullable()->constrained();
      $table->foreignId("scope_id")->constrained();
      $table->foreignId("category_id")->constrained();

      // Fields from DataDefinition - prefixed with data_
      $table->string("data_name");
      $table->foreignId("data_unit_id")->references("id")->on("units");

      // Fields from EmissionFactorDefinition - prefixed with emission_factor_
      $table->string("emission_factor_name");
      $table->foreignId("emission_factor_compound_unit_id")->references("id")->on("compound_units");
      $table->decimal("emission_factor_default_value", 20, 10)->nullable();

      // Fields from ResultDefinition - prefixed with result_
      $table->string("result_name");
      $table->foreignId("result_unit_id")->references("id")->on("units");

      $table->timestamps();
      $table->softDeletes();
    });

    // Create the year pivot relationship
    Schema::create("calculation_definition_year", function (Blueprint $table) {
      $table->id();
      $table->foreignId("calculation_definition_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("calculation_definition_year");
    Schema::dropIfExists("calculation_definitions");
  }
};
