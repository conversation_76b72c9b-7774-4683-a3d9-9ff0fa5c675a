<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("compound_units", function (Blueprint $table) {
      $table->id();
      $table->foreignId("numerator_unit_id")->constrained("units");
      $table->foreignId("denominator_unit_id")->constrained("units");
      $table->timestamps();
      $table->softDeletes();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("compound_units");
  }
};
