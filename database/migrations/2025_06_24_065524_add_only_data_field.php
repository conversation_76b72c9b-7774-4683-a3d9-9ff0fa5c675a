<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Add column with temporary default (false) to populate existing rows
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("only_data")->default(false);
      $table->index("only_data");
    });

    // Remove the default value
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("only_data")->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex(["only_data"]);
      $table->dropColumn("only_data");
    });
  }
};
