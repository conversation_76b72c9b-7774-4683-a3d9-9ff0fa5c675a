<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   *
   * @return void
   */
  public function up()
  {
    Schema::table("emission_factor_definitions", function (Blueprint $table) {
      $table->foreignId("scope_id")->constrained();
    });
  }

  /**
   * Reverse the migrations.
   *
   * @return void
   */
  public function down()
  {
    Schema::table("emission_factor_definitions", function (Blueprint $table) {
      $table->dropForeign(["scope_id"]);
      $table->dropColumn("scope_id");
    });
  }
};
