<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Drop the existing XOR constraint
    DB::statement('
            ALTER TABLE emission_factor_values 
            DROP CONSTRAINT emission_factor_values_value_xor_option_check
        ');

    // Add new constraint that prevents both from being set, but allows both to be NULL
    DB::statement('
            ALTER TABLE emission_factor_values 
            ADD CONSTRAINT emission_factor_values_value_xor_option_check 
            CHECK (
                value IS NULL OR calculation_definition_option_id IS NULL
            )
        ');
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Drop the new constraint
    DB::statement('
            ALTER TABLE emission_factor_values 
            DROP CONSTRAINT emission_factor_values_value_xor_option_check
        ');

    // Restore the original XOR constraint
    DB::statement('
            ALTER TABLE emission_factor_values 
            ADD CONSTRAINT emission_factor_values_value_xor_option_check 
            CHECK (
                (value IS NOT NULL AND calculation_definition_option_id IS NULL) OR 
                (value IS NULL AND calculation_definition_option_id IS NOT NULL)
            )
        ');
  }
};
