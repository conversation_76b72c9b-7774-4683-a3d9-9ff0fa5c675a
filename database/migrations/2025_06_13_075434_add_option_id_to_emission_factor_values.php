<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    // First, add the unique constraint to calculation_definition_options
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->unique(["id", "calculation_definition_id"], "calc_def_opts_id_def_id_unique");
    });

    // Then, modify emission_factor_values
    Schema::table("emission_factor_values", function (Blueprint $table) {
      // Make value nullable with correct decimal size
      $table->decimal("value", 15, 4)->nullable()->change();

      // Add the new option_id column
      $table->unsignedBigInteger("calculation_definition_option_id")->nullable();

      // Single column foreign key
      $table
        ->foreign("calculation_definition_option_id", "emission_factor_values_option_id_fk")
        ->references("id")
        ->on("calculation_definition_options");

      // Compound foreign key to ensure option belongs to the correct definition
      $table
        ->foreign(
          ["calculation_definition_option_id", "emission_factor_definition_id"],
          "emission_factor_values_option_definition_compound_fk",
        )
        ->references(["id", "calculation_definition_id"])
        ->on("calculation_definition_options");

      $table->index("calculation_definition_option_id", "emission_factor_values_option_id_idx");
    });

    // Add check constraint to ensure either value or option_id is set, but not both
    DB::statement('
      ALTER TABLE emission_factor_values 
      ADD CONSTRAINT emission_factor_values_value_xor_option_check 
      CHECK (
        (value IS NOT NULL AND calculation_definition_option_id IS NULL) OR 
        (value IS NULL AND calculation_definition_option_id IS NOT NULL)
      )
    ');
  }

  public function down(): void
  {
    // Drop the check constraint first
    DB::statement('
      ALTER TABLE emission_factor_values 
      DROP CONSTRAINT emission_factor_values_value_xor_option_check
    ');

    // Remove changes from emission_factor_values
    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropForeign("emission_factor_values_option_definition_compound_fk");
      $table->dropForeign("emission_factor_values_option_id_fk");
      $table->dropIndex("emission_factor_values_option_id_idx");
      $table->dropColumn("calculation_definition_option_id");

      // Make value non-nullable again
      $table->decimal("value", 15, 4)->nullable(false)->change();
    });

    // Remove the unique constraint from calculation_definition_options
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->dropUnique("calc_def_opts_id_def_id_unique");
    });
  }
};
