<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("categories", function (Blueprint $table) {
      $table->boolean("hide_total")->nullable();
      $table->index("hide_total");
    });

    DB::table("categories")->update([
      "hide_total" => false,
    ]);

    Schema::table("categories", function (Blueprint $table) {
      $table->boolean("hide_total")->nullable(false)->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("categories", function (Blueprint $table) {
      $table->dropIndex(["hide_total"]);
      $table->dropColumn("hide_total");
    });
  }
};
