<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Create pivot table for category template definitions (using correct Laravel naming)
    Schema::create("calculation_definition_category", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("category_id");
      $table->unsignedBigInteger("calculation_definition_id");
      $table->integer("sort_order")->default(0);
      $table->timestamps();

      $table
        ->foreign("category_id", "calc_def_cat_category_fk")
        ->references("id")
        ->on("categories");
      $table
        ->foreign("calculation_definition_id", "calc_def_cat_definition_fk")
        ->references("id")
        ->on("calculation_definitions");

      $table->unique(["category_id", "calculation_definition_id"], "calc_def_category_unique");
      $table->index(["category_id", "sort_order"]);
    });

    // Add tag and link_id columns to calculation_definitions table
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->string("tag")->nullable()->index();
      $table->uuid("link_id")->nullable()->index();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("calculation_definition_category");

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex(["tag"]);
      $table->dropIndex(["link_id"]);
      $table->dropColumn(["tag", "link_id"]);
    });
  }
};
