<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Drop the check constraint
    DB::statement("ALTER TABLE calculation_definitions DROP CHECK chk_company_visibility");
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Re-add the constraint if rolling back
    DB::statement('
            ALTER TABLE calculation_definitions 
            ADD CONSTRAINT chk_company_visibility 
            CHECK (
                company_id IS NULL 
                OR (
                    hide_from_data_page = false 
                    AND hide_from_emission_factor_page = false 
                    AND hide_from_results_page = false
                )
            )
        ');
  }
};
