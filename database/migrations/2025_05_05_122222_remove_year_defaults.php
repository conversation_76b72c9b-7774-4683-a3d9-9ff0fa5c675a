<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("data_values", function (Blueprint $table) {
      $table->unsignedSmallInteger("year")->after("company_id")->change();
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->unsignedSmallInteger("year")->after("company_id")->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("data_values", function (Blueprint $table) {
      $currentYear = (int) date("Y");
      $table->unsignedSmallInteger("year")->default($currentYear)->after("company_id")->change();
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $currentYear = (int) date("Y");
      $table->unsignedSmallInteger("year")->default($currentYear)->after("company_id")->change();
    });
  }
};
