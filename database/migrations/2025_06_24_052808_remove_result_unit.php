<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropForeign(["result_unit_id"]);
      $table->dropColumn("result_unit_id");
    });
  }

  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->foreignId("result_unit_id")->nullable()->constrained("units");
    });
  }
};
