<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

final class DropDefinitionTables extends Migration
{
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::dropIfExists("result_definitions");
    Schema::dropIfExists("data_definition_year");
    Schema::dropIfExists("emission_factor_definition_year");

    Schema::dropIfExists("data_definitions");
    Schema::dropIfExists("emission_factor_definitions");
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::create("data_definitions", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->foreignId("unit_id")->constrained();
      $table->foreignId("company_id")->nullable()->constrained();
      $table->timestamps();
      $table->softDeletes();
      $table->foreignId("scope_id")->constrained();
      $table->foreignId("category_id")->constrained();
    });

    Schema::create("emission_factor_definitions", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->decimal("default_value", 15, 4)->nullable();
      $table->foreignId("compound_unit_id")->constrained();
      $table->foreignId("company_id")->nullable()->constrained();
      $table->timestamps();
      $table->softDeletes();
      $table->foreignId("scope_id")->constrained();
      $table->foreignId("category_id")->constrained();
    });

    Schema::create("data_definition_year", function (Blueprint $table) {
      $table->id();
      $table->foreignId("data_definition_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->timestamps();
    });

    Schema::create("emission_factor_definition_year", function (Blueprint $table) {
      $table->id();
      $table->foreignId("emission_factor_definition_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->timestamps();
    });

    Schema::create("result_definitions", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->foreignId("data_definition_id")->constrained();
      $table->foreignId("emission_factor_definition_id")->constrained();
      $table->foreignId("result_unit_id")->nullable()->constrained("units");
      $table->foreignId("company_id")->nullable()->constrained();
      $table->timestamps();
      $table->softDeletes();
      $table->unsignedSmallInteger("year")->nullable();
      $table->foreignId("scope_id")->constrained();
      $table->foreignId("category_id")->constrained();
    });
  }
}
