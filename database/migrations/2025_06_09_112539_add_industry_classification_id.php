<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table): void {
      $table->unsignedBigInteger("industry_classification_id")->nullable();

      $table
        ->foreign("industry_classification_id")
        ->references("id")
        ->on("industry_classifications");
    });
  }

  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table): void {
      $table->dropForeign(["industry_classification_id"]);
      $table->dropColumn("industry_classification_id");
    });
  }
};
