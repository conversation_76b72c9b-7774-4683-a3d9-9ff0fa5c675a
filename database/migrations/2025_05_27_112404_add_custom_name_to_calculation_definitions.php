<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->string("custom_name")->nullable();
    });

    DB::statement(
      'ALTER TABLE calculation_definitions ADD CONSTRAINT chk_custom_name_company CHECK ((company_id IS NULL AND custom_name IS NULL) OR 
       (company_id IS NOT NULL AND custom_name IS NOT NULL))',
    );
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    DB::statement("ALTER TABLE calculation_definitions DROP CONSTRAINT chk_custom_name_company");

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropColumn("custom_name");
    });
  }
};
