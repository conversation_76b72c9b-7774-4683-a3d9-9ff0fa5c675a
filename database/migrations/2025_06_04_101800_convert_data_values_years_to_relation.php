<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("data_values", function (Blueprint $table) {
      // Drop foreign keys first
      $table->dropForeign(["data_definition_id"]);
      $table->dropForeign(["company_id"]);

      // Now we can drop the unique index
      $table->dropUnique(["company_id", "data_definition_id", "year"]);

      // Drop the year column
      $table->dropColumn("year");

      // Add year_id column
      $table->foreignId("year_id")->after("company_id")->constrained("years");

      // Re-add the foreign keys
      $table->foreign("data_definition_id")->references("id")->on("calculation_definitions");
      $table->foreign("company_id")->references("id")->on("companies");

      // Add new unique constraint
      $table->unique(["company_id", "data_definition_id", "year_id"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("data_values", function (Blueprint $table) {
      // Drop foreign keys
      $table->dropForeign(["data_definition_id"]);
      $table->dropForeign(["company_id"]);
      $table->dropForeign(["year_id"]);

      // Drop unique constraint
      $table->dropUnique(["company_id", "data_definition_id", "year_id"]);

      // Drop year_id
      $table->dropColumn("year_id");

      // Add back year column
      $table->unsignedSmallInteger("year")->default((int) date("Y"))->after("company_id");

      // Re-add foreign keys
      $table->foreign("data_definition_id")->references("id")->on("calculation_definitions");
      $table->foreign("company_id")->references("id")->on("companies");

      // Add back unique constraint
      $table->unique(["company_id", "data_definition_id", "year"]);
    });
  }
};
