<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("data_definition_year", function (Blueprint $table) {
      $table->id();
      $table->foreignId("data_definition_id")->constrained();
      $table->foreignId("year_id")->constrained();
      $table->timestamps();

      // Each definition can only be associated with a specific year once
      $table->unique(["data_definition_id", "year_id"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("data_definition_year");
  }
};
