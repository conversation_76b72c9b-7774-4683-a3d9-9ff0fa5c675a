<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // 1. Make business_id unique and indexed
    Schema::table("companies", function (Blueprint $table) {
      $table->unique("business_id", "idx_business_id");
    });

    // 2. Prevent duplicate unit conversions
    Schema::table("unit_conversions", function (Blueprint $table) {
      $table->unique(["from_unit_id", "to_unit_id"], "idx_unique_conversion");
    });

    // 3. Prevent duplicate compound units
    Schema::table("compound_units", function (Blueprint $table) {
      $table->unique(["numerator_unit_id", "denominator_unit_id"], "idx_unique_compound");
    });

    // 4. Add value validation constraints
    DB::statement(
      "ALTER TABLE data_values ADD CONSTRAINT chk_data_values_positive CHECK (value >= 0)",
    );
    DB::statement(
      "ALTER TABLE emission_factor_values ADD CONSTRAINT chk_emission_factor_values_positive CHECK (value >= 0)",
    );
    DB::statement(
      "ALTER TABLE unit_conversions ADD CONSTRAINT chk_unit_conversions_positive CHECK (conversion_factor > 0)",
    );

    // 5. Add emission factor validation
    DB::statement(
      "ALTER TABLE calculation_definition_year ADD CONSTRAINT chk_calc_def_year_emission_positive CHECK (emission_factor_default_value IS NULL OR emission_factor_default_value >= 0)",
    );

    // 6. Add indexes for year-based reporting queries
    Schema::table("data_values", function (Blueprint $table) {
      $table->index(["year_id", "company_id"], "idx_year_company");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->index(["year_id", "company_id"], "idx_year_company");
    });

    // 7. Add indexes for translation lookups
    Schema::table("calculation_definition_translations", function (Blueprint $table) {
      $table->index(["locale", "calculation_definition_id"], "idx_locale_calc_def");
    });

    Schema::table("category_translations", function (Blueprint $table) {
      $table->index(["locale", "category_id"], "idx_locale_category");
    });

    Schema::table("grouping_translations", function (Blueprint $table) {
      $table->index(["locale", "grouping_id"], "idx_locale_grouping");
    });

    Schema::table("unit_translations", function (Blueprint $table) {
      $table->index(["locale", "unit_id"], "idx_locale_unit");
    });

    Schema::table("calculation_definition_year_translations", function (Blueprint $table) {
      $table->index(["locale", "calculation_definition_year_id"], "idx_locale_calc_def_year");
    });

    // 8. Add reasonable year range constraint
    DB::statement(
      "ALTER TABLE years ADD CONSTRAINT chk_year_range CHECK (year BETWEEN 1900 AND 2200)",
    );

    // 9. Add index for company-scope queries (common filter pattern)
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->index(["company_id", "scope_id"], "idx_company_scope");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Remove constraints
    DB::statement("ALTER TABLE data_values DROP CONSTRAINT chk_data_values_positive");
    DB::statement(
      "ALTER TABLE emission_factor_values DROP CONSTRAINT chk_emission_factor_values_positive",
    );
    DB::statement("ALTER TABLE unit_conversions DROP CONSTRAINT chk_unit_conversions_positive");
    DB::statement(
      "ALTER TABLE calculation_definition_year DROP CONSTRAINT chk_calc_def_year_emission_positive",
    );
    DB::statement("ALTER TABLE years DROP CONSTRAINT chk_year_range");

    // Remove indexes
    Schema::table("companies", function (Blueprint $table) {
      $table->dropUnique("idx_business_id");
    });

    Schema::table("unit_conversions", function (Blueprint $table) {
      $table->dropUnique("idx_unique_conversion");
    });

    Schema::table("compound_units", function (Blueprint $table) {
      $table->dropUnique("idx_unique_compound");
    });

    Schema::table("data_values", function (Blueprint $table) {
      $table->dropIndex("idx_year_company");
    });

    Schema::table("emission_factor_values", function (Blueprint $table) {
      $table->dropIndex("idx_year_company");
    });

    Schema::table("calculation_definition_translations", function (Blueprint $table) {
      $table->dropIndex("idx_locale_calc_def");
    });

    Schema::table("category_translations", function (Blueprint $table) {
      $table->dropIndex("idx_locale_category");
    });

    Schema::table("grouping_translations", function (Blueprint $table) {
      $table->dropIndex("idx_locale_grouping");
    });

    Schema::table("unit_translations", function (Blueprint $table) {
      $table->dropIndex("idx_locale_unit");
    });

    Schema::table("calculation_definition_year_translations", function (Blueprint $table) {
      $table->dropIndex("idx_locale_calc_def_year");
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex("idx_company_scope");
    });
  }
};
