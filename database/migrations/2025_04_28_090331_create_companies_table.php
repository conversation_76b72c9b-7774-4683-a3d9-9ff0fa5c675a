<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("companies", function (Blueprint $table) {
      $table->id();
      $table->string("name");
      $table->string("business_id", 9);
      $table->unsignedTinyInteger("fiscal_start_month");
      $table->unsignedTinyInteger("fiscal_start_day");
      $table->unsignedTinyInteger("fiscal_end_month");
      $table->unsignedTinyInteger("fiscal_end_day");
      $table->boolean("consent_for_data_examination");
      $table->boolean("applying_for_mark");
      $table->timestamps();
      $table->softDeletes();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("companies");
  }
};
