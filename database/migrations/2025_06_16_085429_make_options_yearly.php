<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    // First, remove the value column from calculation_definition_options
    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->dropColumn("value");
    });

    // Create a pivot table for year-specific option values
    Schema::create("calculation_definition_option_year_values", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("calculation_definition_option_id");
      $table->unsignedBigInteger("year_id");
      $table->decimal("value", 20, 10);
      $table->timestamps();

      // Add foreign keys with shorter names
      $table
        ->foreign("calculation_definition_option_id", "calc_def_opt_year_val_opt_id_fk")
        ->references("id")
        ->on("calculation_definition_options");

      $table->foreign("year_id", "calc_def_opt_year_val_year_id_fk")->references("id")->on("years");

      // Add unique constraint
      $table->unique(
        ["calculation_definition_option_id", "year_id"],
        "calc_def_opt_year_val_unique",
      );

      // Add index
      $table->index("year_id", "calc_def_opt_year_val_year_idx");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("calculation_definition_option_year_values");

    Schema::table("calculation_definition_options", function (Blueprint $table) {
      $table->decimal("value", 20, 10);
    });
  }
};
