<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->text("emission_factor_default_source")->nullable();
    });

    DB::table("calculation_definition_year")->update([
      "emission_factor_default_source" => "",
    ]);

    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->text("emission_factor_default_source")->nullable(false)->change();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definition_year", function (Blueprint $table) {
      $table->dropColumn("emission_factor_default_source");
    });
  }
};
