<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->string("e_invoice_address")->nullable();
      $table->string("e_invoice_operator")->nullable();
      $table->string("e_invoice_reference")->nullable();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("companies", function (Blueprint $table) {
      $table->dropColumn(["e_invoice_address", "e_invoice_operator", "e_invoice_reference"]);
    });
  }
};
