<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("login_tokens", function (Blueprint $table): void {
      $table->uuid("selector")->after("company_id");

      $table->dropUnique("login_tokens_token_unique");

      $table->unique("selector");
      $table->index(["user_id", "expires_at"]);

      $table->unique("user_id");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("login_tokens", function (Blueprint $table): void {
      $table->dropUnique("selector");
      $table->dropIndex(["user_id", "expires_at"]);

      $table->unique("token");

      $table->dropColumn("selector");
      $table->dropUnique("user_id");
    });
  }
};
