<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // Add new columns as nullable first
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("hide_from_data_page")->nullable();
      $table->boolean("hide_from_emission_factor_page")->nullable();
      $table->boolean("hide_from_results_page")->nullable();
    });

    // Migrate existing data based on only_data field
    // If only_data is true: visible on data page only (hide from emission factor and results)
    // If only_data is false or null: visible on all pages
    DB::table("calculation_definitions")
      ->whereNull("deleted_at")
      ->where("only_data", true)
      ->update([
        "hide_from_data_page" => false,
        "hide_from_emission_factor_page" => true,
        "hide_from_results_page" => true,
      ]);

    DB::table("calculation_definitions")
      ->whereNull("deleted_at")
      ->where(function (\Illuminate\Database\Query\Builder $query) {
        $query->where("only_data", false)->orWhereNull("only_data");
      })
      ->update([
        "hide_from_data_page" => false,
        "hide_from_emission_factor_page" => false,
        "hide_from_results_page" => false,
      ]);

    // Handle soft deleted records
    DB::table("calculation_definitions")
      ->whereNotNull("deleted_at")
      ->update([
        "hide_from_data_page" => false,
        "hide_from_emission_factor_page" => false,
        "hide_from_results_page" => false,
      ]);

    // Make columns non-nullable now that all data is migrated
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("hide_from_data_page")->nullable(false)->change();
      $table->boolean("hide_from_emission_factor_page")->nullable(false)->change();
      $table->boolean("hide_from_results_page")->nullable(false)->change();
    });

    // Add indexes for better query performance
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->index("hide_from_data_page");
      $table->index("hide_from_emission_factor_page");
      $table->index("hide_from_results_page");
    });

    // Drop the old only_data column and its index
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex("calculation_definitions_only_data_index");
      $table->dropColumn("only_data");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    // Re-add only_data column
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("only_data")->nullable();
    });

    // Restore only_data values based on the visibility pattern
    DB::table("calculation_definitions")
      ->where("hide_from_data_page", false)
      ->where("hide_from_emission_factor_page", true)
      ->where("hide_from_results_page", true)
      ->update(["only_data" => true]);

    DB::table("calculation_definitions")
      ->where(function (\Illuminate\Database\Query\Builder $query) {
        $query
          ->where("hide_from_data_page", true)
          ->orWhere("hide_from_emission_factor_page", false)
          ->orWhere("hide_from_results_page", false);
      })
      ->update(["only_data" => false]);

    // Make only_data non-nullable and add its index back
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->boolean("only_data")->nullable(false)->change();
      $table->index("only_data");
    });

    // Drop the new columns and their indexes
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex(["hide_from_data_page"]);
      $table->dropIndex(["hide_from_emission_factor_page"]);
      $table->dropIndex(["hide_from_results_page"]);

      $table->dropColumn([
        "hide_from_data_page",
        "hide_from_emission_factor_page",
        "hide_from_results_page",
      ]);
    });
  }
};
