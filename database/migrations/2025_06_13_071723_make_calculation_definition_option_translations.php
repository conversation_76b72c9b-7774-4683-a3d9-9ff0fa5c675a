<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::create("calculation_definition_option_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("calculation_definition_option_id");
      $table
        ->foreign("calculation_definition_option_id", "calc_def_opt_trans_opt_id_fk")
        ->references("id")
        ->on("calculation_definition_options")
        ->onDelete("cascade");
      $table->string("locale", 10)->index();
      $table->string("label", 255);
      $table->timestamps();

      $table->unique(["calculation_definition_option_id", "locale"], "calc_def_opt_trans_unique");
    });
  }

  public function down(): void
  {
    Schema::dropIfExists("calculation_definition_option_translations");
  }
};
