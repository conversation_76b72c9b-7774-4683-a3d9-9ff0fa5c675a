<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->enum("input_method", ["manual", "select"])->change();
    });
  }

  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table
        ->enum("input_method", ["manual", "select"])
        ->default("manual")
        ->change();
    });
  }
};
