<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::table("groupings", function (Blueprint $table) {
      $table->integer("sort_order")->default(0);
      $table->index("sort_order");
    });

    Schema::table("categories", function (Blueprint $table) {
      $table->integer("sort_order")->default(0);
      $table->index("sort_order");
    });

    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->integer("sort_order")->default(0);
      $table->index(
        ["company_id", "scope_id", "category_id", "sort_order"],
        "idx_company_scope_category_sort",
      );
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::table("calculation_definitions", function (Blueprint $table) {
      $table->dropIndex("idx_company_scope_category_sort");
      $table->dropColumn("sort_order");
    });

    Schema::table("categories", function (Blueprint $table) {
      $table->dropIndex(["sort_order"]);
      $table->dropColumn("sort_order");
    });

    Schema::table("groupings", function (Blueprint $table) {
      $table->dropIndex(["sort_order"]);
      $table->dropColumn("sort_order");
    });
  }
};
