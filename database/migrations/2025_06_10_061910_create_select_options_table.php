<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("select_options", function (Blueprint $table) {
      $table->id();
      $table->string("type");
      $table->integer("sort_order")->default(0);
      $table->timestamps();
      $table->softDeletes();

      $table->index("sort_order");
    });

    Schema::create("select_option_translations", function (Blueprint $table) {
      $table->id();
      $table->unsignedBigInteger("select_option_id");
      $table->string("locale", 10)->index();
      $table->string("label");

      $table->unique(["select_option_id", "locale"]);
      $table
        ->foreign("select_option_id")
        ->references("id")
        ->on("select_options")
        ->onDelete("cascade");
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("select_option_translations");
    Schema::dropIfExists("select_options");
  }
};
