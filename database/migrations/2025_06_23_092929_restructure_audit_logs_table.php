<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  public function up(): void
  {
    // Drop the old table
    Schema::dropIfExists("audit_logs");

    // Create new structure
    Schema::create("audit_logs", function (Blueprint $table) {
      $table->id();
      $table->enum("event", ["created", "updated", "deleted"]);
      $table->morphs("auditable");
      $table->foreignId("user_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->uuid("batch_uuid")->index();
      $table->string("field_name");
      $table->text("old_value")->nullable();
      $table->text("new_value")->nullable();
      $table->enum("field_type", ["string", "integer", "float", "boolean"]);
      $table->timestamp("occurred_at");
      $table->timestamps();

      // Individual indices
      $table->index("event");
      $table->index("user_id");
      $table->index("company_id");
      $table->index("occurred_at");

      // Composite index for the main query pattern
      $table->index(["company_id", "user_id", "occurred_at", "id"], "audit_logs_main_query_idx");
    });
  }

  public function down(): void
  {
    // Drop the new table
    Schema::dropIfExists("audit_logs");

    // Recreate the old structure
    Schema::create("audit_logs", function (Blueprint $table) {
      $table->id();
      $table->enum("event", ["created", "updated", "deleted"]);
      $table->morphs("auditable");
      $table->foreignId("user_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->json("properties");
      $table->timestamps();

      $table->index("event");
      $table->index("user_id");
      $table->index("company_id");
    });
  }
};
