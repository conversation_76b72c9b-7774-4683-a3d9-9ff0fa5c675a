<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("company_user", function (Blueprint $table) {
      $table->id();
      $table->foreignId("user_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->boolean("is_primary");
      $table->timestamps();
      $table->softDeletes();

      $table->unique(["user_id", "company_id"]);
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("company_user");
  }
};
