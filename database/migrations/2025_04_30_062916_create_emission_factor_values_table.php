<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create("emission_factor_values", function (Blueprint $table) {
      $table->id();
      $table->decimal("value", 15, 4);
      $table->foreignId("emission_factor_definition_id")->constrained();
      $table->foreignId("company_id")->constrained();
      $table->timestamps();

      $table->index(
        ["emission_factor_definition_id", "company_id"],
        "emission_factor_definition_id_company_id_index",
      );
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists("emission_factor_values");
  }
};
