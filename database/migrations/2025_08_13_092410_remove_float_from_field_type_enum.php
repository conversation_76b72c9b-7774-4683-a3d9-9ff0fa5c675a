<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    // First, update any existing 'float' field_type values to 'string'
    DB::table("audit_logs")
      ->where("field_type", "float")
      ->update(["field_type" => "string"]);

    DB::statement(
      "ALTER TABLE audit_logs MODIFY COLUMN field_type ENUM('string', 'integer', 'boolean') NOT NULL",
    );
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    DB::statement(
      "ALTER TABLE audit_logs MODIFY COLUMN field_type ENUM('string', 'integer', 'float', 'boolean') NOT NULL",
    );
  }
};
