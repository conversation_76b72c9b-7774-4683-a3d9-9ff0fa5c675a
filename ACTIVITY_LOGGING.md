# Activity Logging for Magic Link Authentication

This implementation adds comprehensive activity logging to the magic link login system to track which logins work and provide audit trails.

## What's Been Added

### 1. Database Schema
- **New table**: `activity_logs` with the following columns:
  - `id` - Primary key
  - `user_id` - Foreign key to users (nullable)
  - `company_id` - Foreign key to companies (nullable)
  - `event` - Enum of activity events
  - `description` - Human-readable description
  - `ip_address` - IP address of the request
  - `user_agent` - Browser/client user agent
  - `metadata` - JSON field for additional data
  - `occurred_at` - When the event occurred
  - `created_at`, `updated_at` - Standard timestamps

### 2. Activity Events
New enum `ActivityEvent` with the following events:
- `LOGIN_ATTEMPT` - When a login is attempted
- `LOGIN_SUCCESS` - When a login succeeds
- `LOGIN_FAILURE` - When a login fails
- `MAGIC_LINK_SENT` - When a magic link email is sent
- `MAGIC_LINK_CLICKED` - When a magic link is clicked
- `LOGOUT` - When a user logs out

### 3. ActivityLogger Service
A dedicated service (`App\Services\ActivityLogger`) that provides methods to log different types of activities:
- `logMagicLinkSent()` - Logs when magic links are sent
- `logMagicLinkClicked()` - Logs when magic links are clicked
- `logLoginSuccess()` - Logs successful logins
- `logLoginFailure()` - Logs failed login attempts
- `logLogout()` - Logs user logouts

### 4. Enhanced AuthenticationService
The existing `AuthenticationService` has been updated to include activity logging:
- Logs when magic links are sent
- Logs when magic links are clicked
- Logs successful logins with metadata (login method, email verification status, etc.)
- Logs failed login attempts with specific reasons
- Logs user logouts

## What Gets Logged

### Magic Link Sent
- **Event**: `MAGIC_LINK_SENT`
- **Description**: "Magic link sent to {email}"
- **Metadata**: Email address, company name (if applicable)
- **User**: Not set (user may not exist yet)
- **Company**: Set if it's a company invitation

### Magic Link Clicked
- **Event**: `MAGIC_LINK_CLICKED`
- **Description**: "Magic link clicked by {email}"
- **Metadata**: Email address, company name (if applicable)
- **User**: The user who clicked the link
- **Company**: Set if it's a company invitation

### Login Success
- **Event**: `LOGIN_SUCCESS`
- **Description**: "User {email} logged in successfully"
- **Metadata**: 
  - `login_method`: "magic_link"
  - `was_already_logged_in`: boolean
  - `email_verified`: boolean
  - Email address, company name
- **User**: The user who logged in
- **Company**: Set if it's a company login

### Login Failures
- **Event**: `LOGIN_FAILURE`
- **Description**: "Login failed: {reason}"
- **Metadata**: Failure reason, email (if known), company name
- **User**: Set if user is known
- **Company**: Set if applicable

Common failure reasons logged:
- "Too many verification attempts"
- "Invalid token format"
- "Token not found or expired"
- "Invalid token validator"
- "User not found for token"

### Logout
- **Event**: `LOGOUT`
- **Description**: "User {email} logged out"
- **Metadata**: Email address
- **User**: The user who logged out

## Usage Examples

### Viewing Recent Activity Logs
```bash
php artisan activity:show --limit=50
php artisan activity:show --event=login_success
```

### Querying Activity Logs in Code
```php
use App\Models\ActivityLog;
use App\Enums\ActivityEvent;

// Get recent login attempts
$recentLogins = ActivityLog::where('event', ActivityEvent::LOGIN_SUCCESS)
    ->with(['user', 'company'])
    ->orderBy('occurred_at', 'desc')
    ->limit(10)
    ->get();

// Get failed login attempts for a specific user
$failures = ActivityLog::where('event', ActivityEvent::LOGIN_FAILURE)
    ->where('user_id', $userId)
    ->orderBy('occurred_at', 'desc')
    ->get();

// Get all magic link activity for an email
$email = '<EMAIL>';
$activity = ActivityLog::whereJsonContains('metadata->email', $email)
    ->orderBy('occurred_at', 'desc')
    ->get();
```

## Files Created/Modified

### New Files
- `app/Models/ActivityLog.php` - Activity log model
- `app/Services/ActivityLogger.php` - Activity logging service
- `app/Console/Commands/ShowActivityLogs.php` - Command to view logs
- `database/migrations/2025_01_19_000000_create_activity_logs_table.php` - Migration
- `database/factories/ActivityLogFactory.php` - Factory for testing
- `tests/Feature/ActivityLoggingTest.php` - Tests for activity logging

### Modified Files
- `app/Enums/AuditEvent.php` - Added ActivityEvent enum
- `app/Services/AuthenticationService.php` - Added activity logging throughout

## Testing

Run the activity logging tests:
```bash
php artisan test tests/Feature/ActivityLoggingTest.php
```

## Migration

To apply the database changes:
```bash
php artisan migrate
```

This will create the `activity_logs` table with all necessary indexes for performance.
