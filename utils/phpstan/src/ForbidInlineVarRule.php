<?php

declare(strict_types=1);

namespace Utils\PHPStan;

use Php<PERSON><PERSON><PERSON>\Node;
use PHPStan\Analyser\Scope;
use PHPStan\Rules\Rule;
use PHPStan\Rules\RuleErrorBuilder;

/**
 * @implements Rule<Node>
 */
final class ForbidInlineVarRule implements Rule
{
  public function getNodeType(): string
  {
    return Node::class;
  }

  public function processNode(Node $node, Scope $scope): array
  {
    $filename = $scope->getFile();

    // Skip compiled Blade templates
    if (str_ends_with($filename, "-blade-compiled.php")) {
      return [];
    }

    $errors = [];

    foreach ($node->getComments() as $comment) {
      if (preg_match("/@var\s/", $comment->getText()) === 1) {
        if (!$this->isPropertyDeclaration($node)) {
          $errors[] = RuleErrorBuilder::message("Inline @var PHPDoc comments are forbidden")
            ->line($comment->getStartLine())
            ->identifier("custom.forbidInlineVar")
            ->build();
        }
      }
    }

    return $errors;
  }

  private function isPropertyDeclaration(Node $node): bool
  {
    return $node instanceof Node\Stmt\Property ||
      $node instanceof Node\Stmt\ClassConst ||
      $node instanceof Node\Const_;
  }
}
