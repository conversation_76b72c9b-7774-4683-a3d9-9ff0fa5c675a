<?php

declare(strict_types=1);

namespace Utils\PHPStan;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON>p<PERSON><PERSON><PERSON>\Node\Stmt\Catch_;
use <PERSON>p<PERSON><PERSON><PERSON>\Node\Stmt\Nop;
use PHPStan\Analyser\Scope;
use PHPStan\Rules\Rule;
use PHPStan\Rules\RuleErrorBuilder;

/**
 * @implements Rule<Catch_>
 */
final class ForbidEmptyCatchRule implements Rule
{
  public function getNodeType(): string
  {
    return Catch_::class;
  }

  /**
   * @param  Catch_  $node
   */
  public function processNode(Node $node, Scope $scope): array
  {
    $errors = [];

    // Check if catch block is empty or only contains comments/nops
    if ($this->isCatchBlockEmpty($node)) {
      $errors[] = RuleErrorBuilder::message(
        "Empty catch blocks are forbidden. Either handle the exception, rethrow it, or remove the try-catch.",
      )
        ->line($node->getStartLine())
        ->identifier("custom.forbidEmptyCatch")
        ->build();
    }

    return $errors;
  }

  /**
   * Check if a catch block is effectively empty
   * (no statements, or only comments/nop statements)
   */
  private function isCatchBlockEmpty(Catch_ $catchNode): bool
  {
    // If there are no statements at all, it's empty
    if (count($catchNode->stmts) === 0) {
      return true;
    }

    // Check if all statements are just nops (empty statements) or only comments
    foreach ($catchNode->stmts as $stmt) {
      // Nop nodes are empty statements (like a semicolon by itself)
      if ($stmt instanceof Nop) {
        continue;
      }

      // If we find any real statement, the catch block is not empty
      return false;
    }

    // If we only found Nop statements or no statements, it's empty
    return true;
  }
}
