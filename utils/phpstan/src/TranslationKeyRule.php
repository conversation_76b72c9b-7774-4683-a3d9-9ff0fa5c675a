<?php

declare(strict_types=1);

namespace Utils\PHPStan;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Arg;
use Php<PERSON><PERSON><PERSON>\Node\Expr\FuncCall;
use Php<PERSON><PERSON>er\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar\String_;
use Php<PERSON><PERSON>er\Node\VariadicPlaceholder;
use P<PERSON>Stan\Analyser\Scope;
use PHPStan\Rules\Rule;
use PHPStan\Rules\RuleErrorBuilder;

/**
 * @implements Rule<FuncCall>
 */
final class TranslationKeyRule implements Rule
{
  public function getNodeType(): string
  {
    return FuncCall::class;
  }

  /**
   * @param FuncCall $node
   */
  public function processNode(Node $node, Scope $scope): array
  {
    // FuncCall->name can be Name (regular function) or Expr (dynamic call)
    if (!$node->name instanceof Name) {
      return [];
    }

    // Get the function name - could be namespaced
    $functionName = $node->name->toString();

    // Check if it's a translation function (handle both global and namespaced calls)
    $baseName = $node->name->getLast();
    if (!in_array($baseName, ["__", "trans_choice"], true)) {
      return [];
    }

    // For safety, also check the full name isn't something like SomeClass::__
    // (though that would be a different node type)
    if (
      $node->name->isQualified() &&
      !in_array($functionName, ["__", "trans_choice", "\\__", '\\trans_choice'], true)
    ) {
      // It's a qualified name but not our global translation functions
      return [];
    }

    // Check if there's at least one argument
    if (count($node->args) === 0) {
      return [];
    }

    $firstArg = $node->args[0] ?? null;

    if ($firstArg === null) {
      return [];
    }

    // Check if it's a VariadicPlaceholder
    if ($firstArg instanceof VariadicPlaceholder) {
      return [];
    }

    // Check if the value is a string literal
    if (!$firstArg->value instanceof String_) {
      return [];
    }

    $key = $firstArg->value->value;

    // Validate the translation key
    if (!$this->isValidTranslationKey($key)) {
      return [
        RuleErrorBuilder::message(
          sprintf(
            'Invalid translation key "%s". Keys must use lowercase letters, numbers, underscores, and dots only.',
            $this->truncateKey($key),
          ),
        )
          ->line($node->getStartLine())
          ->identifier("custom.invalidTranslationKey")
          ->build(),
      ];
    }

    return [];
  }

  private function isValidTranslationKey(string $key): bool
  {
    if ($key === "") {
      return false;
    }

    // Keys can contain lowercase letters, numbers, underscores, and dots
    if (preg_match('/^[a-z0-9_]+(\.[a-z0-9_]+)*$/', $key) !== 1) {
      return false;
    }

    return true;
  }

  private function truncateKey(string $key): string
  {
    if (strlen($key) > 60) {
      return substr($key, 0, 57) . "...";
    }

    return $key;
  }
}
