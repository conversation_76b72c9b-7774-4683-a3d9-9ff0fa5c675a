{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "astrotomic/laravel-translatable": "^11.16", "brick/math": "^0.13.1", "filament/filament": "^3.3", "laravel/framework": "^12.25", "laravel/tinker": "^2.10.1", "livewire/livewire": "^3.6.4", "maatwebsite/excel": "^3.1", "sentry/sentry-laravel": "^4.15", "spatie/laravel-csp": "^3.15", "spatie/laravel-permission": "^6.21", "spatie/laravel-settings": "^3.4", "spatie/laravel-translation-loader": "^2.8", "symfony/expression-language": "^7.3"}, "require-dev": {"fakerphp/faker": "^1.24", "larastan/larastan": "^3.6", "laravel/pail": "^1.2.3", "laravel/pint": "^1.24", "laravel/sail": "^1.44", "laravel/telescope": "^5.11", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "phpstan/phpstan": "^2.1", "phpstan/phpstan-deprecation-rules": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^11.5.3", "roave/no-floaters": "^1.13", "tomasvotruba/bladestan": "^0.11.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Utils\\PHPStan\\": "utils/phpstan/src"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}