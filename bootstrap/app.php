<?php

declare(strict_types=1);

use App\Console\Commands\ProcessInvoiceEmailNotificationsCommand;
use App\Console\Commands\SyncIndustryClassificationsCommand;
use App\Console\Commands\SyncMunicipalitiesCommand;
use App\Helpers\Assert;
use App\Http\Middleware\SetLocaleFromRoute;
use App\Http\Middleware\SetPermissionsPolicy;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Sentry\Laravel\Integration;
use Spatie\Csp\AddCspHeaders;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

return Application::configure(basePath: dirname(__DIR__))
  ->withRouting(
    web: __DIR__ . "/../routes/web.php",
    commands: __DIR__ . "/../routes/console.php",
    health: "/up",
  )
  ->withMiddleware(function (Middleware $middleware) {
    $middleware->append(AddCspHeaders::class);
    $middleware->append(SetPermissionsPolicy::class);
    $middleware->append(SetLocaleFromRoute::class);
  })
  ->withExceptions(function (Exceptions $exceptions) {
    Integration::handles($exceptions);

    $exceptions->render(function (NotFoundHttpException $exception, Request $request) {
      $locales = Config::array("translatable.locales");
      Assert::stringArray($locales);
      Assert::nonEmptyList($locales);
      $default_locale = Config::string("app.locale");
      $locale = $request->segment(1);
      // See if locale in url is absent or isn't among known languages.
      if (!in_array($locale, $locales, true)) {
        $uri = $request->getUriForPath("/" . $default_locale . $request->getPathInfo());

        return redirect($uri, 301);
      }
    });
  })
  ->withSchedule(function (Schedule $schedule) {
    $schedule->command(SyncMunicipalitiesCommand::class)->dailyAt("10:00");
    $schedule->command(SyncIndustryClassificationsCommand::class)->dailyAt("10:30");
    $schedule->command(ProcessInvoiceEmailNotificationsCommand::class)->dailyAt("10:00");
  })
  ->create();
