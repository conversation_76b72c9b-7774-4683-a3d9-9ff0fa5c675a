<?php

declare(strict_types=1);

use App\Console\Commands\ProcessInvoiceEmailNotificationsCommand;
use App\Console\Commands\SyncIndustryClassificationsCommand;
use App\Console\Commands\SyncMunicipalitiesCommand;
use App\Http\Middleware\SetPermissionsPolicy;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;
use Spatie\Csp\AddCspHeaders;

return Application::configure(basePath: dirname(__DIR__))
  ->withRouting(
    web: __DIR__ . "/../routes/web.php",
    commands: __DIR__ . "/../routes/console.php",
    health: "/up",
  )
  ->withMiddleware(function (Middleware $middleware) {
    $middleware->append(AddCspHeaders::class);
    $middleware->append(SetPermissionsPolicy::class);
  })
  ->withExceptions(function (Exceptions $exceptions) {
    Integration::handles($exceptions);
  })
  ->withSchedule(function (Schedule $schedule) {
    $schedule->command(SyncMunicipalitiesCommand::class)->dailyAt("10:00");
    $schedule->command(SyncIndustryClassificationsCommand::class)->dailyAt("10:30");
    $schedule->command(ProcessInvoiceEmailNotificationsCommand::class)->dailyAt("10:00");
  })
  ->create();
