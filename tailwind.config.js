import preset from "./vendor/filament/support/tailwind.config.preset";

/** @type {import('tailwindcss').Config} */
export default {
  presets: [preset],
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.ts",
    "./resources/**/*.vue",
    "./app/View/**/*.php",
    "./app/Filament/**/*.php",
    "./vendor/filament/**/*.blade.php",
  ],
  theme: {
    extend: {
      borderRadius: {
        sm: "3px",
        DEFAULT: "6px",
        md: "9px",
        lg: "12px",
        xl: "15px",
        "2xl": "18px",
        "3xl": "21px",
      },
      fontFamily: {
        sans: ['"Work Sans"', "sans-serif"],
      },
      colors: {
        neutral: {
          50: "#F7F7F7", // Generated
          100: "#F0F0F0", // Generated
          200: "#D8D8D8", // Original
          300: "#C7C7C7", // Generated
          400: "#A6A6A6", // Generated
          500: "#959595", // Original
          600: "#737373", // Generated
          700: "#636363", // Original
          800: "#383838", // Original
          900: "#212121", // Original
          950: "#1A1A1A", // Generated
        },
        red: {
          50: "#FEF2F1", // Generated
          100: "#FDE6E2", // Generated
          200: "#FBC9C1", // Generated
          300: "#FD9180", // Original
          400: "#F66B55", // Generated
          500: "#EB472D", // Generated
          600: "#DC1B1B", // Original
          700: "#B31800", // Generated
          800: "#801100", // Generated
          900: "#4D0A00", // Generated
          950: "#330700", // Generated
        },
        yellow: {
          50: "#FAFAF5", // Generated
          100: "#F5F5EA", // Generated
          200: "#E8E2D5", // Original
          300: "#F3ED9B", // Generated
          400: "#FFF342", // Original
          500: "#FFF01A", // Generated
          600: "#E6D700", // Generated
          700: "#B3A700", // Generated
          800: "#807700", // Generated
          900: "#4D4800", // Generated
          950: "#333000", // Generated
        },
        lime: {
          50: "#FAFCF3", // Generated
          100: "#F5F9E6", // Generated
          200: "#EAF2CA", // Generated
          300: "#DCE9A5", // Generated
          400: "#D1E57C", // Original
          500: "#CEF622", // Generated
          600: "#BAE600", // Generated
          700: "#90B300", // Generated
          800: "#678000", // Generated
          900: "#3E4D00", // Generated
          950: "#293300", // Generated
        },
        teal: {
          50: "#F4FAFA", // Generated
          100: "#E2F3F1", // Original
          200: "#D0ECE8", // Generated
          300: "#9CD8D4", // Original
          400: "#76D5CA", // Generated
          500: "#41D8C6", // Generated
          600: "#19CDB7", // Generated
          700: "#08AA97", // Generated
          800: "#01917A", // Original
          900: "#004D44", // Generated
          950: "#00332D", // Generated
        },
        sky: {
          50: "#F3F8FB", // Generated
          100: "#E8F1F9", // Original
          200: "#CBDFF1", // Generated
          300: "#A5C9E7", // Original
          400: "#6AA9E1", // Generated
          500: "#3891E1", // Generated
          600: "#1778CF", // Generated
          700: "#0B5EA7", // Generated
          800: "#04437C", // Generated
          900: "#002854", // Original
          950: "#001B33", // Generated
        },
        blue: {
          50: "#F3F5FC", // Generated
          100: "#E7ECF8", // Generated
          200: "#CBD6F1", // Generated
          300: "#A7B9E7", // Generated
          400: "#7390D8", // Generated
          500: "#4B70CD", // Generated
          600: "#2351C2", // Generated
          700: "#1D397F", // Original
          800: "#0C2A74", // Generated
          900: "#002663", // Original
          950: "#000F33", // Generated
        },
        indigo: {
          50: "#F7F2FC", // Generated
          100: "#EEE6FA", // Generated
          200: "#DAC8F4", // Generated
          300: "#BB95EF", // Original
          400: "#9D70DB", // Generated
          500: "#7A1AFF", // Generated
          600: "#6100E6", // Generated
          700: "#4B00B3", // Generated
          800: "#360080", // Generated
          900: "#20004D", // Generated
          950: "#160033", // Generated
        },
        pink: {
          50: "#FBEDEF", // Original
          100: "#FAE5E8", // Original
          200: "#F2B7BF", // Original
          300: "#EAA4AE", // Generated
          400: "#D47885", // Generated
          500: "#FF1A3A", // Generated
          600: "#E60021", // Generated
          700: "#B30019", // Generated
          800: "#800012", // Generated
          900: "#4D000B", // Generated
          950: "#330007", // Generated
        },
      },
      typography: ({ theme }) => ({
        blue: {
          css: {
            "--tw-prose-body": theme("colors.blue[900]"),
            "--tw-prose-headings": theme("colors.blue[900]"),
            "--tw-prose-lead": theme("colors.blue[900]"),
            "--tw-prose-links": theme("colors.blue[900]"),
            "--tw-prose-bold": theme("colors.blue[900]"),
            "--tw-prose-counters": theme("colors.blue[900]"),
            "--tw-prose-bullets": theme("colors.blue[900]"),
            "--tw-prose-hr": theme("colors.blue[900]"),
            "--tw-prose-quotes": theme("colors.blue[900]"),
            "--tw-prose-quote-borders": theme("colors.blue[900]"),
            "--tw-prose-captions": theme("colors.blue[900]"),
            "--tw-prose-code": theme("colors.blue[900]"),
            "--tw-prose-pre-code": theme("colors.blue[100]"),
            "--tw-prose-pre-bg": theme("colors.blue[900]"),
            "--tw-prose-th-borders": theme("colors.blue[900]"),
            "--tw-prose-td-borders": theme("colors.blue[900]"),
          },
        },
      }),
    },
  },
  plugins: [],
};

// #636363
// #212121
// #383838
// #BB95EF
// #959595
// #002854
// #D8D8D8
// #DC1B1B
// #E2F3F1
// #E8F1F9
// #F2B7BF
// #FAE5E8
// #FBEDEF
// #FFF342
// #FFFFFF
// #9CD8D4
// #E8E2D5
// #FD9180
// #002663
// #1D397F
// #A5C9E7
// #D1E57C
// #01917A
