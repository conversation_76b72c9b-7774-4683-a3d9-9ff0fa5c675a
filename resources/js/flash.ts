import { type AlpineComponent } from "alpinejs";

interface FlashMessage {
  message: string;
  type: string;
}

export const setupParentFlashHandler = () => {
  const handler = (event: MessageEvent<unknown>) => {
    if (typeof event.data !== "object" || event.data === null) {
      return;
    }

    const data = event.data;

    if (!("type" in data) || data.type !== "flash_position_request") {
      return;
    }

    const scriptOrigin = new URL(
      document.currentScript instanceof HTMLScriptElement && "src" in document.currentScript
        ? document.currentScript.src
        : import.meta.url,
    ).origin;

    if (!import.meta.env.DEV && event.origin !== scriptOrigin) {
      console.warn(
        "Parent handler: Flash position request from unexpected origin. Expected:",
        scriptOrigin,
        "Received:",
        event.origin,
      );
      return;
    }

    if (!("messageId" in data) || typeof data.messageId !== "string") {
      console.error("Parent handler: Invalid messageId in request", data);
      return;
    }

    if (!event.source) {
      console.error("Parent handler: No source window available");
      return;
    }

    const targetIframe = [...document.querySelectorAll("iframe")].find(
      (iframe) => iframe.contentWindow === event.source,
    );

    if (!targetIframe) {
      console.warn("Parent handler: Could not find iframe that sent the message");
      return;
    }

    // Get the iframe's position
    const iframeRect = targetIframe.getBoundingClientRect();

    // Calculate the center X position in viewport
    const viewportCenterX = window.innerWidth / 2;

    // Calculate where to position the flash at the top of the visible iframe area
    // If iframe top is below viewport top (positive iframeRect.top), position at top of iframe
    // If iframe top is above viewport top (negative iframeRect.top), position at viewport top
    const visibleTopInIframe = Math.max(0, -iframeRect.top) + 16; // 16px padding from visible top

    // Calculate the position in iframe coordinates
    const iframeCenterX = viewportCenterX - iframeRect.left;
    const iframeTopY = visibleTopInIframe;

    event.source.postMessage(
      {
        type: "flash_position_response",
        messageId: data.messageId,
        position: {
          centerX: iframeCenterX,
          topY: iframeTopY,
        },
      },
      { targetOrigin: event.origin },
    );
  };

  window.addEventListener("message", handler);

  return () => {
    window.removeEventListener("message", handler);
  };
};

interface AlpineFlashComponent {
  show: boolean;
  message: string;
  type: string;
  timeout: number | null;
  currentPositioningId: string | null;
  positioningStates: Map<string, { completed: boolean; aborted: boolean }>;
  init(): void;
  showMessage(detail: FlashMessage[]): void;
  positionFlash(): void;
}

export const flashComponent = (): AlpineComponent<AlpineFlashComponent> => {
  const component: AlpineComponent<AlpineFlashComponent> = {
    show: false,
    message: "",
    type: "success",
    timeout: null,
    currentPositioningId: null,
    positioningStates: new Map(),

    init(): void {
      // Initially hide the flash element
      const flash = this.$el;
      if (flash instanceof HTMLElement) {
        flash.style.display = "none";
        flash.style.opacity = "0";
      }

      // Listen for notify events
      const handleNotify = (event: Event): void => {
        if (event instanceof CustomEvent) {
          const detail: unknown = event.detail;

          if (Array.isArray(detail) && detail.length > 0) {
            // Validate the structure
            const isValidDetail = detail.every(
              (item): item is FlashMessage =>
                typeof item === "object" &&
                item !== null &&
                "message" in item &&
                "type" in item &&
                typeof item.message === "string" &&
                typeof item.type === "string",
            );

            if (isValidDetail) {
              this.showMessage(detail);
            }
          }
        }
      };

      window.addEventListener("notify", handleNotify);
    },

    showMessage(detail: FlashMessage[]): void {
      // Validate detail array
      const firstDetail = detail[0];
      if (!firstDetail) {
        console.error("Flash: Invalid message detail provided");
        return;
      }

      const flash = this.$el;
      if (!(flash instanceof HTMLElement)) {
        console.error("Flash: Element is not an HTMLElement");
        return;
      }

      if (this.timeout !== null) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }

      // Mark previous positioning as aborted
      if (this.currentPositioningId !== null) {
        const state = this.positioningStates.get(this.currentPositioningId);
        if (state) {
          state.aborted = true;
        }
      }

      // If already showing, immediately hide without animation
      if (this.show && flash.style.display !== "none") {
        // Remove transition temporarily to hide immediately
        flash.style.transition = "none";
        flash.style.display = "none";
        flash.style.opacity = "0";
        this.show = false;
      }

      // Update content while hidden
      this.message = firstDetail.message;
      this.type = firstDetail.type;
      this.show = true;

      // Small delay to ensure clean state before repositioning
      requestAnimationFrame(() => {
        this.positionFlash();
      });
    },

    positionFlash(): void {
      const messageId = crypto.randomUUID();
      this.currentPositioningId = messageId;

      // Track this positioning attempt
      this.positioningStates.set(messageId, { completed: false, aborted: false });

      // Clean up old entries (keep only last 10)
      if (this.positioningStates.size > 10) {
        const entries = Array.from(this.positioningStates.entries());
        entries.slice(0, entries.length - 10).forEach(([id]) => {
          this.positioningStates.delete(id);
        });
      }

      const flash = this.$el;

      if (!(flash instanceof HTMLElement)) {
        console.error("Flash: Element is not an HTMLElement");
        return;
      }

      // Setup initial state
      flash.style.display = "block";
      flash.style.opacity = "0";
      flash.style.transition = "none";

      const showFlash = () => {
        requestAnimationFrame(() => {
          flash.style.transition =
            "opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1), transform 250ms cubic-bezier(0.4, 0.0, 0.2, 1)";
          flash.style.opacity = "1";

          // Check if we're using centered positioning
          const currentTransform = flash.style.transform;
          const isCentered = currentTransform.includes("translateX(-50%)");

          flash.style.transform = isCentered
            ? "translateX(-50%) scale(1)"
            : flash.style.position === "fixed"
              ? "translateX(-50%) scale(1)"
              : "scale(1)";

          // Set up auto-hide timeout
          this.timeout = setTimeout(() => {
            flash.style.opacity = "0";
            flash.style.transform = isCentered
              ? "translateX(-50%) scale(0.95)"
              : flash.style.position === "fixed"
                ? "translateX(-50%) scale(0.95)"
                : "scale(0.95)";

            flash.addEventListener(
              "transitionend",
              () => {
                flash.style.display = "none";
                this.show = false;
                this.timeout = null;
              },
              { once: true },
            );
          }, 3000);
        });
      };

      // Check if we're in an iframe
      if (window.parent === window) {
        // Not in iframe, use default positioning
        flash.style.position = "fixed";
        flash.style.left = "50%";
        flash.style.top = "16px";
        flash.style.transform = "translateX(-50%) scale(0.95)";

        const state = this.positioningStates.get(messageId);
        if (state) {
          state.completed = true;
        }

        showFlash();
        return;
      }

      // Setup iframe positioning
      const signal = AbortSignal.timeout(1000);

      signal.addEventListener("abort", () => {
        const state = this.positioningStates.get(messageId);

        // Only trigger fallback if this positioning attempt wasn't completed or aborted
        if (state && !state.completed && !state.aborted) {
          console.warn("Flash: Positioning timed out, falling back to default positioning");

          // Fall back to default positioning
          flash.style.position = "fixed";
          flash.style.left = "50%";
          flash.style.top = "16px";
          flash.style.transform = "translateX(-50%) scale(0.95)";

          state.completed = true;
          showFlash();
        }
      });

      const allowedOrigins = window.appConfig?.allowedFrameOrigins ?? [];

      const listener = (event: MessageEvent<unknown>) => {
        if (typeof event.data !== "object" || event.data === null) {
          return;
        }

        const data = event.data;

        if (!("type" in data) || data.type !== "flash_position_response") {
          return;
        }

        if (!allowedOrigins.includes(event.origin) && !allowedOrigins.includes("*")) {
          console.warn(
            "Flash: Position response from unexpected origin. Expected one of:",
            allowedOrigins,
            "Received:",
            event.origin,
          );
          return;
        }

        if (!("messageId" in data) || data.messageId !== messageId) {
          return;
        }

        const state = this.positioningStates.get(messageId);
        if (!state || state.completed || state.aborted) {
          return;
        }

        if (!("position" in data) || typeof data.position !== "object" || data.position === null) {
          console.error("Flash: Invalid position data", data);
          return;
        }

        const position = data.position;

        if (
          !("centerX" in position) ||
          !("topY" in position) ||
          typeof position.centerX !== "number" ||
          typeof position.topY !== "number"
        ) {
          console.error("Flash: Invalid position structure", position);
          return;
        }

        const scrollX = window.scrollX || document.documentElement.scrollLeft;
        const scrollY = window.scrollY || document.documentElement.scrollTop;

        flash.style.position = "absolute";
        flash.style.left = (position.centerX + scrollX).toString() + "px";
        flash.style.top = (position.topY + scrollY).toString() + "px";
        flash.style.transform = "translateX(-50%) scale(0.95)";

        state.completed = true;
        showFlash();
      };

      window.addEventListener("message", listener, { signal, once: true });

      const origins = window.appConfig?.allowedFrameOrigins;
      if (origins) {
        origins.forEach((origin) => {
          window.parent.postMessage(
            {
              type: "flash_position_request",
              messageId: messageId,
            },
            origin,
          );
        });
      }
    },
  };

  return component;
};
