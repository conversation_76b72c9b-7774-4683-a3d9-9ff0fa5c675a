import { type AlpineComponent } from "alpinejs";

export const setupParentMessageHandler = () => {
  const handler = (event: MessageEvent<unknown>) => {
    if (typeof event.data !== "object" || event.data === null) {
      return;
    }

    const data = event.data;

    if (!("type" in data) || data.type !== "modal_position_request") {
      return;
    }

    const scriptOrigin = new URL(
      document.currentScript && "src" in document.currentScript
        ? document.currentScript.src
        : import.meta.url,
    ).origin;

    if (!import.meta.env.DEV && event.origin !== scriptOrigin) {
      console.warn(
        "Parent handler: Modal position request from unexpected origin. Expected:",
        scriptOrigin,
        "Received:",
        event.origin,
      );
      return;
    }

    if (!("messageId" in data) || typeof data.messageId !== "string") {
      console.error("Parent handler: Invalid messageId in request", data);
      return;
    }

    if (!event.source) {
      console.error("Parent handler: No source window available");
      return;
    }

    const targetIframe = [...document.querySelectorAll("iframe")].find(
      (iframe) => iframe.contentWindow === event.source,
    );

    if (!targetIframe) {
      console.warn("Parent handler: Could not find iframe that sent the message");
      return;
    }

    // Get the iframe's position
    const iframeRect = targetIframe.getBoundingClientRect();

    // Calculate the viewport center
    const viewportCenterX = window.innerWidth / 2;
    const viewportCenterY = window.innerHeight / 2;

    // Calculate the position in iframe coordinates
    const iframeCenterX = viewportCenterX - iframeRect.left;
    const iframeCenterY = viewportCenterY - iframeRect.top;

    event.source.postMessage(
      {
        type: "modal_position_response",
        messageId: data.messageId,
        position: {
          centerX: iframeCenterX,
          centerY: iframeCenterY,
        },
      },
      { targetOrigin: event.origin },
    );
  };

  window.addEventListener("message", handler);

  return () => {
    window.removeEventListener("message", handler);
  };
};

interface AlpineModalComponent {
  positionModal(): void;
}

export const modalComponent = () => {
  const component: AlpineComponent<AlpineModalComponent> = {
    positionModal(): void {
      const messageId = crypto.randomUUID();
      const modalElement = this.$el.querySelector(".modal-container");

      if (!modalElement) {
        console.error("Modal: Could not find .modal-container element");
        return;
      }

      if (!(modalElement instanceof HTMLElement)) {
        console.error("Modal: .modal-container is not an HTMLElement");
        return;
      }

      const modal = modalElement;

      modal.addEventListener(
        "transitionend",
        () => {
          const textarea = modal.querySelector("textarea");
          if (textarea) textarea.focus();
        },
        { once: true },
      );

      const signal = AbortSignal.timeout(1000);

      signal.addEventListener("abort", () => {
        if (modal.style.opacity !== "1") {
          console.warn("Modal: Positioning timed out, falling back to centered positioning");

          // Fall back to centered positioning
          modal.style.position = "fixed";
          modal.style.top = "50%";
          modal.style.left = "50%";

          requestAnimationFrame(() => {
            modal.style.transform = "translate(-50%, -50%) scale(0.8)";
            modal.style.transition = "none";

            requestAnimationFrame(() => {
              modal.style.transition =
                "opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1), transform 250ms cubic-bezier(0.4, 0.0, 0.2, 1)";

              modal.style.opacity = "1";
              modal.style.transform = "translate(-50%, -50%) scale(1)";
            });
          });
        }
      });

      const allowedOrigins = window.appConfig?.allowedFrameOrigins ?? [];

      const listener = (event: MessageEvent<unknown>) => {
        if (typeof event.data !== "object" || event.data === null) {
          return;
        }

        const data = event.data;

        if (!("type" in data) || data.type !== "modal_position_response") {
          return;
        }

        if (!allowedOrigins.includes(event.origin) && !allowedOrigins.includes("*")) {
          console.warn(
            "Modal: Position response from unexpected origin. Expected one of:",
            allowedOrigins,
            "Received:",
            event.origin,
          );
          return;
        }

        if (!("messageId" in data) || data.messageId !== messageId) {
          console.warn("Modal: Message ID mismatch or missing", data);
          return;
        }

        if (!("position" in data) || typeof data.position !== "object" || data.position === null) {
          console.error("Modal: Invalid position data", data);
          return;
        }

        const position = data.position;

        if (!("centerX" in position) || !("centerY" in position)) {
          console.error("Modal: Position is missing centerX or centerY", position);
          return;
        }

        if (typeof position.centerX !== "number" || typeof position.centerY !== "number") {
          console.error("Modal: Position values are not numbers", position);
          return;
        }

        const scrollX = window.scrollX || document.documentElement.scrollLeft;
        const scrollY = window.scrollY || document.documentElement.scrollTop;

        const modalHeight = modal.offsetHeight;
        const modalWidth = modal.offsetWidth;
        const finalTop = position.centerY + scrollY - modalHeight / 2;
        const finalLeft = position.centerX + scrollX - modalWidth / 2;

        modal.style.top = finalTop.toString() + "px";
        modal.style.left = finalLeft.toString() + "px";

        requestAnimationFrame(() => {
          modal.style.transform = "scale(0.8)";
          modal.style.transition = "none";

          requestAnimationFrame(() => {
            modal.style.transition =
              "opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1), transform 250ms cubic-bezier(0.4, 0.0, 0.2, 1)";

            modal.style.opacity = "1";
            modal.style.transform = "scale(1)";
          });
        });
      };

      window.addEventListener("message", listener, { signal, once: true });

      window.appConfig?.allowedFrameOrigins.forEach((origin) => {
        window.parent.postMessage(
          {
            type: "modal_position_request",
            messageId: messageId,
          },
          origin,
        );
      });
    },
  };
  return component;
};
