const authChannel = new BroadcastChannel("auth_channel");

authChannel.addEventListener("message", function (event: MessageEvent<unknown>) {
  if (typeof event.data !== "object" || event.data === null) {
    console.error("Received invalid message format");
    return;
  }

  const data = event.data;

  if (!("type" in data) || typeof data.type !== "string") {
    console.error("Received message with missing or invalid type");
    return;
  }

  if (data.type === "auth_result") {
    if (!("success" in data) || typeof data.success !== "boolean") {
      console.error("Received auth_result with invalid success property");
      return;
    }

    authChannel.postMessage({ type: "auth_response" });

    if (data.success) {
      window.location.reload();
    } else {
      if (!("message" in data) || typeof data.message !== "string") {
        console.error("Received failure message without valid message content");
        return;
      }

      if (window.Livewire) {
        window.Livewire.dispatch("auth-error", { message: data.message });
      }
    }
  } else {
    console.error(`Received message with unknown type: ${data.type}`);
  }
});

window.addEventListener("beforeunload", function () {
  authChannel.close();
});
