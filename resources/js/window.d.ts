import { AxiosStatic } from "axios";
import { type Alpine } from "alpinejs";

declare global {
  interface Window {
    appConfig?: {
      allowedFrameOrigins: string[];
    };
    axios?: AxiosStatic;
    Alpine?: Alpine;
    Livewire?: Livewire;
  }
}

interface Livewire {
  on(event: string, callback: (params: unknown) => void): void;

  dispatch(event: string, data?: unknown): void;

  hook(name: string, callback: (component: unknown, ...params: unknown[]) => unknown): void;

  first(selector?: string): Wire | null;
  find(id: string): Wire | null;
  all(): Wire[];
}

interface Wire {
  entangle<T>(property: string): { get(): T; set(value: T): void };
  get(property: string): unknown;
  set(property: string, value: unknown): Promise<void>;
  call<T>(method: string, ...params: unknown[]): Promise<T>;
}
