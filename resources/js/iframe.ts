function reportIframeHeight() {
  if (window.self !== window.top) {
    const height = document.body.scrollHeight;
    const origins = window.appConfig?.allowedFrameOrigins;

    origins?.forEach((origin) => {
      window.parent.postMessage({ frameHeight: height }, origin);
    });
  }
}

window.addEventListener("DOMContentLoaded", reportIframeHeight);

window.addEventListener("load", reportIframeHeight);

window.addEventListener("resize", reportIframeHeight);

setInterval(reportIframeHeight, 500);

document.addEventListener("livewire:init", function () {
  const Livewire = window.Livewire;

  if (Livewire) {
    Livewire.hook("message.processed", () => {
      reportIframeHeight();
    });
  } else {
    console.warn("Livewire is not available in the global scope");
  }
});
