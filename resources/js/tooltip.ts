import { type AlpineComponent } from "alpinejs";

interface AlpineTooltipComponent {
  content: string;
  targetElement: HTMLElement | null;
  positioningAbortController: AbortController | null;
  init(): void;
  open(event: MouseEvent): void;
  close(): void;
  positionTooltip(): void;
  positionInIframe(tooltipEl: HTMLElement): void;
  positionInViewport(tooltipEl: HTMLElement): void;
  updatePosition(): void;
  handleClickOutside(e: MouseEvent): void;
  handleScroll(): void;
  handleResize(): void;
  destroy(): void;
}

const getTooltipContainer = (): HTMLElement | null => {
  const existingContainer = document.querySelector("#alpine-tooltip-container");
  if (existingContainer instanceof HTMLElement) {
    return existingContainer;
  }

  const container = document.createElement("div");
  container.id = "alpine-tooltip-container";
  container.className = "tooltip-container tooltip-hidden";
  container.style.display = "none";
  container.innerHTML = `<div class="relative"><p class="tooltip-content"></p></div>`;
  document.body.appendChild(container);

  return container;
};

export const tooltipComponent = (content?: string) => {
  const component: AlpineComponent<AlpineTooltipComponent> = {
    content: "",
    targetElement: null,
    positioningAbortController: null,

    init(): void {
      const el = this.$el;
      if (!(el instanceof HTMLElement)) {
        console.error("Tooltip: $el is not an HTMLElement");
        return;
      }

      const dataTooltip = el.dataset["tooltip"];
      const ariaLabel = el.getAttribute("aria-label");

      this.content = content ?? dataTooltip ?? ariaLabel ?? "Ei ohjetekstiä saatavilla";

      this.handleClickOutside = this.handleClickOutside.bind(this);
      this.handleScroll = this.handleScroll.bind(this);
      this.handleResize = this.handleResize.bind(this);

      el.addEventListener("click", (event: MouseEvent) => {
        this.open(event);
      });
    },

    open(event: MouseEvent): void {
      event.stopPropagation();

      const currentTarget = event.currentTarget;
      if (!(currentTarget instanceof HTMLElement)) {
        console.error("Tooltip: currentTarget is not an HTMLElement");
        return;
      }

      const container = getTooltipContainer();
      if (!container) {
        console.error("Tooltip: Could not create container");
        return;
      }

      if (container.classList.contains("tooltip-visible")) {
        // Trigger close on the other tooltip by dispatching a click outside
        document.dispatchEvent(new MouseEvent("click", { bubbles: true }));
      }

      // Abort any pending positioning from previous tooltip
      if (this.positioningAbortController) {
        this.positioningAbortController.abort();
        this.positioningAbortController = null;
      }

      this.targetElement = currentTarget;

      // Update content
      const contentEl = container.querySelector(".tooltip-content");
      if (contentEl instanceof HTMLElement) {
        contentEl.textContent = this.content;
      }

      container.style.display = "";
      container.classList.remove("tooltip-above", "tooltip-visible");
      container.classList.add("tooltip-hidden");

      document.addEventListener("click", this.handleClickOutside);
      window.addEventListener("scroll", this.handleScroll, { passive: true });
      window.addEventListener("resize", this.handleResize);

      // Handle cleanup after transition ends
      const handleTransitionEnd = (e: TransitionEvent): void => {
        if (
          e.propertyName === "opacity" &&
          container.classList.contains("tooltip-hidden") &&
          e.target === container
        ) {
          container.style.display = "none";
          this.targetElement = null;
          if (this.positioningAbortController) {
            this.positioningAbortController.abort();
            this.positioningAbortController = null;
          }
          container.removeEventListener("transitionend", handleTransitionEnd);
        }
      };

      container.addEventListener("transitionend", handleTransitionEnd);

      requestAnimationFrame(() => {
        this.positionTooltip();

        requestAnimationFrame(() => {
          container.classList.remove("tooltip-hidden");
          container.classList.add("tooltip-visible");
        });
      });
    },

    close(): void {
      const container = getTooltipContainer();

      if (!container) {
        return;
      }

      if (!container.classList.contains("tooltip-visible")) {
        return;
      }

      // Abort any pending positioning
      if (this.positioningAbortController) {
        this.positioningAbortController.abort();
        this.positioningAbortController = null;
      }

      document.removeEventListener("click", this.handleClickOutside);
      window.removeEventListener("scroll", this.handleScroll);
      window.removeEventListener("resize", this.handleResize);

      // Start hide transition
      container.classList.remove("tooltip-visible");
      container.classList.add("tooltip-hidden");
    },

    handleClickOutside(e: MouseEvent): void {
      if (!this.targetElement) {
        return;
      }

      const target = e.target;
      if (!(target instanceof Node)) {
        return;
      }

      const container = getTooltipContainer();
      if (!container) {
        return;
      }

      if (this.targetElement.contains(target) || container.contains(target)) {
        return;
      }

      this.close();
    },

    handleScroll(): void {
      if (this.targetElement) {
        this.updatePosition();
      }
    },

    handleResize(): void {
      if (this.targetElement) {
        this.updatePosition();
      }
    },

    positionTooltip(): void {
      const tooltipEl = getTooltipContainer();

      if (!tooltipEl) {
        console.error("Tooltip: Could not find tooltip container");
        return;
      }

      if (!this.targetElement) {
        console.error("Tooltip: No target element available");
        return;
      }

      const isInIframe = window.self !== window.top;

      if (isInIframe) {
        this.positionInIframe(tooltipEl);
      } else {
        this.positionInViewport(tooltipEl);
      }
    },

    positionInIframe(tooltipEl: HTMLElement): void {
      if (!this.targetElement) {
        console.error("Tooltip: No target element for iframe positioning");
        return;
      }

      const messageId = crypto.randomUUID();
      const targetRect = this.targetElement.getBoundingClientRect();

      tooltipEl.style.position = "fixed";

      this.positioningAbortController = new AbortController();
      const signal = this.positioningAbortController.signal;

      const timeoutId = setTimeout(() => {
        if (this.positioningAbortController && !signal.aborted) {
          this.positioningAbortController.abort();
        }
      }, 1000);

      signal.addEventListener("abort", () => {
        clearTimeout(timeoutId);
        if (tooltipEl.classList.contains("tooltip-hidden")) {
          console.warn("Tooltip: Positioning timed out, falling back to standard positioning");
          this.positionInViewport(tooltipEl);
        }
        this.positioningAbortController = null;
      });

      const listener = (event: MessageEvent<unknown>): void => {
        if (signal.aborted) {
          return;
        }

        clearTimeout(timeoutId);

        if (typeof event.data !== "object" || event.data === null) {
          return;
        }

        const data = event.data;

        if (!("type" in data) || data.type !== "tooltip_position_response") {
          return;
        }

        const allowedOrigins = window.appConfig?.allowedFrameOrigins ?? [];
        if (!allowedOrigins.includes(event.origin) && !allowedOrigins.includes("*")) {
          console.warn(
            "Tooltip: Position response from unexpected origin. Expected one of:",
            allowedOrigins,
            "Received:",
            event.origin,
            "Falling back to standard positioning",
          );

          this.positionInViewport(tooltipEl);
          return;
        }

        if (!("messageId" in data) || data.messageId !== messageId) {
          console.warn("Tooltip: Message ID mismatch or missing", data);
          return;
        }

        if (!("position" in data) || typeof data.position !== "object" || data.position === null) {
          console.error("Tooltip: Invalid position data", data);
          return;
        }

        const position = data.position;

        if (
          !("viewportHeight" in position) ||
          !("viewportWidth" in position) ||
          !("iframeTop" in position) ||
          !("iframeLeft" in position)
        ) {
          console.error("Tooltip: Position is missing required properties", position);
          return;
        }

        if (
          typeof position.viewportHeight !== "number" ||
          typeof position.viewportWidth !== "number" ||
          typeof position.iframeTop !== "number" ||
          typeof position.iframeLeft !== "number"
        ) {
          console.error("Tooltip: Position values are not numbers", position);
          return;
        }

        const tooltipHeight = tooltipEl.offsetHeight;
        const tooltipWidth = tooltipEl.offsetWidth;
        const padding = 8;
        const arrowWidth = 9; // Arrow width from CSS

        // Calculate target center for arrow alignment
        const targetCenterX = targetRect.left + targetRect.width / 2;

        // Use iframe dimensions
        const iframeWidth = window.innerWidth;

        // Default position below target
        let top = targetRect.bottom + padding;
        // Position tooltip so arrow is centered on target by default
        let left = targetCenterX - arrowWidth / 2 - 5; // 5px is a reasonable default offset for arrow from edge

        // Calculate tooltip's absolute position in parent viewport
        const tooltipBottomInParent = position.iframeTop + top + tooltipHeight;

        // Check if tooltip would go below parent viewport bottom
        if (tooltipBottomInParent > position.viewportHeight - padding) {
          // Position above instead
          top = targetRect.top - tooltipHeight - padding;
          tooltipEl.classList.add("tooltip-above");

          // Check if positioning above would go above parent viewport
          const tooltipTopInParent = position.iframeTop + top;
          if (tooltipTopInParent < padding) {
            // Keep it below if it doesn't fit above either
            top = targetRect.bottom + padding;
            tooltipEl.classList.remove("tooltip-above");
          }
        } else {
          tooltipEl.classList.remove("tooltip-above");
        }

        // Ensure tooltip doesn't go above iframe top (but only after positioning decision)
        if (top < padding) {
          top = padding;
        }

        // Calculate tooltip's absolute position in parent viewport for horizontal check
        const tooltipRightInParent = position.iframeLeft + left + tooltipWidth;
        const tooltipLeftInParent = position.iframeLeft + left;

        // Adjust horizontal position to keep tooltip in parent viewport
        if (tooltipRightInParent > position.viewportWidth - padding) {
          left = position.viewportWidth - position.iframeLeft - tooltipWidth - padding;
        }
        if (tooltipLeftInParent < padding) {
          left = padding - position.iframeLeft;
        }

        // Also ensure tooltip stays within iframe boundaries
        if (left + tooltipWidth > iframeWidth - padding) {
          left = iframeWidth - tooltipWidth - padding;
        }
        if (left < padding) {
          left = padding;
        }

        // Calculate where arrow should be positioned to point at target center
        // Arrow position is relative to tooltip left edge
        const arrowLeft = targetCenterX - left - arrowWidth / 2;
        tooltipEl.style.setProperty("--arrow-offset", `${arrowLeft.toString()}px`);

        // Set transform origin to the arrow position for better animation
        const transformOriginX = arrowLeft + arrowWidth / 2;
        const transformOriginY = tooltipEl.classList.contains("tooltip-above") ? "100%" : "0%";
        tooltipEl.style.transformOrigin = `${transformOriginX.toString()}px ${transformOriginY}`;

        tooltipEl.style.left = `${left.toString()}px`;
        tooltipEl.style.top = `${top.toString()}px`;

        // Clean up abort controller after successful positioning
        this.positioningAbortController = null;
      };

      window.addEventListener("message", listener, { signal, once: true });

      const allowedOrigins = window.appConfig?.allowedFrameOrigins;
      if (allowedOrigins) {
        allowedOrigins.forEach((origin) => {
          window.parent.postMessage(
            {
              type: "tooltip_position_request",
              messageId: messageId,
            },
            origin,
          );
        });
      }
    },

    positionInViewport(tooltipEl: HTMLElement): void {
      if (!this.targetElement) {
        console.error("Tooltip: No target element for viewport positioning");
        return;
      }

      const targetRect = this.targetElement.getBoundingClientRect();
      const tooltipHeight = tooltipEl.offsetHeight;
      const tooltipWidth = tooltipEl.offsetWidth;
      const padding = 8;
      const arrowWidth = 9; // Arrow width from CSS

      // Calculate target center for arrow alignment
      const targetCenterX = targetRect.left + targetRect.width / 2;

      // Default position below target
      let top = targetRect.bottom + padding;
      // Position tooltip so arrow is centered on target by default
      let left = targetCenterX - arrowWidth / 2 - 5; // 5px is a reasonable default offset for arrow from edge

      // Check if tooltip would go below viewport bottom (including padding)
      if (top + tooltipHeight + padding > window.innerHeight) {
        // Position above instead
        top = targetRect.top - tooltipHeight - padding;
        tooltipEl.classList.add("tooltip-above");
      } else {
        tooltipEl.classList.remove("tooltip-above");
      }

      // Ensure tooltip doesn't go above viewport top
      if (top < padding) {
        top = padding;
      }

      // Adjust horizontal position to keep tooltip in viewport
      if (left + tooltipWidth > window.innerWidth - padding) {
        left = window.innerWidth - tooltipWidth - padding;
      }
      if (left < padding) {
        left = padding;
      }

      // Calculate where arrow should be positioned to point at target center
      // Arrow position is relative to tooltip left edge
      const arrowLeft = targetCenterX - left - arrowWidth / 2;
      tooltipEl.style.setProperty("--arrow-offset", `${arrowLeft.toString()}px`);

      // Set transform origin to the arrow position for better animation
      const transformOriginX = arrowLeft + arrowWidth / 2;
      const transformOriginY = tooltipEl.classList.contains("tooltip-above") ? "100%" : "0%";
      tooltipEl.style.transformOrigin = `${transformOriginX.toString()}px ${transformOriginY}`;

      tooltipEl.style.position = "fixed";
      tooltipEl.style.left = `${left.toString()}px`;
      tooltipEl.style.top = `${top.toString()}px`;
    },

    updatePosition(): void {
      this.positionTooltip();
    },

    destroy(): void {
      this.close();
    },
  };

  return component;
};

export const setupTooltipParentHandler = (): (() => void) => {
  const handler = (event: MessageEvent<unknown>): void => {
    if (typeof event.data !== "object" || event.data === null) {
      return;
    }

    const data = event.data;

    if (!("type" in data) || data.type !== "tooltip_position_request") {
      return;
    }

    const scriptOrigin = new URL(
      document.currentScript instanceof HTMLScriptElement && document.currentScript.src
        ? document.currentScript.src
        : import.meta.url,
    ).origin;

    if (!import.meta.env.DEV && event.origin !== scriptOrigin) {
      console.warn(
        "Tooltip parent handler: Request from unexpected origin. Expected:",
        scriptOrigin,
        "Received:",
        event.origin,
      );
      return;
    }

    if (!("messageId" in data) || typeof data.messageId !== "string") {
      console.error("Tooltip parent handler: Invalid messageId in request", data);
      return;
    }

    if (!event.source) {
      console.error("Tooltip parent handler: No source window available");
      return;
    }

    const iframes = document.querySelectorAll("iframe");
    let targetIframe: HTMLIFrameElement | undefined;

    for (const iframe of iframes) {
      if (iframe.contentWindow === event.source) {
        targetIframe = iframe;
        break;
      }
    }

    if (!targetIframe) {
      console.warn("Tooltip parent handler: Could not find iframe that sent the message");
      return;
    }

    const iframeRect = targetIframe.getBoundingClientRect();

    event.source.postMessage(
      {
        type: "tooltip_position_response",
        messageId: data.messageId,
        position: {
          viewportHeight: window.innerHeight,
          viewportWidth: window.innerWidth,
          iframeTop: iframeRect.top,
          iframeLeft: iframeRect.left,
        },
      },
      { targetOrigin: event.origin },
    );
  };

  window.addEventListener("message", handler);

  return () => {
    window.removeEventListener("message", handler);
  };
};
