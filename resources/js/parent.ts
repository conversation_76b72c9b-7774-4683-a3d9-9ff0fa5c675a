import { setupParentFlashHandler } from "./flash";
import { setupParentMessageHandler } from "./modal";
import { setupTooltipParentHandler } from "./tooltip";

window.addEventListener("message", (e: MessageEvent<unknown>) => {
  if (e.data !== null && typeof e.data === "object") {
    if ("frameHeight" in e.data) {
      const frameHeight = e.data.frameHeight;
      if (typeof frameHeight === "number") {
        const elem = document.getElementById("content-iframe");
        if (elem) {
          elem.style.height = frameHeight.toString() + "px";
        }
      }
    }
  }
});

setupParentMessageHandler();
setupTooltipParentHandler();
setupParentFlashHandler();
