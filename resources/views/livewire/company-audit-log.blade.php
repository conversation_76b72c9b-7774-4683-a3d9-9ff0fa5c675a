<div>
  @if ($auditLogs !== null && $auditLogs->count() > 0)
    <!-- Table -->
    <div class="relative -mx-4 px-4">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-blue-900">
          <thead>
            <tr>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.fields.changed_by') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.fields.target') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.fields.name') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.fields.field') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.values.old') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-left font-semibold">
                {{ __('companies.audit.values.new') }}
              </th>
              <th scope="col" class="whitespace-nowrap px-4 py-3 text-right font-semibold">
                {{ __('companies.audit.fields.change_time') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-blue-900 bg-white">
            @foreach ($auditLogs as $log)
              <tr wire:key="audit-{{ $loop->index }}">
                <td class="whitespace-nowrap px-4 py-3">
                  {{ $log->userEmail }}
                </td>
                <td class="whitespace-nowrap px-4 py-3">
                  {{ $log->modelType }}
                </td>
                <td class="px-4 py-3">
                  {{ $log->modelDescription }}
                </td>
                <td class="px-4 py-3">
                  {{ $log->fieldName }}
                </td>
                <td class="px-4 py-3">
                  {{ $log->oldValue }}
                </td>
                <td class="px-4 py-3">
                  {{ $log->newValue }}
                </td>
                <td class="whitespace-nowrap px-4 py-3 text-right">
                  {{ $log->changedAt }}
                </td>
              </tr>
            @endforeach
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
      {{ $auditLogs->links('components.custom-pagination') }}
    </div>
  @elseif ($auditLogs !== null)
    <div class="rounded-md bg-gray-50 p-4 text-center text-gray-500">
      {{ __('companies.audit.empty.no_history') }}
    </div>
  @endif
</div>
