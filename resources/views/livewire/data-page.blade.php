<div class="relative grid gap-9">
  <x-flash-message />

  @if ($company !== null)
    @if ($currentYear === null)
      <div class="py-12 text-center">
        <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Dataa ei voida näyttää') }}</p>
        <p class="text-gray-500">{{ __('Ota yhteyttä ylläpitoon.') }}</p>
      </div>
    @else
      <x-company-name name="{{ $company->name }}" />
      <x-year-selector :years="$allYears" :current-year="$currentYear" route-name="data" />

      <x-info-box>
        {{ __('Muista kirjata kaikki käyttämäsi tietolähteet, jotta laskenta on tarpeen mukaan toistettavissa / varmennettavissa!') }}
      </x-info-box>

      @if (count($metricDefinitions) > 0)
        <div>
          <div class="mb-10">
            <table class="mt-5 min-w-full divide-y divide-gray-200 border-b border-gray-200">
              <thead class="bg-blue-900 text-sm font-semibold text-white">
                <tr>
                  <th scope="col" class="w-96 px-2 py-1 text-left text-sm font-semibold">
                    {{ __('Taustatiedot') }}
                  </th>
                  @foreach ($tableYears as $year)
                    <th scope="col" class="w-36 px-2.5 py-1 text-right text-sm font-semibold">
                      {{ $year }}
                    </th>
                  @endforeach
                  <th scope="col" class="w-64 px-2 py-1 text-left text-sm font-semibold">
                    {{ __('Yksikkö') }}
                  </th>
                  <th scope="col" class="w-24 px-3 py-1 text-right text-sm font-semibold">
                    {{ __('Selite') }}
                  </th>
                  <th scope="col" class="w-24 px-2 py-1 text-right text-sm font-semibold">
                    {{ __('Ohje') }}
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y divide-gray-200 border-b border-gray-200 bg-white before:block before:h-1 before:content-['']">
                @foreach ($metricDefinitions as $definition)
                  <tr wire:key="metric-{{ $definition->id }}">
                    <td class="max-w-0 overflow-hidden px-5 py-1 text-base text-gray-800">
                      <div class="truncate" x-data="tooltip('{{ $definition->name }}')">
                        {{ $definition->name }}
                      </div>
                    </td>
                    @foreach ($tableYears as $year)
                      <td
                        class="whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                        @if ($year === $currentYear)
                          <input
                            wire:model.lazy="metricValues.{{ $definition->id }}.{{ $year }}"
                            wire:change="saveMetricField({{ $definition->id }})" type="number"
                            inputmode="decimal" step="any"
                            class="ml-auto block w-full rounded-sm border border-neutral-700 px-2 py-0 focus:border-blue-900 focus:ring-blue-900">
                          @if (isset($metricErrors[$definition->id]))
                            <p class="mt-1 text-right text-sm text-red-600">
                              {{ $metricErrors[$definition->id] }}
                            </p>
                          @endif
                        @else
                          @if (isset($metricValues[$definition->id][$year]) && $metricValues[$definition->id][$year] !== '')
                            {{ $metricValues[$definition->id][$year] }}
                          @endif
                        @endif
                      </td>
                    @endforeach
                    <td class="whitespace-nowrap px-3 py-1 text-base text-gray-800">
                      {{ $definition->unitSymbol }}
                    </td>
                    <td class="px-2 py-1 text-right">
                      <button
                        class="{{ isset($metricSources[$definition->id][$currentYear]) && $metricSources[$definition->id][$currentYear] !== '' ? 'text-blue-900' : 'text-pink-200' }} ml-auto flex items-center justify-center"
                        wire:click="openMetricSourceModal({{ $definition->id }}, {{ $currentYear }})"
                        title="{{ isset($metricSources[$definition->id][$currentYear]) && $metricSources[$definition->id][$currentYear] !== '' ? __('Muokkaa lähdetietoa') : __('Lisää lähdetieto') }}">
                        @if (isset($metricSources[$definition->id][$currentYear]) &&
                                $metricSources[$definition->id][$currentYear] !== '')
                          <x-flag-icon />
                        @else
                          <x-info-icon />
                        @endif
                      </button>
                    </td>
                    <td class="px-2 py-1 text-right text-blue-900">
                      @if (isset($metricHelpTexts[$definition->id]) && $metricHelpTexts[$definition->id] !== '')
                        <button class="ml-auto grid place-items-center" x-data="tooltip(@js($metricHelpTexts[$definition->id]))"
                          type="button" aria-label="{{ __('Näytä ohje') }}">
                          <x-help-icon />
                        </button>
                      @endif
                    </td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      @endif

      {{-- Calculation Definitions Section --}}
      @foreach ($availableScopes as $scope)
        @if (isset($scopeGroupings[$scope->id]))
          @foreach ($scopeGroupings[$scope->id] as $grouping)
            @if (isset($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id]))
              <div wire:key="scope-{{ $scope->id }}-grouping-{{ $grouping->id }}">
                <div class="flex items-center justify-between">
                  <x-scope-header>
                    {{ __('Scope :number: :title', ['number' => $scope->number, 'title' => $grouping->title]) }}
                  </x-scope-header>

                  @if ($scope->allowGroupingHiding)
                    <div class="pr-4">
                      <x-toggle
                        wire:click="toggleGrouping({{ $scope->id }}, {{ $grouping->id }})"
                        id="toggle-{{ $scope->id }}-{{ $grouping->id }}" :checked="$this->isGroupingHidden($scope->id, $grouping->id)"
                        :label="$this->isGroupingHidden($scope->id, $grouping->id)
                            ? __('Näytä')
                            : __('Piilota')" />
                    </div>
                  @endif
                </div>

                @if ($scope->allowGroupingHiding && $this->isGroupingHidden($scope->id, $grouping->id))
                  <div class="mb-8 mt-4">
                    <label for="reason-{{ $scope->id }}-{{ $grouping->id }}"
                      class="mb-2 block text-sm font-medium uppercase text-blue-900">
                      {{ __('Syy piilottamiselle') }}
                    </label>

                    @php
                      $key = $scope->id . '.' . $grouping->id;
                      $isCustom = $this->isUsingCustomReason($scope->id, $grouping->id);
                      $selectedOptionId = $this->getCurrentSelectedOptionId(
                          $scope->id,
                          $grouping->id,
                      );
                    @endphp

                    @if (!$isCustom)
                      <select id="reason-{{ $scope->id }}-{{ $grouping->id }}"
                        wire:change="updateReasonSelection({{ $scope->id }}, {{ $grouping->id }}, $event.target.value)"
                        class="block w-full border-blue-900 py-3 focus:border-blue-900 focus:ring-blue-900 sm:text-sm">
                        @foreach ($hidingReasonOptions as $option)
                          @php
                            $optionValue = $option->value;
                            $isSelected =
                                $selectedOptionId !== null && $selectedOptionId === $optionValue;
                          @endphp
                          <option value="{{ $option->value }}"
                            @if ($isSelected) selected @endif>
                            {{ __($option->label) }}
                          </option>
                        @endforeach
                        <option value="null" @if ($selectedOptionId === null) selected @endif>
                          {{ __('Muu syy') }}
                        </option>
                      </select>
                    @else
                      <div class="space-y-2">
                        <textarea id="custom-reason-{{ $scope->id }}-{{ $grouping->id }}"
                          wire:blur="saveCustomReason({{ $scope->id }}, {{ $grouping->id }}, $event.target.value)"
                          rows="2"
                          class="block w-full border-blue-900 py-3 focus:border-blue-900 focus:ring-blue-900 sm:text-sm"
                          placeholder="{{ __('Kirjoita oma syy...') }}">{{ $customReasons[$key] ?? '' }}</textarea>
                        <button type="button"
                          wire:click="switchToPredefinedReasons({{ $scope->id }}, {{ $grouping->id }})"
                          class="text-sm text-blue-900 underline hover:text-blue-950">
                          {{ __('Palaa valmiisiin syihin') }}
                        </button>
                      </div>
                    @endif
                  </div>
                @elseif (!$scope->allowGroupingHiding || !$this->isGroupingHidden($scope->id, $grouping->id))
                  @if (isset($scopeCategories[$scope->id]))
                    @foreach ($scopeCategories[$scope->id] as $category)
                      @if (isset($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id]) &&
                              count($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id]) > 0)
                        <div class="mb-10" wire:key="category-{{ $category->id }}">
                          <x-info>
                            {{ $category->description }}
                          </x-info>
                          <table
                            class="mt-5 min-w-full divide-y divide-gray-200 border-b border-gray-200">
                            <thead class="bg-blue-900 text-sm font-semibold text-white">
                              <tr>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-name"
                                  scope="col"
                                  class="w-96 px-2 py-1 text-left text-sm font-semibold">
                                  {{ $category->title }}</th>
                                @foreach ($tableYears as $year)
                                  <th
                                    id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-year-{{ $year }}"
                                    scope="col"
                                    class="w-36 px-2.5 py-1 text-right text-sm font-semibold">
                                    {{ $year }}
                                  </th>
                                @endforeach
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-unit"
                                  scope="col"
                                  class="w-64 px-2 py-1 text-left text-sm font-semibold">
                                  {{ __('Yksikkö') }}</th>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-source"
                                  scope="col"
                                  class="w-24 px-3 py-1 text-right text-sm font-semibold">
                                  {{ __('Selite') }}</th>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-help_text"
                                  scope="col"
                                  class="w-24 px-2 py-1 text-right text-sm font-semibold">
                                  {{ __('Ohje') }}</th>
                              </tr>
                            </thead>
                            <tbody
                              class="divide-y divide-gray-200 border-b border-gray-200 bg-white before:block before:h-1 before:content-['']">
                              @forelse ($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id] as $definition)
                                <tr wire:key="definition-{{ $definition->id }}">
                                  @if ($definition->companyId !== null)
                                    <td
                                      class="max-w-0 overflow-hidden px-0.5 py-1 text-base text-gray-800">
                                      <!-- Editable name field for company-specific definitions -->
                                      <div class="flex items-center gap-2">
                                        <div
                                          class="h-2.5 w-2.5 flex-shrink-0 rounded-full border border-pink-200 bg-pink-50">
                                        </div>
                                        <input wire:model="definitionNames.{{ $definition->id }}"
                                          wire:blur="updateDefinitionName({{ $definition->id }})"
                                          wire:keydown.enter="updateDefinitionName({{ $definition->id }})"
                                          type="text"
                                          aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-name"
                                          class="block w-full truncate border-none py-0 pl-0 pr-2 focus:border-blue-900 focus:ring-blue-900">
                                      </div>
                                    </td>
                                  @else
                                    <!-- Non-editable name for global definitions -->
                                    <td
                                      class="max-w-0 overflow-hidden px-5 py-1 text-base text-gray-800">
                                      <div class="truncate" x-data="tooltip('{{ $definition->dataName }}')">
                                        {{ $definition->dataName }}</div>
                                    </td>
                                  @endif
                                  @foreach ($tableYears as $year)
                                    <td
                                      class="whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                                      @if ($year === $currentYear)
                                        <input
                                          wire:model.lazy="values.{{ $definition->id }}.{{ $year }}"
                                          wire:change="saveField({{ $definition->id }})"
                                          type="number" inputmode="decimal" step="any"
                                          class="ml-auto block w-full rounded-sm border border-neutral-700 px-2 py-0 focus:border-blue-900 focus:ring-blue-900"
                                          aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-year-{{ $year }}">
                                        @if (isset($errors[$definition->id]))
                                          <p class="mt-1 text-right text-sm text-red-600">
                                            {{ $errors[$definition->id] }}
                                          </p>
                                        @endif
                                      @else
                                        @if (isset($values[$definition->id][$year]) && $values[$definition->id][$year] !== '')
                                          {{ $values[$definition->id][$year] }}
                                        @endif
                                      @endif
                                    </td>
                                  @endforeach
                                  <td class="whitespace-nowrap px-3 py-1 text-base text-gray-800">
                                    @if ($definition->companyId !== null)
                                      <!-- Editable unit field for company-specific definitions -->
                                      <select wire:model="definitionUnitIds.{{ $definition->id }}"
                                        wire:change="updateDefinitionUnit({{ $definition->id }})"
                                        aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-unit"
                                        class="block w-full rounded-sm border-none p-0 focus:border-blue-900 focus:ring-blue-900">
                                        @foreach ($units as $unit)
                                          <option value="{{ $unit->id }}">
                                            {{ $unit->symbol }}
                                          </option>
                                        @endforeach
                                      </select>
                                    @else
                                      <!-- Non-editable unit for global definitions -->
                                      {{ $definition->dataUnitSymbol }}
                                    @endif
                                  </td>
                                  <td class="px-2 py-1 text-right">
                                    <button
                                      class="{{ isset($sources[$definition->id][$currentYear]) && $sources[$definition->id][$currentYear] !== '' ? 'text-blue-900' : 'text-pink-200' }} ml-auto flex items-center justify-center"
                                      wire:click="openSourceModal({{ $definition->id }}, {{ $currentYear }})"
                                      data-definition-id="{{ $definition->id }}"
                                      title="{{ isset($sources[$definition->id][$currentYear]) && $sources[$definition->id][$currentYear] !== '' ? __('Muokkaa lähdetietoa') : __('Lisää lähdetieto') }}">
                                      @if (isset($sources[$definition->id][$currentYear]) &&
                                              $sources[$definition->id][$currentYear] !== '')
                                        <x-flag-icon />
                                      @else
                                        <x-info-icon />
                                      @endif
                                    </button>
                                  </td>
                                  <td class="px-2 py-1 text-right text-blue-900">
                                    @php
                                      $helpText = $this->getHelpText($definition->id, $currentYear);
                                    @endphp
                                    @if ($definition->companyId !== null)
                                      <button wire:click="openDeleteModal({{ $definition->id }})"
                                        class="ml-auto grid place-items-center text-red-600 hover:text-red-800"
                                        type="button" aria-label="{{ __('Poista rivi') }}">
                                        <x-trash-icon />
                                      </button>
                                    @elseif ($helpText !== null && $helpText !== '')
                                      <button class="ml-auto grid place-items-center"
                                        x-data="tooltip(@js($helpText))" type="button"
                                        aria-label="{{ __('Näytä ohje') }}">
                                        <x-help-icon />
                                      </button>
                                    @endif
                                  </td>
                                </tr>
                              @endforeach
                            </tbody>
                          </table>

                          <div class="mt-4 flex justify-between pr-6 text-blue-900">
                            <p class="text-xs">
                              {{ __('Jatka listaa tarpeen mukaan.') }}
                            </p>
                            <x-add-row-button
                              wire:click="addNewRow({{ $scope->id }}, {{ $grouping->id }}, {{ $category->id }})"
                              type="button">
                              {{ __('+ Lisää rivi') }}
                            </x-add-row-button>
                          </div>
                        </div>
                      @endif
                    @endforeach
                  @endif
                @endif
              </div>
            @endif
          @endforeach
        @endif
      @endforeach

      <x-modal :show="$isSourceModalOpen" onBackdropClick="closeSourceModal">
        <div class="absolute right-4 top-4">
          <button wire:click="closeSourceModal" type="button"
            class="text-blue-900 hover:text-blue-950">
            <x-close-icon />
          </button>
        </div>

        <x-slot:header>
          {{ __('Lisää selite') }}
        </x-slot:header>
        <div class="grid gap-4">
          <div class="grid">
            <span class="text-sm font-medium uppercase text-blue-900">{{ __('Data') }}</span>
            <span
              class="text-base text-gray-800">{{ $sourceDefinitionName ?? __('Tuntematon määrittely') }}</span>
          </div>

          <div class="flex gap-2">
            <div class="grow">
              <x-form-input wire:model="sourceText" id="sourceText" label="{{ __('Selite') }}"
                placeholder="{{ __('Esim. Yrityksen HR') }}"></x-form-input>
            </div>
            <div class="flex items-end">
              <x-button wire:click="saveSource"
                class="px-14 py-3 uppercase">{{ __('Tallenna') }}</x-button>
            </div>
          </div>
        </div>
      </x-modal>
    @endif
  @else
    <div class="py-12 text-center">
      <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Yritystä ei ole valittu') }}
      </p>
      <p class="text-gray-500">{{ __('Valitse yritys Yritys-sivulta jatkaaksesi.') }}</p>
      <a href="{{ route('company') }}"
        class="mt-6 inline-block font-semibold text-blue-900 hover:text-blue-950">
        {{ __('Siirry Yritys-sivulle') }} →
      </a>
    </div>
  @endif

  <x-modal :show="$showDeleteModal" onBackdropClick="closeDeleteModal">
    <div class="absolute right-4 top-4">
      <button wire:click="closeDeleteModal" type="button"
        class="text-blue-900 hover:text-blue-950">
        <x-close-icon />
      </button>
    </div>

    <div class="px-16 py-1 text-center">
      <div class="mx-auto mb-6 mt-2 flex items-center justify-center text-blue-900">
        <x-triangle-icon />
      </div>

      @if (count($linkedDefinitionNames) > 0)
        <h3 class="mb-6 px-24 text-3xl font-semibold leading-9 text-blue-900">
          {{ __('Oletko varma, että haluat poistaa nämä linkitetyt rivit?') }}
        </h3>

        <p class="mb-3 text-base italic">
          {{ $definitionToDeleteName }}
        </p>

        <p class="mb-3 text-sm text-gray-600">
          {{ __('Seuraavat linkitetyt rivit poistetaan myös:') }}
        </p>

        <ul class="mb-6 text-base italic">
          @foreach ($linkedDefinitionNames as $linkedName)
            <li>{{ $linkedName }}</li>
          @endforeach
        </ul>

        <p class="mb-6 text-sm text-gray-600">
          {{ __('Yhteensä :count riviä poistetaan', ['count' => count($linkedDefinitionNames) + 1]) }}
        </p>
      @else
        <h3 class="mb-6 px-24 text-3xl font-semibold leading-9 text-blue-900">
          {{ __('Oletko varma, että haluat poistaa tämän rivin?') }}
        </h3>

        <p class="mb-6 text-base italic">
          {{ $definitionToDeleteName }}
        </p>
      @endif

      <div class="grid gap-6 divide-y divide-gray-500">
        <div class="grid">
          <x-button wire:click="confirmDeleteDefinition" class="mx-auto px-9 uppercase">
            @if (count($linkedDefinitionNames) > 0)
              {{ __('Poista kaikki rivit') }}
            @else
              {{ __('Poista rivi') }}
            @endif
          </x-button>
        </div>

        <div class="pt-6">
          <button wire:click="closeDeleteModal" type="button"
            class="flex w-full items-center justify-center gap-4 text-base font-semibold uppercase leading-5 tracking-wide text-blue-900 hover:text-blue-950">
            <x-back-arrow-icon />
            {{ __('Palaa takaisin') }}
          </button>
        </div>
      </div>
    </div>
  </x-modal>
</div>
