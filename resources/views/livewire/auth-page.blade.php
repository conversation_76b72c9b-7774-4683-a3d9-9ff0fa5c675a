<div class="grid h-full place-items-center">
  <div class="w-full max-w-3xl">
    <div class="bg-sky-100 px-14 py-8">
      <h1 class="mb-6 text-3xl font-semibold leading-8 tracking-wide text-blue-900">
        {{ __('auth.title') }}</h1>

      <p class="mb-8 text-lg font-normal leading-7 text-blue-900">
        {{ __('auth.instructions') }}
      </p>

      @if ($errorMessage !== '')
        <div class="mb-4 border border-red-300 bg-white px-4 py-3 text-red-600">
          {{ $errorMessage }}
        </div>
      @endif

      @if ($successMessage !== '')
        <div class="mb-4 border border-green-300 bg-green-100 px-4 py-3 text-green-800">
          {{ $successMessage }}
        </div>

        <div class="mt-6 text-sm text-blue-900">
          <p class="mb-2"><strong>{{ __('auth.help.link_not_received') }}</strong>
            {{ __('auth.help.check_spam') }}</p>
          <p><strong>{{ __('auth.help.problems') }}</strong>
            {{ __('auth.help.contact_support') }}</p>
        </div>

        <div class="mt-6 text-center">
          <button wire:click="resetForm" type="button"
            class="inline-flex justify-center border border-transparent bg-white px-4 py-2 text-sm font-medium text-blue-900 hover:bg-sky-50 focus:outline-none">
            {{ __('common.actions.try_again') }}
          </button>
        </div>
      @else
        <form wire:submit.prevent="authenticate">
          <x-form-input id="email" label="{{ __('auth.fields.email') }}"
            placeholder="{{ __('auth.placeholders.email') }}" wire:model="email"
            :error="$errors->first('email')" />

          <div class="mt-2">
            <button type="submit"
              class="flex w-full justify-center border border-transparent bg-blue-900 px-4 py-2.5 text-base font-semibold uppercase leading-5 tracking-wide text-white hover:bg-blue-950 focus:outline-none">
              {{ __('auth.actions.login') }}
            </button>
          </div>
        </form>

        <div class="mt-6 text-sm text-blue-900">
          <p>
            {{ __('auth.messages.check_email') }}
          </p>
        </div>
      @endif
    </div>
  </div>
</div>
