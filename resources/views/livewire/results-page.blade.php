@php
  use Brick\Math\BigDecimal;
@endphp

<div class="relative grid gap-3">
  <x-flash-message />

  @if ($company !== null)
    @if ($currentYear === null)
      <div class="py-12 text-center">
        <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Dataa ei voida näyttää') }}</p>
        <p class="text-gray-500">{{ __('Ota yhteyttä ylläpitoon.') }}</p>
      </div>
    @else
      <div class="grid gap-3 divide-y divide-blue-900">
        <div class="grid grid-cols-2">
          <x-company-name name="{{ $company->name }}" />

          <x-download-excel class="justify-self-end" :year-id="$yearId" />
        </div>
        <div class="pt-6">
          <x-year-selector :years="$allYears" :current-year="$currentYear" route-name="results" />

          <div class="mt-16 grid gap-5">
            <h3 class="text-3xl font-semibold leading-10 text-blue-900">
              {{ __('Organisaation hiilijalanjälki (:unit)', ['unit' => $this->getEmissionUnitSymbol()]) }}
            </h3>

            <x-info>
              {{ __('Huom! Kaikki päästöt on ilmoitettu yksikössä :unit.', ['unit' => $this->getEmissionUnitSymbol()]) }}
            </x-info>

            <div id="total-emissions-chart" class="mt-4">
              <livewire:carbon-footprint-chart :years="$allYears" :scopeResults="$scopeTotals" />
            </div>

            <div class="mt-24">
              <h3 class="mb-3 text-3xl font-semibold leading-10 text-blue-900">
                {{ __('Organisaation hiilijalanjälki') }}
              </h3>

              <div class="bg-white">
                <table class="min-w-full">
                  <tbody class="divide-y divide-gray-200">
                    @foreach ($scopeVariantTotals as $variant)
                      <tr>
                        <td class="w-1/2 px-3 py-2 text-base">
                          {{ $variant->label }}
                        </td>
                        <td class="w-1/4 px-3 py-2 text-right text-base">
                          @php
                            $rawTotal = $variant->totals[$currentYear] ?? '0';
                            $formatted = $this->formatValue($rawTotal);
                          @endphp
                          @if ($formatted === '—')
                            <span class="text-gray-400">{{ $formatted }}</span>
                          @else
                            {{ $formatted }}
                          @endif
                        </td>
                        <td class="w-1/4 px-3 py-2 text-right text-sm">
                          {{ $this->getEmissionUnitSymbol() }}
                        </td>
                      </tr>
                    @endforeach

                    <tr class="font-semibold">
                      <td class="px-3 py-2 text-base">
                        {{ __('Hiilijalanjälki yhteensä') }}
                      </td>
                      <td class="px-3 py-2 text-right text-base">
                        @if ($formattedCurrentYearTotal === '—')
                          <span
                            class="font-normal text-gray-400">{{ $formattedCurrentYearTotal }}</span>
                        @else
                          {{ $formattedCurrentYearTotal }}
                        @endif
                      </td>
                      <td class="px-3 py-2 text-right text-sm font-normal">
                        {{ $this->getEmissionUnitSymbol() }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <h3 class="mt-24 text-3xl font-semibold leading-10 text-blue-900">
              {{ __('Organisaation hiilijalanjälki kategorioittain') }}</h3>

            <div id="grouping-emissions-chart" class="mt-4">
              <livewire:carbon-footprint-donut-chart :year="$currentYear" :groupingData="$groupingTotals"
                :unitSymbol="$this->getEmissionUnitSymbol()" />
            </div>
          </div>

          <div class="mb-24 mt-24">
            <table class="mt-5 min-w-full divide-y divide-gray-200 border-b border-gray-200">
              <thead class="bg-blue-900 text-sm font-semibold text-white">
                <tr>
                  <th id="metric-col-name" scope="col"
                    class="w-96 px-2 py-1 text-left text-sm font-semibold">
                    {{ __('Taustatiedot') }}</th>

                  @foreach ($tableYears as $year)
                    <th id="metric-col-year-{{ $year }}" scope="col"
                      class="w-36 px-2.5 py-1 text-right text-sm font-semibold">
                      {{ $year }}
                    </th>
                  @endforeach

                  <th id="metric-col-help_text" scope="col"
                    class="w-24 px-2 py-1 pr-7 text-right text-sm font-semibold">
                    {{ __('Ohje') }}</th>
                </tr>
              </thead>
              <tbody
                class="divide-y divide-gray-200 border-b border-gray-200 bg-white before:block before:h-1 before:content-['']">
                @foreach ($metricDefinitions as $definition)
                  <tr wire:key="metric-{{ $definition->id }}">
                    <td class="max-w-0 overflow-hidden px-5 py-1 text-base text-gray-800">
                      <div class="truncate" x-data="tooltip('{{ $definition->name }}')">
                        {{ $definition->name }}</div>
                    </td>

                    @foreach ($tableYears as $year)
                      @php
                        $result = $metricValues[$definition->id][$year] ?? null;
                      @endphp

                      <td
                        class="whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                        @if ($result === null)
                          <span class="text-gray-400">—</span>
                        @else
                          <span>{{ $result }}</span>
                        @endif
                      </td>
                    @endforeach

                    <td class="px-2 py-1 pr-7 text-right text-blue-900">
                      @php
                        $helpText = $metricHelpTexts[$definition->id] ?? null;
                      @endphp
                      @if ($helpText !== null && $helpText !== '')
                        <button class="ml-auto grid place-items-center" x-data="tooltip(@js($helpText))"
                          type="button" aria-label="{{ __('Näytä ohje') }}">
                          <x-help-icon />
                        </button>
                      @endif
                    </td>
                  </tr>
                @endforeach
              </tbody>
            </table>

          </div>
          @foreach ($scopedGroupedDefinitions as $scopeId => $groupingDefinitions)
            @php
              $scopeDefinition = $definitions->first(fn($def) => $def->scope_id === $scopeId);
              if ($scopeDefinition === null || $scopeDefinition->scope === null) {
                  continue;
              }
              $scope = $scopeDefinition->scope;
            @endphp

            @foreach ($groupingDefinitions as $groupingId => $categoryDefinitions)
              @php
                $groupingDefinition = $definitions->first(
                    fn($def) => $def->grouping_id === $groupingId,
                );
                if ($groupingDefinition === null || $groupingDefinition->grouping === null) {
                    continue;
                }
                $grouping = $groupingDefinition->grouping;
              @endphp

              <div class="mb-10">
                @if (($scopeAllowsHiding[$scope->id] ?? false) && $this->isGroupingHidden($scope->id, $grouping->id))
                  <x-scope-header>
                    {{ __('Scope :number: :title', ['number' => $scope->number, 'title' => $grouping->translate()->title ?? '']) }}
                  </x-scope-header>
                  <div class="mt-2 text-sm">
                    @php
                      $reason = $this->getHidingReason($scope->id, $grouping->id);
                    @endphp
                    @if ($reason !== '')
                      {!! __('Tämä osio on piilotettu — <span class="italic">:reason</span>', [
                          'reason' => e($reason),
                      ]) !!}
                    @else
                      {{ __('Tämä osio on piilotettu') }}
                    @endif
                  </div>
                @else
                  <x-scope-header>
                    {{ __('Scope :number: :title', ['number' => $scope->number, 'title' => $grouping->translate()->title ?? '']) }}
                  </x-scope-header>

                  @foreach ($categoryDefinitions as $categoryId => $categoryItems)
                    @php
                      $category = $categoryItems->first()?->category;
                      if ($category === null) {
                          continue;
                      }
                    @endphp

                    <div class="mb-10">
                      <x-info>
                        {{ $category->translate()?->description }}
                      </x-info>
                      <table
                        class="mt-5 min-w-full divide-y divide-gray-200 border-b border-gray-200">
                        <thead class="bg-blue-900 text-sm font-semibold text-white">
                          <tr>
                            <th
                              id="{{ $scope->number . '-' . $grouping->translate()?->title . '-' . $category->translate()?->title }}-col-name"
                              scope="col" class="w-96 px-2 py-1 text-left text-sm font-semibold">
                              {{ $category->translate()?->title }}</th>

                            @foreach ($tableYears as $year)
                              <th
                                id="{{ $scope->number . '-' . $grouping->translate()?->title . '-' . $category->translate()?->title }}-col-year-{{ $year }}"
                                scope="col"
                                class="w-36 px-2.5 py-1 text-right text-sm font-semibold">
                                {{ $year }}
                              </th>
                            @endforeach

                            <th
                              id="{{ $scope->number . '-' . $grouping->translate()?->title . '-' . $category->translate()?->title }}-col-help_text"
                              scope="col"
                              class="w-24 px-2 py-1 pr-7 text-right text-sm font-semibold">
                              {{ __('Ohje') }}</th>
                          </tr>
                        </thead>
                        <tbody
                          class="divide-y divide-gray-200 border-b border-gray-200 bg-white before:block before:h-1 before:content-['']">
                          @foreach ($categoryItems as $item)
                            <tr>
                              @if ($item->company_id !== null)
                                <td
                                  class="max-w-0 overflow-hidden px-0.5 py-1 text-base text-gray-800">
                                  <div class="flex items-center gap-2">
                                    <div
                                      class="h-2.5 w-2.5 flex-shrink-0 rounded-full border border-pink-200 bg-pink-50">
                                    </div>
                                    <div class="truncate" x-data="tooltip(@js($item->custom_name))">
                                      {{ $item->custom_name }}</div>
                                  </div>
                                </td>
                              @else
                                <td
                                  class="max-w-0 overflow-hidden px-5 py-1 text-base text-gray-800">
                                  <div class="truncate" x-data="tooltip('{{ $item->translate()?->result_name }}')">
                                    {{ $item->translate()?->result_name }}</div>
                                </td>
                              @endif

                              @foreach ($tableYears as $year)
                                @php
                                  $result = $allResults[$year][$item->id] ?? null;
                                @endphp

                                <td
                                  class="whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                                  @if ($result instanceof \App\DataTransferObjects\Success)
                                    @php
                                      $formattedValue = $this->formatValue($result->value);
                                    @endphp
                                    @if ($formattedValue === '—')
                                      <span class="text-gray-400">{{ $formattedValue }}</span>
                                    @else
                                      <span>{{ $formattedValue }}</span>
                                    @endif
                                  @elseif($result instanceof \App\DataTransferObjects\Failure)
                                    <span class="cursor-help text-red-500"
                                      x-data="tooltip('{{ $result->error }}')">{{ __('Virhe') }}</span>
                                  @else
                                    <span class="text-gray-400">—</span>
                                  @endif
                                </td>
                              @endforeach

                              <td class="px-2 py-1 pr-7 text-right text-blue-900">
                                @php
                                  $helpText = $helpTexts[$item->id][$currentYear] ?? null;
                                @endphp
                                @if ($helpText !== null && $helpText !== '')
                                  <button class="ml-auto grid place-items-center"
                                    x-data="tooltip(@js($helpText))" type="button"
                                    aria-label="{{ __('Näytä ohje') }}">
                                    <x-help-icon />
                                  </button>
                                @endif
                              </td>
                            </tr>
                          @endforeach

                          @if ($categoryItems->isEmpty())
                            <tr>
                              <td colspan="{{ count($tableYears) + 2 }}"
                                class="whitespace-nowrap text-center text-sm font-medium">
                                {{ __('Tuloksia ei löytynyt tälle kategorialle.') }}
                              </td>
                            </tr>
                          @endif
                        </tbody>
                      </table>
                      @if (!$this->shouldHideCategoryTotal($categoryId))
                        <table class="min-w-full border-t border-gray-200">
                          <tbody class="bg-white">
                            <tr>
                              <td class="w-96 px-5 py-1 text-base text-gray-800">
                                <div class="truncate">
                                  <span class="font-bold">{{ __('Yhteensä') }}</span>
                                </div>
                              </td>
                              @foreach ($tableYears as $year)
                                @php
                                  $categoryTotal = BigDecimal::zero();
                                  foreach ($categoryItems as $item) {
                                      $result = $allResults[$year][$item->id] ?? null;
                                      if ($result instanceof \App\DataTransferObjects\Success) {
                                          $resultValue = BigDecimal::of($result->value);
                                          if (!$resultValue->isZero()) {
                                              $categoryTotal = $categoryTotal->plus($resultValue);
                                          }
                                      }
                                  }
                                  $categoryTotalString = (string) $categoryTotal;
                                  $formattedTotal = $this->formatValue($categoryTotalString);
                                @endphp
                                <td
                                  class="w-36 whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                                  @if ($formattedTotal === '—')
                                    <span class="text-gray-400">{{ $formattedTotal }}</span>
                                  @else
                                    <span class="font-bold">{{ $formattedTotal }}</span>
                                  @endif
                                </td>
                              @endforeach
                              <td class="w-24 px-2 py-1 pr-7"></td>
                            </tr>
                          </tbody>
                        </table>
                      @endif
                    </div>
                  @endforeach
                @endif
              </div>
            @endforeach
          @endforeach

          @if (count($scopedGroupedDefinitions) === 0)
            <div class="rounded bg-white p-4 shadow">
              <p class="text-center text-gray-500">
                {{ __('Hiilijalanjälkituloksia ei löytynyt.') }}
              </p>
            </div>
          @endif
        </div>
      </div>
    @endif
  @else
    <div class="py-12 text-center">
      <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Yritystä ei ole valittu') }}</p>
      <p class="text-gray-500">{{ __('Valitse yritys Yritys-sivulta jatkaaksesi.') }}</p>
      <a href="{{ route('company') }}"
        class="mt-6 inline-block font-semibold text-blue-900 hover:text-blue-950">
        {{ __('Siirry Yritys-sivulle') }} →
      </a>
    </div>
  @endif
</div>
