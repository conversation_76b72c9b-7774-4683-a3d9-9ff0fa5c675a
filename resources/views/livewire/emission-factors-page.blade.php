<div class="relative grid gap-9">
  <x-flash-message />

  @if ($company !== null)
    @if ($currentYear === null)
      <div class="py-12 text-center">
        <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Dataa ei voida näyttää') }}</p>
        <p class="text-gray-500">{{ __('Ota yhteyttä ylläpitoon.') }}</p>
      </div>
    @else
      <x-company-name name="{{ $company->name }}" />
      <x-year-selector :years="$allYears" :current-year="$currentYear" route-name="emission-factors" />

      <x-info-box>
        {{ __('Muista kirjata kaikki käyttämäsi tietolähteet, jotta laskenta on tarpeen mukaan toistettavissa / varmennettavissa!') }}
      </x-info-box>

      @foreach ($availableScopes as $scope)
        @if (isset($scopeGroupings[$scope->id]))
          @foreach ($scopeGroupings[$scope->id] as $grouping)
            @if (isset($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id]))
              <div wire:key="scope-{{ $scope->id }}-grouping-{{ $grouping->id }}">
                <x-scope-header>
                  {{ __('Scope :number: :title', ['number' => $scope->number, 'title' => $grouping->title]) }}
                </x-scope-header>

                @if ($scope->allowGroupingHiding && $this->isGroupingHidden($scope->id, $grouping->id))
                  <div class="mt-2 text-sm">
                    @php
                      $reason = $this->getHidingReason($scope->id, $grouping->id);
                    @endphp
                    @if ($reason !== '')
                      {!! __('Tämä osio on piilotettu — <span class="italic">:reason</span>', [
                          'reason' => e($reason),
                      ]) !!}
                    @else
                      {{ __('Tämä osio on piilotettu') }}
                    @endif
                  </div>
                @else
                  @if (isset($scopeCategories[$scope->id]))
                    @foreach ($scopeCategories[$scope->id] as $category)
                      @if (isset($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id]) &&
                              count($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id]) > 0)
                        <div class="mb-10" wire:key="category-{{ $category->id }}">
                          <x-info>
                            {{ $category->description }}
                          </x-info>
                          <table
                            class="mt-5 min-w-full divide-y divide-gray-200 border-b border-gray-200">
                            <thead class="bg-blue-900 text-sm font-semibold text-white">
                              <tr>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-name"
                                  scope="col"
                                  class="w-96 px-2 py-1 text-left text-sm font-semibold">
                                  {{ $category->title }}</th>
                                @foreach ($tableYears as $year)
                                  <th
                                    id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-year-{{ $year }}"
                                    scope="col"
                                    class="w-36 px-2.5 py-1 text-right text-sm font-semibold">
                                    {{ $year }}
                                  </th>
                                @endforeach
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-unit"
                                  scope="col"
                                  class="w-64 px-2 py-1 text-left text-sm font-semibold">
                                  {{ __('Yksikkö') }}</th>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-source"
                                  scope="col"
                                  class="w-24 px-3 py-1 text-right text-sm font-semibold">
                                  {{ __('Selite') }}</th>
                                <th
                                  id="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-help_text"
                                  scope="col"
                                  class="w-24 px-2 py-1 text-right text-sm font-semibold">
                                  {{ __('Ohje') }}</th>
                              </tr>
                            </thead>
                            <tbody
                              class="divide-y divide-gray-200 border-b border-gray-200 bg-white before:block before:h-1 before:content-['']">
                              @forelse ($scopedGroupedCategorizedDefinitions[$scope->id][$grouping->id][$category->id] as $definition)
                                <tr wire:key="definition-{{ $definition->id }}">
                                  @if ($definition->companyId !== null)
                                    <td
                                      class="max-w-0 overflow-hidden px-0.5 py-1 text-base text-gray-800">
                                      <!-- Editable name field for company-specific definitions -->
                                      <div class="flex items-center gap-2">
                                        <div
                                          class="h-2.5 w-2.5 flex-shrink-0 rounded-full border border-pink-200 bg-pink-50">
                                        </div>
                                        <input wire:model="definitionNames.{{ $definition->id }}"
                                          wire:blur="updateDefinitionName({{ $definition->id }})"
                                          wire:keydown.enter="updateDefinitionName({{ $definition->id }})"
                                          type="text"
                                          aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-name"
                                          class="block w-full truncate border-none py-0 pl-0 pr-2 focus:border-blue-900 focus:ring-blue-900">
                                      </div>
                                    </td>
                                  @else
                                    <!-- Non-editable name for global definitions -->
                                    <td
                                      class="max-w-0 overflow-hidden px-5 py-1 text-base text-gray-800">
                                      <div class="truncate" x-data="tooltip('{{ $definition->emissionFactorName ?? '' }}')">
                                        {{ $definition->emissionFactorName ?? '' }}</div>
                                    </td>
                                  @endif
                                  @foreach ($tableYears as $year)
                                    <td
                                      class="whitespace-nowrap px-2.5 py-0.5 text-right text-base text-gray-800">
                                      @if ($year === $currentYear)
                                        @if ($definition->inputMethod === App\Enums\InputMethod::MANUAL)
                                          @php
                                            $defaultValue =
                                                $definitionDefaultValues[$definition->id] ?? '';
                                            $placeholderValue =
                                                $defaultValue !== '' ? $defaultValue : '';
                                          @endphp
                                          <input
                                            value="{{ $values[$definition->id][$year] ?? '' }}"
                                            placeholder="{{ $placeholderValue }}"
                                            wire:change="saveField({{ $definition->id }}, $event.target.value)"
                                            type="number" inputmode="decimal" step="any"
                                            class="ml-auto block w-full rounded-sm border border-neutral-700 px-2 py-0 text-blue-900 focus:border-blue-900 focus:ring-blue-900"
                                            aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-year-{{ $year }}">
                                        @elseif ($definition->inputMethod === App\Enums\InputMethod::SELECT)
                                          <select
                                            wire:change="saveField({{ $definition->id }}, $event.target.value)"
                                            class="ml-auto block w-full rounded-sm border border-neutral-700 px-2 py-0 focus:border-blue-900 focus:ring-blue-900"
                                            aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-year-{{ $year }}">
                                            <option value="">{{ __('Valitse') }}</option>
                                            @foreach ($definition->options as $option)
                                              @php
                                                $optionValue = $option->yearValues[$year] ?? null;
                                                $currentValue =
                                                    $values[$definition->id][$year] ?? null;
                                              @endphp
                                              @if ($optionValue !== null)
                                                <option value="{{ $option->id }}"
                                                  @if ($currentValue !== null && ((int) $currentValue) === $option->id) selected @endif>
                                                  {{ $option->label }}
                                                  ({{ $optionValue }})
                                                </option>
                                              @endif
                                            @endforeach
                                          </select>
                                        @endif
                                        @if (isset($errors[$definition->id]))
                                          <p class="mt-1 text-right text-sm text-red-600">
                                            {{ $errors[$definition->id] }}
                                          </p>
                                        @endif
                                      @else
                                        @if (isset($values[$definition->id][$year]) && $values[$definition->id][$year] !== '')
                                          @php
                                            $displayValue = $values[$definition->id][$year];
                                            // For SELECT inputs, show the label instead of the ID
                                            if (
                                                $definition->inputMethod ===
                                                App\Enums\InputMethod::SELECT
                                            ) {
                                                foreach ($definition->options as $option) {
                                                    if ($option->id === (int) $displayValue) {
                                                        $displayValue = $option->label;
                                                        break;
                                                    }
                                                }
                                            }
                                          @endphp
                                          {{ $displayValue }}
                                        @endif
                                      @endif
                                    </td>
                                  @endforeach
                                  <td class="whitespace-nowrap px-3 py-1 text-base text-gray-800">
                                    @if ($definition->companyId !== null)
                                      <!-- Editable unit field for company-specific definitions -->
                                      <select
                                        wire:model="definitionCompoundUnitIds.{{ $definition->id }}"
                                        wire:change="updateDefinitionUnit({{ $definition->id }})"
                                        aria-labelledby="{{ $scope->number . '-' . $grouping->title . '-' . $category->title }}-col-unit"
                                        class="block w-full rounded-sm border-none p-0 focus:border-blue-900 focus:ring-blue-900">
                                        @foreach ($compoundUnits as $unit)
                                          <option value="{{ $unit->id }}">
                                            {{ $unit->formatted }}
                                          </option>
                                        @endforeach
                                      </select>
                                    @else
                                      <!-- Non-editable unit for global definitions -->
                                      {{ $definition->emissionFactorCompoundUnitFormatted ?? '' }}
                                    @endif
                                  </td>
                                  <td class="px-2 py-1 text-right">
                                    <button
                                      class="{{ isset($sources[$definition->id][$currentYear]) && $sources[$definition->id][$currentYear] !== '' ? 'text-blue-900' : 'text-pink-200' }} ml-auto flex items-center justify-center"
                                      wire:click="openSourceModal({{ $definition->id }}, {{ $currentYear }})"
                                      data-definition-id="{{ $definition->id }}"
                                      title="{{ isset($sources[$definition->id][$currentYear]) && $sources[$definition->id][$currentYear] !== '' ? __('Muokkaa lähdetietoa') : __('Lisää lähdetieto') }}">
                                      @if (isset($sources[$definition->id][$currentYear]) &&
                                              $sources[$definition->id][$currentYear] !== '')
                                        <x-flag-icon />
                                      @else
                                        <x-info-icon />
                                      @endif
                                    </button>
                                  </td>
                                  <td class="px-2 py-1 text-right text-blue-900">
                                    @php
                                      $helpText = $this->getHelpText($definition->id, $currentYear);
                                    @endphp
                                    @if ($definition->companyId === null && $helpText !== null && $helpText !== '')
                                      <button class="ml-auto grid place-items-center"
                                        x-data="tooltip(@js($helpText))" type="button"
                                        aria-label="{{ __('Näytä ohje') }}">
                                        <x-help-icon />
                                      </button>
                                    @endif
                                  </td>
                                </tr>
                              @endforeach
                            </tbody>
                          </table>
                        </div>
                      @endif
                    @endforeach
                  @endif
                @endif
              </div>
            @endif
          @endforeach
        @endif
      @endforeach

      @if (count($availableScopes) === 0 || count($scopedGroupedCategorizedDefinitions) === 0)
        <div class="rounded bg-white p-4 shadow">
          <p class="text-center text-gray-500">{{ __('Päästökerroinmäärittelyjä ei löytynyt.') }}
          </p>
        </div>
      @endif

      <x-modal :show="$isSourceModalOpen" onBackdropClick="closeSourceModal">
        <div class="absolute right-4 top-4">
          <button wire:click="closeSourceModal" type="button"
            class="text-blue-900 hover:text-blue-950">
            <x-close-icon />
          </button>
        </div>

        <x-slot:header>
          {{ __('Lisää selite') }}
        </x-slot:header>
        <div class="grid gap-4">
          <div class="grid">
            <span
              class="text-sm font-medium uppercase text-blue-900">{{ __('Päästökerroin') }}</span>
            <span
              class="text-base text-gray-800">{{ $sourceDefinitionName ?? __('Tuntematon määrittely') }}</span>
          </div>

          <div class="flex gap-2">
            <div class="grow">
              <x-form-textarea wire:model="sourceText" id="sourceText" label="{{ __('Selite') }}"
                placeholder="{{ $sourceDefaultText !== '' ? $sourceDefaultText : (string) __('Kirjoita lähde tähän (esim. DEFRA 2025)') }}"
                :rows="1">
              </x-form-textarea>
            </div>
            <div class="flex items-end">
              <x-button wire:click="saveSource"
                class="px-14 py-3 uppercase">{{ __('Tallenna') }}</x-button>
            </div>
          </div>
        </div>
      </x-modal>
    @endif
  @else
    <div class="py-12 text-center">
      <p class="mb-4 text-2xl font-semibold text-gray-700">{{ __('Yritystä ei ole valittu') }}</p>
      <p class="text-gray-500">{{ __('Valitse yritys Yritys-sivulta jatkaaksesi.') }}</p>
      <a href="{{ route('company') }}"
        class="mt-6 inline-block font-semibold text-blue-900 hover:text-blue-950">
        {{ __('Siirry Yritys-sivulle') }} →
      </a>
    </div>
  @endif
</div>
