<div class="grid h-screen place-items-center bg-gray-50">
  <div class="w-full max-w-md">
    <div class="bg-white px-6 py-8 shadow-sm sm:px-10">
      @if (!$actionTaken && $isValid)
        {{-- Show invitation details and accept/decline buttons --}}
        <h1 class="mb-4 text-center text-2xl font-bold text-blue-900">
          {{ (string) __('auth.invitation.title') }}
        </h1>

        <p class="mb-6 text-center text-gray-700">
          {{ __('auth.invitation.invited_to_company', ['company' => $companyName]) }}
        </p>

        <p class="mb-6 text-center text-sm text-gray-600">
          {{ $inviteeEmail }}
        </p>

        <div class="space-y-3">
          <button wire:click="acceptInvitation" wire:loading.attr="disabled"
            class="w-full rounded-md bg-blue-900 px-4 py-2 text-white hover:bg-blue-800 disabled:opacity-50">
            <span wire:loading.remove wire:target="acceptInvitation">
              {{ __('auth.invitation.actions.accept') }}
            </span>
            <span wire:loading wire:target="acceptInvitation">
              {{ __('common.processing') }}
            </span>
          </button>

          <button wire:click="declineInvitation" wire:loading.attr="disabled"
            class="w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50 disabled:opacity-50">
            <span wire:loading.remove wire:target="declineInvitation">
              {{ __('auth.invitation.actions.decline') }}
            </span>
            <span wire:loading wire:target="declineInvitation">
              {{ __('common.processing') }}
            </span>
          </button>
        </div>
      @else
        {{-- Show result after action or if invalid --}}
        <h1
          class="{{ $success ? 'text-blue-900' : 'text-red-600' }} mb-4 text-center text-2xl font-bold">
          {{ $success ? (string) __('auth.invitation.messages.accepted') : (string) ($actionTaken ? __('auth.invitation.messages.declined') : __('auth.invitation.errors.invalid')) }}
        </h1>
        <p class="mb-4 text-center text-gray-700">{{ $message }}</p>

        @if ($success)
          <div class="mt-6 space-y-3">
            <a href="{{ route('login', ['_locale' => app()->getLocale()]) }}"
              class="block w-full rounded-md bg-blue-900 px-4 py-2 text-center text-white hover:bg-blue-800">
              {{ __('auth.invitation.actions.sign_in') }}
            </a>
            @if (!$userExists)
              <p class="text-center text-xs text-gray-500">
                {{ __('auth.invitation.create_or_login') }}
              </p>
            @endif
          </div>
        @endif
      @endif
    </div>
  </div>
</div>

<script>
  document.addEventListener('livewire:init', () => {
    Livewire.on('invitation-accepted', (event) => {
      // Listen for login events from other tabs/windows
      const authChannel = new BroadcastChannel('auth_channel');
      const redirectUrl = event[0].redirectUrl;

      authChannel.addEventListener('message', (event) => {
        // If user logs in from another tab, redirect this tab too
        if (event.data && event.data.type === 'auth_result' && event.data.success) {
          window.location.href = redirectUrl;
        }
      });

      // Clean up on page unload
      window.addEventListener('beforeunload', () => {
        authChannel.close();
      });
    });
  });
</script>
