<div class="relative grid gap-10">
  <x-flash-message />

  <div class="grid gap-4">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-semibold leading-9 tracking-wide text-blue-900">
        @if ($selectedCompanyId === -1)
          {{ __('companies.actions.create_new') }}
        @else
          {{ __('companies.sections.company_info') }}
        @endif
      </h1>

      @if ($showSelector && $selectedCompanyId !== -1)
        <div class="w-64">
          <label for="company-selector"
            class="sr-only">{{ __('companies.labels.select_company') }}</label>
          <select id="company-selector" wire:model.live="selectedCompanyId"
            class="mt-1 block w-full border border-blue-900 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            <option value="0" disabled>{{ __('companies.placeholders.select_company') }}
            </option>
            @foreach ($availableCompanies as $availableCompany)
              <option value="{{ $availableCompany->id }}">
                {{ $availableCompany->name }} ({{ $availableCompany->business_id }})
              </option>
            @endforeach
            @if ($canCreateCompany)
              <option value="-1">— {{ __('companies.actions.create_new') }}</option>
            @endif
          </select>
        </div>
      @endif
    </div>

    @if ($company !== null || $selectedCompanyId === -1)
      <div class="ml-1 grid grid-cols-12 gap-7">
        <!-- Company name field  -->
        <div class="col-span-4">
          <x-form-input id="company-name" label="{{ __('companies.fields.company_name_required') }}"
            placeholder="{{ __('companies.placeholders.company_placeholder') }}" :error="$errors->first('name')"
            wire:model.live="name" />
        </div>
        <!-- Business ID field  -->
        <div class="col-span-2">
          <x-form-input id="business-id" label="{{ __('companies.fields.business_id_required') }}"
            :error="$errors->first('business_id')" placeholder="{{ __('companies.placeholders.business_id') }}"
            wire:model.live="business_id" maxlength="9" />
        </div>

        @if ($selectedCompanyId === -1)
          <!-- Terms of Service Acceptance for Company Creation -->
          <div class="col-span-12 mb-1">
            <x-toggle id="terms-of-service" wire:model.live="terms_of_service_accepted"
              :error="$errors->first('terms_of_service_accepted')">
              {!! str(
                  __('companies.consent.accept_terms', [
                      'terms' =>
                          '<a href="' .
                          e(__('urls.terms_and_conditions')) .
                          '" target="_blank" class="hover:underline text-blue-900" @click.stop>' .
                          e(__('legal.terms_of_service')) .
                          '</a>',
                      'privacy' =>
                          '<a href="' .
                          e(__('urls.privacy_policy')) .
                          '" target="_blank" class="hover:underline text-blue-900" @click.stop>' .
                          e(__('legal.privacy_policy')) .
                          '</a>',
                  ]),
              )->sanitizeHtml() !!}
            </x-toggle>
          </div>
        @endif

        @if ($selectedCompanyId !== -1)
          <div class="col-span-3">
            <!-- Fiscal Period fields -->
            <x-month-day-selector id="fiscal-start" wire:model.month.live="fiscal_start_month"
              wire:model.day.live="fiscal_start_day"
              label="{{ __('companies.fields.fiscal_year_start') }}" :error="$errors->first('fiscal_start_month') === '' ? $errors->first('fiscal_start_day') : $errors->first('fiscal_start_month')" />
          </div>
          <div class="col-span-3">
            <x-month-day-selector id="fiscal-end" wire:model.month.live="fiscal_end_month"
              wire:model.day.live="fiscal_end_day"
              label="{{ __('companies.fields.fiscal_year_end') }}" :error="$errors->first('fiscal_end_month') === '' ? $errors->first('fiscal_end_day') : $errors->first('fiscal_start_month')" />
          </div>
          <div class="col-span-6">
            <x-form-select id="municipality" label="{{ __('companies.fields.municipality') }}"
              placeholder="{{ __('companies.placeholders.select_municipality') }}"
              :options="$municipalities" :error="$errors->first('municipality_id')" wire:model.live="municipality_id" />
          </div>

          <div class="col-span-6">
            <x-form-select id="industry-classification"
              placeholder="{{ __('companies.placeholders.select_industry') }}" :options="$industryClassifications"
              :error="$errors->first('industry_classification_id')" wire:model.live="industry_classification_id">
              {{ __('companies.fields.industry') }}

              <a href="{{ __('urls.nace_classification') }}" target="_blank"
                class="inline-flex items-center hover:underline">
                {{ __('companies.labels.nace_classification_before_icon') }}<x-link-icon />{{ __('companies.labels.nace_classification_after_icon') }}
              </a>
            </x-form-select>
          </div>

          <div class="col-span-6">
            <x-form-select id="revenue-range" label="{{ __('companies.fields.revenue') }}"
              placeholder="{{ __('companies.placeholders.select_revenue') }}" :options="$revenueRanges"
              :error="$errors->first('revenue_range_id')" wire:model.live="revenue_range_id" />
          </div>

          <div class="col-span-6">
            <x-form-select id="employee-count-range"
              label="{{ __('companies.fields.employee_count') }}"
              placeholder="{{ __('companies.placeholders.select_employee_count') }}"
              :options="$employeeCountRanges" :error="$errors->first('employee_count_range_id')" wire:model.live="employee_count_range_id" />
          </div>
        @endif
      </div>

      @if ($selectedCompanyId !== -1)
        <h2 class="mt-7 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
          {{ __('companies.sections.permissions') }}</h2>
        <div class="mb-3 ml-1 grid gap-4">
          <x-toggle id="consent-data" label="{{ __('companies.consent.data_viewing_permission') }}"
            wire:model.live="consent_for_data_examination" :error="$errors->first('consent_for_data_examination')" />
          <x-toggle id="applying-scope-1-2-mark" :label="__('companies.labels.applying_scope_1_2_badge')"
            wire:model.live="applying_for_scope1_2_mark" :error="$errors->first('applying_for_scope1_2_mark')" />
          <x-toggle id="applying-scope-1-3-mark" :label="__('companies.labels.applying_scope_1_3_badge')"
            wire:model.live="applying_for_scope1_3_mark" :error="$errors->first('applying_for_scope1_3_mark')" />
        </div>

        @if ($applying_for_scope1_2_mark || $applying_for_scope1_3_mark)
          <div class="ml-1 mt-4 grid grid-cols-12 gap-7">
            <div class="col-span-6">
              <x-form-input id="e-invoice-contact-name"
                label="{{ __('companies.fields.contact_name') }}"
                placeholder="{{ __('companies.placeholders.contact_name') }}" :error="$errors->first('e_invoice_contact_name')"
                wire:model.live="e_invoice_contact_name" />
            </div>
            <div class="col-span-6">
              <x-form-input id="e-invoice-contact-email"
                label="{{ __('companies.fields.contact_email') }}"
                placeholder="{{ __('companies.placeholders.contact_email') }}" :error="$errors->first('e_invoice_contact_email')"
                wire:model.live="e_invoice_contact_email" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-address"
                label="{{ __('companies.fields.einvoice_address') }}"
                placeholder="{{ __('companies.fields.einvoice_address') }}" :error="$errors->first('e_invoice_address')"
                wire:model.live="e_invoice_address" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-operator"
                label="{{ __('companies.fields.einvoice_operator') }}"
                placeholder="{{ __('companies.fields.einvoice_operator') }}" :error="$errors->first('e_invoice_operator')"
                wire:model.live="e_invoice_operator" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-reference" label="{{ __('companies.fields.reference') }}"
                placeholder="{{ __('companies.fields.reference') }}" :error="$errors->first('e_invoice_reference')"
                wire:model.live="e_invoice_reference" />
            </div>
            <div class="col-span-12">
              <x-form-textarea id="e-invoice-additional-info"
                label="{{ __('companies.fields.additional_info') }}"
                placeholder="{{ __('companies.placeholders.invoice_additional_info') }}"
                :error="$errors->first('e_invoice_additional_info')" wire:model.live="e_invoice_additional_info"
                :rows="2" />
            </div>
          </div>
        @endif
      @endif
    @else
      <div class="py-8 text-center">
        <p class="text-gray-500">{{ __('companies.messages.select_or_create') }}</p>
      </div>
    @endif
  </div>

  <!-- Users section -->
  @if ($company !== null && $selectedCompanyId !== -1)
    <div class="border-t border-blue-900 py-12">
      <div class="text-lg">
        <h2 class="mb-5 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
          {{ __('companies.sections.users') }}</h2>

        @if (Gate::allows(App\Enums\Abilities::MANAGE_COMPANY_USERS, $company))
          <p class="font-bold">
            {{ __('companies.labels.invite_users') }}
          </p>
        @endif
      </div>

      @if (Gate::allows(App\Enums\Abilities::MANAGE_COMPANY_USERS, $company))
        <form wire:submit.prevent="inviteUser" class="mt-3 flex gap-2">
          <div class="grow">
            <x-form-input id="invite-user-email"
              label="{{ __('companies.fields.email_required') }}"
              placeholder="{{ __('companies.placeholders.email') }}" wire:model="newUserEmail"
              :error="$errors->first('newUserEmail')" />
          </div>
          <div class="flex items-end">
            <x-button
              class="px-14 py-3 uppercase">{{ __('companies.actions.send_invitation') }}</x-button>
          </div>
        </form>
      @endif

      <table class="mt-6 min-w-full divide-y divide-blue-900">
        <thead>
          <tr>
            <th scope="col" class="py-3 text-left font-semibold">
              {{ __('companies.fields.email') }}</th>
            <th scope="col" class="py-3 text-left font-semibold">
              {{ __('companies.fields.user_level') }}</th>
            @if ($currentUserIsPrimaryUser)
              <th scope="col" class="relative text-right">
                {{ __('companies.actions.remove_user') }}
              </th>
            @endif
          </tr>
        </thead>
        <tbody class="divide-y divide-blue-900 bg-white">
          @forelse($companyUsers as $user)
            <tr>
              <td class="whitespace-nowrap py-3 pl-5">
                {{ $user->email }}
              </td>
              <td class="whitespace-nowrap py-3">
                @if ($user->pivot->is_primary)
                  {{ __('companies.roles.admin') }}
                @else
                  {{ __('companies.fields.user') }}
                @endif
              </td>
              @if ($currentUserIsPrimaryUser)
                <td class="relative whitespace-nowrap pr-5 text-right">
                  @if ($user->id !== $currentUserId)
                    <button wire:click="openDeleteModal({{ $user->id }})"
                      class="text-red-600 hover:text-red-700"
                      aria-label="{{ __('companies.actions.remove_user') }} - {{ $user->email }}">
                      <x-trash-icon />
                    </button>
                  @endif
                </td>
              @endif
            </tr>
          @empty
            <tr>
              <td colspan="3" class="whitespace-nowrap text-center">
                {{ __('companies.empty.no_users') }}
              </td>
            </tr>
          @endforelse
        </tbody>
      </table>
    </div>
  @endif

  @if (
      $company !== null &&
          $selectedCompanyId !== -1 &&
          Gate::allows(App\Enums\Abilities::VIEW_COMPANY_AUDIT_LOGS, $company))
    <div class="overflow-x-hidden border-t border-blue-900 py-24">
      <h2 class="mb-5 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
        {{ __('companies.tabs.audit_log') }}
      </h2>

      <livewire:company-audit-log :company-id="$selectedCompanyId" :key="'audit-log-' . $selectedCompanyId" />
    </div>
  @endif

  <x-modal :show="$showDeleteModal" onBackdropClick="closeDeleteModal">
    <div class="absolute right-4 top-4">
      <button wire:click="closeDeleteModal" type="button"
        class="text-blue-900 hover:text-blue-950" aria-label="{{ __('common.actions.close') }}">
        <x-close-icon />
      </button>
    </div>

    <div class="px-16 py-1 text-center">
      <div class="mx-auto mb-6 mt-2 flex items-center justify-center text-blue-900">
        <x-triangle-icon />
      </div>

      <h3 class="mb-6 px-24 text-3xl font-semibold leading-9 text-blue-900">
        {{ __('companies.confirm.remove_user') }}
      </h3>

      <div class="grid gap-6 divide-y divide-gray-500">

        <div class="grid">
          <x-button wire:click="confirmRemoveUser" class="mx-auto px-9 uppercase">
            {{ __('companies.actions.remove_user') }}
          </x-button>
        </div>

        <div class="pt-6">
          <button wire:click="closeDeleteModal" type="button"
            class="flex w-full items-center justify-center gap-4 text-base font-semibold uppercase leading-5 tracking-wide text-blue-900 hover:text-blue-950">
            <x-back-arrow-icon />
            {{ __('common.actions.go_back') }}
          </button>
        </div>
      </div>
    </div>
  </x-modal>
</div>
