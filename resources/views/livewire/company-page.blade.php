<div class="relative grid gap-10">
  <x-flash-message />

  <div class="grid gap-4">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-semibold leading-9 tracking-wide text-blue-900">
        @if ($selectedCompanyId === -1)
          {{ __('Luo uusi yritys') }}
        @else
          {{ __('<PERSON><PERSON><PERSON><PERSON> tiedot') }}
        @endif
      </h1>

      @if ($showSelector && $selectedCompanyId !== -1)
        <div class="w-64">
          <select id="company-selector" wire:model.live="selectedCompanyId"
            class="mt-1 block w-full border border-blue-900 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            <option value="0" disabled>{{ __('Valitse yritys...') }}</option>
            @foreach ($availableCompanies as $availableCompany)
              <option value="{{ $availableCompany->id }}">
                {{ $availableCompany->name }} ({{ $availableCompany->business_id }})
              </option>
            @endforeach
            @if ($canCreateCompany)
              <option value="-1">— {{ __('Luo uusi yritys') }}</option>
            @endif
          </select>
        </div>
      @endif
    </div>

    @if ($company !== null || $selectedCompanyId === -1)
      <div class="ml-1 grid grid-cols-12 gap-7">
        <!-- Company name field  -->
        <div class="col-span-4">
          <x-form-input id="company-name" label="{{ __('Yrityksen nimi*') }}"
            placeholder="{{ __('Yritys Oy') }}" :error="$errors->first('name')" wire:model.live="name" />
        </div>
        <!-- Business ID field  -->
        <div class="col-span-2">
          <x-form-input id="business-id" label="{{ __('Y-tunnus*') }}" :error="$errors->first('business_id')"
            placeholder="{{ __('1234567-8') }}" wire:model.live="business_id" maxlength="9" />
        </div>

        @if ($selectedCompanyId === -1)
          <!-- Terms of Service Acceptance for Company Creation -->
          <div class="col-span-12 mb-1">
            <x-toggle id="terms-of-service"
              label="{{ __('Hyväksyn palvelun käyttöehdot ja tietosuojaselosteen*') }}"
              wire:model.live="terms_of_service_accepted" :error="$errors->first('terms_of_service_accepted')" />
          </div>
        @endif

        @if ($selectedCompanyId !== -1)
          <div class="col-span-3">
            <!-- Fiscal Period fields -->
            <x-month-day-selector id="fiscal-start" wire:model.month.live="fiscal_start_month"
              wire:model.day.live="fiscal_start_day" label="{{ __('Tilikauden alku') }}"
              :error="$errors->first('fiscal_start_month') === '' ? $errors->first('fiscal_start_day') : $errors->first('fiscal_start_month')" />
          </div>
          <div class="col-span-3">
            <x-month-day-selector id="fiscal-end" wire:model.month.live="fiscal_end_month"
              wire:model.day.live="fiscal_end_day" label="{{ __('Tilikauden loppu') }}"
              :error="$errors->first('fiscal_end_month') === '' ? $errors->first('fiscal_end_day') : $errors->first('fiscal_start_month')" />
          </div>
          <div class="col-span-6">
            <x-form-select id="municipality" label="{{ __('Kunta') }}"
              placeholder="{{ __('Valitse kunta...') }}" :options="$municipalities" :error="$errors->first('municipality_id')"
              wire:model.live="municipality_id" />
          </div>

          <div class="col-span-6">
            <x-form-select id="industry-classification"
              label="{{ __('Toimiala (NACE-luokitus)') }}"
              placeholder="{{ __('Valitse toimiala...') }}" :options="$industryClassifications" :error="$errors->first('industry_classification_id')"
              wire:model.live="industry_classification_id" />
          </div>

          <div class="col-span-6">
            <x-form-select id="revenue-range" label="{{ __('Liikevaihto') }}"
              placeholder="{{ __('Valitse liikevaihto...') }}" :options="$revenueRanges" :error="$errors->first('revenue_range_id')"
              wire:model.live="revenue_range_id" />
          </div>

          <div class="col-span-6">
            <x-form-select id="employee-count-range" label="{{ __('Henkilöstön määrä') }}"
              placeholder="{{ __('Valitse henkilöstön määrä...') }}" :options="$employeeCountRanges"
              :error="$errors->first('employee_count_range_id')" wire:model.live="employee_count_range_id" />
          </div>
        @endif
      </div>

      @if ($selectedCompanyId !== -1)
        <h2 class="mt-7 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
          {{ __('Oikeudet') }}</h2>
        <div class="mb-3 ml-1 grid gap-4">
          <x-toggle id="consent-data"
            label="{{ __('Annan Keskuskauppakamarin asiantuntijoille oikeuden tarkastella yrityksen dataa') }}"
            wire:model.live="consent_for_data_examination" :error="$errors->first('consent_for_data_examination')" />

          <x-toggle id="applying-scope1-2-mark" :label="__('Haen Scope 1 & 2 merkkiä')"
            wire:model.live="applying_for_scope1_2_mark" :error="$errors->first('applying_for_scope1_2_mark')" />

          <x-toggle id="applying-scope3-mark" :label="__('Haen Scope 3 merkkiä')"
            wire:model.live="applying_for_scope3_mark" :error="$errors->first('applying_for_scope3_mark')" />
        </div>

        @if ($applying_for_scope1_2_mark || $applying_for_scope3_mark)
          <div class="ml-1 mt-4 grid grid-cols-12 gap-7">
            <div class="col-span-6">
              <x-form-input id="e-invoice-contact-name" label="{{ __('Yhteyshenkilön nimi') }}"
                placeholder="{{ __('Etunimi Sukunimi') }}" :error="$errors->first('e_invoice_contact_name')"
                wire:model.live="e_invoice_contact_name" />
            </div>
            <div class="col-span-6">
              <x-form-input id="e-invoice-contact-email"
                label="{{ __('Yhteyshenkilön sähköposti') }}"
                placeholder="{{ __('yhteyshenkilö@yritys.fi') }}" :error="$errors->first('e_invoice_contact_email')"
                wire:model.live="e_invoice_contact_email" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-address" label="{{ __('Verkkolaskuosoite') }}"
                placeholder="{{ __('Verkkolaskuosoite') }}" :error="$errors->first('e_invoice_address')"
                wire:model.live="e_invoice_address" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-operator" label="{{ __('Verkkolaskuoperaattori') }}"
                placeholder="{{ __('Verkkolaskuoperaattori') }}" :error="$errors->first('e_invoice_operator')"
                wire:model.live="e_invoice_operator" />
            </div>
            <div class="col-span-4">
              <x-form-input id="e-invoice-reference" label="{{ __('Viite') }}"
                placeholder="{{ __('Viite') }}" :error="$errors->first('e_invoice_reference')"
                wire:model.live="e_invoice_reference" />
            </div>
            <div class="col-span-12">
              <x-form-textarea id="e-invoice-additional-info" label="{{ __('Lisätiedot') }}"
                placeholder="{{ __('Lisätietoja laskutukseen liittyen...') }}" :error="$errors->first('e_invoice_additional_info')"
                wire:model.live="e_invoice_additional_info" :rows="2" />
            </div>
          </div>
        @endif
      @endif
    @else
      <div class="py-8 text-center">
        <p class="text-gray-500">{{ __('Valitse yritys tai luo uusi yritys.') }}</p>
      </div>
    @endif
  </div>

  <!-- Users section -->
  @if ($company !== null && $selectedCompanyId !== -1)
    <div class="border-t border-blue-900 py-12">
      <div class="text-lg">
        <h2 class="mb-5 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
          {{ __('Yrityksen käyttäjät') }}</h2>
        <p class="font-bold">
          {{ __('Kutsu yrityksen henkilöitä palveluun') }}
        </p>
      </div>

      <form wire:submit.prevent="inviteUser" class="mt-3 flex gap-2">
        <div class="grow">
          <x-form-input id="email" label="{{ __('Sähköpostiosoite*') }}"
            placeholder="{{ __('<EMAIL>') }}" wire:model="newUserEmail"
            :error="$errors->first('newUserEmail')" />
        </div>
        <div class="flex items-end">
          <x-button class="px-14 py-3 uppercase">{{ __('Lähetä kutsu') }}</x-button>
        </div>
      </form>

      <table class="mt-6 min-w-full divide-y divide-blue-900">
        <thead>
          <tr>
            <th scope="col" class="py-3 text-left font-semibold">
              {{ __('Sähköpostiosoite') }}</th>
            <th scope="col" class="py-3 text-left font-semibold">
              {{ __('Käyttäjätaso') }}</th>
            @if ($currentUserIsPrimaryUser)
              <th scope="col" class="relative text-right">
                {{ __('Poista käyttäjä') }}
              </th>
            @endif
          </tr>
        </thead>
        <tbody class="divide-y divide-blue-900 bg-white">
          @forelse($companyUsers as $user)
            <tr>
              <td class="whitespace-nowrap py-3 pl-5">
                {{ $user->email }}
              </td>
              <td class="whitespace-nowrap py-3">
                @if ($user->pivot->is_primary)
                  {{ __('Pääkäyttäjä') }}
                @else
                  {{ __('Käyttäjä') }}
                @endif
              </td>
              @if ($currentUserIsPrimaryUser)
                <td class="relative whitespace-nowrap pr-5 text-right">
                  @if ($user->id !== $currentUserId)
                    <button wire:click="openDeleteModal({{ $user->id }})"
                      class="text-red-600 hover:text-red-700">
                      <x-trash-icon />
                    </button>
                  @endif
                </td>
              @endif
            </tr>
          @empty
            <tr>
              <td colspan="3" class="whitespace-nowrap text-center">
                {{ __('Ei käyttäjiä') }}
              </td>
            </tr>
          @endforelse
        </tbody>
      </table>
    </div>
  @endif

  @if (
      $company !== null &&
          $selectedCompanyId !== -1 &&
          Gate::allows(App\Enums\Abilities::VIEW_COMPANY_AUDIT_LOGS, $company))
    <div class="overflow-x-hidden border-t border-blue-900 py-24">
      <h2 class="mb-5 text-3xl font-semibold leading-9 tracking-wide text-blue-900">
        {{ __('Loki') }}
      </h2>

      <livewire:company-audit-log :company-id="$selectedCompanyId" :key="'audit-log-' . $selectedCompanyId" />
    </div>
  @endif

  <x-modal :show="$showDeleteModal" onBackdropClick="closeDeleteModal">
    <div class="absolute right-4 top-4">
      <button wire:click="closeDeleteModal" type="button"
        class="text-blue-900 hover:text-blue-950">
        <x-close-icon />
      </button>
    </div>

    <div class="px-16 py-1 text-center">
      <div class="mx-auto mb-6 mt-2 flex items-center justify-center text-blue-900">
        <x-triangle-icon />
      </div>

      <h3 class="mb-6 px-24 text-3xl font-semibold leading-9 text-blue-900">
        {{ __('Oletko varma, että haluat poistaa käyttäjän?') }}
      </h3>

      <div class="grid gap-6 divide-y divide-gray-500">

        <div class="grid">
          <x-button wire:click="confirmRemoveUser" class="mx-auto px-9 uppercase">
            {{ __('Poista käyttäjä') }}
          </x-button>
        </div>

        <div class="pt-6">
          <button wire:click="closeDeleteModal" type="button"
            class="flex w-full items-center justify-center gap-4 text-base font-semibold uppercase leading-5 tracking-wide text-blue-900 hover:text-blue-950">
            <x-back-arrow-icon />
            {{ __('Palaa takaisin') }}
          </button>
        </div>
      </div>
    </div>
  </x-modal>
</div>
