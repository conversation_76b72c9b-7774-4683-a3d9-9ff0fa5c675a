<div class="grid h-screen place-items-center bg-gray-50">
  <div class="w-full max-w-md">
    <div class="bg-white px-6 py-8 shadow-sm sm:px-10">
      <h1
        class="{{ $success ? 'text-blue-900' : 'text-red-600' }} mb-4 text-center text-2xl font-bold">
        {{ $success ? (string) __('Kirjautuminen onnistui!') : (string) __('Kirjautuminen epäonnistui') }}
      </h1>
      <p class="mb-4 text-center text-gray-700">{{ $message }}</p>

      @if ($success)
        <p class="text-center text-sm text-gray-600">
          {{ (string) __('Voit nyt sulkea tämän ikkunan ja jatkaa sovelluksen käyttöä.') }}
        </p>
      @endif
    </div>
  </div>

  <script>
    const authChannel = new BroadcastChannel('auth_channel');
    const redirectUrl = @js($redirectUrl);

    authChannel.postMessage({
      type: 'auth_result',
      success: {{ $success ? 'true' : 'false' }},
      message: @json($message)
    });

    // Listen for response before redirecting
    authChannel.addEventListener('message', (event) => {
      // Check if this is a response to our auth_result message
      if (event.data && event.data.type === 'auth_response') {
        authChannel.close();

        // Redirect if URL is configured after 3 second delay
        if (redirectUrl) {
          setTimeout(() => {
            window.location.href = redirectUrl;
          }, 3000);
        }
      }
    });

    // Fallback: redirect after 5 seconds if no response
    if (redirectUrl) {
      setTimeout(() => {
        authChannel.close();
        window.location.href = redirectUrl;
      }, 5000);
    }
  </script>
</div>
