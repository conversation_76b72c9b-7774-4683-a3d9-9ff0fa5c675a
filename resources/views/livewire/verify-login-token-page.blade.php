<div class="grid h-screen place-items-center bg-gray-50" wire:init="performLogin">
  <div class="w-full max-w-md">
    <div class="bg-white px-6 py-8 shadow-sm sm:px-10">
      {{-- Just show spinner continuously --}}
      <h1 class="mb-4 text-center text-2xl font-bold text-blue-900">
        {{ (string) __('auth.login.signing_in') }}
      </h1>
      <div class="flex justify-center">
        <svg class="h-8 w-8 animate-spin text-blue-900" xmlns="http://www.w3.org/2000/svg"
          fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
            stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('livewire:init', () => {
    Livewire.on('login-completed', (event) => {
      const authChannel = new BroadcastChannel('auth_channel');
      const success = event[0].success;
      const message = event[0].message;
      const redirectUrl = event[0].redirectUrl;

      authChannel.postMessage({
        type: 'auth_result',
        success: success,
        message: message
      });

      // Listen for response before redirecting
      authChannel.addEventListener('message', (event) => {
        // Check if this is a response to our auth_result message
        if (event.data && event.data.type === 'auth_response') {
          authChannel.close();

          // Redirect if URL is configured after 3 second delay
          if (redirectUrl) {
            setTimeout(() => {
              window.location.href = redirectUrl;
            }, 3000);
          }
        }
      });

      // Fallback: redirect after 5 seconds if no response
      if (redirectUrl) {
        setTimeout(() => {
          authChannel.close();
          window.location.href = redirectUrl;
        }, 5000);
      }
    });
  });
</script>
