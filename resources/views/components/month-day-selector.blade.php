<div x-data="{
    getDaysInMonth(month) {
            return new Date(2025, month, 0).getDate();
        },
        shouldHideDay(day) {
            const monthSelect = document.getElementById('{{ $id }}-month');
            if (!monthSelect || !(monthSelect instanceof HTMLSelectElement)) return false;

            const month = parseInt(monthSelect.value);
            if (isNaN(month)) return false;

            const maxDays = this.getDaysInMonth(month);

            return day > maxDays;
        },
        adjustDayIfNeeded() {
            const monthSelect = document.getElementById('{{ $id }}-month');
            const daySelect = document.getElementById('{{ $id }}-day');

            if (!monthSelect || !(monthSelect instanceof HTMLSelectElement) ||
                !daySelect || !(daySelect instanceof HTMLSelectElement)) return;

            const month = parseInt(monthSelect.value);
            const day = parseInt(daySelect.value);

            if (isNaN(month) || isNaN(day)) return;

            const maxDays = this.getDaysInMonth(month);

            if (day > maxDays) {
                daySelect.value = maxDays.toString();
                daySelect.dispatchEvent(new Event('change'));
            }
        }
}">
  <fieldset>
    <legend
      class="{{ $hasError ? 'text-red-600' : '' }} block text-sm font-medium uppercase text-blue-900">
      {{ $label }}</legend>
    <div class="mt-1 flex space-x-2">
      <div class="w-1/3">
        <label for="{{ $id }}-day" class="sr-only">{{ __('common.fields.day') }}</label>
        <select id="{{ $id }}-day"
          {{ $attributes->filter(fn(string $value, string $key) => str_contains($key, 'model.day')) }}
          aria-label="{{ $label }} - {{ __('common.fields.day') }}"
          class="{{ $hasError ? 'border-red-500 ring-1 ring-red-500' : '' }} block w-full border-blue-900 py-3 focus:ring-blue-900 sm:text-sm">
          @for ($i = 1; $i <= 31; $i++)
            <option value="{{ $i }}" x-bind:hidden="shouldHideDay({{ $i }})">
              {{ $i }}
            </option>
          @endfor
        </select>
      </div>
      <div class="w-2/3">
        <label for="{{ $id }}-month"
          class="sr-only">{{ __('common.fields.month') }}</label>
        <select id="{{ $id }}-month" @change="adjustDayIfNeeded()"
          {{ $attributes->filter(fn(string $value, string $key) => str_contains($key, 'model.month')) }}
          aria-label="{{ $label }} - {{ __('common.fields.month') }}"
          class="{{ $hasError ? 'border-red-500 ring-1 ring-red-500' : '' }} block w-full border-blue-900 py-3 focus:ring-blue-900 sm:text-sm">
          @foreach ($monthNames as $index => $name)
            <option value="{{ $index + 1 }}">{{ $name }}</option>
          @endforeach
        </select>
      </div>
    </div>
  </fieldset>
</div>
