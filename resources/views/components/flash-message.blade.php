<div x-data="flash" x-init="@if ($message !== null) showMessage([{ message: @js($message), type: @js($type) }]) @endif"
  class="absolute z-50 rounded-sm border bg-white px-4 py-2 shadow"
  :class="{
      'text-green-800 border-green-600': type === 'success',
      'text-red-800 border-red-600': type === 'error',
      'text-yellow-800 border-yellow-600': type === 'warning',
      'text-blue-800 border-blue-600': type === 'info'
  }"
  x-show="show" x-cloak>
  <div class="flex items-center">
    <template x-if="type === 'success'">
      <svg class="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clip-rule="evenodd" />
      </svg>
    </template>
    <template x-if="type === 'error'">
      <svg class="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
          clip-rule="evenodd" />
      </svg>
    </template>
    <template x-if="type === 'warning'">
      <svg class="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd" />
      </svg>
    </template>
    <template x-if="type === 'info'">
      <svg class="mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd"
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
          clip-rule="evenodd" />
      </svg>
    </template>
    <span class="text-sm font-medium" x-text="message"></span>
  </div>
</div>
