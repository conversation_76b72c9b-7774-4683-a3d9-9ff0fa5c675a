@if ($slot->isNotEmpty())
  <div class="flex gap-3 pt-3 text-blue-900">
    <div class="grid place-items-center">
      <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1"
        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g transform="translate(-222, -927)" fill="currentColor">
            <path
              d="M230.695652,934.391304 L233.304348,934.391304 L233.304348,931.782609 L230.695652,931.782609 L230.695652,934.391304 Z M230.369565,939.956522 L231.347826,939.956522 L231.347826,937.521739 L230.369565,937.521739 L229.391304,937.521739 L229.391304,935.695652 L230.369565,935.695652 L232.326087,935.695652 L233.304348,935.695652 L233.304348,936.608696 L233.304348,939.956522 L233.630435,939.956522 L234.608696,939.956522 L234.608696,941.782609 L233.630435,941.782609 L230.369565,941.782609 L229.391304,941.782609 L229.391304,939.956522 L230.369565,939.956522 Z M232,947 C237.522852,947 242,942.522852 242,937 C242,931.477148 237.522852,927 232,927 C226.477148,927 222,931.477148 222,937 C222,942.522852 226.477148,947 232,947 L232,947 Z">
            </path>
          </g>
        </g>
      </svg>
    </div>
    <p class="text-base">
      {{ $slot }}
    </p>
  </div>
@endif
