<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
  <meta charset="utf-8">

  <meta name="application-name" content="{{ config('app.name') }}">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>{{ config('app.name') }}</title>

  <style>
    [x-cloak] {
      display: none !important;
    }
  </style>

  @filamentStyles
  @vite('resources/css/app.css')
</head>

<body class="antialiased">

  <div>
    @auth
      @if (!request()->routeIs('login.verify'))
        <x-navigation />
      @endif
    @endauth
    <main>
      <div class="notification-container"></div>
      {{ $slot }}
    </main>
  </div>

  @filamentScripts
  <script>
    window.appConfig = Object.freeze({
      allowedFrameOrigins: @json(app(App\Settings\FrameAncestorsSettings::class)->allowed_frame_ancestors)
    });
  </script>
  @vite('resources/js/app.ts')
</body>

</html>
