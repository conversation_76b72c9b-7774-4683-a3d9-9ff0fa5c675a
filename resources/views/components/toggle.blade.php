<div>
  <label for="{{ $id }}"
    class="{{ $labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row' }} flex cursor-pointer items-center">
    <div class="relative h-9 w-16">
      <input id="{{ $id }}" type="checkbox" {{ $attributes }}
        {{ $disabled ? 'disabled' : '' }} class="peer sr-only" />

      <div @class([
          'absolute inset-0 rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none peer-focus:ring-2 peer-focus:ring-offset-2',
          'peer-checked:bg-blue-900' => $color === 'blue',
          'peer-checked:bg-gray-600' => $color === 'gray',
          'peer-focus:ring-blue-900' => $color === 'blue',
          'peer-focus:ring-gray-600' => $color === 'gray',
          'border-red-500 ring-1 ring-red-500' => $hasError,
      ])></div>

      <span
        class="pointer-events-none absolute left-1 top-1 h-7 w-7 transform rounded-full bg-white shadow transition-transform duration-200 ease-in-out peer-checked:translate-x-7"></span>
    </div>

    @if (isset($label))
      <span id="{{ $id }}-label"
        class="{{ $labelPosition === 'left' ? 'mr-3 ml-0' : 'ml-3' }} {{ $hasError ? 'text-red-600' : '' }} text-lg font-medium">
        {{ $label }}
      </span>
    @elseif ($slot->isNotEmpty())
      <span id="{{ $id }}-label"
        class="{{ $labelPosition === 'left' ? 'mr-3 ml-0' : 'ml-3' }} {{ $hasError ? 'text-red-600' : '' }} text-lg font-medium">
        {{ $slot }}
      </span>
    @endif
  </label>
</div>
