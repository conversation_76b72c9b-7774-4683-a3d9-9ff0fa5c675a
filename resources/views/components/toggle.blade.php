<div>
  <div class="{{ $labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row' }} flex items-center">
    <div class="relative h-9 w-16">
      <input id="{{ $id }}" type="checkbox" {{ $attributes }}
        {{ $disabled ? 'disabled' : '' }} class="peer sr-only" />
      <label for="{{ $id }}" @class([
          'absolute inset-0 rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out cursor-pointer focus:outline-none peer-focus:ring-2 peer-focus:ring-offset-2',
          'peer-checked:bg-blue-900' => $color === 'blue',
          'peer-checked:bg-gray-600' => $color === 'gray',
          'peer-focus:ring-blue-900' => $color === 'blue',
          'peer-focus:ring-gray-600' => $color === 'gray',
          'border-red-500 ring-1 ring-red-500' => $hasError,
      ])></label>

      <span
        class="pointer-events-none absolute left-1 top-1 h-7 w-7 transform rounded-full bg-white shadow transition-transform duration-200 ease-in-out peer-checked:translate-x-7"></span>
    </div>

    @if ($label !== null)
      <label id="{{ $id }}-label" for="{{ $id }}"
        class="{{ $labelPosition === 'left' ? 'mr-3 ml-0' : 'ml-3' }} {{ $hasError ? 'text-red-600' : '' }} cursor-pointer text-lg font-medium">
        {{ $label }}
      </label>
    @endif
  </div>
</div>
