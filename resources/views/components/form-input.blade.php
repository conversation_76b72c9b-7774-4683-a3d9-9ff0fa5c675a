<div>
  <label for="{{ $id }}"
    class="{{ $hasError ? 'text-red-600' : '' }} block text-sm font-medium uppercase text-blue-900">{{ $label }}</label>
  <div class="mt-1">
    <input type="{{ $type }}" id="{{ $id }}" placeholder="{{ $placeholder }}"
      {{ $attributes->merge(['class' => 'focus:ring-blue-900 py-3 block w-full sm:text-sm border-blue-900 ' . ($hasError ? 'border-red-500 ring-1 ring-red-500' : '')]) }}>
  </div>
</div>
