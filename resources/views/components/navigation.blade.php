<nav class="mb-12 bg-white">
  @if (auth()->user()->hasPermissionTo('view-admin-panel'))
    <div class="mb-2 text-center">
      <a href="/{{ request()->route('_locale') ?? app()->getLocale() }}/admin" target="_blank"
        rel="noopener noreferrer"
        class="inline-block rounded-md border-2 border-sky-100 bg-sky-100 px-8 py-3.5 text-center text-base font-semibold text-blue-900 transition-colors hover:border-blue-900">
        {{ __('Hallintapaneeli') }}
      </a>
    </div>
  @endif

  <div class="grid grid-cols-1 gap-2 lg:grid-cols-5">
    <a href="{{ route('instructions') }}"
      class="{{ request()->routeIs('instructions') ? 'bg-blue-900 text-white border-blue-900' : 'text-blue-900 bg-sky-100 border-sky-100 hover:border-t-blue-900 hover:border-l-blue-900 hover:border-r-blue-900' }} rounded-t-md border-b-0 border-l-2 border-r-2 border-t-2 py-3.5 text-center text-base font-semibold transition-colors">
      {{ __('Käyttöohje') }}
    </a>

    <a href="{{ route('company') }}"
      class="{{ request()->routeIs('company') || request()->routeIs('company.create') ? 'bg-blue-900 text-white border-blue-900' : 'text-blue-900 bg-sky-100 border-sky-100 hover:border-t-blue-900 hover:border-l-blue-900 hover:border-r-blue-900' }} rounded-t-md border-b-0 border-l-2 border-r-2 border-t-2 py-3.5 text-center text-base font-semibold transition-colors">
      {{ __('Yrityksen tiedot') }}
    </a>

    <a href="{{ route('data') }}"
      class="{{ request()->routeIs('data') ? 'bg-blue-900 text-white border-blue-900' : 'text-blue-900 bg-sky-100 border-sky-100 hover:border-t-blue-900 hover:border-l-blue-900 hover:border-r-blue-900' }} rounded-t-md border-b-0 border-l-2 border-r-2 border-t-2 py-3.5 text-center text-base font-semibold transition-colors">
      {{ __('Kulutustiedot') }}
    </a>

    <a href="{{ route('emission-factors') }}"
      class="{{ request()->routeIs('emission-factors') ? 'bg-blue-900 text-white border-blue-900' : 'text-blue-900 bg-sky-100 border-sky-100 hover:border-t-blue-900 hover:border-l-blue-900 hover:border-r-blue-900' }} rounded-t-md border-b-0 border-l-2 border-r-2 border-t-2 py-3.5 text-center text-base font-semibold transition-colors">
      {{ __('Päästökertoimet') }}
    </a>

    <a href="{{ route('results') }}"
      class="{{ request()->routeIs('results') ? 'bg-blue-900 text-white border-blue-900' : 'text-blue-900 bg-sky-100 border-sky-100 hover:border-t-blue-900 hover:border-l-blue-900 hover:border-r-blue-900' }} rounded-t-md border-b-0 border-l-2 border-r-2 border-t-2 py-3.5 text-center text-base font-semibold transition-colors">
      {{ __('Tulokset') }}
    </a>

  </div>
  <div class="h-[2px] w-full bg-blue-900"></div>
</nav>
