@if ($paginator->hasPages())
  <nav role="navigation" aria-label="{{ __('common.pagination.navigation') }}"
    class="flex items-center justify-between">
    <div class="flex flex-1 justify-between sm:hidden">
      @if ($paginator->onFirstPage())
        <span
          class="relative inline-flex cursor-default items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
          {{ __('common.pagination.previous') }}
        </span>
      @else
        <a href="{{ $paginator->previousPageUrl() }}" wire:click.prevent="previousPage"
          class="relative inline-flex items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900">
          {{ __('common.pagination.previous') }}
        </a>
      @endif

      @if ($paginator->hasMorePages())
        <a href="{{ $paginator->nextPageUrl() }}" wire:click.prevent="nextPage"
          class="relative inline-flex items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900">
          {{ __('common.pagination.next') }}
        </a>
      @else
        <span
          class="relative inline-flex cursor-default items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
          {{ __('common.pagination.next') }}
        </span>
      @endif
    </div>

    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div>
        <p class="text-sm leading-5 text-blue-900">
          {{ __('common.pagination.showing') }}
          @if ($paginator->firstItem())
            <span class="font-medium">{{ $paginator->firstItem() }}</span>
            {{ __('common.pagination.separator') }}
            <span class="font-medium">{{ $paginator->lastItem() }}</span>
          @else
            {{ $paginator->count() }}
          @endif
          {{ __('common.pagination.total') }}
          <span class="font-medium">{{ $paginator->total() }}</span>
          {{ __('common.pagination.of_changes') }}
        </p>
      </div>

      <div>
        <span class="relative z-0 inline-flex rounded-md shadow-sm">
          {{-- Previous Page Link --}}
          @if ($paginator->onFirstPage())
            <span
              class="relative inline-flex cursor-default items-center rounded-l-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
              <span class="sr-only">{{ __('common.pagination.previous_page') }}</span>
            </span>
          @else
            <a href="{{ $paginator->previousPageUrl() }}" wire:click.prevent="previousPage"
              rel="prev"
              class="relative inline-flex items-center rounded-l-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
              aria-label="{{ __('common.pagination.previous_page') }}">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          @endif

          {{-- Pagination Elements --}}
          @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
              <span
                class="relative -ml-px inline-flex cursor-default items-center border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900">{{ $element }}</span>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
              @foreach ($element as $page => $url)
                @if ($page == $paginator->currentPage())
                  <span aria-current="page"
                    class="relative -ml-px inline-flex cursor-default items-center border border-blue-700 bg-blue-700 px-4 py-2 text-sm font-medium leading-5 text-white">
                    <span class="sr-only">{{ __('common.pagination.current_page') }}</span>
                    {{ $page }}
                  </span>
                @else
                  <a href="{{ $url }}" wire:click.prevent="gotoPage({{ $page }})"
                    class="relative -ml-px inline-flex items-center border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
                    aria-label="{{ __('common.pagination.go_to_page', ['page' => $page]) }}">
                    {{ $page }}
                  </a>
                @endif
              @endforeach
            @endif
          @endforeach

          {{-- Next Page Link --}}
          @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" wire:click.prevent="nextPage" rel="next"
              class="relative -ml-px inline-flex items-center rounded-r-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
              aria-label="{{ __('common.pagination.next_page') }}">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          @else
            <span
              class="relative -ml-px inline-flex cursor-default items-center rounded-r-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clip-rule="evenodd" />
              </svg>
              <span class="sr-only">{{ __('common.pagination.next_page') }}</span>
            </span>
          @endif
        </span>
      </div>
    </div>
  </nav>
@endif
