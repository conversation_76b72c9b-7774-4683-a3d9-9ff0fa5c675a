@if ($paginator->hasPages())
  <nav role="navigation" aria-label="{{ __('Sivutusnavigaatio') }}"
    class="flex items-center justify-between">
    <div class="flex flex-1 justify-between sm:hidden">
      @if ($paginator->onFirstPage())
        <span
          class="relative inline-flex cursor-default items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
          {{ __('Edellinen') }}
        </span>
      @else
        <a href="{{ $paginator->previousPageUrl() }}" wire:click.prevent="previousPage"
          class="relative inline-flex items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900">
          {{ __('<PERSON><PERSON>nen') }}
        </a>
      @endif

      @if ($paginator->hasMorePages())
        <a href="{{ $paginator->nextPageUrl() }}" wire:click.prevent="nextPage"
          class="relative inline-flex items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900">
          {{ __('Seuraava') }}
        </a>
      @else
        <span
          class="relative inline-flex cursor-default items-center rounded-md border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50">
          {{ __('Seuraava') }}
        </span>
      @endif
    </div>

    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div>
        <p class="text-sm leading-5 text-blue-900">
          {{ __('Näytetään') }}
          @if ($paginator->firstItem())
            <span class="font-medium">{{ $paginator->firstItem() }}</span>
            {{ __('-') }}
            <span class="font-medium">{{ $paginator->lastItem() }}</span>
          @else
            {{ $paginator->count() }}
          @endif
          {{ __('yhteensä') }}
          <span class="font-medium">{{ $paginator->total() }}</span>
          {{ __('muutoksesta') }}
        </p>
      </div>

      <div>
        <span class="relative z-0 inline-flex rounded-md shadow-sm">
          {{-- Previous Page Link --}}
          @if ($paginator->onFirstPage())
            <span aria-disabled="true" aria-label="{{ __('Edellinen sivu') }}">
              <span
                class="relative inline-flex cursor-default items-center rounded-l-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50"
                aria-hidden="true">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </span>
            </span>
          @else
            <a href="{{ $paginator->previousPageUrl() }}" wire:click.prevent="previousPage"
              rel="prev"
              class="relative inline-flex items-center rounded-l-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
              aria-label="{{ __('Edellinen sivu') }}">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          @endif

          {{-- Pagination Elements --}}
          @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
              <span aria-disabled="true">
                <span
                  class="relative -ml-px inline-flex cursor-default items-center border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900">{{ $element }}</span>
              </span>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
              @foreach ($element as $page => $url)
                @if ($page == $paginator->currentPage())
                  <span aria-current="page">
                    <span
                      class="relative -ml-px inline-flex cursor-default items-center border border-blue-700 bg-blue-700 px-4 py-2 text-sm font-medium leading-5 text-white">{{ $page }}</span>
                  </span>
                @else
                  <a href="{{ $url }}" wire:click.prevent="gotoPage({{ $page }})"
                    class="relative -ml-px inline-flex items-center border border-blue-900 bg-white px-4 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
                    aria-label="{{ __('Siirry sivulle :page', ['page' => $page]) }}">
                    {{ $page }}
                  </a>
                @endif
              @endforeach
            @endif
          @endforeach

          {{-- Next Page Link --}}
          @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" wire:click.prevent="nextPage" rel="next"
              class="relative -ml-px inline-flex items-center rounded-r-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 ring-sky-300 transition duration-150 ease-in-out hover:bg-sky-100 focus:z-10 focus:border-blue-700 focus:outline-none focus:ring active:bg-sky-300 active:text-blue-900"
              aria-label="{{ __('Seuraava sivu') }}">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </a>
          @else
            <span aria-disabled="true" aria-label="{{ __('Seuraava sivu') }}">
              <span
                class="relative -ml-px inline-flex cursor-default items-center rounded-r-md border border-blue-900 bg-white px-2 py-2 text-sm font-medium leading-5 text-blue-900 opacity-50"
                aria-hidden="true">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </span>
            </span>
          @endif
        </span>
      </div>
    </div>
  </nav>
@endif
