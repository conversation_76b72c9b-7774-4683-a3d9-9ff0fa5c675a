@php
  $classes = match ($variant) {
      'primary'
          => 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-white bg-blue-900 hover:bg-blue-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-900',
      'secondary'
          => 'inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-900',
      default
          => 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-white bg-blue-900 hover:bg-blue-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-900',
  };
@endphp

<button {{ $attributes->merge(['class' => $classes]) }}>
  {{ $slot }}
</button>
