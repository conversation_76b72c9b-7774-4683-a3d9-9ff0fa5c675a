<div>
  <label for="{{ $id }}"
    class="{{ $hasError ? 'text-red-600' : '' }} block text-sm font-medium uppercase text-blue-900">
    @if (!$slot->isEmpty())
      {{ $slot }}
    @else
      {{ $label ?? '' }}
    @endif
  </label>
  <div class="mt-1">
    <select id="{{ $id }}"
      {{ $attributes->merge(['class' => 'focus:ring-blue-900 py-3 block w-full sm:text-sm border-blue-900 ' . ($hasError ? 'border-red-500 ring-1 ring-red-500' : '')]) }}>
      <option value="">{{ $placeholder }}</option>
      @foreach ($options as $option)
        <option value="{{ $option->value }}">{{ $option->label }}</option>
      @endforeach
    </select>
  </div>
</div>
