<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Päästövähennyslaskuri</title>

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">

  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: "Work Sans", sans-serif;
    }

    .hero {
      position: relative;
      width: 100%;
      height: 350px;
      overflow: hidden;
      background: #E8F1F9;
      display: flex;
      align-items: center;
    }

    .hero-content {
      position: relative;
      max-width: 1056px;
      margin: 0 auto;
      width: 100%;
      display: flex;
      align-items: center;
      gap: 50px;
      padding: 50px 0px;
    }

    .hero-icon {
      flex-shrink: 0;
      position: relative;
      display: flex;
      align-self: stretch;
      align-items: flex-start;
      justify-content: center;
    }

    .hero-text {
      flex: 1;
    }

    .hero h1 {
      margin: 0 0 16px 0;
      color: #002663;
      font-size: 50px;
      font-weight: 700;
      letter-spacing: 0.56px;
      line-height: 58px;
      font-weight: 600;
    }

    .hero p {
      margin: 0;
      color: #383838;
      font-size: 21px;
      line-height: 30px;
      max-width: 750px;
      letter-spacing: 0.23px;
      font-weight: 500;
    }

    .language-selector {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 100;
    }

    .language-selector select {
      padding: 10px 15px;
      font-size: 16px;
      font-family: "Work Sans", sans-serif;
      font-weight: 500;
      border: 2px solid #002663;
      border-radius: 8px;
      background-color: white;
      color: #002663;
      cursor: pointer;
      outline: none;
      transition: all 0.3s ease;
      min-width: 150px;
    }

    .iframe-container {
      display: flex;
      justify-content: center;
      width: 100%;
      padding-top: 102px;
    }

    iframe {
      width: 1056px;
      height: 800px;
      border: none;
    }
  </style>
</head>

<body>
  <div class="hero">
    <div class="language-selector">
      <select id="languageSelect" onchange="changeLanguage(this.value)">
        <option value="fi">Suomi</option>
        <option value="sv">Svenska</option>
        <option value="en">English</option>
      </select>
    </div>

    <div class="hero-content">
      <div class="hero-icon">

        <svg width="149px" height="133px" viewBox="0 0 149 133" version="1.1"
          xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill-rule="nonzero">
              <ellipse fill="#FFFFFF"
                transform="translate(81.2515, 65.1492) rotate(-9.22) translate(-81.2515, -65.1492)"
                cx="81.2515148" cy="65.1492056" rx="53.7382303" ry="52.6055371"></ellipse>
              <path
                d="M18.9813339,12.7455117 C16.2630803,14.457096 15.2447323,17.8621295 16.5915688,20.7361297 C17.9384054,23.6101298 21.2408677,25.0791433 24.3414821,24.1834747 C27.4420964,23.2878061 29.3979986,20.2998188 28.9314124,17.1715424 C28.4648262,14.043266 25.7176356,11.7259765 22.4845596,11.7335231 C21.2424793,11.7403666 20.0278079,12.0912464 18.9813339,12.7455117 L18.9813339,12.7455117 Z"
                fill="#F2B7BF"></path>
              <path
                d="M148.828916,31.9909727 L130.055015,31.9909727 C128.392011,25.6201226 123.377944,20.5763145 116.87152,18.7292781 L116.87152,0.195096647 C115.110366,0.0682838265 113.31902,0 111.507548,0 C109.696075,0 107.909762,0.0682838265 106.143576,0.195096647 L106.143576,18.7292781 C99.6382417,20.5780593 94.6249243,25.6211137 92.9600805,31.9909727 L74.191211,31.9909727 C74.0654143,33.6785587 74,35.4051641 74,37.1317694 C74,38.8583747 74.0704462,40.6581413 74.2012747,42.389624 L93.322375,42.389624 C95.3100518,48.1693881 100.09224,52.638978 106.123449,54.3539259 L106.123449,74.1172163 C107.889634,74.2440291 109.675948,74.3123129 111.48742,74.3123129 C113.298893,74.3123129 115.090238,74.2440291 116.851392,74.1172163 L116.851392,54.3539259 C122.886425,52.6507199 127.678057,48.1901988 129.677625,42.4140111 L148.798725,42.4140111 C148.929554,40.6825284 149,38.931536 149,37.1561565 C149,35.380777 148.954713,33.6785587 148.828916,31.9909727 Z M119.583697,42.4140111 C118.839404,43.381109 117.918432,44.2076862 116.866488,44.8527192 C113.596868,46.8818259 109.408164,46.8818259 106.138544,44.8527192 C105.089166,44.2062534 104.170096,43.3798536 103.426367,42.4140111 C100.279722,38.3703118 100.826973,32.669284 104.689261,29.2577143 C108.551549,25.8461447 114.458515,25.8461447 118.320803,29.2577143 C122.18309,32.669284 122.730341,38.3703118 119.583697,42.4140111 L119.583697,42.4140111 Z"
                fill="#002663"></path>
              <path
                d="M41.7434028,50.7638881 L36.202466,49.8674732 L35.1950229,55.7779013 C37.0776955,55.8780848 38.9447018,56.1685877 40.766183,56.6447641 L41.7434028,50.7638881 Z M25.2565972,114.478303 L30.797534,115.379644 L31.9157958,108.91265 C30.0410685,108.791932 28.1841767,108.481619 26.374859,107.986683 L25.2565972,114.478303 Z M24.3498985,51.0003052 L19.0708969,52.9704479 L21.2822344,58.6740111 C22.9606321,57.8408936 24.7230543,57.1806311 26.5410871,56.7038684 L24.3498985,51.0003052 Z M42.6501015,114.241886 L47.9291031,112.301296 L45.6170213,106.292361 C43.921927,107.097004 42.1460392,107.727541 40.3178708,108.173847 L42.6501015,114.241886 Z M66.0781896,90.6888302 L67,85.246311 L60.8646718,84.2612396 C60.7331957,86.0956012 60.4056706,87.9114631 59.8874521,89.6791321 L66.0781896,90.6888302 Z M0.92181039,74.5582867 L0,80.000806 L6.54837982,81.0597577 C6.63981424,79.217859 6.9285139,77.3904112 7.40974363,75.6073877 L0.92181039,74.5582867 Z M1.16359672,91.5704691 L3.14825953,96.732243 L9.45989023,94.4074746 C8.60519559,92.7657883 7.92983655,91.0405932 7.44500414,89.2604767 L1.16359672,91.5704691 Z M65.8364033,73.6766479 L63.8517405,68.5247247 L58.1445756,70.6229267 C58.9777076,72.2770454 59.6310836,74.012056 60.0939779,75.7994766 L65.8364033,73.6766479 Z M14.3560635,101.061632 L10.0593188,106.179077 L14.41651,109.681006 L18.7132546,104.56356 C17.9277232,104.055309 17.1694593,103.50787 16.4414706,102.923416 C15.7134453,102.338987 15.0173489,101.717534 14.3560635,101.061632 L14.3560635,101.061632 Z M53.0771371,63.6535468 L56.9306067,59.0631143 L52.5734155,55.566111 L48.7199459,60.1516181 C49.5054328,60.65817 50.2636998,61.2039667 50.9917299,61.7868366 C51.7209285,62.3716839 52.4170902,62.9948391 53.0771371,63.6535468 L53.0771371,63.6535468 Z M14.3560635,63.6781736 L9.41959251,59.7378882 L5.83813247,63.9983218 L10.8300128,67.9829354 C11.4030143,67.1111727 12.0269871,66.272468 12.6988196,65.4710035 C13.22269,64.8405578 13.7751046,64.2429478 14.3560635,63.6781736 L14.3560635,63.6781736 Z M52.5986016,101.524615 L57.5904819,105.533856 L61.171942,101.268497 L56.2304338,97.3282112 C55.7720472,97.988209 55.2884746,98.6383561 54.7595669,99.2688017 C54.0831721,100.05985 53.3617056,100.812982 52.5986016,101.524615 Z"
                fill="#A5C9E7"></path>
              <polygon fill="#F2B7BF"
                transform="translate(102.6094, 112.9368) rotate(-30) translate(-102.6094, -112.9368)"
                points="96.305264 94.2329234 108.548869 94.3513109 108.913505 131.64077 96.6698996 131.522382">
              </polygon>
            </g>
          </g>
        </svg>
      </div>

      <div class="hero-text">
        <h1>Päästövähennyslaskuri</h1>
        <p>Tämä työkalu on tarkoitettu Keskuskauppakamarin ilmastositoumukseen liittyville
          yrityksille ja yhteisöille
          oman toimintansa hiilijalanjäljen laskentaa ja raportointia varten. Laskennan taustat ja
          vaatimukset on esitetty tarkemmin erillisessä laskentaohjeessa.</p>
      </div>
    </div>
  </div>

  <div class="iframe-container">
    <iframe id="content-iframe" src="{{ url(LaravelLocalization::getCurrentLocale()) }}"></iframe>
  </div>

  <script>
    const baseUrl = '{{ url('/') }}';
    const currentLang = '{{ LaravelLocalization::getCurrentLocale() }}';

    function changeLanguage(lang) {
      window.location.href = `${baseUrl}/${lang}`;
    }

    document.addEventListener('DOMContentLoaded', function() {
      const languageSelect = document.getElementById('languageSelect');

      languageSelect.value = currentLang;

      languageSelect.addEventListener('change', function() {
        changeLanguage(this.value);
      });
    });
  </script>

  @vite('resources/js/parent.js')
</body>

</html>
