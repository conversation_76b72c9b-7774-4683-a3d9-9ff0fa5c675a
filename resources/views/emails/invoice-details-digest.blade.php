<x-mail::message>
# {{ __('emails.invoice.title') }}

{{ __('emails.invoice.companies_updated') }}

<x-mail::table>
| {{ __('emails.invoice.fields.company') }} | {{ __('emails.invoice.fields.business_id') }} | {{ __('emails.invoice.fields.e_invoice_address') }} | {{ __('emails.invoice.fields.operator') }} | {{ __('emails.invoice.fields.reference') }} |
|:--------|:----------|:-----------------|:------------|:------|
@foreach ($companies as $company)
| {{ $company->name }} | {{ $company->business_id }} | {{ $company->e_invoice_address ?? '-' }} | {{ $company->e_invoice_operator ?? '-' }} | {{ $company->e_invoice_reference ?? '-' }} |
@endforeach
</x-mail::table>

**{{ __('emails.invoice.report_created') }}** {{ $updateTime->format('d.m.Y H:i:s') }}

<x-mail::subcopy>{{ __('emails.invoice.automatic_summary', ['app' => Config::string('app.name')]) }}</x-mail::subcopy>
</x-mail::message>