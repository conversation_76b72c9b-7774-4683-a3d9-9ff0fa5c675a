.tooltip-container {
  @apply fixed z-50 max-w-xs rounded border border-blue-900 bg-white px-3 py-1.5 text-sm text-neutral-900 shadow-md;

  word-wrap: break-word;
  --arrow-offset: 5px; /* Default arrow position from left edge */

  transition:
    opacity 200ms ease-out,
    transform 200ms ease-out;
}

.tooltip-container.tooltip-hidden {
  @apply scale-95 opacity-0;
}

.tooltip-container.tooltip-visible {
  @apply scale-100 opacity-100;
}

.tooltip-container > div {
  @apply relative;
}

.tooltip-container p {
  @apply m-0 text-xs text-neutral-800;
}

.tooltip-container::before {
  @apply absolute border border-b-0 border-r-0 border-blue-900 bg-white;
  content: "";
  top: -5px;
  left: var(--arrow-offset, 5px);
  width: 9px;
  height: 9px;
  transform: rotate(45deg);
}

.tooltip-container.tooltip-above::before {
  @apply border border-l-0 border-t-0 border-blue-900;
  top: auto;
  bottom: -5px;
}

.tooltip-container::after {
  @apply absolute bg-white;
  content: "";
  top: -4px;
  left: calc(var(--arrow-offset, 5px) + 1px);
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
}

.tooltip-container.tooltip-above::after {
  top: auto;
  bottom: -4px;
}
