{"admin.calculations.actions.edit_options": "Edit options", "admin.calculations.errors.formula_check_system": "System error in formula validation", "admin.calculations.errors.formula_syntax": "Invalid formula syntax", "admin.calculations.errors.formula_validation_failed": "Formula validation failed", "admin.calculations.errors.invalid_formula": "Invalid formula", "admin.calculations.fields.category": "Category", "admin.calculations.fields.data_instruction": "Data instruction", "admin.calculations.fields.data_instruction_text": "Data instruction text", "admin.calculations.fields.data_name": "Data name", "admin.calculations.fields.data_unit": "Data unit", "admin.calculations.fields.default_source": "Default source", "admin.calculations.fields.emission_factor_compound_unit": "Emission factor compound unit", "admin.calculations.fields.emission_factor_default_source": "Emission factor default source", "admin.calculations.fields.emission_factor_default_value": "Emission factor default value", "admin.calculations.fields.emission_factor_instruction": "Emission factor instruction", "admin.calculations.fields.emission_factor_instruction_text": "Emission factor instruction text", "admin.calculations.fields.emission_factor_name": "Emission factor name", "admin.calculations.fields.formula": "Formula", "admin.calculations.fields.grouping": "Grouping", "admin.calculations.fields.identifier": "Identifier", "admin.calculations.fields.input_type": "Input type", "admin.calculations.fields.name": "Name", "admin.calculations.fields.option_set": "Option set", "admin.calculations.fields.order": "Order", "admin.calculations.fields.reference": "Reference", "admin.calculations.fields.result_instruction": "Result instruction", "admin.calculations.fields.result_instruction_text": "Result instruction text", "admin.calculations.fields.result_name": "Result name", "admin.calculations.fields.scope": "<PERSON><PERSON>", "admin.calculations.formulas.absolute_value": "returns absolute value", "admin.calculations.formulas.addition_full_precision": "addition with full precision", "admin.calculations.formulas.array_keys": "returns array keys", "admin.calculations.formulas.array_values": "returns array values (reindexed)", "admin.calculations.formulas.basic_arithmetic": "basic arithmetic", "admin.calculations.formulas.check_between_minmax": "checks if value is between min-max", "admin.calculations.formulas.check_in_array": "checks if value is in array", "admin.calculations.formulas.comparison_operators": "comparison operators", "admin.calculations.formulas.conditional": "conditional selection", "admin.calculations.formulas.count_array": "counts array elements", "admin.calculations.formulas.create_number_range": "creates number range", "admin.calculations.formulas.current_year": "current calculation year", "admin.calculations.formulas.division_with_precision": "division with specified precision (default 10)", "admin.calculations.formulas.exponentiation": "exponentiation", "admin.calculations.formulas.filter_by_field": "filters array by field value", "admin.calculations.formulas.filter_equals": "filters using == comparison", "admin.calculations.formulas.first_matching": "returns first matching element", "admin.calculations.formulas.flatten_arrays": "flattens nested arrays", "admin.calculations.formulas.get_data_value": "gets another field's data value", "admin.calculations.formulas.get_emission_factor": "gets another field's emission factor", "admin.calculations.formulas.get_field_or_default": "gets field value or returns default", "admin.calculations.formulas.group_by_field": "groups array by field value", "admin.calculations.formulas.logical_operators": "logical operators (also && || !)", "admin.calculations.formulas.map_transform": "transforms each element using expression (use _ to reference element)", "admin.calculations.formulas.maximum_value": "returns maximum value", "admin.calculations.formulas.minimum_value": "returns minimum value", "admin.calculations.formulas.multiplication_full_precision": "multiplication with full precision", "admin.calculations.formulas.power": "calculates x to the power of y", "admin.calculations.formulas.round_precision": "rounds to specified precision", "admin.calculations.formulas.scientific_notation": "scientific notation (= 1500)", "admin.calculations.formulas.square_root": "calculates square root", "admin.calculations.formulas.subtraction_full_precision": "subtraction with full precision", "admin.calculations.formulas.sum_full_precision": "sum of multiple values with full precision", "admin.calculations.formulas.underscores_in_numbers": "underscores in numbers for readability", "admin.calculations.formulas.yearly_values": "yearly values", "admin.calculations.help.arithmetic": "Arithmetic:", "admin.calculations.help.array_functions": "Array functions:", "admin.calculations.help.basic_operators": "Basic operators (floating-point arithmetic):", "admin.calculations.help.check_between": "Check between:", "admin.calculations.help.click_to_edit": "Click on definition to edit it:", "admin.calculations.help.common_formulas": "Common formula templates:", "admin.calculations.help.comparison_logic": "Comparison and logic:", "admin.calculations.help.comparison_operators": "Supported comparison operators in where/firstWhere functions: ==, ===, !=, !==, >, >=, <, <=", "admin.calculations.help.conditional": "Conditional calculation:", "admin.calculations.help.data_fetch_functions": "Data fetch functions:", "admin.calculations.help.data_page_help": "Help text shown to users on the data entry page", "admin.calculations.help.data_processing_functions": "Data processing functions:", "admin.calculations.help.emission_page_help": "Help text shown to users on the emission factors page", "admin.calculations.help.formula_description": "You can define a formula that calculates data value based on other data fields, and manage field visibility on different pages", "admin.calculations.help.group_identifier": "Used to identify groups in formulas", "admin.calculations.help.hide_data_page": "When enabled, the field won't show on the data page.", "admin.calculations.help.hide_emission_page": "When enabled, the field won't show on the emission factors page.", "admin.calculations.help.hide_results_page": "When enabled, the field won't show on the results page.", "admin.calculations.help.math_functions": "Mathematical functions:", "admin.calculations.help.multiple_emissions": "Combining multiple emissions:", "admin.calculations.help.option_set_identifier": "Identifier for this option set", "admin.calculations.help.percentage": "Percentage calculation:", "admin.calculations.help.placeholder_value": "This value is shown as a placeholder if the company hasn't entered their own value", "admin.calculations.help.precision_functions": "Precision-preserving functions:", "admin.calculations.help.results_page_help": "Help text shown to users on the results page", "admin.calculations.help.select_or_create_option_set": "Select or create an option set to use in this definition", "admin.calculations.help.simple_emission": "Simple emission:", "admin.calculations.help.variables": "Variables and special features:", "admin.calculations.help.year_adjustment": "Yearly adjustment:", "admin.calculations.input_types.manual": "Manual input", "admin.calculations.input_types.select": "Select menu", "admin.calculations.label": "Calculation definition", "admin.calculations.labels.data": "Data", "admin.calculations.placeholders.enter_formula": "Enter calculation formula here", "admin.calculations.plural": "Calculation definitions", "admin.calculations.references.definitions_referencing": "Definitions referencing field: ", "admin.calculations.references.fields_using": "Fields using this: ", "admin.calculations.references.none": "No references", "admin.calculations.references.these_reference_field": "These definitions reference this field:", "admin.calculations.references.used_by": "Used by: ", "admin.calculations.references.uses_references": "Uses references: ", "admin.calculations.sections.formula_visibility": "Calculation formula and visibility", "admin.calculations.sections.input": "Input", "admin.calculations.sections.page_visibility": "Page visibility", "admin.calculations.sections.references": "References", "admin.calculations.sections.units": "Units", "admin.calculations.sections.visibility": "Visibility", "admin.calculations.types.emission_factor": "Emission factor", "admin.calculations.types.results": "Results", "admin.calculations.visibility.hidden": "Hidden: ", "admin.calculations.visibility.hide_data_page": "Hide from data page", "admin.calculations.visibility.hide_emission_page": "Hide from emission factors page", "admin.calculations.visibility.hide_results_page": "Hide from results page", "admin.calculations.visibility.shown": "Shown: ", "admin.calculations.warnings.affects_calculations": "Changes to this field will affect these calculations too.", "admin.calculations.warnings.referenced_in_formulas": "This field is referenced in other formulas", "admin.categories.fields.description": "Description", "admin.categories.fields.grouping": "Classification", "admin.categories.fields.hide_total": "Hide total", "admin.categories.fields.name": "Name", "admin.categories.fields.order": "Order", "admin.categories.fields.template_definitions": "Template definitions", "admin.categories.fields.templates": "Templates", "admin.categories.help.hide_total_row": "Hide total row", "admin.categories.help.leave_empty_for_single": "Leave empty to create a single empty row", "admin.categories.help.select_definitions_to_copy": "Select definitions to copy when user adds a new row in this category", "admin.categories.help.select_multiple_for_group": "Select multiple to create a group of rows", "admin.categories.label": "Category", "admin.categories.plural": "Categories", "admin.categories.sections.definition_templates": "Definition templates", "admin.categories.units.template": "templates", "admin.common.added": "Added", "admin.common.all": "All", "admin.common.and_count_more": "and :count more", "admin.common.basic_info": "Basic information", "admin.common.created": "Created", "admin.common.delete": "Delete", "admin.common.delete_selected": "Delete selected", "admin.common.deleted": "Deleted", "admin.common.edit": "Edit", "admin.common.fields.order": "Order", "admin.common.language": "Language", "admin.common.name": "Name", "admin.common.trashed": "Trashed", "admin.common.updated": "Updated", "admin.companies.actions.add_year": "Add year", "admin.companies.actions.open": "Open", "admin.companies.badges.granted": "Granted", "admin.companies.badges.granted_scope_1_2": "Scope 1&2 badge granted", "admin.companies.badges.granted_scope_1_3": "Scope 1-3 badge granted", "admin.companies.badges.not_granted": "Not granted", "admin.companies.badges.scope_1_2": "Scope 1&2 badge", "admin.companies.badges.scope_1_3": "Scope 1-3 badge", "admin.companies.empty.no_year_data": "No year data", "admin.companies.fields.additional_info": "Additional information", "admin.companies.fields.applying_scope_1_2": "Applying for Scope 1-2 badge", "admin.companies.fields.applying_scope_1_3": "Applying for Scope 1-3 badge", "admin.companies.fields.business_id": "Business ID", "admin.companies.fields.contact_email": "Contact email", "admin.companies.fields.contact_name": "Contact name", "admin.companies.fields.data_processing_consent": "Data processing consent", "admin.companies.fields.einvoice_address": "E-invoice address", "admin.companies.fields.einvoice_operator": "E-invoice operator", "admin.companies.fields.employee_count": "Employee count", "admin.companies.fields.fiscal_end_day": "Fiscal year end day", "admin.companies.fields.fiscal_end_month": "Fiscal year end month", "admin.companies.fields.fiscal_start_day": "Fiscal year start day", "admin.companies.fields.fiscal_start_month": "Fiscal year start month", "admin.companies.fields.industry": "Industry", "admin.companies.fields.industry_classification": "Industry classification", "admin.companies.fields.municipality": "Municipality", "admin.companies.fields.name": "Name", "admin.companies.fields.reference": "Reference", "admin.companies.fields.revenue": "Revenue", "admin.companies.help.add_year_data": "Add year data using the button above", "admin.companies.label": "Company", "admin.companies.labels.badges": "Badges", "admin.companies.labels.participations": "Participations", "admin.companies.participation.climate_community": "Participates in climate community", "admin.companies.participation.climate_program": "Participates in climate program", "admin.companies.participation.not_participating": "Not participating", "admin.companies.participation.participating": "Participating", "admin.companies.plural": "Companies", "admin.companies.programs.annual_climate_programs": "Annual climate programs", "admin.companies.programs.climate_community": "Climate community", "admin.companies.programs.climate_program": "Climate program", "admin.companies.programs.climate_programs": "Climate programs", "admin.companies.scopes.scope_1_2": "Scope 1&2", "admin.companies.scopes.scope_1_3": "Scope 1-3", "admin.companies.sections.company_size": "Company size", "admin.companies.sections.consents": "Consents", "admin.companies.sections.fiscal_year": "Fiscal year", "admin.companies.sections.invoice_details": "Invoice details", "admin.companies.sections.location_industry": "Location and industry", "admin.groupings.fields.name": "Name", "admin.groupings.fields.order": "Order", "admin.groupings.label": "Grouping", "admin.groupings.plural": "Groupings", "admin.instructions.actions.save_changes": "Save changes", "admin.instructions.fields.content": "Content", "admin.instructions.messages.updated": "Instructions updated successfully", "admin.instructions.title": "Manage instructions", "admin.instructions.translations": "Translations", "admin.instructions.user_guide": "User guide", "admin.languages.english": "English", "admin.languages.finnish": "Finnish", "admin.languages.swedish": "Swedish", "admin.metrics.empty.no_year_definitions": "No year definitions", "admin.metrics.fields.help_text": "Help text", "admin.metrics.fields.name": "Metric name", "admin.metrics.fields.unit": "Unit", "admin.metrics.fields.unit_symbol": "Unit symbol", "admin.metrics.help.add_year_definitions": "Add year definitions using the button above", "admin.metrics.help.display_name": "Metric name displayed to users", "admin.metrics.help.instruction_text": "Provide help text to help users understand the metric's significance", "admin.metrics.help.select_unit": "Select the unit in which the metric value is reported", "admin.metrics.label": "Metric definition", "admin.metrics.plural": "Metric definitions", "admin.metrics.year_definition": "Year definition", "admin.metrics.year_definitions": "Year definitions", "admin.navigation.calculations": "Calculations", "admin.navigation.settings": "Settings", "admin.options.actions.add_year_value": "Add year value", "admin.options.empty.no_options": "No select options", "admin.options.errors.cannot_delete": "Cannot delete", "admin.options.errors.cannot_delete_in_use": "Cannot delete option set as it's used in definitions", "admin.options.errors.one_or_more_in_use": "One or more option sets are used in definitions", "admin.options.fields.employee_count": "Employee count", "admin.options.fields.grouping_hiding_reason": "Grouping hiding reason", "admin.options.fields.option_name": "Option name", "admin.options.fields.order": "Order", "admin.options.fields.revenue": "Revenue", "admin.options.fields.title": "Title", "admin.options.fields.type": "Type", "admin.options.fields.value": "Value", "admin.options.fields.values": "Values", "admin.options.fields.year": "Year", "admin.options.help.create_new": "Create a new select option using the button above", "admin.options.label": "Option", "admin.options.option_set": "Option set", "admin.options.option_sets": "Option sets", "admin.options.plural": "Options", "admin.options.sections.option_details": "Option details", "admin.options.sections.year_values": "Year values", "admin.options.select_option": "Select option", "admin.options.select_options": "Select options", "admin.options.status.active": "Active", "admin.options.status.inactive": "Inactive", "admin.scopes.errors.expression_check_system": "System error in expression validation", "admin.scopes.errors.expression_syntax": "Invalid expression syntax", "admin.scopes.errors.expression_validation_failed": "Expression validation failed", "admin.scopes.errors.invalid_filter_expression": "Invalid filter expression", "admin.scopes.fields.allow_grouping_hiding": "Allow grouping hiding", "admin.scopes.fields.filter_expression": "Filter expression", "admin.scopes.fields.grouping": "Classification", "admin.scopes.fields.hiding_allowed": "Hiding allowed", "admin.scopes.fields.name": "Name", "admin.scopes.fields.number": "Number", "admin.scopes.fields.order": "Order", "admin.scopes.filters.id_range": "ID range:", "admin.scopes.filters.no_filter": "No filter", "admin.scopes.filters.no_specific_groups": "No specific groups:", "admin.scopes.filters.specific_categories": "Specific categories:", "admin.scopes.formulas.check_between": "check if value is between", "admin.scopes.formulas.check_in_list": "check if value is in list", "admin.scopes.formulas.count_items": "count items", "admin.scopes.formulas.create_range": "create number range", "admin.scopes.formulas.max_value": "maximum value", "admin.scopes.formulas.min_value": "minimum value", "admin.scopes.help.available_variables": "Available variables:", "admin.scopes.help.categories_10_50": "Categories 10-50:", "admin.scopes.help.combination": "Combination:", "admin.scopes.help.examples": "Examples:", "admin.scopes.help.filter_expression": "Define filter expression to limit which calculation definitions are included in this variant", "admin.scopes.help.include_in_total": "Determines if this variant is included in total emissions", "admin.scopes.help.leave_empty_for_all": "Leave empty to include all definitions", "admin.scopes.help.supported_functions": "Supported functions:", "admin.scopes.help.supported_operators": "Supported operators:", "admin.scopes.label": "<PERSON><PERSON>", "admin.scopes.messages.variant_added_to_total": "Variant added to total", "admin.scopes.messages.variant_removed_from_total": "Variant removed from total", "admin.scopes.plural": "<PERSON><PERSON><PERSON>", "admin.scopes.sections.filtering": "Filtering", "admin.scopes.variables.category_id": "category ID", "admin.scopes.variables.definition_id": "definition ID", "admin.scopes.variables.grouping_id": "grouping ID", "admin.scopes.variants.in_total": "In total", "admin.scopes.variants.include_in_total": "Include in total", "admin.scopes.variants.included": "Included", "admin.scopes.variants.label": "Calculation variant", "admin.scopes.variants.not_included": "Not included", "admin.scopes.variants.plural": "Calculation variants", "admin.translations.actions.import_from_files": "Import from files", "admin.translations.details": "Translation details", "admin.translations.fields.group": "Group", "admin.translations.fields.key": "Key", "admin.translations.for_locale": "Translation (:locale)", "admin.translations.label": "Translation", "admin.translations.messages.imported_success": ":count translations imported successfully", "admin.translations.missing": "Missing translations", "admin.translations.plural": "Translations", "admin.units.compound_unit": "Compound unit", "admin.units.compound_units": "Compound units", "admin.units.conversion": "Unit conversion", "admin.units.conversions": "Unit conversions", "admin.units.fields.conversion_factor": "Conversion factor", "admin.units.fields.denominator_unit": "Denominator unit", "admin.units.fields.from_unit": "From unit", "admin.units.fields.name": "Name", "admin.units.fields.numerator_unit": "Numerator unit", "admin.units.fields.symbol": "Symbol", "admin.units.fields.to_unit": "To unit", "admin.units.label": "Unit", "admin.units.plural": "Units", "admin.years.actions.add_company": "Add company", "admin.years.actions.download_badges": "Download year badges", "admin.years.actions.download_criteria": "Download criteria and procedures", "admin.years.actions.hide": "<PERSON>de", "admin.years.actions.publish": "Publish", "admin.years.empty.no_companies": "No companies", "admin.years.help.add_companies": "Add companies for this year using the button above", "admin.years.help.scope_1_2_badge_image": "Scope 1&2 badge (JPG, PNG, SVG or WEBP, max 5MB)", "admin.years.help.scope_1_2_criteria_document": "Scope 1&2 criteria and procedures document (PDF, max 20MB)", "admin.years.help.scope_1_3_badge_image": "Scope 1-3 badge (JPG, PNG, SVG or WEBP, max 5MB)", "admin.years.help.scope_1_3_criteria_document": "Scope 1-3 criteria and procedures document (PDF, max 20MB)", "admin.years.label": "Year", "admin.years.labels.documents": "Documents", "admin.years.labels.scope_1_2_badge": "Scope 1&2 badge", "admin.years.labels.scope_1_2_criteria": "Scope 1&2 criteria and procedures", "admin.years.labels.scope_1_3_badge": "Scope 1-3 badge", "admin.years.labels.scope_1_3_criteria": "Scope 1-3 criteria and procedures", "admin.years.messages.scope_1_2_badge_uploaded": "Scope 1&2 badge uploaded", "admin.years.messages.scope_1_2_pdf_uploaded": "Scope 1&2 PDF uploaded", "admin.years.messages.scope_1_3_badge_uploaded": "Scope 1-3 badge uploaded", "admin.years.messages.scope_1_3_pdf_uploaded": "Scope 1-3 PDF uploaded", "admin.years.no_scope_1_2_badge": "No Scope 1&2 badge", "admin.years.no_scope_1_2_pdf": "No Scope 1&2 PDF", "admin.years.no_scope_1_3_badge": "No Scope 1-3 badge", "admin.years.no_scope_1_3_pdf": "No Scope 1-3 PDF", "admin.years.plural": "Years", "admin.years.scope_1_2_pdf": "1&2 PDF", "admin.years.scope_1_3_pdf": "1-3 PDF", "admin.years.scopes.scope_1_2": "Scope 1&2", "admin.years.scopes.scope_1_3": "Scope 1-3", "admin.years.sections.badge_images": "Badge images", "admin.years.status.published": "Published", "audit.actions.created": "Created", "audit.actions.deleted": "Deleted", "audit.actions.permanently_deleted": "Permanently deleted", "audit.actions.restored": "Restored", "audit.actions.updated": "Updated", "audit.empty.no_history": "No change history", "audit.empty.no_records": "No change history has been recorded yet.", "audit.fields.action": "Action", "audit.fields.additional_info": "Additional info", "audit.fields.applying_badge": "Applying for badge", "audit.fields.applying_scope_1_2": "Applying for Scope 1-2 badge", "audit.fields.applying_scope_1_3": "Applying for Scope 1-3 badge", "audit.fields.applying_scope_3": "Applying for Scope 3 badge", "audit.fields.business_id": "Business ID", "audit.fields.change_time": "Change time", "audit.fields.changed_by": "Changed by", "audit.fields.company": "Company", "audit.fields.company_name": "Company name", "audit.fields.contact_email": "Contact email", "audit.fields.contact_name": "Contact name", "audit.fields.data_viewing_permission": "Data viewing permission", "audit.fields.einvoice_address": "E-invoice address", "audit.fields.einvoice_operator": "E-invoice operator", "audit.fields.employee_count": "Employee count", "audit.fields.ending": "Ending", "audit.fields.field": "Field", "audit.fields.fiscal_end_day": "Fiscal year end (day)", "audit.fields.fiscal_end_month": "Fiscal year end (month)", "audit.fields.fiscal_start_day": "Fiscal year start (day)", "audit.fields.fiscal_start_month": "Fiscal year start (month)", "audit.fields.industry": "Industry", "audit.fields.municipality": "Municipality", "audit.fields.name": "Name", "audit.fields.reference": "Reference", "audit.fields.revenue": "Revenue", "audit.fields.row": "Row", "audit.fields.source": "Source", "audit.fields.starting_from": "Starting from", "audit.fields.target": "Target", "audit.fields.terms_accepted": "Terms accepted", "audit.fields.time_range": "Time range", "audit.fields.user": "User", "audit.fields.value": "Value", "audit.periods.all": "All", "audit.periods.seven_days": "7 days", "audit.periods.six_months": "6 months", "audit.periods.thirty_days": "30 days", "audit.periods.three_months": "3 months", "audit.periods.today": "Today", "audit.periods.week": "Week", "audit.periods.year": "Year", "audit.stats.change_count": "Number of changes", "audit.stats.changes": "Changes", "audit.stats.changes_by_target": "Changes by target", "audit.stats.changes_this_month": "Changes this month", "audit.stats.changes_this_week": "Changes this week", "audit.stats.changes_today": "Changes today", "audit.stats.most_active_company": "Most active company", "audit.stats.no_change_from_previous": "No change from previous", "audit.stats.no_change_from_yesterday": "No change from yesterday", "audit.stats.no_changes": "No changes", "audit.stats.no_changes_this_month": "No changes this month", "audit.status.hidden": "Hidden", "audit.status.target_hidden": "This target is hidden", "audit.title": "Change history", "audit.types.data": "Data", "audit.types.emission_factor": "Emission factor", "audit.types.other": "Other", "audit.unknown": "Unknown", "audit.values.new": "New", "audit.values.old": "Old", "auth.actions.login": "Sign in", "auth.errors.invalid_login_link": "Invalid login link. Check the link or request a new one.", "auth.errors.invalid_or_expired_link": "Invalid or expired login link.", "auth.errors.link_already_sent": "Login link has already been sent to this email address. You can request a new link in :minutes minutes.", "auth.errors.rate_limit": "Too many attempts. Try again in :minutes minutes.", "auth.errors.too_many_attempts": "Too many login attempts. Please try again later.", "auth.fields.email": "Email address", "auth.help.check_spam": "Check your spam folder or try again.", "auth.help.contact_support": "Contact support.", "auth.help.link_not_received": "Didn't receive the link?", "auth.help.problems": "In case of problems:", "auth.instructions": "Sign in with your email address – we'll send you a one-time link to access the service directly.", "auth.invitation.actions.accept": "Accept invitation", "auth.invitation.actions.decline": "Decline invitation", "auth.invitation.actions.sign_in": "Sign in", "auth.invitation.create_or_login": "Create an account or sign in to continue.", "auth.invitation.errors.decline_failed": "Failed to decline invitation.", "auth.invitation.errors.invalid": "Invalid invitation", "auth.invitation.errors.invalid_check_link": "Invalid invitation. Check the link or request a new invitation.", "auth.invitation.errors.invalid_invitation": "Invalid invitation.", "auth.invitation.errors.invalid_or_expired": "Invalid or expired invitation.", "auth.invitation.errors.too_many_attempts": "Too many attempts. Please try again later.", "auth.invitation.invited_to_company": "You have been invited to join company :company", "auth.invitation.messages.accepted": "Invitation accepted!", "auth.invitation.messages.accepted_create_account": "Invitation accepted! Create an account or sign in to continue.", "auth.invitation.messages.added_to_company": "You have been added to the company. Sign in to continue.", "auth.invitation.messages.declined": "Invitation declined", "auth.invitation.messages.invitation_declined": "The invitation has been declined.", "auth.invitation.title": "Company invitation", "auth.login.signing_in": "Signing in...", "auth.messages.check_email": "Check your email – you'll receive a link to access the service. The link is valid for 15 minutes.", "auth.messages.login_success": "Login successful!", "auth.placeholders.email": "<EMAIL>", "auth.title": "Sign in to Carbon Footprint Calculator", "calculations.errors.cannot_edit_global": "Global definitions cannot be edited", "calculations.errors.company_context_missing": "Company context missing", "calculations.errors.compound_unit_config_missing": "Compound unit configuration missing", "calculations.errors.decimal_processing": "Error processing decimal number", "calculations.errors.definition_not_found_or_unauthorized": "Definition not found or unauthorized", "calculations.errors.emission_factor_missing": "Emission factor missing and no default factor defined", "calculations.errors.formula_execution": "Error executing formula", "calculations.errors.formula_invalid_value": "Formula returned invalid value", "calculations.errors.formula_missing": "Formula missing or empty", "calculations.errors.formula_non_numeric": "Formula returned non-numeric value", "calculations.errors.formula_syntax": "Invalid formula syntax", "calculations.errors.missing_natural_unit": "Natural result unit identifier missing from compound unit configuration", "calculations.errors.template_not_found": "Template definitions not found", "calculations.errors.unexpected_calculation": "Unexpected error in calculation", "calculations.errors.unexpected_formula_evaluation": "Unexpected error in formula evaluation", "calculations.errors.unit_conversion_failed": "Unit conversion failed", "calculations.errors.unit_mismatch": "Units are incompatible. Data unit doesn't match factor denominator unit.", "calculations.templates.new_consumption_data": "New consumption data", "calculations.templates.new_emission_factor": "New emission factor", "common.actions.close": "Close", "common.actions.download_data": "Download data", "common.actions.download_excel": "Download Excel", "common.actions.go_back": "Go back", "common.actions.save": "Save", "common.actions.select": "Select", "common.actions.try_again": "Try again", "common.dash": "—", "common.errors.contact_admin": "Contact administrator.", "common.messages.select_company": "Select a company from the Company page to continue.", "common.navigation.go_to_company": "Go to Company page", "common.no": "No", "common.organization_name": "Finnish Central Chamber of Commerce", "common.pagination.go_to_page": "Go to page :page", "common.pagination.navigation": "Pagination navigation", "common.pagination.next": "Next", "common.pagination.next_page": "Next page", "common.pagination.of_changes": "of changes", "common.pagination.previous": "Previous", "common.pagination.previous_page": "Previous page", "common.pagination.separator": "-", "common.pagination.showing": "Showing", "common.pagination.total": "total", "common.processing": "Processing...", "common.text": "text", "common.total": "Total", "common.year": "Year", "common.yes": "Yes", "companies.actions.create_new": "Create new company", "companies.actions.remove_user": "Remove user", "companies.actions.send_invitation": "Send invitation", "companies.audit.empty.no_history": "No change history", "companies.audit.fields.change_time": "Change time", "companies.audit.fields.changed_by": "Changed by", "companies.audit.fields.field": "Field", "companies.audit.fields.name": "Name", "companies.audit.fields.target": "Target", "companies.audit.values.new": "New", "companies.audit.values.old": "Old", "companies.confirm.remove_user": "Are you sure you want to remove this user?", "companies.consent.accept_terms": "I accept the :terms and :privacy*", "companies.consent.data_viewing_permission": "I grant permission to Central Chamber of Commerce experts to view company data", "companies.empty.no_users": "No users", "companies.errors.cannot_create_here": "Cannot create a new company in this view", "companies.errors.cannot_remove_user": "Cannot remove this user", "companies.errors.company_not_found": "Company not found", "companies.errors.field_not_allowed": "Field not allowed", "companies.errors.field_not_exists": "Field does not exist", "companies.errors.field_update_failed": "Failed to update field", "companies.errors.invitation_already_sent": "Invitation has already been sent to this email address. You can request a new invitation in :minutes minutes.", "companies.errors.loading_failed": "Failed to load company", "companies.errors.modal_open_failed": "Failed to open modal", "companies.errors.no_create_permission": "You don't have permission to create a new company", "companies.errors.no_edit_permission": "You don't have permission to edit company information", "companies.errors.no_view_permission": "You don't have permission to view this company", "companies.errors.only_admin_can_invite": "Only administrators can invite new users", "companies.errors.rate_limit": "Too many attempts. Try again in :minutes minutes.", "companies.errors.switch_failed": "Failed to switch company", "companies.errors.too_many_invitations": "Too many invitation requests. Please try again later.", "companies.errors.user_already_member": "User is already a member of this company", "companies.errors.user_invite_failed": "Failed to invite user", "companies.errors.user_not_found": "User not found", "companies.errors.user_remove_failed": "Failed to remove user", "companies.fields.additional_info": "Additional information", "companies.fields.business_id_required": "Business ID*", "companies.fields.company_name": "Company name", "companies.fields.company_name_required": "Company name*", "companies.fields.contact_email": "Contact email", "companies.fields.contact_name": "Contact name", "companies.fields.einvoice_address": "E-invoice address", "companies.fields.einvoice_operator": "E-invoice operator", "companies.fields.email": "Email address", "companies.fields.email_required": "Email address*", "companies.fields.employee_count": "Employee count", "companies.fields.fiscal_year_end": "Fiscal year end", "companies.fields.fiscal_year_start": "Fiscal year start", "companies.fields.industry": "Industry", "companies.fields.municipality": "Municipality", "companies.fields.reference": "Reference", "companies.fields.revenue": "Revenue", "companies.fields.user": "User", "companies.fields.user_level": "User level", "companies.labels.applying_scope_1_2_badge": "Applying for Carbon Footprint Calculated Scope 1&2 badge", "companies.labels.applying_scope_1_3_badge": "Applying for Emissions Calculator Badge (Scope 1-3)", "companies.labels.invite_users": "Invite company personnel to the service", "companies.labels.nace_classification_before_icon": "(NACE Classification ", "companies.labels.nace_classification_after_icon": ")", "companies.messages.company_created": "Company created successfully", "companies.messages.create_company_first": "Please create a company first", "companies.messages.field_updated": "Field updated", "companies.messages.invitation_sent": "Invitation sent successfully", "companies.messages.select_or_create": "Select a company or create a new company.", "companies.messages.user_removed": "User removed from company successfully", "companies.placeholders.business_id": "1234567-8", "companies.placeholders.company_placeholder": "Company Ltd", "companies.placeholders.contact_email": "<EMAIL>", "companies.placeholders.contact_name": "First Last", "companies.placeholders.email": "<EMAIL>", "companies.placeholders.invoice_additional_info": "Additional information related to invoicing...", "companies.placeholders.select_company": "Select company...", "companies.placeholders.select_employee_count": "Select employee count...", "companies.placeholders.select_industry": "Select industry...", "companies.placeholders.select_municipality": "Select municipality...", "companies.placeholders.select_revenue": "Select revenue...", "companies.roles.admin": "Administrator", "companies.sections.company_info": "Company information", "companies.sections.permissions": "Permissions", "companies.sections.users": "Company users", "companies.tabs.audit_log": "Log", "companies.validation.invalid_business_id": "Invalid business ID", "companies.validation.invalid_fiscal_end": "Invalid fiscal year end date", "companies.validation.invalid_fiscal_start": "Invalid fiscal year start date", "companies.validation.scope_badge_exclusive": "You can apply for either Scope 1-2 or Scope 1-3 badge, not both", "data.actions.add_note": "Add note", "data.actions.add_row": "+ Add row", "data.actions.add_source": "Add source", "data.actions.back_to_reasons": "Back to preset reasons", "data.actions.delete_all_rows": "Delete all rows", "data.actions.delete_row": "Delete row", "data.actions.edit_source": "Edit source", "data.actions.hide": "<PERSON>de", "data.actions.show": "Show", "data.actions.show_help": "Show help", "data.confirm.delete_linked_rows": "Are you sure you want to delete these linked rows?", "data.confirm.delete_row": "Are you sure you want to delete this row?", "data.errors.action_not_available": "Action not available", "data.errors.can_only_delete_own": "You can only delete your own company's definitions", "data.errors.can_only_edit_own": "You can only edit your own company's definitions", "data.errors.cannot_display": "Data cannot be displayed", "data.errors.category_missing": "Category missing", "data.errors.company_context_missing": "Company context missing", "data.errors.definition_not_found": "Definition not found", "data.errors.definition_not_found_or_unauthorized": "Definition not found or unauthorized", "data.errors.grouping_missing": "Grouping missing", "data.errors.grouping_toggle_failed": "Failed to toggle grouping visibility", "data.errors.hiding_reason_save_failed": "Failed to save hiding reason", "data.errors.loading_failed": "Failed to load data", "data.errors.modal_open_failed": "Failed to open modal", "data.errors.name_update_failed": "Failed to update name", "data.errors.no_company_selected": "No company selected", "data.errors.no_delete_permission": "You don't have permission to delete definitions", "data.errors.no_edit_permission": "You don't have permission to edit company data", "data.errors.no_view_permission": "You don't have permission to view company data", "data.errors.reason_update_failed": "Failed to update reason", "data.errors.row_add_failed": "Failed to add row", "data.errors.row_delete_failed": "Failed to delete row", "data.errors.scope_missing": "<PERSON><PERSON> missing", "data.errors.source_save_failed": "Failed to save source", "data.errors.switch_to_reasons_failed": "Failed to switch to preset reasons", "data.errors.unit_update_failed": "Failed to update unit", "data.errors.unknown_definition": "Unknown definition", "data.errors.value_save_failed": "Failed to save value", "data.errors.year_not_available": "Year :year is not available", "data.fields.hiding_reason": "Reason for hiding", "data.fields.note": "Note", "data.fields.other_reason": "Other reason", "data.fields.unit": "Unit", "data.help.continue_list": "Continue the list as needed.", "data.help.record_sources": "Remember to record all data sources you use so the calculation can be repeated/verified as needed!", "data.labels.help": "Help", "data.labels.no_unit": "No unit", "data.messages.definition_added": "Calculation definition added successfully", "data.messages.definition_deleted": "Definition deleted successfully", "data.messages.definitions_added": "Calculation definitions added successfully", "data.messages.linked_definitions_deleted": "Linked definitions (:count items) deleted successfully", "data.messages.name_updated": "Name updated", "data.messages.reason_updated": "Reason updated", "data.messages.source_updated": "Source updated successfully", "data.messages.total_rows_deleted": "Total :count rows will be deleted", "data.messages.unit_updated": "Unit updated", "data.messages.value_deleted": "Value deleted", "data.messages.value_updated": "Value updated successfully", "data.placeholders.custom_reason": "Enter your own reason...", "data.placeholders.source_example": "E.g. Company HR", "data.scopes.scope_format": "Scope :number: :title", "data.sections.background": "Background information", "data.title": "Data", "data.validation.reason_too_long": "Reason is too long (maximum :n characters)", "data.warnings.linked_rows_deleted": "The following linked rows will also be deleted:", "emails.closing": "Best regards,", "emails.greeting": "Hello!", "emails.invitation.actions.accept": "Accept invitation", "emails.invitation.ignore_message": "If you didn't expect this invitation, you can ignore this message.", "emails.invitation.instructions": "Click the button below to accept the invitation. This link expires in :time.", "emails.invitation.project_invitation": "You have been invited to join company :company's Carbon Footprint Calculator project.", "emails.invitation.subject": "Invitation to join company :company", "emails.invoice.automatic_summary": "This is an automatic summary from system :app.", "emails.invoice.companies_updated": "The following companies have updated their e-invoice information:", "emails.invoice.fields.business_id": "Business ID", "emails.invoice.fields.company": "Company", "emails.invoice.fields.e_invoice_address": "E-invoice address", "emails.invoice.fields.operator": "Operator", "emails.invoice.fields.reference": "Reference", "emails.invoice.report_created": "Report created:", "emails.invoice.subject": "E-invoice details updated - :count company|E-invoice details updated - :count companies", "emails.invoice.title": "E-invoice details update summary", "emails.login.actions.sign_in": "Sign in", "emails.login.ignore_message": "If you didn't request this login link, you don't need to do anything.", "emails.login.instructions": "Click the button below to sign in to your account. This link expires in :time.", "emails.login.subject": "Your login link", "emails.signature": "Central Chamber of Commerce Sustainability Team", "emails.time_format": ":hours_text and :minutes_text", "emissions.actions.add_note": "Add note", "emissions.actions.add_source": "Add source", "emissions.actions.edit_source": "Edit source", "emissions.actions.show_help": "Show help", "emissions.empty.no_definitions": "No emission factor definitions found.", "emissions.errors.action_not_available": "Action not available", "emissions.errors.can_only_edit_own": "You can only edit your own company's definitions", "emissions.errors.cannot_display": "Data cannot be displayed", "emissions.errors.category_missing": "Category missing", "emissions.errors.company_context_missing": "Company context missing", "emissions.errors.definition_not_found": "Definition not found", "emissions.errors.definition_not_found_or_unauthorized": "Definition not found or unauthorized", "emissions.errors.grouping_missing": "Grouping missing", "emissions.errors.invalid_selected_value": "Selected value is not valid", "emissions.errors.loading_failed": "Failed to load emission factors", "emissions.errors.name_update_failed": "Failed to update name", "emissions.errors.no_company_selected": "No company selected", "emissions.errors.no_edit_permission": "You don't have permission to edit company emission factors", "emissions.errors.no_value_for_year": "No value defined for this year", "emissions.errors.no_view_permission": "You don't have permission to view company emission factors", "emissions.errors.number_processing": "Error processing numbers", "emissions.errors.save_failed": "Failed to save emission factor", "emissions.errors.scope_missing": "<PERSON><PERSON> missing", "emissions.errors.source_save_failed": "Failed to save source", "emissions.errors.unit_update_failed": "Failed to update unit", "emissions.errors.unknown_definition": "Unknown definition", "emissions.errors.year_not_available": "Year :year is not available", "emissions.fields.note": "Note", "emissions.fields.unit": "Unit", "emissions.help.record_sources": "Remember to record all data sources you use so the calculation can be repeated/verified as needed!", "emissions.labels.help": "Help", "emissions.labels.using_default": "Using default value", "emissions.messages.name_updated": "Name updated", "emissions.messages.section_hidden": "This section is hidden", "emissions.messages.source_updated": "Source updated successfully", "emissions.messages.unit_updated": "Unit updated", "emissions.messages.value_deleted": "Value deleted", "emissions.messages.value_updated": "Value updated successfully", "emissions.placeholders.source": "Enter source here (e.g. DEFRA 2025)", "emissions.scopes.scope_format": "Scope :number: :title", "emissions.title": "Emission factor", "enums.select_option_type.employee_count": "Employee Count Range", "enums.select_option_type.grouping_hiding_reason": "Grouping Hiding Reason", "enums.select_option_type.revenue": "Revenue Range", "errors.401.title": "Unauthorized", "errors.402.title": "Payment Required", "errors.403.title": "Forbidden", "errors.404.title": "Not Found", "errors.419.title": "Page Expired", "errors.429.title": "Too Many Requests", "errors.500.title": "Server Error", "errors.503.title": "Service Unavailable", "exports.actions.export_to_excel": "Export to Excel", "exports.common.name": "Name", "exports.common.unit": "Unit", "exports.data.fields.value": "Value", "exports.data.labels.source": "Source: :source", "exports.data.title": "Consumption data", "exports.emissions.fields.value": "Value", "exports.emissions.labels.source": "Source: :source", "exports.emissions.title": "Emission factors", "exports.errors.no_company_selected": "No company selected. Select a company before exporting.", "exports.errors.no_export_permission": "You don't have permission to export company data.", "exports.filters.all_years": "all_years", "exports.filters.year": "year", "exports.results.errors.missing_conversion": "ERROR: Missing conversion factor", "exports.results.fields.result": "Result", "exports.results.title": "Results", "exports.scopes.scope_format": "Scope :number: :title", "exports.sheets.emission_calculations": "emission_calculations", "legal.privacy_policy": "privacy policy", "legal.terms_of_service": "terms of service", "metrics.errors.company_context_missing": "Company context missing", "metrics.errors.year_not_available": "Year :year is not available", "metrics.messages.source_updated": "Source updated successfully", "metrics.messages.value_deleted": "Value deleted", "metrics.messages.value_updated": "Value updated successfully", "navigation.admin_panel": "Admin panel", "navigation.company_info": "Company information", "navigation.consumption_data": "Consumption data", "navigation.emission_factors": "Emission factors", "navigation.results": "Results", "navigation.user_guide": "User guide", "results.actions.download_criteria": "Download criteria", "results.actions.show_help": "Show help", "results.badges.scope_1_2": "Scope 1&2 badge", "results.badges.scope_1_3": "Scope 1-3 badge", "results.empty.no_results": "No carbon footprint results found.", "results.empty.no_results_for_category": "No results found for this category.", "results.errors.cannot_display": "Data cannot be displayed", "results.errors.loading_failed": "Failed to load results", "results.errors.no_company_selected": "No company selected", "results.errors.no_view_permission": "You don't have permission to view company results", "results.fields.reason_from_list": "Selected reason from list", "results.labels.error": "Error", "results.labels.granted_badges": "Granted badges", "results.labels.help": "Help", "results.labels.total_carbon_footprint": "Total carbon footprint", "results.messages.section_hidden": "This section is hidden", "results.notes.emissions_unit": "Note! All emissions are reported in unit :unit.", "results.scopes.scope_1_2": "Scope 1&2", "results.scopes.scope_1_3": "Scope 1-3", "results.scopes.scope_format": "Scope :number: :title", "results.sections.background": "Background information", "results.title.footprint_by_category": "Organization carbon footprint by category", "results.title.organization_footprint": "Organization carbon footprint", "results.title.organization_footprint_with_unit": "Organization carbon footprint (:unit)", "urls.nace_classification": "https://stat.fi/en/luokitukset/toimiala/toimiala_1_20250101#searchInput-label", "urls.privacy_policy": "https://kauppakamari.fi/en/privacy-statement-of-finland-chamber-of-commerce/", "urls.terms_and_conditions": "https://kauppakamari.fi/kayttoehdot/", "validations.valid_number": "Enter a valid number", "validations.value.not_negative": "Value cannot be negative", "navigation.logout": "Logout", "admin.companies.users.title": "Users", "admin.companies.users.settings": "User Settings", "admin.companies.users.is_primary": "Primary User", "admin.companies.users.is_primary_help": "Primary users have administrative rights for this company", "admin.companies.users.primary": "Primary", "admin.companies.users.primary_status": "Primary Status", "admin.companies.users.primary_only": "Primary Users Only", "admin.companies.users.non_primary": "Non-Primary Users", "admin.companies.users.remove_confirmation": "Remove User", "admin.companies.users.remove_description": "Are you sure you want to remove this user from the company?", "admin.companies.users.removing_primary": "Removing Primary User", "admin.companies.actions.add_user": "Add User", "admin.companies.empty.no_users": "No users assigned", "admin.companies.help.add_users": "Add users to manage this company", "admin.users.label": "User", "admin.users.plural": "Users", "admin.users.email": "Email", "admin.users.email_verified": "<PERSON><PERSON>", "admin.users.verification_status": "Verification Status", "admin.users.verified": "Verified", "admin.users.unverified": "Unverified", "admin.users.select_user": "Select a user", "admin.common.remove": "Remove", "common.fields.day": "Day", "common.fields.month": "Month", "common.pagination.current_page": "Current page", "companies.labels.select_company": "Select company", "data.fields.definition_name": "Definition name", "data.fields.custom_hiding_reason": "Custom hiding reason", "emissions.fields.definition_name": "Definition name"}