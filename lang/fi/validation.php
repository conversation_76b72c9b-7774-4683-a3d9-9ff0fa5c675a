<?php

declare(strict_types=1);

return [
  /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

  "accepted" => "Kenttä :attribute on hyväksyttävä.",
  "accepted_if" => "Kenttä :attribute on hyväksyttävä, kun :other on :value.",
  "active_url" => "Kentän :attribute on oltava kelvollinen URL-osoite.",
  "after" => "Kentän :attribute on oltava päivämäärä jälkeen :date.",
  "after_or_equal" => "Kentän :attribute on oltava päivämäärä :date tai sen jälkeen.",
  "alpha" => "Kenttä :attribute saa sisältää vain kirjaimia.",
  "alpha_dash" =>
    "Kenttä :attribute saa sisältää vain kirjaimia, numeroita, viivoja ja alaviivoja.",
  "alpha_num" => "Kenttä :attribute saa sisältää vain kirjaimia ja numeroita.",
  "any_of" => "Kenttä :attribute on virheellinen.",
  "array" => "Kentän :attribute on oltava taulukko.",
  "ascii" =>
    "Kenttä :attribute saa sisältää vain yhden tavun alfanumeerisia merkkejä ja symboleja.",
  "before" => "Kentän :attribute on oltava päivämäärä ennen :date.",
  "before_or_equal" => "Kentän :attribute on oltava päivämäärä :date tai sitä ennen.",
  "between" => [
    "array" => "Kentässä :attribute on oltava :min - :max kohdetta.",
    "file" => "Kentän :attribute on oltava :min - :max kilotavua.",
    "numeric" => "Kentän :attribute on oltava :min - :max välillä.",
    "string" => "Kentän :attribute on oltava :min - :max merkkiä.",
  ],
  "boolean" => "Kentän :attribute on oltava tosi tai epätosi.",
  "can" => "Kenttä :attribute sisältää luvattoman arvon.",
  "confirmed" => "Kentän :attribute vahvistus ei täsmää.",
  "contains" => "Kentästä :attribute puuttuu vaadittu arvo.",
  "current_password" => "Salasana on virheellinen.",
  "date" => "Kentän :attribute on oltava kelvollinen päivämäärä.",
  "date_equals" => "Kentän :attribute on oltava päivämäärä :date.",
  "date_format" => "Kentän :attribute on vastattava muotoa :format.",
  "decimal" => "Kentässä :attribute on oltava :decimal desimaalia.",
  "declined" => "Kenttä :attribute on hylättävä.",
  "declined_if" => "Kenttä :attribute on hylättävä, kun :other on :value.",
  "different" => "Kenttien :attribute ja :other on oltava erilaisia.",
  "digits" => "Kentän :attribute on oltava :digits numeroa.",
  "digits_between" => "Kentän :attribute on oltava :min - :max numeroa.",
  "dimensions" => "Kentällä :attribute on virheelliset kuvan mitat.",
  "distinct" => "Kentällä :attribute on kaksoiskappale.",
  "doesnt_end_with" => "Kenttä :attribute ei saa päättyä mihinkään seuraavista: :values.",
  "doesnt_start_with" => "Kenttä :attribute ei saa alkaa millään seuraavista: :values.",
  "email" => "Kentän :attribute on oltava kelvollinen sähköpostiosoite.",
  "ends_with" => "Kentän :attribute on päätyttävä johonkin seuraavista: :values.",
  "enum" => "Valittu :attribute on virheellinen.",
  "exists" => "Valittu :attribute on virheellinen.",
  "extensions" => "Kentällä :attribute on oltava jokin seuraavista tiedostopäätteistä: :values.",
  "file" => "Kentän :attribute on oltava tiedosto.",
  "filled" => "Kentällä :attribute on oltava arvo.",
  "gt" => [
    "array" => "Kentässä :attribute on oltava enemmän kuin :value kohdetta.",
    "file" => "Kentän :attribute on oltava suurempi kuin :value kilotavua.",
    "numeric" => "Kentän :attribute on oltava suurempi kuin :value.",
    "string" => "Kentän :attribute on oltava suurempi kuin :value merkkiä.",
  ],
  "gte" => [
    "array" => "Kentässä :attribute on oltava :value kohdetta tai enemmän.",
    "file" => "Kentän :attribute on oltava vähintään :value kilotavua.",
    "numeric" => "Kentän :attribute on oltava vähintään :value.",
    "string" => "Kentän :attribute on oltava vähintään :value merkkiä.",
  ],
  "hex_color" => "Kentän :attribute on oltava kelvollinen heksadesimaalinen väri.",
  "image" => "Kentän :attribute on oltava kuva.",
  "in" => "Valittu :attribute on virheellinen.",
  "in_array" => "Kentän :attribute on löydyttävä kentästä :other.",
  "integer" => "Kentän :attribute on oltava kokonaisluku.",
  "ip" => "Kentän :attribute on oltava kelvollinen IP-osoite.",
  "ipv4" => "Kentän :attribute on oltava kelvollinen IPv4-osoite.",
  "ipv6" => "Kentän :attribute on oltava kelvollinen IPv6-osoite.",
  "json" => "Kentän :attribute on oltava kelvollinen JSON-merkkijono.",
  "list" => "Kentän :attribute on oltava lista.",
  "lowercase" => "Kentän :attribute on oltava pienaakkosia.",
  "lt" => [
    "array" => "Kentässä :attribute on oltava vähemmän kuin :value kohdetta.",
    "file" => "Kentän :attribute on oltava pienempi kuin :value kilotavua.",
    "numeric" => "Kentän :attribute on oltava pienempi kuin :value.",
    "string" => "Kentän :attribute on oltava pienempi kuin :value merkkiä.",
  ],
  "lte" => [
    "array" => "Kentässä :attribute ei saa olla enempää kuin :value kohdetta.",
    "file" => "Kentän :attribute on oltava enintään :value kilotavua.",
    "numeric" => "Kentän :attribute on oltava enintään :value.",
    "string" => "Kentän :attribute on oltava enintään :value merkkiä.",
  ],
  "mac_address" => "Kentän :attribute on oltava kelvollinen MAC-osoite.",
  "max" => [
    "array" => "Kentässä :attribute ei saa olla enempää kuin :max kohdetta.",
    "file" => "Kenttä :attribute ei saa olla suurempi kuin :max kilotavua.",
    "numeric" => "Kenttä :attribute ei saa olla suurempi kuin :max.",
    "string" => "Kenttä :attribute ei saa olla suurempi kuin :max merkkiä.",
  ],
  "max_digits" => "Kentässä :attribute ei saa olla enempää kuin :max numeroa.",
  "mimes" => "Kentän :attribute on oltava tiedostotyyppiä: :values.",
  "mimetypes" => "Kentän :attribute on oltava tiedostotyyppiä: :values.",
  "min" => [
    "array" => "Kentässä :attribute on oltava vähintään :min kohdetta.",
    "file" => "Kentän :attribute on oltava vähintään :min kilotavua.",
    "numeric" => "Kentän :attribute on oltava vähintään :min.",
    "string" => "Kentän :attribute on oltava vähintään :min merkkiä.",
  ],
  "min_digits" => "Kentässä :attribute on oltava vähintään :min numeroa.",
  "missing" => "Kenttä :attribute on puututtava.",
  "missing_if" => "Kenttä :attribute on puututtava, kun :other on :value.",
  "missing_unless" => "Kenttä :attribute on puututtava, ellei :other ole :value.",
  "missing_with" => "Kenttä :attribute on puututtava, kun :values on läsnä.",
  "missing_with_all" => "Kenttä :attribute on puututtava, kun :values ovat läsnä.",
  "multiple_of" => "Kentän :attribute on oltava :value monikerta.",
  "not_in" => "Valittu :attribute on virheellinen.",
  "not_regex" => "Kentän :attribute muoto on virheellinen.",
  "numeric" => "Kentän :attribute on oltava numero.",
  "password" => [
    "letters" => "Kentässä :attribute on oltava vähintään yksi kirjain.",
    "mixed" => "Kentässä :attribute on oltava vähintään yksi iso ja yksi pieni kirjain.",
    "numbers" => "Kentässä :attribute on oltava vähintään yksi numero.",
    "symbols" => "Kentässä :attribute on oltava vähintään yksi symboli.",
    "uncompromised" => "Annettu :attribute on esiintynyt tietovuodossa. Valitse toinen :attribute.",
  ],
  "present" => "Kenttä :attribute on oltava läsnä.",
  "present_if" => "Kenttä :attribute on oltava läsnä, kun :other on :value.",
  "present_unless" => "Kenttä :attribute on oltava läsnä, ellei :other ole :value.",
  "present_with" => "Kenttä :attribute on oltava läsnä, kun :values on läsnä.",
  "present_with_all" => "Kenttä :attribute on oltava läsnä, kun :values ovat läsnä.",
  "prohibited" => "Kenttä :attribute on kielletty.",
  "prohibited_if" => "Kenttä :attribute on kielletty, kun :other on :value.",
  "prohibited_if_accepted" => "Kenttä :attribute on kielletty, kun :other hyväksytään.",
  "prohibited_if_declined" => "Kenttä :attribute on kielletty, kun :other hylätään.",
  "prohibited_unless" => "Kenttä :attribute on kielletty, ellei :other ole joukossa :values.",
  "prohibits" => "Kenttä :attribute kieltää kentän :other läsnäolon.",
  "regex" => "Kentän :attribute muoto on virheellinen.",
  "required" => "Kenttä :attribute vaaditaan.",
  "required_array_keys" => "Kentässä :attribute on oltava merkinnät: :values.",
  "required_if" => "Kenttä :attribute vaaditaan, kun :other on :value.",
  "required_if_accepted" => "Kenttä :attribute vaaditaan, kun :other hyväksytään.",
  "required_if_declined" => "Kenttä :attribute vaaditaan, kun :other hylätään.",
  "required_unless" => "Kenttä :attribute vaaditaan, ellei :other ole joukossa :values.",
  "required_with" => "Kenttä :attribute vaaditaan, kun :values on läsnä.",
  "required_with_all" => "Kenttä :attribute vaaditaan, kun :values ovat läsnä.",
  "required_without" => "Kenttä :attribute vaaditaan, kun :values ei ole läsnä.",
  "required_without_all" => "Kenttä :attribute vaaditaan, kun mikään :values ei ole läsnä.",
  "same" => "Kentän :attribute on vastattava kenttää :other.",
  "size" => [
    "array" => "Kentässä :attribute on oltava :size kohdetta.",
    "file" => "Kentän :attribute on oltava :size kilotavua.",
    "numeric" => "Kentän :attribute on oltava :size.",
    "string" => "Kentän :attribute on oltava :size merkkiä.",
  ],
  "starts_with" => "Kentän :attribute on alettava jollain seuraavista: :values.",
  "string" => "Kentän :attribute on oltava merkkijono.",
  "timezone" => "Kentän :attribute on oltava kelvollinen aikavyöhyke.",
  "unique" => ":attribute on jo varattu.",
  "uploaded" => ":attribute lataus epäonnistui.",
  "uppercase" => "Kentän :attribute on oltava suuraakkosia.",
  "url" => "Kentän :attribute on oltava kelvollinen URL-osoite.",
  "ulid" => "Kentän :attribute on oltava kelvollinen ULID.",
  "uuid" => "Kentän :attribute on oltava kelvollinen UUID.",

  /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

  "custom" => [
    "attribute-name" => [
      "rule-name" => "custom-message",
    ],
  ],

  /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

  "attributes" => [],
];
