<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Helpers\NumericStringFormatter;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class NumericStringFormatterTest extends TestCase
{
  private NumericStringFormatter $formatter;

  protected function setUp(): void
  {
    parent::setUp();
    // Test with Finnish format (space for thousands, comma for decimals)
    $this->formatter = new NumericStringFormatter(" ", ",");
  }

  #[DataProvider("validNumberProvider")]
  public function testFormatsValidNumbers(string $input, string $expected): void
  {
    $this->assertEquals($expected, $this->formatter->format($input));
  }

  /**
   * @return list<array{string, string}>
   */
  public static function validNumberProvider(): array
  {
    return [
      // Simple integers
      ["0", "0"],
      ["1", "1"],
      ["12", "12"],
      ["123", "123"],
      ["1234", "1 234"],
      ["12345", "12 345"],
      ["123456", "123 456"],
      ["1234567", "1 234 567"],
      ["12345678", "12 345 678"],
      ["123456789", "123 456 789"],
      ["**********", "1 234 567 890"],

      // Decimals
      ["0.0", "0,0"],
      ["0.1", "0,1"],
      ["0.12", "0,12"],
      ["0.123", "0,123"],
      ["0.1234", "0,1234"],
      ["1.5", "1,5"],
      ["12.34", "12,34"],
      ["123.456", "123,456"],
      ["1234.5678", "1 234,5678"],
      ["12345.67890", "12 345,67890"],

      // Small decimals
      ["0.001", "0,001"],
      ["0.0001", "0,0001"],
      ["0.00001", "0,00001"],

      // Negative numbers
      ["-0", "-0"],
      ["-1", "-1"],
      ["-12", "-12"],
      ["-123", "-123"],
      ["-1234", "-1 234"],
      ["-12345", "-12 345"],
      ["-123456", "-123 456"],
      ["-1234567", "-1 234 567"],

      // Negative decimals
      ["-0.1", "-0,1"],
      ["-0.12", "-0,12"],
      ["-1.5", "-1,5"],
      ["-12.34", "-12,34"],
      ["-123.456", "-123,456"],
      ["-1234.5678", "-1 234,5678"],
      ["-12345.67890", "-12 345,67890"],

      // Edge cases
      ["999", "999"],
      ["1000", "1 000"],
      ["9999", "9 999"],
      ["10000", "10 000"],
      ["99999", "99 999"],
      ["100000", "100 000"],
      ["999999", "999 999"],
      ["1000000", "1 000 000"],

      // Very large numbers
      ["**********123456789", "1 234 567 890 123 456 789"],
      ["9999999999999999999", "9 999 999 999 999 999 999"],

      // Numbers with many decimal places
      ["1.**********12345", "1,**********12345"],
      ["1234.**********12345", "1 234,**********12345"],
    ];
  }

  #[DataProvider("invalidNumberProvider")]
  public function testThrowsExceptionForInvalidInput(string $input): void
  {
    $this->expectException(\InvalidArgumentException::class);
    $this->formatter->format($input);
  }

  /**
   * @return list<array{string}>
   */
  public static function invalidNumberProvider(): array
  {
    return [
      [""],
      [" "],
      ["abc"],
      ["12.34.56"],
      ["12,34"],
      ["1 234"],
      ['$1234'],
      ['1234$'],
      ["12e10"],
      ["12E10"],
      ["+1234"],
      ["--1234"],
      ["1234-"],
      ["12-34"],
      ["12.34."],
      [".1234"],
      ["1234."],
      ["NaN"],
      ["Infinity"],
      ["-Infinity"],
      ["0x1234"],
      ["0b1010"],
      ["1.2.3.4"],
      ["1,234,567"],
      ["1 234 567"],
      ["١٢٣٤"], // Arabic numerals
    ];
  }

  public function testDefaultConstructorUsesDotForDecimals(): void
  {
    $defaultFormatter = new NumericStringFormatter();
    $this->assertEquals("1 234 567.89", $defaultFormatter->format("1234567.89"));
    $this->assertEquals("0.123", $defaultFormatter->format("0.123"));
    $this->assertEquals("-1 234.56", $defaultFormatter->format("-1234.56"));
  }

  public function testCustomSeparators(): void
  {
    // Test with comma for thousands and period for decimals (US format)
    $usFormatter = new NumericStringFormatter(",", ".");
    $this->assertEquals("1,234,567.89", $usFormatter->format("1234567.89"));
    $this->assertEquals("-1,234,567.89", $usFormatter->format("-1234567.89"));

    // Test with apostrophe for thousands (Swiss format)
    $swissFormatter = new NumericStringFormatter("'", ".");
    $this->assertEquals("1'234'567.89", $swissFormatter->format("1234567.89"));

    // Test with underscore for thousands (programming style)
    $progFormatter = new NumericStringFormatter("_", ".");
    $this->assertEquals("1_234_567.89", $progFormatter->format("1234567.89"));

    // Test with no thousand separator
    $noThousandFormatter = new NumericStringFormatter("", ".");
    $this->assertEquals("1234567.89", $noThousandFormatter->format("1234567.89"));
  }

  public function testHandlesZerosProperly(): void
  {
    $this->assertEquals("0", $this->formatter->format("0"));
    $this->assertEquals("-0", $this->formatter->format("-0"));
    $this->assertEquals("0,0", $this->formatter->format("0.0"));
    $this->assertEquals("0,00", $this->formatter->format("0.00"));
    $this->assertEquals("0,000", $this->formatter->format("0.000"));
  }

  public function testHandlesLeadingZeros(): void
  {
    // Leading zeros in integer part should be preserved
    $this->assertEquals("001", $this->formatter->format("001"));
    $this->assertEquals("00 123", $this->formatter->format("00123"));
    $this->assertEquals("000 123", $this->formatter->format("000123"));
    $this->assertEquals("1 000", $this->formatter->format("1000"));
  }

  public function testPreservesDecimalPrecision(): void
  {
    // Should preserve all decimal places exactly as given
    $this->assertEquals("1,0", $this->formatter->format("1.0"));
    $this->assertEquals("1,00", $this->formatter->format("1.00"));
    $this->assertEquals("1,000", $this->formatter->format("1.000"));
    $this->assertEquals("1,10", $this->formatter->format("1.10"));
    $this->assertEquals("1,100", $this->formatter->format("1.100"));
  }

  public function testBoundaryConditions(): void
  {
    // Test around thousand boundaries
    $this->assertEquals("999", $this->formatter->format("999"));
    $this->assertEquals("1 000", $this->formatter->format("1000"));
    $this->assertEquals("1 001", $this->formatter->format("1001"));

    $this->assertEquals("999 999", $this->formatter->format("999999"));
    $this->assertEquals("1 000 000", $this->formatter->format("1000000"));
    $this->assertEquals("1 000 001", $this->formatter->format("1000001"));

    // Negative boundaries
    $this->assertEquals("-999", $this->formatter->format("-999"));
    $this->assertEquals("-1 000", $this->formatter->format("-1000"));
    $this->assertEquals("-1 001", $this->formatter->format("-1001"));
  }

  public function testEmptySeparator(): void
  {
    $formatter = new NumericStringFormatter("", ",");
    $this->assertEquals("1234567,89", $formatter->format("1234567.89"));
    $this->assertEquals("123,456", $formatter->format("123.456"));
  }

  public function testMultiCharacterSeparators(): void
  {
    $formatter = new NumericStringFormatter(" - ", " | ");
    $this->assertEquals("1 - 234 - 567 | 89", $formatter->format("1234567.89"));
    $this->assertEquals("-1 - 234 - 567 | 89", $formatter->format("-1234567.89"));
  }

  public function testIdempotence(): void
  {
    // Formatting the same number multiple times should yield the same result
    $input = "1234567.89";
    $expected = "1 234 567,89";

    $this->assertEquals($expected, $this->formatter->format($input));
    $this->assertEquals($expected, $this->formatter->format($input));
    $this->assertEquals($expected, $this->formatter->format($input));
  }

  public function testThreadSafety(): void
  {
    // Test that the formatter doesn't maintain state between calls
    $this->assertEquals("1 234", $this->formatter->format("1234"));
    $this->assertEquals("5 678", $this->formatter->format("5678"));
    $this->assertEquals("1 234", $this->formatter->format("1234"));
  }
}
