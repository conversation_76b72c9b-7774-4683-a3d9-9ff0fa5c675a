<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Helpers\RichTextProcessor;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Test;

class RichTextProcessorTest extends TestCase
{
  #[Test]
  public function it_handles_empty_input(): void
  {
    $this->assertEquals("", RichTextProcessor::ensureImageAlts(""));
  }

  #[Test]
  public function it_adds_empty_alt_to_images_without_alt(): void
  {
    $html = '<img src="test.jpg">';
    $expected = '<img src="test.jpg" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_preserves_existing_alt_attributes(): void
  {
    $html = '<img src="test.jpg" alt="Existing alt text">';
    $expected = '<img src="test.jpg" alt="Existing alt text">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_standalone_images(): void
  {
    $html = '<img src="standalone.jpg">';
    $expected = '<img src="standalone.jpg" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_adds_empty_alt_to_images_in_figures_with_figcaption(): void
  {
    $html = '<figure><img src="test.jpg"><figcaption>Caption text</figcaption></figure>';
    $expected = '<figure><img src="test.jpg" alt=""><figcaption>Caption text</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_figure_without_figcaption(): void
  {
    $html = '<figure><img src="test.jpg"></figure>';
    $expected = '<figure><img src="test.jpg" alt=""></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_empty_figcaption(): void
  {
    $html = '<figure><img src="test.jpg"><figcaption></figcaption></figure>';
    $expected = '<figure><img src="test.jpg" alt=""><figcaption></figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_whitespace_only_figcaption(): void
  {
    $html = '<figure><img src="test.jpg"><figcaption>   </figcaption></figure>';
    $expected = '<figure><img src="test.jpg" alt=""><figcaption>   </figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_nested_html_in_figcaption(): void
  {
    $html =
      '<figure><img src="test.jpg"><figcaption>Caption with <strong>bold</strong> text</figcaption></figure>';
    $expected =
      '<figure><img src="test.jpg" alt=""><figcaption>Caption with <strong>bold</strong> text</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_does_not_override_existing_alt_in_figures(): void
  {
    $html =
      '<figure><img src="test.jpg" alt="Existing"><figcaption>Different caption</figcaption></figure>';
    $expected =
      '<figure><img src="test.jpg" alt="Existing"><figcaption>Different caption</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_multiple_images(): void
  {
    $html = '<img src="img1.jpg"><img src="img2.jpg" alt="Has alt"><img src="img3.jpg">';
    $expected =
      '<img src="img1.jpg" alt=""><img src="img2.jpg" alt="Has alt"><img src="img3.jpg" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_multiple_images_in_single_figure(): void
  {
    $html =
      '<figure><img src="img1.jpg"><img src="img2.jpg"><figcaption>Multiple images</figcaption></figure>';
    $expected =
      '<figure><img src="img1.jpg" alt=""><img src="img2.jpg" alt=""><figcaption>Multiple images</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_multiple_figcaptions_in_single_figure(): void
  {
    $html =
      '<figure><img src="test.jpg"><figcaption>First</figcaption><figcaption>Second</figcaption></figure>';
    $expected =
      '<figure><img src="test.jpg" alt=""><figcaption>First</figcaption><figcaption>Second</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_figcaption_before_img(): void
  {
    $html = '<figure><figcaption>Caption first</figcaption><img src="test.jpg"></figure>';
    $expected =
      '<figure><figcaption>Caption first</figcaption><img src="test.jpg" alt=""></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_multiple_figures(): void
  {
    $html =
      '<figure><img src="img1.jpg"><figcaption>First image</figcaption></figure><figure><img src="img2.jpg"><figcaption>Second image</figcaption></figure>';
    $expected =
      '<figure><img src="img1.jpg" alt=""><figcaption>First image</figcaption></figure><figure><img src="img2.jpg" alt=""><figcaption>Second image</figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_mixed_content(): void
  {
    $html =
      '<p>Some text</p><figure><img src="fig.jpg"><figcaption>Figure caption</figcaption></figure><img src="standalone.jpg"><p>More text</p>';
    $expected =
      '<p>Some text</p><figure><img src="fig.jpg" alt=""><figcaption>Figure caption</figcaption></figure><img src="standalone.jpg" alt=""><p>More text</p>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_images_with_various_attributes(): void
  {
    $html =
      '<img src="test.jpg" class="responsive" id="hero" data-lazy="true" width="100" height="200">';
    $expected =
      '<img src="test.jpg" class="responsive" id="hero" data-lazy="true" width="100" height="200" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_malformed_html(): void
  {
    $html = '<img src="test.jpg"><p>Unclosed paragraph';
    $expected = '<img src="test.jpg" alt=""><p>Unclosed paragraph</p>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_nested_structures(): void
  {
    $html =
      '<div><section><figure><img src="nested.jpg"><figcaption>Nested caption</figcaption></figure></section></div>';
    $expected =
      '<div><section><figure><img src="nested.jpg" alt=""><figcaption>Nested caption</figcaption></figure></section></div>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_broken_figure_structure(): void
  {
    $html = '<img src="test.jpg"><figcaption>Orphan caption</figcaption>';
    $expected = '<img src="test.jpg" alt=""><figcaption>Orphan caption</figcaption>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_self_closing_svg_images(): void
  {
    $html = '<img src="icon.svg">';
    $expected = '<img src="icon.svg" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_images_with_srcset(): void
  {
    $html = '<img src="small.jpg" srcset="medium.jpg 1000w, large.jpg 2000w">';
    $expected = '<img src="small.jpg" srcset="medium.jpg 1000w, large.jpg 2000w" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_data_uri_images(): void
  {
    $html = '<img src="data:image/png;base64,iVBORw0KGgoAAAANS...">';
    $expected = '<img src="data:image/png;base64,iVBORw0KGgoAAAANS..." alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_picture_element_with_img(): void
  {
    $html =
      '<picture><source srcset="mobile.jpg" media="(max-width: 768px)"><img src="desktop.jpg"></picture>';
    $expected =
      '<picture><source srcset="mobile.jpg" media="(max-width: 768px)"><img src="desktop.jpg" alt=""></picture>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_very_long_figcaption(): void
  {
    $longCaption = str_repeat("Very long caption text ", 100);
    $html = "<figure><img src=\"test.jpg\"><figcaption>$longCaption</figcaption></figure>";
    $expected = "<figure><img src=\"test.jpg\" alt=\"\"><figcaption>$longCaption</figcaption></figure>";

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_special_characters_in_figcaption(): void
  {
    $html =
      '<figure><img src="test.jpg"><figcaption>Caption with "quotes" & special <characters></figcaption></figure>';
    $expected =
      '<figure><img src="test.jpg" alt=""><figcaption>Caption with "quotes" &amp; special <characters></characters></figcaption></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_html_entities_in_attributes(): void
  {
    $html = '<img src="test.jpg" title="&copy; 2024 &mdash; Test">';
    $expected = '<img src="test.jpg" title="© 2024 — Test" alt="">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_trix_editor_output(): void
  {
    $html =
      '<figure data-trix-attachment="{&quot;contentType&quot;:&quot;image/jpeg&quot;}" class="attachment attachment--preview"><a href="https://example.com/image.jpg"><img src="https://example.com/image.jpg" width="1416" height="1077"><figcaption class="attachment__caption">Test caption</figcaption></a></figure>';
    $expected =
      '<figure data-trix-attachment="{&quot;contentType&quot;:&quot;image/jpeg&quot;}" class="attachment attachment--preview"><a href="https://example.com/image.jpg"><img src="https://example.com/image.jpg" width="1416" height="1077" alt=""><figcaption class="attachment__caption">Test caption</figcaption></a></figure>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_complex_real_world_content(): void
  {
    $html =
      '<article><h2>Article Title</h2><figure class="main-image"><img src="hero.jpg" loading="lazy"><figcaption>Hero image caption</figcaption></figure><p>Text with <img src="inline.gif" style="width: 20px;"> inline image.</p><div class="gallery"><img src="gallery1.jpg" alt="Already has alt"><img src="gallery2.jpg"></div></article>';
    $expected =
      '<article><h2>Article Title</h2><figure class="main-image"><img src="hero.jpg" loading="lazy" alt=""><figcaption>Hero image caption</figcaption></figure><p>Text with <img src="inline.gif" style="width: 20px;" alt=""> inline image.</p><div class="gallery"><img src="gallery1.jpg" alt="Already has alt"><img src="gallery2.jpg" alt=""></div></article>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_handles_international_characters(): void
  {
    $html =
      '<h2>PÅÄÖÅ</h2><figure><img src="test.jpg"><figcaption>Ääkköset öljyssä</figcaption></figure><p>Texte en français avec é è à ç</p>';
    $expected =
      '<h2>PÅÄÖÅ</h2><figure><img src="test.jpg" alt=""><figcaption>Ääkköset öljyssä</figcaption></figure><p>Texte en français avec é è à ç</p>';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }

  #[Test]
  public function it_follows_lighthouse_accessibility_best_practices(): void
  {
    $html =
      '<figure><img src="chart.jpg"><figcaption>Sales chart showing Q3 growth</figcaption></figure><img src="decorative.jpg"><img src="info.jpg" alt="Important information">';
    $expected =
      '<figure><img src="chart.jpg" alt=""><figcaption>Sales chart showing Q3 growth</figcaption></figure><img src="decorative.jpg" alt=""><img src="info.jpg" alt="Important information">';

    $this->assertEquals($expected, RichTextProcessor::ensureImageAlts($html));
  }
}
