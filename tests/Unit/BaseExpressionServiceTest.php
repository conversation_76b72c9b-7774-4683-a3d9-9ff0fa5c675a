<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Services\BaseExpressionService;
use Exception;
use PHPUnit\Framework\TestCase;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

final class TestableExpressionService extends BaseExpressionService
{
  public function getExpressionLanguage(): ExpressionLanguage
  {
    if ($this->expressionLanguage === null) {
      $this->createExpressionLanguage();
    }
    // We know it's not null after createExpressionLanguage()
    assert($this->expressionLanguage !== null);

    return $this->expressionLanguage;
  }

  /**
   * @param  array<string, mixed>  $values
   */
  public function evaluate(string $expression, array $values = []): mixed
  {
    return $this->getExpressionLanguage()->evaluate($expression, $values);
  }
}

final class BaseExpressionServiceTest extends TestCase
{
  private TestableExpressionService $service;

  protected function setUp(): void
  {
    $this->service = new TestableExpressionService();
  }

  /**
   * Test where function with null handling
   */
  public function test_where_with_null_values(): void
  {
    $definitions = [
      ["id" => 1, "link_id" => 123, "tag" => "kunta_lampo"],
      ["id" => 2, "link_id" => null, "tag" => "alkuperatakuu_lampo"],
      ["id" => 3, "tag" => "test"], // link_id not set at all
      ["id" => 4, "link_id" => 456, "tag" => "kunta_lampo"],
    ];

    // Test new Laravel style
    $newStyleExpression = 'where(definitions, "link_id", "!=", null)';

    $newResult = $this->service->evaluate($newStyleExpression, ["definitions" => $definitions]);

    // Assert the result is an array
    $this->assertIsArray($newResult);

    echo "New style result count: " . count($newResult) . "\n";
    echo "New style IDs: " . implode(", ", array_column($newResult, "id")) . "\n";

    // Both should filter out items where link_id is null or not set
    $this->assertCount(2, $newResult, "Should have 2 items with non-null link_id");
    $this->assertArrayHasKey(0, $newResult);
    $this->assertArrayHasKey(3, $newResult);
  }

  /**
   * Test where function with undefined fields
   */
  public function test_where_with_undefined_fields(): void
  {
    $definitions = [
      ["id" => 1, "link_id" => 123],
      ["id" => 2], // link_id undefined
      ["id" => 3, "link_id" => null], // link_id explicitly null
    ];

    // Test != null check
    $result = $this->service->evaluate('where(definitions, "link_id", "!=", null)', [
      "definitions" => $definitions,
    ]);

    $this->assertIsArray($result);

    echo "Items with link_id != null: " . count($result) . "\n";
    echo "IDs: " . implode(", ", array_column($result, "id")) . "\n";

    // Should only return item 1 (link_id = 123)
    $this->assertCount(1, $result);
    $firstItem = reset($result);
    if ($firstItem !== false && is_array($firstItem) && array_key_exists("id", $firstItem)) {
      $this->assertEquals(1, $firstItem["id"]);
    } else {
      self::fail("Result should have an item with id key");
    }

    // Test == null check
    $nullResult = $this->service->evaluate('where(definitions, "link_id", "==", null)', [
      "definitions" => $definitions,
    ]);

    $this->assertIsArray($nullResult);

    echo "Items with link_id == null: " . count($nullResult) . "\n";

    // Should return items 2 and 3 (undefined and null are both treated as null)
    $this->assertCount(2, $nullResult);
  }

  /**
   * Test groupBy with various field scenarios
   */
  public function test_group_by(): void
  {
    $definitions = [
      ["id" => 1, "link_id" => 123, "tag" => "kunta_lampo"],
      ["id" => 2, "link_id" => 123, "tag" => "alkuperatakuu_lampo"],
      ["id" => 3, "link_id" => 456, "tag" => "kunta_lampo"],
      ["id" => 4, "link_id" => null, "tag" => "test"],
      ["id" => 5, "tag" => "test", "link_id" => null], // explicitly set to null for consistency
    ];

    $grouped = $this->service->evaluate('groupBy(definitions, "link_id")', [
      "definitions" => $definitions,
    ]);

    $this->assertIsArray($grouped);

    echo "Grouped keys: " . implode(", ", array_keys($grouped)) . "\n";

    // Check grouping exists
    $this->assertTrue(array_key_exists("123", $grouped));
    $this->assertTrue(array_key_exists("456", $grouped));
    $this->assertTrue(array_key_exists("null", $grouped));

    // Use isset() which PHPStan understands better for type narrowing
    if (isset($grouped["123"]) && isset($grouped["456"]) && isset($grouped["null"])) {
      $group123 = $grouped["123"];
      $group456 = $grouped["456"];
      $groupNull = $grouped["null"];

      $this->assertIsArray($group123);
      $this->assertIsArray($group456);
      $this->assertIsArray($groupNull);

      $this->assertCount(2, $group123);
      $this->assertCount(1, $group456);
      $this->assertCount(2, $groupNull); // Items 4 and 5
    } else {
      self::fail("Expected groups not found");
    }
  }

  /**
   * Test firstWhere function
   */
  public function test_first_where(): void
  {
    $items = [
      ["id" => 1, "tag" => "kunta_lampo", "value" => 100],
      ["id" => 2, "tag" => "alkuperatakuu_lampo", "value" => 200],
      ["id" => 3, "tag" => "kunta_lampo", "value" => 150],
    ];

    $result = $this->service->evaluate('firstWhere(items, "tag", "kunta_lampo")', [
      "items" => $items,
    ]);

    $this->assertIsArray($result);

    if (array_key_exists("id", $result) && array_key_exists("value", $result)) {
      $this->assertEquals(1, $result["id"]);
      $this->assertEquals(100, $result["value"]);
    } else {
      self::fail("Result should have id and value keys");
    }

    // Test with non-existent value
    $notFound = $this->service->evaluate('firstWhere(items, "tag", "non_existent")', [
      "items" => $items,
    ]);

    $this->assertNull($notFound);
  }

  /**
   * Test the complete formula flow
   */
  public function test_complete_formula_flow(): void
  {
    $definitions = [
      ["id" => 1, "link_id" => "group1", "tag" => "kunta_lampo"],
      ["id" => 2, "link_id" => "group1", "tag" => "alkuperatakuu_lampo"],
      ["id" => 3, "link_id" => "group2", "tag" => "kunta_lampo"],
      ["id" => 4, "link_id" => "group2", "tag" => "alkuperatakuu_lampo"],
      ["id" => 5, "link_id" => null, "tag" => "kunta_lampo"], // Should be filtered out
      ["id" => 6, "link_id" => null, "tag" => "alkuperatakuu_lampo"], // link_id explicitly null
    ];

    // Mock data function
    $dataValues = [
      1 => "10",
      2 => "20",
      3 => "30",
      4 => "40",
      5 => "50",
      6 => "60",
      25 => "100",
      26 => "200",
    ];

    // Mock kerroin function
    $kerroinValues = [
      1 => "2",
      3 => "3",
      25 => "1.5",
    ];

    // Register mock functions
    $expr = $this->service->getExpressionLanguage();
    $expr->register(
      "data",
      fn(int $id): string => "data($id)",
      /** @param array<string, mixed> $args */
      fn(array $args, int $id): string => $dataValues[$id] ?? "0",
    );
    $expr->register(
      "kerroin",
      fn(int $id): string => "kerroin($id)",
      /** @param array<string, mixed> $args */
      fn(array $args, int $id): string => $kerroinValues[$id] ?? "1",
    );

    // Step 1: Filter where link_id != null
    $filtered = $this->service->evaluate('where(definitions, "link_id", "!=", null)', [
      "definitions" => $definitions,
    ]);

    $this->assertIsArray($filtered);

    echo "Filtered items (link_id != null): " . count($filtered) . "\n";
    echo "Filtered IDs: " . implode(", ", array_column($filtered, "id")) . "\n";
    $this->assertCount(4, $filtered); // Should have items 1,2,3,4

    // Step 2: Group by link_id
    $grouped = $this->service->evaluate('groupBy(filtered, "link_id")', ["filtered" => $filtered]);

    $this->assertIsArray($grouped);

    echo "Groups: " . implode(", ", array_keys($grouped)) . "\n";
    $this->assertCount(2, $grouped); // Should have 'group1' and 'group2'

    // Step 3: Get values (array of groups)
    $groups = $this->service->evaluate("values(grouped)", ["grouped" => $grouped]);

    $this->assertIsArray($groups);

    echo "Number of groups: " . count($groups) . "\n";

    // Step 4: Map over each group
    $mapExpression = 'multiply(
            add(
                data(get(firstWhere(_, "tag", "kunta_lampo"), "id")),
                data(get(firstWhere(_, "tag", "alkuperatakuu_lampo"), "id"))
            ),
            kerroin(get(firstWhere(_, "tag", "kunta_lampo"), "id"))
        )';

    $mapped = $this->service->evaluate('map(groups, \'' . $mapExpression . '\')', [
      "groups" => $groups,
    ]);

    $this->assertIsArray($mapped);

    echo "Mapped values: " . implode(", ", $mapped) . "\n";

    // Group 1: (10 + 20) * 2 = 60
    // Group 2: (30 + 40) * 3 = 210
    $this->assertCount(2, $mapped);
    if (count($mapped) >= 2 && array_key_exists(0, $mapped) && array_key_exists(1, $mapped)) {
      $this->assertNumericEquals("60", $mapped[0]);
      $this->assertNumericEquals("210", $mapped[1]);
    } else {
      self::fail("Mapped array should have 2 elements");
    }

    // Step 5: Sum
    $sum = $this->service->evaluate("sum(mapped)", ["mapped" => $mapped]);

    $this->assertIsString($sum);

    echo "Sum of mapped values: $sum\n";
    $this->assertNumericEquals("270", $sum);

    // Step 6: Complete formula
    $base = $this->service->evaluate("multiply(add(data(25), data(26)), kerroin(25))", []);

    $this->assertIsString($base);

    echo "Base calculation: (100 + 200) * 1.5 = $base\n";
    $this->assertNumericEquals("450", $base);

    $total = $this->service->evaluate("add(base, mappedSum)", [
      "base" => $base,
      "mappedSum" => $sum,
    ]);

    $this->assertIsString($total);

    echo "Total: $base + $sum = $total\n";
    $this->assertNumericEquals("720", $total);
  }

  /**
   * Test edge cases for where function
   */
  public function test_where_edge_cases(): void
  {
    // Test with empty array
    $result = $this->service->evaluate('where([], "field", "!=", null)', []);
    $this->assertEquals([], $result);

    // Test with non-array
    $result = $this->service->evaluate('where("not_an_array", "field", "!=", null)', []);
    $this->assertEquals([], $result);

    // Test with mixed types
    $data = [
      ["id" => 1, "value" => "123"],
      ["id" => 2, "value" => 123],
      ["id" => 3, "value" => null],
    ];

    // == comparison (loose)
    $result = $this->service->evaluate('where(data, "value", "==", 123)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(2, $result); // Should match both string and int

    // === comparison (strict)
    $result = $this->service->evaluate('where(data, "value", "===", 123)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(1, $result); // Should only match int
  }

  /**
   * Test where with two arguments (equality check)
   */
  public function test_where_two_arguments(): void
  {
    $data = [
      ["id" => 1, "status" => "active"],
      ["id" => 2, "status" => "inactive"],
      ["id" => 3, "status" => "active"],
    ];

    // Two argument form should default to equality
    $result = $this->service->evaluate('where(data, "status", "active")', ["data" => $data]);
    $this->assertIsArray($result);

    $this->assertCount(2, $result);
    $values = array_values($result);

    // Check both indices exist
    $this->assertArrayHasKey(0, $values);
    $this->assertArrayHasKey(1, $values);

    if (array_key_exists(0, $values)) {
      $firstValue = $values[0];
      if (is_array($firstValue) && array_key_exists("id", $firstValue)) {
        $this->assertEquals(1, $firstValue["id"]);
      }
    }

    if (array_key_exists(1, $values)) {
      $secondValue = $values[1];
      if (is_array($secondValue) && array_key_exists("id", $secondValue)) {
        $this->assertEquals(3, $secondValue["id"]);
      }
    }
  }

  /**
   * Test comparison operators
   */
  public function test_comparison_operators(): void
  {
    $data = [
      ["id" => 1, "score" => 50],
      ["id" => 2, "score" => 75],
      ["id" => 3, "score" => 100],
      ["id" => 4, "score" => 75],
    ];

    // Greater than
    $result = $this->service->evaluate('where(data, "score", ">", 60)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(3, $result);

    // Greater than or equal
    $result = $this->service->evaluate('where(data, "score", ">=", 75)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(3, $result);

    // Less than
    $result = $this->service->evaluate('where(data, "score", "<", 75)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(1, $result);

    // Less than or equal
    $result = $this->service->evaluate('where(data, "score", "<=", 75)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(3, $result);

    // Not equal
    $result = $this->service->evaluate('where(data, "score", "!=", 75)', ["data" => $data]);
    $this->assertIsArray($result);
    $this->assertCount(2, $result);
  }

  /**
   * Test math functions with decimal output
   */
  public function test_math_functions_decimal_normalization(): void
  {
    // Test BigDecimal output format
    $result = $this->service->evaluate("add(100, 50)", []);
    $this->assertNumericEquals("150", $result);

    $result = $this->service->evaluate("multiply(100, 1.5)", []);
    $this->assertNumericEquals("150", $result);

    $result = $this->service->evaluate("multiply(100, 1.50)", []);
    $this->assertNumericEquals("150", $result);

    // Test that necessary decimals are preserved
    $result = $this->service->evaluate("multiply(100, 1.25)", []);
    $this->assertNumericEquals("125", $result);

    $result = $this->service->evaluate("multiply(100, 1.33)", []);
    $this->assertNumericEquals("133", $result);

    // Test subtract
    $result = $this->service->evaluate("subtract(100.5, 0.5)", []);
    $this->assertNumericEquals("100", $result);

    $result = $this->service->evaluate("subtract(100.7, 0.5)", []);
    $this->assertNumericEquals("100.2", $result);
  }

  /**
   * Test get function with various scenarios
   */
  public function test_get_function(): void
  {
    // Test with array
    $data = ["id" => 123, "name" => "test", "value" => null];

    $result = $this->service->evaluate('get(data, "id")', ["data" => $data]);
    $this->assertEquals(123, $result);

    $result = $this->service->evaluate('get(data, "name")', ["data" => $data]);
    $this->assertEquals("test", $result);

    $result = $this->service->evaluate('get(data, "value")', ["data" => $data]);
    $this->assertNull($result);

    // Test with default value
    $result = $this->service->evaluate('get(data, "missing", "default")', ["data" => $data]);
    $this->assertEquals("default", $result);

    // Test with object
    $obj = (object) ["id" => 456, "name" => "object"];
    $result = $this->service->evaluate('get(obj, "id")', ["obj" => $obj]);
    $this->assertEquals(456, $result);

    // Test with null target
    $result = $this->service->evaluate('get(null, "field")', []);
    $this->assertNull($result);
  }

  /**
   * Test nested operations
   */
  public function test_nested_operations(): void
  {
    $data = [
      ["category" => "A", "items" => [["price" => 10], ["price" => 20]]],
      ["category" => "B", "items" => [["price" => 30], ["price" => 40]]],
      ["category" => "A", "items" => [["price" => 50]]],
    ];

    // Group by category
    $grouped = $this->service->evaluate('groupBy(data, "category")', ["data" => $data]);
    $this->assertIsArray($grouped);

    $this->assertTrue(array_key_exists("A", $grouped));
    $this->assertTrue(array_key_exists("B", $grouped));

    // Access the groups - we know these keys exist from the test data
    $groupA = $grouped["A"];
    $groupB = $grouped["B"];

    $this->assertIsArray($groupA);
    $this->assertIsArray($groupB);

    $this->assertCount(2, $groupA);
    $this->assertCount(1, $groupB);

    // Values of grouped
    $values = $this->service->evaluate("values(grouped)", ["grouped" => $grouped]);
    $this->assertIsArray($values);
    $this->assertCount(2, $values);
  }

  /**
   * Test firstWhere with various operators
   */
  public function test_first_where_operators(): void
  {
    $items = [["id" => 1, "score" => 50], ["id" => 2, "score" => 75], ["id" => 3, "score" => 100]];

    // Greater than
    $result = $this->service->evaluate('firstWhere(items, "score", ">", 60)', ["items" => $items]);
    $this->assertIsArray($result);
    if (array_key_exists("id", $result)) {
      $this->assertEquals(2, $result["id"]);
    }

    // Greater than or equal
    $result = $this->service->evaluate('firstWhere(items, "score", ">=", 100)', [
      "items" => $items,
    ]);
    $this->assertIsArray($result);
    if (array_key_exists("id", $result)) {
      $this->assertEquals(3, $result["id"]);
    }

    // Less than
    $result = $this->service->evaluate('firstWhere(items, "score", "<", 60)', ["items" => $items]);
    $this->assertIsArray($result);
    if (array_key_exists("id", $result)) {
      $this->assertEquals(1, $result["id"]);
    }

    // Not equal
    $result = $this->service->evaluate('firstWhere(items, "score", "!=", 75)', ["items" => $items]);
    $this->assertIsArray($result);
    if (array_key_exists("id", $result)) {
      $this->assertEquals(1, $result["id"]);
    }
  }

  /**
   * Test complex formula with different data patterns
   */
  public function test_complex_formula_variations(): void
  {
    // Test with string link_ids (as in original formula)
    $definitions = [
      ["id" => 10, "link_id" => "link_1", "tag" => "kunta_lampo"],
      ["id" => 20, "link_id" => "link_1", "tag" => "alkuperatakuu_lampo"],
      ["id" => 30, "link_id" => "link_2", "tag" => "kunta_lampo"],
      ["id" => 40, "link_id" => "link_2", "tag" => "alkuperatakuu_lampo"],
    ];

    // Register mock functions
    $expr = $this->service->getExpressionLanguage();
    $expr->register(
      "data",
      fn(int $id): string => "data($id)",
      /** @param array<string, mixed> $args */
      fn(array $args, int $id): string => (string) ($id * 10),
    );
    $expr->register(
      "kerroin",
      fn(int $id): string => "kerroin($id)",
      /** @param array<string, mixed> $args */
      fn(array $args, int $id): string => $id === 10 ? "2" : "3",
    );

    $formula = 'sum(
      map(
        values(groupBy(where(definitions, "link_id", "!=", null), "link_id")),
        "multiply(
          add(
            data(get(firstWhere(_, \\"tag\\", \\"kunta_lampo\\"), \\"id\\")),
            data(get(firstWhere(_, \\"tag\\", \\"alkuperatakuu_lampo\\"), \\"id\\"))
          ),
          kerroin(get(firstWhere(_, \\"tag\\", \\"kunta_lampo\\"), \\"id\\"))
        )"
      )
    )';

    $result = $this->service->evaluate($formula, ["definitions" => $definitions]);

    // link_1: (10*10 + 20*10) * 2 = 300 * 2 = 600
    // link_2: (30*10 + 40*10) * 3 = 700 * 3 = 2100
    // Total: 600 + 2100 = 2700
    $this->assertNumericEquals("2700", $result);
  }

  /**
   * Test array functions
   */
  public function test_array_functions(): void
  {
    // Test count
    $result = $this->service->evaluate("count([1, 2, 3, 4, 5])", []);
    $this->assertEquals(5, $result);

    $result = $this->service->evaluate("count([])", []);
    $this->assertEquals(0, $result);

    $result = $this->service->evaluate('count("not_array")', []);
    $this->assertEquals(0, $result);

    // Test in_array
    $result = $this->service->evaluate("in_array(3, [1, 2, 3, 4, 5])", []);
    $this->assertTrue($result);

    $result = $this->service->evaluate("in_array(10, [1, 2, 3, 4, 5])", []);
    $this->assertFalse($result);

    // Test range
    $result = $this->service->evaluate("range(1, 5)", []);
    $this->assertEquals([1, 2, 3, 4, 5], $result);

    $result = $this->service->evaluate("range(0, 10, 2)", []);
    $this->assertEquals([0, 2, 4, 6, 8, 10], $result);

    $result = $this->service->evaluate("range(5, 1, -1)", []);
    $this->assertEquals([5, 4, 3, 2, 1], $result);
  }

  /**
   * Test helper functions
   */
  public function test_helper_functions(): void
  {
    // Test between
    $result = $this->service->evaluate("between(5, 1, 10)", []);
    $this->assertTrue($result);

    $result = $this->service->evaluate("between(0, 1, 10)", []);
    $this->assertFalse($result);

    $result = $this->service->evaluate("between(10, 1, 10)", []);
    $this->assertTrue($result); // Inclusive

    $result = $this->service->evaluate("between(1, 1, 10)", []);
    $this->assertTrue($result); // Inclusive

    // Test with decimals
    $result = $this->service->evaluate("between(5.5, 5, 6)", []);
    $this->assertTrue($result);

    $result = $this->service->evaluate("between(4.9, 5, 6)", []);
    $this->assertFalse($result);
  }

  /**
   * Test flatten function
   */
  public function test_flatten_function(): void
  {
    // Flatten nested array
    $nested = [[1, 2], [3, 4], [5]];
    $result = $this->service->evaluate("flatten(nested)", ["nested" => $nested]);
    $this->assertEquals([1, 2, 3, 4, 5], $result);

    // Flatten mixed array
    $mixed = [[1, 2], 3, [4, 5], 6];
    $result = $this->service->evaluate("flatten(mixed)", ["mixed" => $mixed]);
    $this->assertEquals([1, 2, 3, 4, 5, 6], $result);

    // Flatten empty array
    $result = $this->service->evaluate("flatten([])", []);
    $this->assertEquals([], $result);

    // Flatten non-array
    $result = $this->service->evaluate('flatten("not_array")', []);
    $this->assertEquals([], $result);
  }

  /**
   * Test keys function
   */
  public function test_keys_function(): void
  {
    $assoc = ["a" => 1, "b" => 2, "c" => 3];
    $result = $this->service->evaluate("keys(assoc)", ["assoc" => $assoc]);
    $this->assertEquals(["a", "b", "c"], $result);

    $indexed = [10, 20, 30];
    $result = $this->service->evaluate("keys(indexed)", ["indexed" => $indexed]);
    $this->assertEquals([0, 1, 2], $result);

    $result = $this->service->evaluate("keys([])", []);
    $this->assertEquals([], $result);

    $result = $this->service->evaluate('keys("not_array")', []);
    $this->assertEquals([], $result);
  }

  /**
   * Test math functions with edge cases
   */
  public function test_math_edge_cases(): void
  {
    // Test divide by zero
    try {
      $this->service->evaluate("divide(10, 0)", []);
      self::fail("Should throw exception for division by zero");
    } catch (Exception $e) {
      $this->assertStringContainsString("Division by zero", $e->getMessage());
    }

    // Test divide with scale
    $result = $this->service->evaluate("divide(10, 3, 2)", []);
    $this->assertNumericEquals("3.33", $result);

    $result = $this->service->evaluate("divide(10, 3, 5)", []);
    $this->assertNumericEquals("3.33333", $result);

    // Test pow
    $result = $this->service->evaluate("pow(2, 10)", []);
    $this->assertNumericEquals("1024", $result);

    $result = $this->service->evaluate("pow(10, -2)", []);
    $this->assertNumericEquals("0.01", $result);

    // Test sqrt
    $result = $this->service->evaluate("sqrt(16)", []);
    $this->assertEquals("4.0000000000", $result);

    $result = $this->service->evaluate("sqrt(2)", []);
    $this->assertIsString($result);
    $this->assertStringStartsWith("1.414213562", $result); // Check first 9 decimal places

    // Test sqrt of negative should throw
    try {
      $this->service->evaluate("sqrt(-1)", []);
      self::fail("Should throw exception for negative sqrt");
    } catch (Exception $e) {
      $this->assertStringContainsString("negative", $e->getMessage());
    }

    // Test round
    $result = $this->service->evaluate("round(3.14159, 2)", []);
    $this->assertNumericEquals("3.14", $result);

    $result = $this->service->evaluate("round(3.14159, 0)", []);
    $this->assertNumericEquals("3", $result);

    // Test abs
    $result = $this->service->evaluate("abs(-42)", []);
    $this->assertNumericEquals("42", $result);

    $result = $this->service->evaluate("abs(42)", []);
    $this->assertNumericEquals("42", $result);

    // Test min/max
    $result = $this->service->evaluate("min(5, 2, 8, 1, 9)", []);
    $this->assertEquals(1, $result);

    $result = $this->service->evaluate("max(5, 2, 8, 1, 9)", []);
    $this->assertEquals(9, $result);
  }

  /**
   * Test map function with complex expressions
   */
  public function test_map_complex_expressions(): void
  {
    $items = [
      ["price" => 10, "qty" => 2],
      ["price" => 20, "qty" => 3],
      ["price" => 15, "qty" => 4],
    ];

    // Map to calculate total for each item
    $result = $this->service->evaluate(
      'map(items, "multiply(get(_, \\"price\\"), get(_, \\"qty\\"))")',
      ["items" => $items],
    );

    $this->assertNumericArrayEquals(["20", "60", "60"], $result);

    // Map with conditional logic using between
    $result = $this->service->evaluate('map(items, "between(get(_, \\"price\\"), 10, 15)")', [
      "items" => $items,
    ]);

    $this->assertEquals([true, false, true], $result);
  }

  /**
   * Test complete real-world scenario with all features
   */
  public function test_real_world_scenario(): void
  {
    // Simulate a pricing calculation with groups and multipliers
    $products = [
      ["id" => 1, "category" => "electronics", "base_price" => 100, "tax_rate" => 0.2],
      ["id" => 2, "category" => "electronics", "base_price" => 200, "tax_rate" => 0.2],
      ["id" => 3, "category" => "books", "base_price" => 20, "tax_rate" => 0.1],
      ["id" => 4, "category" => "books", "base_price" => 30, "tax_rate" => 0.1],
      ["id" => 5, "category" => null, "base_price" => 50, "tax_rate" => 0.15],
    ];

    // First, let's test step by step
    // Step 1: Filter non-null categories
    $filtered = $this->service->evaluate('where(products, "category", "!=", null)', [
      "products" => $products,
    ]);
    $this->assertIsArray($filtered);
    $this->assertCount(4, $filtered);

    // Step 2: Group by category
    $grouped = $this->service->evaluate('groupBy(filtered, "category")', ["filtered" => $filtered]);
    $this->assertIsArray($grouped);
    $this->assertCount(2, $grouped);

    // Step 3: Calculate price with tax for each product in a group
    $this->assertTrue(array_key_exists("electronics", $grouped));

    // We know electronics key exists from the test data
    $electronics = $grouped["electronics"];
    $this->assertIsArray($electronics);

    $pricesWithTax = $this->service->evaluate(
      'map(items, "multiply(get(_, \\"base_price\\"), add(1, get(_, \\"tax_rate\\")))")',
      ["items" => $electronics],
    );
    $this->assertIsArray($pricesWithTax);
    $this->assertNumericArrayEquals(["120", "240"], $pricesWithTax);

    // Step 4: Sum prices for electronics category
    $electronicsTotal = $this->service->evaluate("sum(prices)", ["prices" => $pricesWithTax]);
    $this->assertNumericEquals("360", $electronicsTotal);

    // Now test the complete formula with simpler escaping
    // Calculate total with tax for electronics only
    $formula = 'sum(map(
      where(products, "category", "electronics"),
      "multiply(get(_, \\"base_price\\"), add(1, get(_, \\"tax_rate\\")))"
    ))';

    $result = $this->service->evaluate($formula, ["products" => $products]);
    $this->assertNumericEquals("360", $result);

    // Calculate total for books
    $formula2 = 'sum(map(
      where(products, "category", "books"),
      "multiply(get(_, \\"base_price\\"), add(1, get(_, \\"tax_rate\\")))"
    ))';

    $result2 = $this->service->evaluate($formula2, ["products" => $products]);
    $this->assertNumericEquals("55", $result2);
  }

  /**
   * Helper method to compare numeric strings ignoring trailing zeros
   */
  private function assertNumericEquals(string $expected, mixed $actual, string $message = ""): void
  {
    $this->assertIsString($actual);

    // Normalize both strings by removing trailing zeros after decimal point
    $normalizeNumber = function (string $num): string {
      if (mb_strpos($num, ".") !== false) {
        $num = mb_rtrim(mb_rtrim($num, "0"), ".");
      }

      return $num;
    };

    $expectedNormalized = $normalizeNumber($expected);
    $actualNormalized = $normalizeNumber($actual);

    $this->assertEquals($expectedNormalized, $actualNormalized, $message);
  }

  /**
   * Helper method to compare arrays of numeric strings
   *
   * @param  array<string>  $expected
   */
  private function assertNumericArrayEquals(
    array $expected,
    mixed $actual,
    string $message = "",
  ): void {
    $this->assertIsArray($actual);
    $this->assertCount(count($expected), $actual, $message);

    $actualValues = array_values($actual);
    foreach ($expected as $index => $expectedValue) {
      if (!array_key_exists($index, $actualValues)) {
        self::fail("Expected index $index not found in actual array");
      }
      $this->assertNumericEquals($expectedValue, $actualValues[$index], $message);
    }
  }
}
