<?php

namespace Tests\Feature;

use App\Helpers\Assert;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route as RouteFacade;
use Mcamara\LaravelLocalization\Commands\RouteTranslationsCacheCommand;
use <PERSON><PERSON>ara\LaravelLocalization\Commands\RouteTranslationsClearCommand;
use Tests\TestCase;

class RouteAuthorizationTest extends TestCase
{
  use RefreshDatabase;

  /**
   * @var array<int, array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>}>
   */
  private array $discoveredRoutes = [];

  protected function setUp(): void
  {
    parent::setUp();
    $this->withoutMiddleware([\App\Http\Middleware\EnsureInIframe::class]);

    // Mock Vite for routes that use Vite assets
    $this->withoutVite();

    // Cache routes to test with production-like environment
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsCacheCommand::class)->assertSuccessful();

    $this->discoverRoutes();
  }

  protected function tearDown(): void
  {
    // Clear route cache after test
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsClearCommand::class)->assertSuccessful();

    parent::tearDown();
  }

  private function discoverRoutes(): void
  {
    $this->discoveredRoutes = [];

    $routes = RouteFacade::getRoutes();
    foreach ($routes->getRoutes() as $route) {
      $methods = $route->methods();
      Assert::intKeyedArray($methods);
      Assert::stringArray($methods);

      $parameters = $route->parameterNames();
      Assert::intKeyedArray($parameters);
      Assert::stringArray($parameters);

      $this->discoveredRoutes[] = [
        "uri" => $route->uri(),
        "name" => $route->getName(),
        "methods" => $methods,
        "parameters" => $parameters,
      ];
    }
  }

  public function test_routes_deny_access_based_on_permissions(): void
  {
    $regularUser = User::factory()->create();
    $routesAccessibleToBasicUsers = [];
    $routesRequiringElevatedPermissions = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);

      // Try accessing as authenticated user without special permissions
      $response = $this->actingAs($regularUser)->get($uri);
      $status = $response->getStatusCode();

      // Skip routes that require authentication first (401)
      // Treat 500 errors as accessible for security analysis
      if ($status === 401) {
        continue;
      }

      // 403 indicates permission denied (user is authenticated but not authorized)
      if ($status === 403) {
        $routesRequiringElevatedPermissions[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      } else {
        // All other statuses including 500 are treated as accessible
        $routesAccessibleToBasicUsers[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      }
    }

    // Define routes that should require elevated permissions by route name
    // These routes should return 403 for regular users
    $expectedRestrictedRouteNames = [
      // Add route names that should require admin/elevated permissions
      // Example: "admin.dashboard", "admin.users.index", "settings.system"
    ];

    // For routes without names that should be restricted
    $expectedRestrictedUris = [
      // Add exact URIs that should require elevated permissions
      // Only use this for routes that cannot have names assigned
    ];

    // Find routes that are accessible to basic users but shouldn't be
    // Commented out since arrays are empty - uncomment when you add expected restrictions
    /*
    $unexpectedlyAccessibleRoutes = array_filter($routesAccessibleToBasicUsers, function ($routeData) use (
      $expectedRestrictedRouteNames,
      $expectedRestrictedUris
    ) {
      // Check if this route should be restricted by name
      if ($routeData["name"] !== null && count($expectedRestrictedRouteNames) > 0) {
        return in_array($routeData["name"], $expectedRestrictedRouteNames, true);
      }
      
      // Check if this route should be restricted by URI
      if (count($expectedRestrictedUris) > 0) {
        return in_array($routeData["uri"], $expectedRestrictedUris, true);
      }
      
      return false;
    });
    */
    $unexpectedlyAccessibleRoutes = [];

    // Format for output
    $unexpectedlyAccessibleIdentifiers = array_map(function ($routeData) {
      $uri = $routeData["uri"];
      $name = $routeData["name"];
      $identifier = is_string($uri) ? $uri : "";
      if ($name !== null && is_string($name)) {
        $identifier .= " (name: " . $name . ")";
      }
      return $identifier;
    }, $unexpectedlyAccessibleRoutes);

    // Assert no admin/management routes are accidentally accessible
    $this->assertEmpty(
      $unexpectedlyAccessibleIdentifiers,
      "The following routes should require elevated permissions but are accessible to basic users: \n" .
        implode("\n", $unexpectedlyAccessibleIdentifiers) .
        "\n\n" .
        "These routes should implement proper authorization checks.",
    );
  }

  public function test_admin_panel_routes_require_admin_access(): void
  {
    $regularUser = User::factory()->create();
    $accessibleAdminRoutes = [];
    $protectedAdminRoutes = [];

    // Define admin routes by name
    $adminRouteNames = [
      // Add specific admin route names here
      // Example: "admin.dashboard", "admin.users.index", "filament.admin.pages.dashboard"
    ];

    // Collect admin routes by matching route names
    // Commented out since adminRouteNames is empty - uncomment when you add admin routes
    /*
    $adminRoutes = array_filter($this->discoveredRoutes, function ($routeInfo) use ($adminRouteNames) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        return false;
      }
      
      if ($routeInfo["name"] === null) {
        return false;
      }
      
      // Check if this route name matches any admin route pattern
      foreach ($adminRouteNames as $adminRouteName) {
        if ($routeInfo["name"] === $adminRouteName || str_starts_with($routeInfo["name"], $adminRouteName . ".")) {
          return true;
        }
      }
      
      // Also check common admin route name patterns
      return str_starts_with($routeInfo["name"], "admin.") ||
             str_starts_with($routeInfo["name"], "filament.") ||
             str_starts_with($routeInfo["name"], "management.");
    });
    */

    // For now, just check routes with common admin patterns
    $adminRoutes = array_filter($this->discoveredRoutes, function ($routeInfo) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        return false;
      }

      if ($routeInfo["name"] === null) {
        return false;
      }

      // Check common admin route name patterns
      return str_starts_with($routeInfo["name"], "admin.") ||
        str_starts_with($routeInfo["name"], "filament.") ||
        str_starts_with($routeInfo["name"], "management.");
    });

    foreach ($adminRoutes as $routeInfo) {
      $uri = $this->buildTestUri($routeInfo);
      $response = $this->actingAs($regularUser)->get($uri);
      $status = $response->getStatusCode();

      // Routes returning 200 are accessible to regular users (security issue!)
      // Treat 500 errors as potentially accessible for security analysis
      if (in_array($status, [200, 500], true)) {
        $accessibleAdminRoutes[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      } else {
        $protectedAdminRoutes[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      }
    }

    // Format accessible routes for output
    $accessibleIdentifiers = array_map(function ($routeData) {
      $identifier = $routeData["uri"];
      if ($routeData["name"] !== null) {
        $identifier .= " (name: " . $routeData["name"] . ")";
      }
      return $identifier;
    }, $accessibleAdminRoutes);

    // Assert that NO admin routes are accessible to regular users
    $this->assertEmpty(
      $accessibleIdentifiers,
      "SECURITY ISSUE: The following admin routes are accessible to regular users: \n" .
        implode("\n", $accessibleIdentifiers) .
        "\n\n" .
        "These routes must implement proper authorization checks immediately.",
    );

    // Verify we found and protected some admin routes
    if (count($adminRoutes) > 0) {
      $this->assertNotEmpty(
        $protectedAdminRoutes,
        "No protected admin routes found. Either no admin routes exist or there's a test issue.",
      );
    }

    // Test with admin user to ensure routes work when properly authorized
    if (count($protectedAdminRoutes) > 0) {
      $adminUser = User::factory()->withRole("admin")->create();

      // Test only the protected admin routes we found
      foreach (array_slice($protectedAdminRoutes, 0, 5) as $routeData) {
        // Test a sample to avoid slow tests
        $response = $this->actingAs($adminUser)->get($routeData["uri"]);
        $status = $response->getStatusCode();

        $this->assertNotEquals(
          403,
          $status,
          "Admin route [{$routeData["uri"]}] should be accessible to admin user but returned 403",
        );
      }
    }
  }

  public function test_permission_hierarchy_is_respected(): void
  {
    $hierarchyViolations = [];
    $properlyProtectedRoutes = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes - they have their own security mechanism
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);

      // Test as guest - ensure clean state
      $this->app->forgetInstance("auth.driver");
      Auth::logout();

      $guestResponse = $this->get($uri);
      $guestStatus = $guestResponse->getStatusCode();

      // If guest gets 500, check if it's an auth-related error
      if ($guestStatus === 500) {
        $responseContent = $guestResponse->getContent();
        // Check for auth-related error messages in the response
        if (
          $responseContent !== false &&
          (str_contains($responseContent, "Redirector") ||
            str_contains($responseContent, "RedirectIfAuthenticated") ||
            str_contains($responseContent, "Symfony\Component\HttpFoundation\Response") ||
            str_contains($responseContent, "Livewire\Features\SupportRedirects"))
        ) {
          // This is an auth-related 500 - treat as redirect
          $guestStatus = 302;
        }
      }

      $guestLocationHeader = $guestResponse->headers->get("Location");
      $guestLocation = $guestLocationHeader !== null ? $guestLocationHeader : "";

      // Create fresh users for each route to avoid state contamination
      $regularUser = User::factory()->create();
      $this->actingAs($regularUser);
      $regularResponse = $this->get($uri);
      $regularStatus = $regularResponse->getStatusCode();

      // Clear auth and create admin user
      $this->app->forgetInstance("auth.driver");
      $adminUser = User::factory()->withRole("admin")->create();
      $this->actingAs($adminUser);
      $adminResponse = $this->get($uri);
      $adminStatus = $adminResponse->getStatusCode();

      // Clear auth state for next iteration
      $this->app->forgetInstance("auth.driver");
      Auth::logout();

      // For hierarchy checking, treat non-auth 500s as accessible
      $effectiveGuestStatus = $guestStatus === 500 ? 200 : $guestStatus;
      $effectiveRegularStatus = $regularStatus === 500 ? 200 : $regularStatus;
      $effectiveAdminStatus = $adminStatus === 500 ? 200 : $adminStatus;

      // Detect actual protection status
      $guestIsProtected =
        ($effectiveGuestStatus === 302 && str_contains($guestLocation, "login")) ||
        $effectiveGuestStatus === 302 || // Any redirect for guests
        $effectiveGuestStatus === 401 ||
        $effectiveGuestStatus === 403;
      $regularHasAccess = !in_array($effectiveRegularStatus, [302, 401, 403], true);
      $adminHasAccess = !in_array($effectiveAdminStatus, [302, 401, 403], true);

      // Check if this is the login route (authenticated users get redirected away from login)
      $routeName = $routeInfo["name"];
      $isLoginRoute = $routeName === "login" || $uri === "login" || str_ends_with($uri, "/login");

      // Check for hierarchy violations
      if (!$guestIsProtected && !$regularHasAccess && !$isLoginRoute) {
        // Guest has access but regular user doesn't - this is wrong
        $hierarchyViolations[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
          "issue" => "Guest has access but regular user is denied",
          "guest_status" => $guestStatus,
          "regular_status" => $regularStatus,
          "admin_status" => $adminStatus,
        ];
      }

      if ($regularHasAccess && !$adminHasAccess) {
        // Regular user has access but admin doesn't - this is wrong
        $hierarchyViolations[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
          "issue" => "Regular user has access but admin user is denied",
          "guest_status" => $guestStatus,
          "regular_status" => $regularStatus,
          "admin_status" => $adminStatus,
        ];
      }

      // Track properly protected routes
      if ($guestIsProtected && !in_array($regularStatus, [401], true)) {
        $properlyProtectedRoutes[] = $uri;
      }
    }

    // Assert no hierarchy violations exist
    if (count($hierarchyViolations) > 0) {
      $violationMessages = array_map(function ($violation) {
        $identifier = $violation["uri"];
        if ($violation["name"] !== null) {
          $identifier .= " (name: " . $violation["name"] . ")";
        }

        return sprintf(
          "%s: %s (Guest: %s, Regular: %s, Admin: %s)",
          $identifier,
          $violation["issue"],
          $violation["guest_status"],
          $violation["regular_status"],
          $violation["admin_status"],
        );
      }, $hierarchyViolations);

      $this->fail(
        "Permission hierarchy violations detected:\n" .
          implode("\n", $violationMessages) .
          "\n\n" .
          "The permission hierarchy should be: Admin >= Regular User >= Guest",
      );
    }

    // Sanity check: ensure we have some properly protected routes
    $this->assertNotEmpty(
      $properlyProtectedRoutes,
      "No properly protected routes found. Expected some routes to require authentication.",
    );
  }

  public function test_post_routes_authorization_behavior(): void
  {
    $regularUser = User::factory()->create();
    $unauthorizedAccessibleRoutes = [];

    // Define POST routes that should require elevated permissions by route name
    $restrictedPostRouteNames = [
      // Add route names that should require admin/elevated permissions
      // Example: "admin.users.store", "settings.update"
    ];

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("POST", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);

      // Test as authenticated user
      $response = $this->actingAs($regularUser)->post($uri);
      $status = $response->getStatusCode();

      // Skip authentication failures
      if ($status === 401) {
        continue;
      }

      // Check if this route should be restricted
      $shouldBeRestricted = false;

      if ($routeInfo["name"] !== null) {
        // Check by route name if we have restricted route names defined
        // Commented out since restrictedPostRouteNames is empty - uncomment when you add restrictions
        /*
        if (count($restrictedPostRouteNames) > 0 && in_array($routeInfo["name"], $restrictedPostRouteNames, true)) {
          $shouldBeRestricted = true;
        }
        */

        // Check common admin patterns in route names
        if (
          str_starts_with($routeInfo["name"], "admin.") ||
          str_starts_with($routeInfo["name"], "filament.") ||
          str_starts_with($routeInfo["name"], "management.") ||
          str_starts_with($routeInfo["name"], "settings.")
        ) {
          $shouldBeRestricted = true;
        }
      }

      // If this looks like an admin route but returns success (including 500 as potentially accessible)
      if ($shouldBeRestricted && in_array($status, [200, 201, 204, 500], true)) {
        $identifier = $uri;
        if ($routeInfo["name"] !== null) {
          $identifier .= " (name: " . $routeInfo["name"] . ")";
        }
        $unauthorizedAccessibleRoutes[] = $identifier;
      }
    }

    // Assert no restricted POST routes are accidentally accessible
    $this->assertEmpty(
      $unauthorizedAccessibleRoutes,
      "SECURITY ISSUE: The following POST routes should require elevated permissions but are accessible to basic users: \n" .
        implode("\n", $unauthorizedAccessibleRoutes) .
        "\n\n" .
        "These routes must implement proper authorization checks.",
    );
  }

  /**
   * @param array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>} $routeInfo
   */
  private function buildTestUri(array $routeInfo): string
  {
    $uri = $routeInfo["uri"];

    foreach ($routeInfo["parameters"] as $param) {
      $uri = str_replace("{" . $param . "}", "1", $uri);
      $uri = str_replace("{" . $param . "?}", "1", $uri);
    }

    return $uri;
  }
}
