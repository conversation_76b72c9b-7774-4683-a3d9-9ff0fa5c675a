<?php

namespace Tests\Feature;

use App\Helpers\Assert;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route as RouteFacade;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;
use Tests\TestCase;

class RouteLocalizationTest extends TestCase
{
  use RefreshDatabase;

  /**
   * @var array<int, string>
   */
  private array $supportedLocales = ["fi", "sv", "en"];

  /**
   * @var array<int, array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>}>
   */
  private array $discoveredRoutes = [];

  /**
   * @var array<string, array<int, array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>}>>
   */
  private array $routesByMethod = [];

  protected function setUp(): void
  {
    parent::setUp();
    $this->withoutMiddleware([\App\Http\Middleware\EnsureInIframe::class]);

    // Mock Vite for routes that use Vite assets
    $this->withoutVite();
  }

  protected function refreshApplicationWithLocale(string $locale): void
  {
    self::tearDown();
    putenv("ROUTING_LOCALE=" . $locale);
    self::setUp();
  }

  protected function tearDown(): void
  {
    putenv("ROUTING_LOCALE");
    parent::tearDown();
  }

  private function discoverRoutes(): void
  {
    $this->discoveredRoutes = [];
    $this->routesByMethod = [];

    $routes = RouteFacade::getRoutes();
    foreach ($routes->getRoutes() as $route) {
      $methods = $route->methods();
      Assert::intKeyedArray($methods);
      Assert::stringArray($methods);

      $parameters = $route->parameterNames();
      Assert::intKeyedArray($parameters);
      Assert::stringArray($parameters);

      $routeInfo = [
        "uri" => $route->uri(),
        "name" => $route->getName(),
        "methods" => $methods,
        "parameters" => $parameters,
      ];

      $this->discoveredRoutes[] = $routeInfo;

      foreach ($methods as $method) {
        if (!array_key_exists($method, $this->routesByMethod)) {
          $this->routesByMethod[$method] = [];
        }
        $this->routesByMethod[$method][] = $routeInfo;
      }
    }
  }

  public function test_routes_are_discovered_for_each_locale(): void
  {
    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);
      $this->discoverRoutes();

      $this->assertGreaterThan(
        0,
        count($this->discoveredRoutes),
        "No routes discovered for locale [{$locale}]",
      );
    }
  }

  public function test_get_routes_respond_for_each_locale(): void
  {
    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);
      $this->discoverRoutes();

      $getRoutes = $this->routesByMethod["GET"] ?? [];
      $this->assertNotEmpty($getRoutes, "No GET routes found for locale [{$locale}]");

      foreach ($getRoutes as $routeInfo) {
        $uri = $this->buildTestUri($routeInfo);
        $response = $this->get($uri);
        $status = $response->getStatusCode();
        $name = $routeInfo["name"] ?? "unnamed";

        $this->assertNotEquals(
          500,
          $status,
          "Route [{$name}] with URI [{$uri}] returned 500 error for locale [{$locale}]",
        );
      }
    }
  }

  public function test_route_uris_contain_locale_prefixes(): void
  {
    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);
      $this->discoverRoutes();

      $localizedRoutes = 0;
      $nonLocalizedRoutes = [];

      foreach ($this->discoveredRoutes as $routeInfo) {
        $uri = $routeInfo["uri"];

        $hasLocale = false;
        foreach ($this->supportedLocales as $checkLocale) {
          if (str_starts_with($uri, $checkLocale . "/") || $uri === $checkLocale) {
            $hasLocale = true;
            break;
          }
        }

        if ($hasLocale) {
          $localizedRoutes++;
        } else {
          $nonLocalizedRoutes[] = $uri;
        }
      }

      $this->assertGreaterThan(
        0,
        $localizedRoutes,
        "No localized routes found for locale [{$locale}]. Non-localized: " .
          implode(", ", array_slice($nonLocalizedRoutes, 0, 5)),
      );
    }
  }

  public function test_laravel_localization_url_generation(): void
  {
    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);

      $this->assertEquals($locale, LaravelLocalization::getCurrentLocale());

      $testPath = "test/path";

      $url1 = LaravelLocalization::localizeUrl($testPath);
      $this->assertNotFalse($url1, "localizeUrl failed for locale [{$locale}]");

      $url2 = LaravelLocalization::getLocalizedURL(null, $testPath);
      $this->assertNotFalse($url2, "getLocalizedURL failed for locale [{$locale}]");

      $url3 = LaravelLocalization::getLocalizedURL($locale, $testPath);
      $this->assertNotFalse($url3, "getLocalizedURL with explicit locale failed for [{$locale}]");
    }
  }

  public function test_route_names_consistent_across_locales(): void
  {
    $routeNamesByLocale = [];

    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);
      $this->discoverRoutes();

      $routeNames = [];
      foreach ($this->discoveredRoutes as $routeInfo) {
        $name = $routeInfo["name"];
        if ($name !== null && $name !== "") {
          $routeNames[] = $name;
        }
      }

      $routeNamesByLocale[$locale] = $routeNames;
    }

    if (count($routeNamesByLocale) > 1) {
      // Since supportedLocales is populated and we iterate through it,
      // we know the keys will be the locale strings
      $firstLocale = $this->supportedLocales[0] ?? null;
      if ($firstLocale === null) {
        return;
      }

      $firstNames = $routeNamesByLocale[$firstLocale] ?? [];
      if (count($firstNames) === 0) {
        return;
      }

      foreach ($routeNamesByLocale as $currentLocale => $names) {
        if ($currentLocale === $firstLocale) {
          continue;
        }

        foreach ($firstNames as $name) {
          $this->assertContains(
            $name,
            $names,
            "Route name [{$name}] from locale [{$firstLocale}] not found in locale [{$currentLocale}]",
          );
        }
      }
    }
  }

  public function test_routes_with_parameters(): void
  {
    foreach ($this->supportedLocales as $locale) {
      $this->refreshApplicationWithLocale($locale);
      $this->discoverRoutes();

      $parametricRoutes = [];
      foreach ($this->discoveredRoutes as $routeInfo) {
        $parameters = $routeInfo["parameters"];
        if (count($parameters) > 0) {
          $parametricRoutes[] = [
            "uri" => $routeInfo["uri"],
            "name" => $routeInfo["name"],
            "parameters" => $parameters,
          ];
        }
      }

      $this->assertNotEmpty($parametricRoutes, "No parametric routes found for locale [{$locale}]");

      foreach ($parametricRoutes as $route) {
        $routeUri = $route["uri"];
        $routeParams = $route["parameters"];

        foreach ($routeParams as $param) {
          $this->assertStringContainsString(
            "{" . $param,
            $routeUri,
            "Parameter [{$param}] not found in URI [{$routeUri}]",
          );
        }
      }

      // Test with sample parameter values
      foreach ($parametricRoutes as $route) {
        $testUri = $route["uri"];
        $routeParams = $route["parameters"];
        foreach ($routeParams as $param) {
          $testUri = str_replace("{" . $param . "}", "1", $testUri);
          $testUri = str_replace("{" . $param . "?}", "1", $testUri);
        }

        $response = $this->get($testUri);
        $status = $response->getStatusCode();
        $routeName = $route["name"] ?? "unnamed";

        $this->assertNotEquals(500, $status, "Parametric route [{$routeName}] returned 500 error");
      }
    }
  }

  /**
   * @param array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>} $routeInfo
   */
  private function buildTestUri(array $routeInfo): string
  {
    $uri = $routeInfo["uri"];

    foreach ($routeInfo["parameters"] as $param) {
      $uri = str_replace("{" . $param . "}", "1", $uri);
      $uri = str_replace("{" . $param . "?}", "1", $uri);
    }

    return $uri;
  }
}
