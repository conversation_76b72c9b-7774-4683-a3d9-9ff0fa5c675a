<?php

namespace Tests\Feature;

use App\Helpers\Assert;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route as RouteFacade;
use Illuminate\Testing\PendingCommand;
use <PERSON>camara\LaravelLocalization\Commands\RouteTranslationsCacheCommand;
use <PERSON><PERSON>ara\LaravelLocalization\Commands\RouteTranslationsClearCommand;
use Tests\TestCase;
use Mockery;

class RouteMiddlewareTest extends TestCase
{
  use RefreshDatabase;

  /**
   * @var array<int, array{uri: string, name: string|null, methods: array<int, string>, middleware: array<int, mixed>, parameters: array<int, string>}>
   */
  private array $discoveredRoutes = [];

  /**
   * @var array<string, array<int, array{uri: string, name: string|null, methods: array<int, string>, middleware: array<int, mixed>, parameters: array<int, string>}>>
   */
  private array $routesByMethod = [];

  protected function setUp(): void
  {
    parent::setUp();

    // Mock Vite for routes that use Vite assets
    $this->withoutVite();

    // Cache routes to test with production-like environment
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsCacheCommand::class)->assertSuccessful();

    $this->discoverRoutes();
  }

  protected function tearDown(): void
  {
    // Clear route cache after test
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsClearCommand::class)->assertSuccessful();

    Mockery::close();
    parent::tearDown();
  }

  private function discoverRoutes(): void
  {
    $this->discoveredRoutes = [];
    $this->routesByMethod = [];

    $routes = RouteFacade::getRoutes();
    foreach ($routes->getRoutes() as $route) {
      $methods = $route->methods();
      Assert::intKeyedArray($methods);
      Assert::stringArray($methods);

      $parameters = $route->parameterNames();
      Assert::intKeyedArray($parameters);
      Assert::stringArray($parameters);

      $middleware = $route->middleware();
      Assert::intKeyedArray($middleware);

      $routeInfo = [
        "uri" => $route->uri(),
        "name" => $route->getName(),
        "methods" => $methods,
        "middleware" => $middleware,
        "parameters" => $parameters,
      ];

      $this->discoveredRoutes[] = $routeInfo;

      foreach ($methods as $method) {
        if (!array_key_exists($method, $this->routesByMethod)) {
          $this->routesByMethod[$method] = [];
        }
        $this->routesByMethod[$method][] = $routeInfo;
      }
    }
  }

  public function test_post_routes_have_csrf_protection(): void
  {
    $postRoutes = $this->routesByMethod["POST"] ?? [];
    $routesWithoutProtection = [];

    foreach ($postRoutes as $routeInfo) {
      $uri = $routeInfo["uri"];
      $middlewareList = $routeInfo["middleware"];

      $hasWebMiddleware = false;
      $hasApiMiddleware = false;

      foreach ($middlewareList as $m) {
        // Get middleware name safely
        $middlewareName = is_object($m) ? get_class($m) : (is_string($m) ? $m : "");

        if (
          str_contains($middlewareName, "web") ||
          str_contains($middlewareName, "VerifyCsrfToken")
        ) {
          $hasWebMiddleware = true;
        }
        if (str_contains($middlewareName, "api")) {
          $hasApiMiddleware = true;
        }
      }

      // Still no exception for API routes
      // Livewire handles CSRF internally
      if (str_contains($uri, "livewire/")) {
        continue;
      }

      // ALL POST routes must have CSRF protection (except Livewire)
      if (!$hasWebMiddleware) {
        $routesWithoutProtection[] = $uri;
      }
    }

    $this->assertEmpty(
      $routesWithoutProtection,
      "POST routes lacking CSRF protection (no web middleware): " .
        implode(", ", $routesWithoutProtection),
    );
  }

  public function test_all_routes_have_csrf_protection(): void
  {
    // New test: Check ALL HTTP methods that modify state
    $modifyingMethods = ["POST", "PUT", "PATCH", "DELETE"];
    $routesWithoutProtection = [];

    foreach ($modifyingMethods as $method) {
      $routes = $this->routesByMethod[$method] ?? [];

      foreach ($routes as $routeInfo) {
        $uri = $routeInfo["uri"];
        $middlewareList = $routeInfo["middleware"];

        // Livewire handles CSRF internally, not through standard middleware
        if (str_contains($uri, "livewire/")) {
          continue;
        }

        $hasCSRFProtection = false;

        foreach ($middlewareList as $m) {
          $middlewareName = is_object($m) ? get_class($m) : (is_string($m) ? $m : "");

          if (
            str_contains($middlewareName, "web") ||
            str_contains($middlewareName, "VerifyCsrfToken")
          ) {
            $hasCSRFProtection = true;
            break;
          }
        }

        if (!$hasCSRFProtection) {
          $routesWithoutProtection[] = "{$method} {$uri}";
        }
      }
    }

    $this->assertEmpty(
      $routesWithoutProtection,
      "Routes lacking CSRF protection: " . implode("; ", $routesWithoutProtection),
    );
  }

  public function test_middleware_stacks(): void
  {
    $middlewarePatterns = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      $pattern = implode(
        "|",
        array_map(function ($m) {
          // Get middleware name safely
          if (is_object($m)) {
            return class_basename($m);
          } elseif (is_string($m)) {
            return $m;
          } else {
            return "";
          }
        }, $routeInfo["middleware"]),
      );

      if (!isset($middlewarePatterns[$pattern])) {
        $middlewarePatterns[$pattern] = [];
      }
      $middlewarePatterns[$pattern][] = $routeInfo["uri"];
    }

    $this->assertNotEmpty($middlewarePatterns, "No middleware patterns found");

    $this->assertGreaterThan(
      1,
      count($middlewarePatterns),
      "Only one middleware pattern found, expected multiple",
    );

    // Verify common middleware combinations
    foreach ($middlewarePatterns as $pattern => $routes) {
      if (str_contains($pattern, "auth")) {
        $this->assertTrue(
          str_contains($pattern, "web"),
          "Auth middleware found without web middleware in pattern: {$pattern}",
        );
      }
    }
  }

  public function test_api_routes_have_appropriate_middleware(): void
  {
    $apiRoutes = array_filter($this->discoveredRoutes, function ($routeInfo) {
      $routeName = $routeInfo["name"];
      // Check for API routes with or without locale prefix
      return str_contains($routeInfo["uri"], "/api/") ||
        str_contains($routeInfo["uri"], "api/") ||
        ($routeName !== null && str_starts_with($routeName, "api."));
    });

    // REMOVED: No early return if no API routes found
    // Test runs regardless and will pass if no API routes exist

    foreach ($apiRoutes as $routeInfo) {
      $hasApiMiddleware = false;
      $hasWebMiddleware = false;
      $middlewareList = $routeInfo["middleware"];

      foreach ($middlewareList as $middleware) {
        // Get middleware name safely
        $middlewareName = is_object($middleware)
          ? get_class($middleware)
          : (is_string($middleware)
            ? $middleware
            : "");

        if (str_contains($middlewareName, "api")) {
          $hasApiMiddleware = true;
        }
        if (str_contains($middlewareName, "web")) {
          $hasWebMiddleware = true;
        }
      }

      $this->assertTrue(
        $hasApiMiddleware,
        "API route [{$routeInfo["uri"]}] missing api middleware",
      );

      $this->assertFalse(
        $hasWebMiddleware,
        "API route [{$routeInfo["uri"]}] should not have web middleware",
      );
    }
  }

  public function test_all_routes_have_required_middleware(): void
  {
    // New comprehensive test: Ensure ALL routes have some form of middleware protection
    $routesWithoutMiddleware = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      // Livewire routes handle their own security internally
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      // Laravel's health check endpoint doesn't need middleware
      if ($routeInfo["uri"] === "up") {
        continue;
      }

      if (count($routeInfo["middleware"]) === 0) {
        $routesWithoutMiddleware[] = implode(",", $routeInfo["methods"]) . " " . $routeInfo["uri"];
      }
    }

    $this->assertEmpty(
      $routesWithoutMiddleware,
      "Routes without any middleware protection: " . implode("; ", $routesWithoutMiddleware),
    );
  }

  public function test_iframe_middleware_present(): void
  {
    // New test: Ensure EnsureInIframe middleware is properly applied where needed
    $routesRequiringIframe = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      $uri = $routeInfo["uri"];

      // Skip API routes, Livewire routes, and health check
      if (str_contains($uri, "api/") || str_contains($uri, "livewire/") || $uri === "up") {
        continue;
      }

      // Skip Filament admin panel routes
      if (str_starts_with($uri, "filament") || str_contains($uri, "/filament")) {
        continue;
      }

      // Skip admin routes
      if (str_starts_with($uri, "admin") || str_contains($uri, "/admin")) {
        continue;
      }

      // Skip authentication flow routes that need to work outside iframe
      if (
        str_starts_with($uri, "login/verify/") ||
        str_starts_with($uri, "invitation/accept/") ||
        $uri === "logout"
      ) {
        continue;
      }

      // Skip export routes that need direct access
      if (str_starts_with($uri, "export/emission-calculations")) {
        continue;
      }

      $hasIframeMiddleware = false;

      foreach ($routeInfo["middleware"] as $middleware) {
        $middlewareName = is_object($middleware)
          ? get_class($middleware)
          : (is_string($middleware)
            ? $middleware
            : "");

        if (str_contains($middlewareName, "EnsureInIframe")) {
          $hasIframeMiddleware = true;
          break;
        }
      }

      if (!$hasIframeMiddleware) {
        $routesRequiringIframe[] = $uri;
      }
    }

    // Assert that all non-exempted routes have iframe middleware
    $this->assertEmpty(
      $routesRequiringIframe,
      "Routes missing iframe middleware: " . implode(", ", $routesRequiringIframe),
    );
  }
}
