<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Enums\ActivityEvent;
use App\Models\ActivityLog;
use App\Models\Company;
use App\Models\LoginToken;
use App\Models\User;
use App\Services\ActivityLogger;
use App\Services\AuthenticationService;
use App\Values\SplitToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class ActivityLoggingTest extends TestCase
{
    use RefreshDatabase;

    public function test_magic_link_sent_is_logged(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $company = Company::factory()->create();

        $activityLogger = app(ActivityLogger::class);
        $activityLogger->logMagicLinkSent($user->email, $company);

        $this->assertDatabaseHas('activity_logs', [
            'event' => ActivityEvent::MAGIC_LINK_SENT->value,
            'description' => "Magic link sent to {$user->email}",
            'company_id' => $company->id,
        ]);
    }

    public function test_magic_link_clicked_is_logged(): void
    {
        $user = User::factory()->create();
        $company = Company::factory()->create();

        $activityLogger = app(ActivityLogger::class);
        $activityLogger->logMagicLinkClicked($user, $company);

        $this->assertDatabaseHas('activity_logs', [
            'event' => ActivityEvent::MAGIC_LINK_CLICKED->value,
            'description' => "Magic link clicked by {$user->email}",
            'user_id' => $user->id,
            'company_id' => $company->id,
        ]);
    }

    public function test_login_success_is_logged(): void
    {
        $user = User::factory()->create();
        $company = Company::factory()->create();

        $activityLogger = app(ActivityLogger::class);
        $activityLogger->logLoginSuccess($user, $company, ['login_method' => 'magic_link']);

        $this->assertDatabaseHas('activity_logs', [
            'event' => ActivityEvent::LOGIN_SUCCESS->value,
            'description' => "User {$user->email} logged in successfully",
            'user_id' => $user->id,
            'company_id' => $company->id,
        ]);

        $log = ActivityLog::where('event', ActivityEvent::LOGIN_SUCCESS)->first();
        $this->assertEquals(['login_method' => 'magic_link'], $log->metadata);
    }

    public function test_login_failure_is_logged(): void
    {
        $user = User::factory()->create();

        $activityLogger = app(ActivityLogger::class);
        $activityLogger->logLoginFailure('Invalid token', $user);

        $this->assertDatabaseHas('activity_logs', [
            'event' => ActivityEvent::LOGIN_FAILURE->value,
            'description' => 'Login failed: Invalid token',
            'user_id' => $user->id,
        ]);
    }

    public function test_logout_is_logged(): void
    {
        $user = User::factory()->create();

        $activityLogger = app(ActivityLogger::class);
        $activityLogger->logLogout($user);

        $this->assertDatabaseHas('activity_logs', [
            'event' => ActivityEvent::LOGOUT->value,
            'description' => "User {$user->email} logged out",
            'user_id' => $user->id,
        ]);
    }
}
