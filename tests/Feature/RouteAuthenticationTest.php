<?php

namespace Tests\Feature;

use App\Helpers\Assert;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route as RouteFacade;
use Mcamara\LaravelLocalization\Commands\RouteTranslationsCacheCommand;
use <PERSON><PERSON><PERSON>\LaravelLocalization\Commands\RouteTranslationsClearCommand;
use Tests\TestCase;

class RouteAuthenticationTest extends TestCase
{
  use RefreshDatabase;

  /**
   * @var array<int, array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>}>
   */
  private array $discoveredRoutes = [];

  protected function setUp(): void
  {
    parent::setUp();
    $this->withoutMiddleware([\App\Http\Middleware\EnsureInIframe::class]);

    // Mock Vite for routes that use Vite assets
    $this->withoutVite();

    // Cache routes to test with production-like environment
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsCacheCommand::class)->assertSuccessful();

    $this->discoverRoutes();
  }

  protected function tearDown(): void
  {
    // Clear route cache after test
    /** @phpstan-ignore-next-line method.nonObject */
    $this->artisan(RouteTranslationsClearCommand::class)->assertSuccessful();

    parent::tearDown();
  }

  private function discoverRoutes(): void
  {
    $this->discoveredRoutes = [];

    $routes = RouteFacade::getRoutes();
    foreach ($routes->getRoutes() as $route) {
      $methods = $route->methods();
      Assert::intKeyedArray($methods);
      Assert::stringArray($methods);

      $parameters = $route->parameterNames();
      Assert::intKeyedArray($parameters);
      Assert::stringArray($parameters);

      $this->discoveredRoutes[] = [
        "uri" => $route->uri(),
        "name" => $route->getName(),
        "methods" => $methods,
        "parameters" => $parameters,
      ];
    }
  }

  public function test_routes_requiring_authentication_redirect_guests(): void
  {
    $unprotectedGetRoutes = [];
    $protectedGetRoutes = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);
      $response = $this->get($uri);

      // Check if response is a redirect (protected) or direct access (unprotected)
      $statusCode = $response->getStatusCode();
      $isRedirect = $response->isRedirect();

      // Special handling for routes that are public but may have parameter validation issues
      // login.verify and invitation.accept are public routes that need valid tokens
      if (
        $statusCode === 500 &&
        in_array($routeInfo["name"], ["login.verify", "invitation.accept"], true)
      ) {
        // These are public routes that failed due to invalid parameters, not auth
        $unprotectedGetRoutes[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      } elseif ($statusCode === 500) {
        $responseContent = $response->getContent();
        // Check for auth-related error messages in the response
        if (
          $responseContent !== false &&
          (str_contains($responseContent, "Redirector") ||
            str_contains($responseContent, "RedirectIfAuthenticated") ||
            str_contains($responseContent, "Symfony\Component\HttpFoundation\Response") ||
            str_contains($responseContent, "Livewire\Features\SupportRedirects"))
        ) {
          // This is an auth-related 500 - treat as protected
          $protectedGetRoutes[] = [
            "uri" => $uri,
            "name" => $routeInfo["name"],
          ];
        } else {
          // Non-auth 500 - treat as unprotected
          $unprotectedGetRoutes[] = [
            "uri" => $uri,
            "name" => $routeInfo["name"],
          ];
        }
      } elseif ($isRedirect || $statusCode === 401) {
        $protectedGetRoutes[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      } else {
        // Treat all other responses as unprotected
        // This includes 200 (public), 404
        $unprotectedGetRoutes[] = [
          "uri" => $uri,
          "name" => $routeInfo["name"],
        ];
      }
    }

    // Define which GET routes are expected to be publicly accessible by route name
    // Routes without names cannot be excluded and must be protected
    $expectedPublicRouteNames = [
      "login",
      "login.verify",
      "invitation.accept",
      // Add other expected public route names here
      // Note: Only add explicit route names, not URI patterns
    ];

    // Also allow specific URIs for routes that don't have names (use sparingly)
    // This should only be used for routes that cannot have names assigned
    $expectedPublicUris = [
      "up",
      // Avoid adding anything here if possible - prefer named routes
    ];

    // Track which expected entries were actually matched
    $matchedNames = [];
    $matchedUris = [];

    // Filter out expected public routes by checking route names
    $unexpectedlyUnprotectedRoutes = array_filter($unprotectedGetRoutes, function ($routeData) use (
      $expectedPublicRouteNames,
      $expectedPublicUris,
      &$matchedNames,
      &$matchedUris,
    ) {
      // If route has a name, check against expected names
      if ($routeData["name"] !== null) {
        if (in_array($routeData["name"], $expectedPublicRouteNames, true)) {
          $matchedNames[] = $routeData["name"];
          return false; // This route is expected to be public
        }
        return true; // This route is unexpectedly public
      }

      // For unnamed routes, check against specific URIs
      if (in_array($routeData["uri"], $expectedPublicUris, true)) {
        $matchedUris[] = $routeData["uri"];
        return false; // This route is expected to be public
      }
      return true; // This route is unexpectedly public
    });

    // Check for unmatched expected route names (these don't exist or weren't tested)
    $unmatchedNames = array_diff($expectedPublicRouteNames, array_unique($matchedNames));
    $this->assertEmpty(
      $unmatchedNames,
      "The following route names in expectedPublicRouteNames do not match any discovered GET routes: \n" .
        implode("\n", $unmatchedNames) .
        "\n\n" .
        "Either these routes don't exist, are not GET routes, or have different names. Remove or fix these entries.",
    );

    // Check for unmatched expected URIs (these don't exist or weren't tested)
    $unmatchedUris = array_diff($expectedPublicUris, array_unique($matchedUris));
    $this->assertEmpty(
      $unmatchedUris,
      "The following URIs in expectedPublicUris do not match any discovered GET routes: \n" .
        implode("\n", $unmatchedUris) .
        "\n\n" .
        "Either these routes don't exist, are not GET routes, or have different URIs. Remove or fix these entries.",
    );

    // Extract just the URIs for the error message
    $unexpectedUris = array_map(function ($routeData) {
      $identifier = $routeData["uri"];
      if ($routeData["name"] !== null) {
        $identifier .= " (name: " . $routeData["name"] . ")";
      }
      return $identifier;
    }, $unexpectedlyUnprotectedRoutes);

    // Assert that no routes are unexpectedly unprotected
    $this->assertEmpty(
      $unexpectedUris,
      "The following GET routes are not protected by authentication: \n" .
        implode("\n", $unexpectedUris) .
        "\n\n" .
        "If these routes should be public, add their route names to expectedPublicRouteNames array. " .
        "Routes without names should be given names, or as a last resort, added to expectedPublicUris.",
    );

    // Verify we found some protected routes (sanity check)
    $this->assertNotEmpty(
      $protectedGetRoutes,
      "No protected GET routes found, which suggests a test configuration issue",
    );
  }

  public function test_authenticated_users_can_access_routes_that_redirect_guests(): void
  {
    $user = User::factory()->create();

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("GET", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes - they have their own security mechanism
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);

      // First check if this route requires auth (as a guest)
      $guestResponse = $this->get($uri);
      $guestStatus = $guestResponse->getStatusCode();

      // If it redirects or returns 401 for guests, test with auth
      if (in_array($guestStatus, [301, 302, 401], true)) {
        $authResponse = $this->actingAs($user)->get($uri);
        $authStatus = $authResponse->getStatusCode();

        $this->assertNotEquals(
          401,
          $authStatus,
          "Route [{$uri}] returned 401 even for authenticated user",
        );

        // Don't assert against 500 errors as they may be due to test setup issues
      }
    }
  }

  public function test_post_routes_authentication_behavior(): void
  {
    $unprotectedPostRoutes = [];
    $protectedPostRoutes = [];

    foreach ($this->discoveredRoutes as $routeInfo) {
      if (!in_array("POST", $routeInfo["methods"], true)) {
        continue;
      }

      // Skip Livewire internal routes - they have their own security mechanism
      if (str_contains($routeInfo["uri"], "livewire/")) {
        continue;
      }

      $uri = $this->buildTestUri($routeInfo);

      // Test as guest
      $guestResponse = $this->post($uri);
      $guestStatus = $guestResponse->getStatusCode();

      // Routes that redirect or return 401 are protected
      if (in_array($guestStatus, [301, 302, 401], true)) {
        $protectedPostRoutes[] = $uri;
      } else {
        // All other responses (including 500) are treated as unprotected
        $identifier = $uri;
        if ($routeInfo["name"] !== null) {
          $identifier .= " (name: " . $routeInfo["name"] . ")";
        }
        $unprotectedPostRoutes[] = $identifier;
      }
    }

    // Assert that no POST routes are unprotected
    $this->assertEmpty(
      $unprotectedPostRoutes,
      "The following POST routes are not protected by authentication: \n" .
        implode("\n", $unprotectedPostRoutes) .
        "\n\n" .
        "All POST routes should require authentication.",
    );

    // Also verify we found some protected routes (sanity check)
    $this->assertNotEmpty(
      $protectedPostRoutes,
      "No protected POST routes found, which suggests a test configuration issue",
    );
  }

  public function test_login_route_accessible_to_guests(): void
  {
    $loginRoute = null;

    foreach ($this->discoveredRoutes as $routeInfo) {
      if ($routeInfo["name"] === "login" && in_array("GET", $routeInfo["methods"], true)) {
        $loginRoute = $routeInfo;
        break;
      }
    }

    if ($loginRoute === null) {
      $this->markTestSkipped("No login route found");
    }

    $uri = $this->buildTestUri($loginRoute);
    $response = $this->get($uri);
    $status = $response->getStatusCode();

    $this->assertEquals(200, $status, "Login route should be accessible to guests");
  }

  /**
   * @param array{uri: string, name: string|null, methods: array<int, string>, parameters: array<int, string>} $routeInfo
   */
  private function buildTestUri(array $routeInfo): string
  {
    $uri = $routeInfo["uri"];

    foreach ($routeInfo["parameters"] as $param) {
      $uri = str_replace("{" . $param . "}", "1", $uri);
      $uri = str_replace("{" . $param . "?}", "1", $uri);
    }

    return $uri;
  }
}
