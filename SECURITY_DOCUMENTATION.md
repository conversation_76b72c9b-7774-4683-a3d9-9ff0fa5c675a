# Security Documentation - Carbon Footprint Calculator

## Executive Summary

This document outlines the comprehensive security measures implemented in the Carbon Footprint Calculator application. The system employs multiple layers of security controls including secure authentication, comprehensive audit logging, rate limiting, and robust access controls.

## Authentication & Authorization

### Magic Link Authentication
- **Passwordless Authentication**: Eliminates password-related vulnerabilities (weak passwords, reuse, breaches)
- **Time-Limited Tokens**: Magic links expire after 15 minutes to minimize exposure window
- **Single-Use Tokens**: Each token can only be used once, preventing replay attacks
- **Cryptographically Secure Tokens**: Uses UUID7 selectors with 64-character random validators
- **Hash-Based Validation**: Token validators are stored as SHA-256 hashes, not plaintext

### User Agent Validation
- **Bot Protection**: Login attempts without user agent headers are automatically blocked
- **Automated Attack Prevention**: Prevents headless browsers and simple scripts from accessing accounts
- **Audit Trail**: All blocked attempts are logged with specific failure reasons

### Rate Limiting
- **IP-Based Limits**: Maximum verification attempts per IP address
- **Email-Based Limits**: Prevents spam to specific email addresses
- **Time Windows**: Configurable time windows for rate limit resets
- **Exponential Backoff**: Increasing delays for repeated failed attempts

## Activity Logging & Monitoring

### Comprehensive Event Tracking
- **Login Events**: Success/failure with detailed metadata
- **Magic Link Events**: Sent, clicked, and validation attempts
- **User Actions**: Logout events and session management
- **Security Events**: Failed authentication attempts with reasons

### Audit Data Collection
- **IP Address Tracking**: All activities logged with source IP
- **User Agent Logging**: Browser/client information for forensic analysis
- **Timestamp Precision**: Exact occurrence times for all events
- **Metadata Storage**: Additional context (login method, email verification status)
- **Company Association**: Activities linked to company context when applicable

### Security Monitoring Capabilities
```sql
-- Failed login attempts by IP
SELECT ip_address, COUNT(*) as attempts, MAX(occurred_at) as last_attempt
FROM activity_logs 
WHERE event = 'login_failure' 
GROUP BY ip_address 
ORDER BY attempts DESC;

-- Suspicious activity patterns
SELECT user_id, ip_address, COUNT(*) as events
FROM activity_logs 
WHERE occurred_at > NOW() - INTERVAL 1 HOUR
GROUP BY user_id, ip_address
HAVING events > 10;
```

## Data Protection

### Database Security
- **Encrypted Connections**: All database connections use TLS
- **Prepared Statements**: Eloquent ORM prevents SQL injection
- **Input Validation**: Comprehensive validation on all user inputs
- **Sensitive Data Handling**: Tokens stored as hashes, not plaintext

### Session Management
- **Secure Session Handling**: Laravel's built-in session security
- **Session Invalidation**: Proper cleanup on logout
- **Token Regeneration**: CSRF tokens regenerated on authentication state changes

## Access Control

### Role-Based Access Control (RBAC)
- **Spatie Permissions**: Industry-standard permission system
- **Company-Based Isolation**: Users can only access their company data
- **Granular Permissions**: Specific permissions for different actions
- **Admin Separation**: Administrative functions require elevated permissions

### Company Data Isolation
```php
// Example: Users can only view companies they belong to
Gate::define(Abilities::VIEW_COMPANY, function (User $user, Company $company) {
    if ($user->hasPermissionTo("view any company")) {
        return true;
    }
    return $user->companies()->where("companies.id", $company->id)->exists();
});
```

## Infrastructure Security

### Framework Security
- **Laravel Framework**: Industry-standard PHP framework with regular security updates
- **Dependency Management**: Composer for secure package management
- **Security Headers**: CSRF protection, XSS prevention
- **Input Sanitization**: Automatic escaping and validation

### Configuration Security
- **Environment Variables**: Sensitive configuration stored in .env files
- **Debug Mode**: Disabled in production environments
- **Error Handling**: Secure error pages without information disclosure

## Compliance & Audit Features

### Audit Trail Capabilities
- **Immutable Logs**: Activity logs cannot be modified after creation
- **Comprehensive Coverage**: All security-relevant events logged
- **Retention Policy**: Configurable log retention periods
- **Export Capabilities**: Audit logs can be exported for compliance reporting

### Data Integrity
- **Database Constraints**: Foreign key constraints ensure data consistency
- **Validation Rules**: Multi-layer validation (client, server, database)
- **Atomic Transactions**: Database operations wrapped in transactions

## Security Monitoring & Alerting

### Real-Time Monitoring
- **Failed Login Tracking**: Automatic detection of brute force attempts
- **Anomaly Detection**: Unusual access patterns flagged
- **Geographic Analysis**: IP-based location tracking for suspicious access

### Incident Response
- **Detailed Logging**: Sufficient information for forensic analysis
- **User Blocking**: Ability to disable accounts immediately
- **Token Invalidation**: Emergency token revocation capabilities

## Security Best Practices Implemented

### Development Security
- **Secure Coding**: Following OWASP guidelines
- **Code Review**: Security-focused code review process
- **Dependency Scanning**: Regular updates and vulnerability scanning
- **Type Safety**: Strict typing in PHP for reduced errors

### Operational Security
- **Principle of Least Privilege**: Minimal required permissions
- **Defense in Depth**: Multiple security layers
- **Fail Secure**: Secure defaults when systems fail
- **Regular Updates**: Framework and dependency updates

## Risk Mitigation

### Authentication Risks
- **Password Attacks**: Eliminated through passwordless authentication
- **Credential Stuffing**: Not applicable due to magic link system
- **Session Hijacking**: Mitigated through secure session handling
- **Brute Force**: Prevented through rate limiting and user agent validation

### Application Risks
- **SQL Injection**: Prevented through ORM and prepared statements
- **XSS**: Mitigated through automatic escaping
- **CSRF**: Protected through Laravel's CSRF tokens
- **Information Disclosure**: Secure error handling and logging

## Compliance Considerations

### GDPR Compliance
- **Data Minimization**: Only necessary data collected
- **Purpose Limitation**: Data used only for stated purposes
- **Audit Logs**: Comprehensive tracking for compliance reporting
- **User Rights**: Ability to export and delete user data

### Industry Standards
- **OWASP Top 10**: Protection against common vulnerabilities
- **ISO 27001**: Security management best practices
- **SOC 2**: Controls for security, availability, and confidentiality

## Security Configuration

### Environment Variables
```env
# Security-related configuration
APP_ENV=production
APP_DEBUG=false
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
BCRYPT_ROUNDS=12
```

### Rate Limiting Configuration
- **Max IP Attempts**: Configurable per environment
- **Max Email Attempts**: Prevents email bombing
- **Time Windows**: Adjustable based on security requirements
- **Token Expiration**: 15-minute default with configuration options

## Incident Response Procedures

### Security Event Response
1. **Detection**: Automated monitoring alerts
2. **Assessment**: Log analysis and impact evaluation
3. **Containment**: Account suspension and token revocation
4. **Investigation**: Forensic analysis using audit logs
5. **Recovery**: System restoration and security updates
6. **Lessons Learned**: Process improvement and documentation updates

## Contact Information

For security-related inquiries or incident reporting:
- **Security Team**: [<EMAIL>]
- **Emergency Contact**: [<EMAIL>]
- **Incident Reporting**: [<EMAIL>]

## Technical Security Assessment

### Authentication Flow Security Analysis

#### Magic Link Generation
```php
// Cryptographically secure token generation
$selector = Uuid::uuid7()->toString();  // Time-ordered UUID
$validator = Str::random(64);           // 64-character random string
$hashedValidator = hash('sha256', $validator);  // SHA-256 hash storage
```

#### Token Validation Process
1. **Selector Lookup**: Database query using UUID7 selector
2. **Expiration Check**: Time-based validation (15-minute window)
3. **Hash Comparison**: Constant-time comparison using `hash_equals()`
4. **Single Use**: Token deleted immediately after successful use
5. **User Agent Validation**: Request must include valid user agent header

### Security Headers Implementation
```php
// CSRF Protection
'csrf' => [
    'except' => [], // No exceptions - all routes protected
],

// Session Security
'session' => [
    'secure' => env('SESSION_SECURE_COOKIE', true),
    'http_only' => true,
    'same_site' => 'strict',
],
```

### Database Security Schema
```sql
-- Activity logs table with security indexes
CREATE TABLE activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NULL,
    company_id BIGINT NULL,
    event ENUM('login_success', 'login_failure', 'magic_link_sent', 'magic_link_clicked', 'logout'),
    description VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    metadata JSON NULL,
    occurred_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id (user_id),
    INDEX idx_company_id (company_id),
    INDEX idx_event (event),
    INDEX idx_occurred_at (occurred_at),
    INDEX idx_ip_address (ip_address)
);
```

### Rate Limiting Implementation
```php
// IP-based rate limiting
$ipKey = "verify-token-ip:" . hash("sha256", Request::ip() ?? "");
if (RateLimiter::tooManyAttempts($ipKey, $maxAttempts)) {
    throw new TooManyRequestsHttpException($seconds, $message);
}

// Email-based rate limiting
$emailKey = "login-email:" . hash("sha256", $email);
RateLimiter::hit($emailKey, $timeWindowMinutes * 60);
```

### Security Metrics & KPIs

#### Authentication Security Metrics
- **Failed Login Rate**: < 5% of total login attempts
- **Token Expiration Rate**: 15-minute window enforcement
- **Rate Limit Triggers**: Automated blocking after threshold
- **User Agent Compliance**: 100% enforcement for valid requests

#### Monitoring Thresholds
- **Suspicious IP Activity**: > 10 failed attempts per hour
- **Geographic Anomalies**: Logins from unusual locations
- **Time-based Patterns**: Off-hours access attempts
- **User Agent Anomalies**: Missing or suspicious user agents

---

**Document Version**: 1.0
**Last Updated**: January 2025
**Next Review**: July 2025
**Classification**: Internal Use
