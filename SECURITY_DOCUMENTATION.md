# Security Documentation - Carbon Footprint Calculator

## Executive Summary

This document outlines the comprehensive security measures implemented in the Carbon Footprint Calculator application. The system employs multiple layers of security controls including secure passwordless authentication, Content Security Policy, rate limiting, role-based access control, and robust data protection measures.

## Authentication & Authorization

### Magic Link Authentication
- **Passwordless Authentication**: Eliminates password-related vulnerabilities (weak passwords, reuse, breaches)
- **Time-Limited Tokens**: Magic links expire after configurable time period (default 15 minutes) to minimize exposure window
- **Single-Use Tokens**: Each token can only be used once, preventing replay attacks
- **Cryptographically Secure Tokens**: Uses UUID7 selectors with 64-character random validators
- **Hash-Based Validation**: Token validators are stored as SHA-256 hashes, not plaintext
- **Constant-Time Comparison**: Uses `hash_equals()` to prevent timing attacks

### Rate Limiting
- **IP-Based Limits**: Maximum verification attempts per IP address
- **Email-Based Limits**: Prevents spam to specific email addresses
- **Time Windows**: Configurable time windows for rate limit resets
- **Configurable Thresholds**: All limits configurable via AuthenticationSettings

## Security Headers & Content Protection

### Content Security Policy (CSP)
- **Spatie CSP Package**: Industry-standard CSP implementation
- **Basic Preset**: Secure default CSP configuration
- **Script Protection**: Controlled script execution with nonce support
- **Style Protection**: Inline styles controlled and monitored
- **Image Sources**: Data URIs allowed for embedded content
- **Report URI**: CSP violations can be reported for monitoring

### Permissions Policy
- **Hardware Access Control**: Camera, microphone, geolocation disabled
- **Payment API**: Payment interfaces disabled
- **USB/MIDI**: Hardware interfaces disabled
- **Screen Wake Lock**: Power management controls disabled
- **XR Spatial Tracking**: VR/AR features disabled

### Security Middleware Stack
- **CSP Headers**: Automatic Content Security Policy enforcement
- **Permissions Policy**: Hardware and API access restrictions
- **Locale Security**: Secure locale handling from route parameters
- **Iframe Protection**: Ensures application runs in intended iframe context

## Data Protection & Database Security

### Database Security
- **Encrypted Connections**: SSL/TLS support for MySQL/MariaDB connections
- **Prepared Statements**: Eloquent ORM prevents SQL injection attacks
- **Foreign Key Constraints**: Database-level referential integrity
- **Strict Mode**: Database strict mode enabled for data integrity
- **Destructive Command Protection**: Production database protected from destructive operations
- **Sensitive Data Handling**: Login tokens stored as SHA-256 hashes, not plaintext

### Session Management
- **Secure Session Handling**: Laravel's built-in session security
- **Session Invalidation**: Proper cleanup on logout with session regeneration
- **CSRF Protection**: All state-changing operations protected by CSRF tokens
- **Secure Cookies**: HTTPS-only, HttpOnly, and SameSite=Strict cookies

## Access Control & Authorization

### Role-Based Access Control (RBAC)
- **Spatie Laravel Permission**: Industry-standard permission system
- **Company-Based Isolation**: Users can only access their company data
- **Granular Permissions**: Specific permissions for different actions
- **Gate-Based Authorization**: Laravel Gates for complex authorization logic
- **Admin Panel Security**: Filament admin panel with authentication middleware

### Company Data Isolation
```php
// Example: Users can only view companies they belong to
Gate::define(Abilities::VIEW_COMPANY, function (User $user, Company $company) {
    if ($user->hasPermissionTo("view any company")) {
        return true;
    }
    return $user->companies()->where("companies.id", $company->id)->exists();
});

// Company editing permissions
Gate::define(Abilities::EDIT_COMPANY, function (User $user, Company $company) {
    if ($user->hasPermissionTo("edit any company")) {
        return true;
    }
    return $user->companies()->where("companies.id", $company->id)->exists();
});
```

### Authorization Capabilities
- **VIEW_COMPANY**: Users can view companies they belong to
- **VIEW_ANY_COMPANY**: Admin users can view all companies
- **EDIT_COMPANY**: Users can edit companies they belong to
- **EDIT_COMPANY_DATA**: Users can modify company data
- **VIEW_COMPANY_AUDIT_LOGS**: Primary users can view audit logs

## Infrastructure Security

### Framework Security
- **Laravel Framework**: Industry-standard PHP framework with regular security updates
- **Dependency Management**: Composer for secure package management
- **Model Strictness**: Eloquent models configured with strict mode
- **HTTPS Enforcement**: Forced HTTPS in production environments
- **Stray Request Prevention**: HTTP client configured to prevent unintended requests
- **Password Security**: Minimum 12 characters, maximum 255, breach checking enabled

### Configuration Security
- **Environment Variables**: Sensitive configuration stored in .env files
- **AES-256-CBC Encryption**: Strong encryption for application data
- **Key Rotation Support**: Previous encryption keys supported for rotation
- **Debug Mode**: Disabled in production environments
- **Secure Error Handling**: Production error pages without information disclosure

### Development Security
- **Type Safety**: Strict typing enforced throughout application
- **Code Quality**: PHPStan and other static analysis tools
- **Immutable Dates**: CarbonImmutable used for date handling
- **Vite Integration**: Secure asset compilation and delivery

## Audit & Compliance Features

### Built-in Audit System
- **Auditable Trait**: Comprehensive model change tracking
- **Immutable Audit Logs**: Audit entries cannot be modified after creation
- **Batch Tracking**: Related changes grouped with UUID batch identifiers
- **Field-Level Tracking**: Individual field changes tracked with old/new values
- **User Context**: All changes linked to authenticated users
- **Company Context**: Changes associated with company scope

### Data Integrity
- **Database Constraints**: Foreign key constraints ensure referential integrity
- **Validation Rules**: Multi-layer validation (client, server, database)
- **Atomic Transactions**: Database operations wrapped in transactions
- **Model Strictness**: Eloquent strict mode prevents common errors

## Activity Logging & Monitoring

### Comprehensive Authentication Logging
- **Login Events**: Success/failure with detailed metadata and failure reasons
- **Magic Link Events**: Sent, clicked, and validation attempts tracked
- **User Actions**: Logout events and session management logged
- **Security Events**: Failed authentication attempts with specific reasons

### Audit Data Collection
- **IP Address Tracking**: All activities logged with source IP address
- **User Agent Logging**: Browser/client information for forensic analysis
- **Timestamp Precision**: Exact occurrence times for all events
- **Metadata Storage**: Additional context (login method, email verification status)
- **Company Association**: Activities linked to company context when applicable

### Security Monitoring Capabilities
- **Rate Limit Violations**: Automatic detection and blocking with logging
- **Authentication Failures**: Comprehensive failed login attempt tracking
- **Token Validation**: Invalid token attempt monitoring with detailed reasons
- **Session Security**: Session hijacking prevention and logout tracking

## Security Best Practices Implemented

### Development Security
- **Secure Coding**: Following OWASP guidelines and Laravel best practices
- **Type Safety**: Strict typing throughout PHP codebase
- **Input Validation**: Comprehensive validation at all entry points
- **Output Encoding**: Automatic XSS protection through Blade templating
- **Dependency Management**: Composer with security-focused package selection

### Operational Security
- **Principle of Least Privilege**: Role-based permissions with minimal access
- **Defense in Depth**: Multiple security layers (CSP, permissions, validation)
- **Fail Secure**: Secure defaults when authentication or authorization fails
- **Environment Separation**: Clear separation between development and production
- **Configuration Security**: Sensitive data in environment variables only

## Risk Assessment & Mitigation

### Authentication Risks
- **Password Attacks**: ✅ Eliminated through passwordless magic link authentication
- **Credential Stuffing**: ✅ Not applicable due to magic link system
- **Session Hijacking**: ✅ Mitigated through secure session handling and HTTPS
- **Brute Force**: ✅ Prevented through IP-based rate limiting
- **Token Replay**: ✅ Prevented through single-use tokens

### Application Security Risks
- **SQL Injection**: ✅ Prevented through Eloquent ORM and prepared statements
- **XSS (Cross-Site Scripting)**: ✅ Mitigated through Blade auto-escaping and CSP
- **CSRF (Cross-Site Request Forgery)**: ✅ Protected through Laravel's CSRF tokens
- **Clickjacking**: ✅ Prevented through iframe restrictions and CSP
- **Information Disclosure**: ✅ Secure error handling in production

### Infrastructure Risks
- **Man-in-the-Middle**: ✅ Mitigated through HTTPS enforcement
- **Data Breach**: ✅ Reduced through encryption and access controls
- **Privilege Escalation**: ✅ Prevented through RBAC and company isolation
- **Configuration Exposure**: ✅ Sensitive config in environment variables only

## Compliance Considerations

### GDPR Compliance
- **Data Minimization**: Only necessary data collected
- **Purpose Limitation**: Data used only for stated purposes
- **Audit Logs**: Comprehensive tracking for compliance reporting
- **User Rights**: Ability to export and delete user data

### Industry Standards
- **OWASP Top 10**: Protection against common vulnerabilities
- **ISO 27001**: Security management best practices
- **SOC 2**: Controls for security, availability, and confidentiality

## Security Configuration

### Environment Variables
```env
# Security-related configuration
APP_ENV=production
APP_DEBUG=false
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
BCRYPT_ROUNDS=12
```

### Rate Limiting Configuration
- **Max IP Attempts**: Configurable per environment
- **Max Email Attempts**: Prevents email bombing
- **Time Windows**: Adjustable based on security requirements
- **Token Expiration**: 15-minute default with configuration options

## Incident Response Procedures

### Security Event Response
1. **Detection**: Automated monitoring alerts
2. **Assessment**: Log analysis and impact evaluation
3. **Containment**: Account suspension and token revocation
4. **Investigation**: Forensic analysis using audit logs
5. **Recovery**: System restoration and security updates
6. **Lessons Learned**: Process improvement and documentation updates

## Contact Information

For security-related inquiries or incident reporting:
- **Security Team**: [<EMAIL>]
- **Emergency Contact**: [<EMAIL>]
- **Incident Reporting**: [<EMAIL>]

## Technical Security Assessment

### Authentication Flow Security Analysis

#### Magic Link Generation
```php
// Cryptographically secure token generation in SplitToken::generate()
$selector = Uuid::uuid7()->toString();  // Time-ordered UUID
$validator = Str::random(64);           // 64-character random string
$hashedValidator = hash('sha256', $validator);  // SHA-256 hash storage

// Token storage in AuthenticationService
LoginToken::create([
    "user_id" => $user->id,
    "selector" => $splitToken->selector(),
    "token" => $splitToken->hashedValidator(),
    "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
]);
```

#### Token Validation Process
1. **Rate Limiting Check**: IP-based rate limiting before validation
2. **Selector Lookup**: Database query using UUID7 selector with row locking
3. **Expiration Check**: Time-based validation (configurable window)
4. **Hash Comparison**: Constant-time comparison using `hash_equals()`
5. **Single Use**: Token deleted immediately after successful use
6. **Session Management**: Proper logout/login handling for user switching

### Security Headers Implementation
```php
// Content Security Policy (config/csp.php)
'presets' => [Spatie\Csp\Presets\Basic::class],
'directives' => [
    [Directive::SCRIPT, [Keyword::UNSAFE_EVAL, Keyword::UNSAFE_INLINE]],
    [Directive::STYLE, [Keyword::UNSAFE_INLINE]],
    [Directive::IMG, ["data:"]],
],

// Permissions Policy (SetPermissionsPolicy middleware)
"Permissions-Policy" => "camera=(), microphone=(), geolocation=(), payment=(), usb=(), midi=(), screen-wake-lock=(), xr-spatial-tracking=()"
```

### Database Security Schema
```sql
-- Login tokens table with security features
CREATE TABLE login_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    selector VARCHAR(36) NOT NULL UNIQUE,  -- UUID7 selector
    token VARCHAR(64) NOT NULL,            -- SHA-256 hash of validator
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_selector (selector),
    INDEX idx_expires_at (expires_at)
);

-- Audit logs table for model changes
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    event ENUM('created', 'updated', 'deleted', 'soft_deleted', 'force_deleted', 'restored'),
    auditable_type VARCHAR(255) NOT NULL,
    auditable_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    company_id BIGINT NOT NULL,
    batch_uuid CHAR(36) NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    old_value TEXT NULL,
    new_value TEXT NULL,
    field_type ENUM('string', 'integer', 'float', 'boolean'),
    occurred_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_auditable (auditable_type, auditable_id),
    INDEX idx_user_id (user_id),
    INDEX idx_company_id (company_id),
    INDEX idx_batch_uuid (batch_uuid),
    INDEX idx_occurred_at (occurred_at)
);
```

### Rate Limiting Implementation
```php
// IP-based rate limiting in AuthenticationService
$ipKey = "verify-token-ip:" . hash("sha256", Request::ip() ?? "");
if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
    $seconds = RateLimiter::availableIn($ipKey);
    throw new TooManyRequestsHttpException($seconds, $message);
}

// Email-based rate limiting for magic link sending
$emailKey = "login-email:" . hash("sha256", $email);
RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

// Configurable settings via AuthenticationSettings
public int $tokenExpirationMinutes;
public int $maxIpAttempts;
public int $ipTimeWindowMinutes;
public int $maxEmailAttempts;
public int $emailTimeWindowMinutes;
public int $maxVerificationAttempts;
public int $verificationTimeWindowMinutes;
```

### Security Configuration Management

#### Authentication Settings
- **Token Expiration**: Configurable via settings (default 15 minutes)
- **Rate Limits**: All thresholds configurable per environment
- **Time Windows**: Adjustable rate limiting windows
- **IP Protection**: SHA-256 hashed IP addresses for rate limiting keys
- **Email Protection**: SHA-256 hashed emails for rate limiting keys

#### Application Security Settings
- **Model Strictness**: `Model::shouldBeStrict()` enabled
- **HTTPS Enforcement**: `URL::forceHttps()` in production
- **Password Requirements**: Minimum 12 chars, breach checking enabled
- **Database Protection**: Destructive commands prohibited in production

---

**Document Version**: 1.0
**Last Updated**: January 2025
**Next Review**: July 2025
**Classification**: Internal Use
