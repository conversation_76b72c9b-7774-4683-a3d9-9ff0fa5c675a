<?php

declare(strict_types=1);

use App\Http\Middleware\EnsureInIframe;
use App\Http\Middleware\SetLocaleFromRoute;
use App\Livewire\AuthPage;
use App\Livewire\CompanyPage;
use App\Livewire\DataPage;
use App\Livewire\EmissionFactorsPage;
use App\Livewire\InstructionsPage;
use App\Livewire\ResultsPage;
use App\Livewire\VerifyLoginTokenPage;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Contracts\Routing\UrlGenerator as UrlGeneratorContract;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Livewire\Livewire;

Route::pattern("_locale", implode("|", ["fi", "sv", "en"]));

Route::prefix("{_locale}")->group(function (): void {
  Route::middleware([RedirectIfAuthenticated::class, EnsureInIframe::class])->group(
    function (): void {
      Route::get("/login", AuthPage::class)->name("login");
    },
  );

  Route::middleware([])->group(function (): void {
    Route::get("/login/verify/{token}", VerifyLoginTokenPage::class)->name("login.verify");
  });

  Route::middleware([Authenticate::class, EnsureInIframe::class])->group(function (): void {
    Route::get("/", function (): RedirectResponse {
      return redirect()->route("company", ["_locale" => App::getLocale()]);
    });
    Route::get("/company", CompanyPage::class)->name("company");
    Route::get("/company/create", CompanyPage::class)->name("company.create");
    Route::get("/data/{year?}", DataPage::class)->whereNumber("year")->name("data");
    Route::get("/emission-factors/{year?}", EmissionFactorsPage::class)
      ->whereNumber("year")
      ->name("emission-factors");
    Route::get("/results/{year?}", ResultsPage::class)->whereNumber("year")->name("results");
    Route::get("/instructions", InstructionsPage::class)->name("instructions");
  });

  Route::middleware([Authenticate::class])->group(function (): void {
    Route::get("/export/emission-calculations/{yearId?}", [
      \App\Http\Controllers\ExportController::class,
      "exportEmissionCalculations",
    ])
      ->whereNumber("yearId")
      ->name("export.emission-calculations");
  });

  Livewire::setUpdateRoute(function (array $handle) {
    return Route::post("/livewire/update", $handle)->name("localized.");
  });
});

Route::middleware([])->group(function (): void {
  if (app()->isProduction()) {
    return;
  }

  Route::get("/mailable", function () {
    return new \App\Mail\CompanyInvitation("Test", "Test", 5);
  });

  Route::get("/iframe", function () {
    $url = url("/");

    return response()->view("iframe", ["url" => $url]);
  });
});
