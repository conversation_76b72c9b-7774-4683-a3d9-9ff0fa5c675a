<?php

declare(strict_types=1);

use App\Http\Controllers\LogoutController;
use App\Http\Middleware\EnsureInIframe;
use App\Livewire\AcceptInvitationPage;
use App\Livewire\AuthPage;
use App\Livewire\CompanyPage;
use App\Livewire\DataPage;
use App\Livewire\EmissionFactorsPage;
use App\Livewire\InstructionsPage;
use App\Livewire\ResultsPage;
use App\Livewire\VerifyLoginTokenPage;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Support\Facades\Route;
use Livewire\Livewire;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

Route::group(
  [
    "prefix" => LaravelLocalization::setLocale(),
  ],
  function () {
    // Guest routes
    Route::middleware([RedirectIfAuthenticated::class, EnsureInIframe::class])->group(function () {
      Route::get("/login", AuthPage::class)->name("login");
    });

    // Public routes (no auth required)
    Route::get("/login/verify/{selector}/{validator}", VerifyLoginTokenPage::class)->name(
      "login.verify",
    );
    Route::get("/invitation/accept/{selector}/{validator}", AcceptInvitationPage::class)->name(
      "invitation.accept",
    );

    // Authenticated routes
    Route::middleware([Authenticate::class, EnsureInIframe::class])->group(function () {
      Route::get("/", function () {
        return redirect()->to(LaravelLocalization::localizeUrl("company"));
      });
      Route::get("/company", CompanyPage::class)->name("company");
      Route::get("/company/create", CompanyPage::class)->name("company.create");
      Route::get("/data/{year?}", DataPage::class)->whereNumber("year")->name("data");
      Route::get("/emission-factors/{year?}", EmissionFactorsPage::class)
        ->whereNumber("year")
        ->name("emission-factors");
      Route::get("/results/{year?}", ResultsPage::class)->whereNumber("year")->name("results");
      Route::get("/instructions", InstructionsPage::class)->name("instructions");
    });

    // Auth required, no iframe check
    Route::middleware([Authenticate::class])->group(function () {
      Route::post("/logout", [LogoutController::class, "logout"])->name("logout");
      Route::get("/export/emission-calculations/{yearId?}", [
        \App\Http\Controllers\ExportController::class,
        "exportEmissionCalculations",
      ])
        ->whereNumber("yearId")
        ->name("export.emission-calculations");
    });

    // Livewire route
    Livewire::setUpdateRoute(function (array $handle) {
      return Route::post("/livewire/update", $handle);
    });

    // Dev routes - conditionally registered
    if (app()->isLocal()) {
      Route::get("/mailable", function () {
        return new \App\Mail\CompanyInvitation("Test", "Test", 5);
      });

      Route::get("/iframe", function () {
        return response()->view("iframe");
      });
    }
  },
);
