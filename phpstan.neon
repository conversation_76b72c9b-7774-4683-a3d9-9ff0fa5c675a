includes:
    - phar://phpstan.phar/conf/bleedingEdge.neon
    - vendor/phpstan/phpstan-strict-rules/rules.neon
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - vendor/larastan/larastan/extension.neon
    - vendor/tomasvotruba/bladestan/config/extension.neon
    - vendor/roave/no-floaters/rules.neon

rules:
    - Utils\PHPStan\ForbidInlineVarRule
    - Utils\PHPStan\ForbidEmptyCatchRule

parameters:
    paths:
        - app
        - bootstrap
        - config
        - database
        - public
        - resources
        - routes
        - tests
        - utils
    
    excludePaths:
        analyse:
            - bootstrap/cache
            - database/migrations/2025_05_08_130515_create_permission_tables.php

    level: max
    
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    reportPossiblyNonexistentGeneralArrayOffset: true
    reportPossiblyNonexistentConstantArrayOffset: true

    exceptions:
        check:
            missingCheckedExceptionInThrows: true
            tooWideThrowType: true
        uncheckedExceptionClasses:
            - 'App\Exceptions\AssertionException'
            - 'Illuminate\Validation\ValidationException'
            - 'Spatie\Permission\Exceptions\PermissionDoesNotExist'

    ignoreErrors:
        - '#Dynamic call to static method Illuminate\\Database\\Eloquent\\Builder<.*#'
        - '#Dynamic call to static method PHPUnit\\Framework\\Assert::assert.*#'
        - '#Dynamic call to static method Illuminate\\Support\\Stringable::sanitizeHtml\(\)#'
        - '#Parameter \$view of class Illuminate\\View\\AnonymousComponent constructor expects string, Illuminate\\Contracts\\View\\View given\.#'
        -
            identifier: missingType.checkedException
            path: database/migrations
        -
            identifier: missingType.checkedException
            path: database/seeders
        -
            identifier: missingType.checkedException
            path: tests
        - 
            message: '#No hint path defined for \[mail\]#'
            paths:
                - app/Mail/*