<?php

declare(strict_types=1);

namespace App\Traits;

use Astrotomic\Translatable\Translatable as AstrotomicTranslatable;
use Illuminate\Database\Eloquent\Model;
use RuntimeException;

/**
 * @template T of Model
 */
trait Translatable
{
  use AstrotomicTranslatable {
    AstrotomicTranslatable::translate as parentTranslate;
    AstrotomicTranslatable::translateOrNew as parentTranslateOrNew;
  }

  /**
   * @return T|null
   */
  public function translate(?string $locale = null, bool $withFallback = false): ?Model
  {
    if (!$this->relationLoaded("translation") && !$this->relationLoaded("translations")) {
      report(
        new RuntimeException(
          sprintf(
            "Attempting to access translation on model [%s] without eager loading.",
            static::class,
          ),
        ),
      );
    }

    /** @phpstan-ignore-next-line custom.forbidInlineVar */
    /** @var T|null */
    $parentReturn = $this->parentTranslate($locale, $withFallback);

    return $parentReturn;
  }

  /**
   * @return T
   */
  public function translateOrNew(?string $locale = null): Model
  {
    if (!$this->relationLoaded("translation") && !$this->relationLoaded("translations")) {
      report(
        new RuntimeException(
          sprintf(
            "Attempting to access translation on model [%s] without eager loading.",
            static::class,
          ),
        ),
      );
    }

    /** @phpstan-ignore-next-line custom.forbidInlineVar */
    /** @var T */
    $parentReturn = $this->parentTranslateOrNew($locale);

    return $parentReturn;
  }
}
