<?php

declare(strict_types=1);

namespace App\Traits;

use App\Enums\AuditEvent;
use App\Enums\ScalarType;
use App\Helpers\Assert;
use App\Interfaces\Auditable as AuditableContract;
use App\Models\AuditLog;
use App\Models\Company;
use Carbon\Carbon;
use DateTime;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use LogicException;
use RuntimeException;
use Stringable;

trait Auditable
{
  /**
   * Flag to disable audit logging (useful for seeding)
   */
  protected static bool $auditingEnabled = true;

  /**
   * @return array<int, string>
   */
  abstract public function getAuditableFields(): array;

  /**
   * Disable audit logging
   */
  public static function disableAuditing(): void
  {
    static::$auditingEnabled = false;
  }

  /**
   * Enable audit logging
   */
  public static function enableAuditing(): void
  {
    static::$auditingEnabled = true;
  }

  public static function bootAuditable(): void
  {
    // Check once if this model uses soft deletes
    $usesSoftDeletes = in_array(SoftDeletes::class, class_uses_recursive(static::class), true);

    // Log created event
    static::created(function (Model&AuditableContract $model) {
      $model->logAuditEvent(AuditEvent::CREATED);
    });

    // Log updated event
    static::updated(function (Model&AuditableContract $model) {
      $model->logAuditEvent(AuditEvent::UPDATED);
    });

    if ($usesSoftDeletes) {
      // Soft delete event
      static::softDeleted(function (Model&AuditableContract $model) {
        $model->logAuditEvent(AuditEvent::SOFT_DELETED);
      });

      // Force delete event
      static::forceDeleted(function (Model&AuditableContract $model) {
        $model->logAuditEvent(AuditEvent::FORCE_DELETED);
      });

      // Restore event
      static::restored(function (Model&AuditableContract $model) {
        $model->logAuditEvent(AuditEvent::RESTORED);
      });
    } else {
      // Only register deleted event for models without soft deletes
      static::deleted(function (Model&AuditableContract $model) {
        $model->logAuditEvent(AuditEvent::DELETED);
      });
    }
  }

  /**
   * @throws RuntimeException
   * @throws LogicException
   */
  public function logAuditEvent(AuditEvent $event): void
  {
    // Skip if auditing is disabled
    if (!static::$auditingEnabled) {
      return;
    }

    $user = Auth::user();

    if ($user === null) {
      throw new RuntimeException("Cannot log audit without user context");
    }

    $userId = $user->id;

    // For company creation, use the company's own ID
    if ($this instanceof Company && $event === AuditEvent::CREATED) {
      $companyId = $this->getKey();
      Assert::int($companyId);
    } else {
      $companyId = $user->selected_company_id;
    }

    if ($companyId === null) {
      throw new RuntimeException("Cannot log audit without company context");
    }

    $auditableType = $this->getMorphClass();
    $auditableId = $this->getKey();
    Assert::int($auditableId);

    $auditableFields = $this->getAuditableFields();
    $batchId = (string) Str::uuid7();
    $occurredAt = now();
    $logsToCreate = [];

    switch ($event) {
      case AuditEvent::CREATED:
        foreach ($auditableFields as $field) {
          $value = $this->getAttribute($field);
          if ($value !== null && $this->isValidChangeValue($value)) {
            $logsToCreate[] = [
              "batch_uuid" => $batchId,
              "event" => $event->value,
              "auditable_type" => $auditableType,
              "auditable_id" => $auditableId,
              "user_id" => $userId,
              "company_id" => $companyId,
              "field_name" => $field,
              "old_value" => null,
              "new_value" => $this->formatValueForStorage($value),
              "field_type" => ScalarType::fromValue($value),
              "occurred_at" => $occurredAt,
              "created_at" => $occurredAt,
              "updated_at" => $occurredAt,
            ];
          }
        }
        break;

      case AuditEvent::UPDATED:
        foreach ($auditableFields as $field) {
          $old = $this->getOriginal($field);
          $new = $this->getAttribute($field);

          // Compare values, not object instances
          if (
            $this->hasValueChanged($old, $new) &&
            $this->isValidChangeValue($old) &&
            $this->isValidChangeValue($new)
          ) {
            $fieldType = match (true) {
              $new !== null => ScalarType::fromValue($new),
              $old !== null => ScalarType::fromValue($old),
              default => throw new LogicException(
                "Both old and new values cannot be null in an update",
              ),
            };

            $logsToCreate[] = [
              "batch_uuid" => $batchId,
              "event" => $event->value,
              "auditable_type" => $auditableType,
              "auditable_id" => $auditableId,
              "user_id" => $userId,
              "company_id" => $companyId,
              "field_name" => $field,
              "old_value" => $this->formatValueForStorage($old),
              "new_value" => $this->formatValueForStorage($new),
              "field_type" => $fieldType,
              "occurred_at" => $occurredAt,
              "created_at" => $occurredAt,
              "updated_at" => $occurredAt,
            ];
          }
        }
        break;

      case AuditEvent::DELETED:
      case AuditEvent::SOFT_DELETED:
      case AuditEvent::FORCE_DELETED:
        foreach ($auditableFields as $field) {
          $value = $this->getAttribute($field);
          if ($value !== null && $this->isValidChangeValue($value)) {
            $logsToCreate[] = [
              "batch_uuid" => $batchId,
              "event" => $event->value,
              "auditable_type" => $auditableType,
              "auditable_id" => $auditableId,
              "user_id" => $userId,
              "company_id" => $companyId,
              "field_name" => $field,
              "old_value" => $this->formatValueForStorage($value),
              "new_value" => null,
              "field_type" => ScalarType::fromValue($value),
              "occurred_at" => $occurredAt,
              "created_at" => $occurredAt,
              "updated_at" => $occurredAt,
            ];
          }
        }
        break;

      case AuditEvent::RESTORED:
        foreach ($auditableFields as $field) {
          $value = $this->getAttribute($field);
          if ($value !== null && $this->isValidChangeValue($value)) {
            $logsToCreate[] = [
              "batch_uuid" => $batchId,
              "event" => $event->value,
              "auditable_type" => $auditableType,
              "auditable_id" => $auditableId,
              "user_id" => $userId,
              "company_id" => $companyId,
              "field_name" => $field,
              "old_value" => null,
              "new_value" => $this->formatValueForStorage($value),
              "field_type" => ScalarType::fromValue($value),
              "occurred_at" => $occurredAt,
              "created_at" => $occurredAt,
              "updated_at" => $occurredAt,
            ];
          }
        }
        break;
    }

    // Bulk insert all audit logs
    if (count($logsToCreate) !== 0) {
      AuditLog::insert($logsToCreate);
    }
  }

  /**
   * @return MorphMany<AuditLog, $this>
   */
  public function auditLogs(): MorphMany
  {
    return $this->morphMany(AuditLog::class, "auditable");
  }

  /**
   * Check if a value has actually changed
   * Handles proper comparison for datetime objects
   */
  private function hasValueChanged(mixed $old, mixed $new): bool
  {
    // If both are null, no change
    if ($old === null && $new === null) {
      return false;
    }

    // If one is null and the other isn't, there's a change
    if ($old === null || $new === null) {
      return true;
    }

    // Handle datetime comparisons - check Carbon first (subclass), then DateTime (parent)
    if (
      ($old instanceof Carbon || $old instanceof DateTimeImmutable) &&
      ($new instanceof Carbon || $new instanceof DateTimeImmutable)
    ) {
      // Compare formatted strings instead of object instances
      $oldFormatted = $old instanceof Carbon ? $old->toIso8601String() : $old->format("c");
      $newFormatted = $new instanceof Carbon ? $new->toIso8601String() : $new->format("c");

      return $oldFormatted !== $newFormatted;
    }

    // For other types, use standard comparison
    return $old !== $new;
  }

  /**
   * @phpstan-assert-if-true string|int|bool|null|\Stringable $value
   */
  private function isValidChangeValue(mixed $value): bool
  {
    return $value === null ||
      is_string($value) ||
      is_int($value) ||
      is_bool($value) ||
      $value instanceof Stringable;
  }

  /**
   * Format value for storage
   */
  private function formatValueForStorage(string|int|bool|null|Stringable $value): ?string
  {
    if ($value === null) {
      return null;
    }

    if (is_bool($value)) {
      return $value ? "1" : "0";
    }

    return (string) $value;
  }
}
