<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Export database schema
 */
final class SchemaJsonCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'schema:json 
    {--table= : Export specific table}
    {--format=compact : Output format: compact or full}
    {--no-glossary : Exclude glossary from compact format}';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = "Export database schema";

  /**
   * Execute the console command.
   *
   * @return 0
   */
  public function handle(): int
  {
    $format = $this->option("format");
    if (!is_string($format)) {
      $format = "compact";
    }

    $noGlossary = $this->option("no-glossary") === true;

    $tables = $this->getTables();

    $output = match ($format) {
      "full" => $this->exportAsFull($tables),
      default => $this->exportAsCompact($tables, !$noGlossary),
    };

    $this->line($output);

    return Command::SUCCESS;
  }

  /**
   * Get tables to export
   *
   * @return array<string, array{
   *   columns: array<string, array{type: string, null: bool, key: string, extra: string, default: mixed}>,
   *   indexes: array<string, array{unique: bool, cols: array<int, string>}>,
   *   foreign_keys: array<string, array{col: string, ref: string, on_delete: string, on_update: string}>,
   *   check_constraints: array<string, string>
   * }>
   */
  private function getTables(): array
  {
    $tableOption = $this->option("table");
    $tableNames = [];

    if ($tableOption !== null) {
      $tableNames = [$tableOption];
    } else {
      $tablesRaw = DB::select("SHOW TABLES");
      foreach ($tablesRaw as $table) {
        if (!is_object($table)) {
          continue;
        }
        $tableArray = (array) $table;
        $firstValue = reset($tableArray);
        if (is_string($firstValue)) {
          $tableNames[] = $firstValue;
        }
      }
    }

    $tables = [];
    foreach ($tableNames as $tableName) {
      $tables[$tableName] = $this->getTableSchema($tableName);
    }

    return $tables;
  }

  /**
   * Get schema for a single table
   *
   * @return array{
   *   columns: array<string, array{type: string, null: bool, key: string, extra: string, default: mixed}>,
   *   indexes: array<string, array{unique: bool, cols: array<int, string>}>,
   *   foreign_keys: array<string, array{col: string, ref: string, on_delete: string, on_update: string}>,
   *   check_constraints: array<string, string>
   * }
   */
  private function getTableSchema(string $tableName): array
  {
    // Get columns
    $columnsRaw = DB::select("SHOW COLUMNS FROM `{$tableName}`");
    $columns = [];

    foreach ($columnsRaw as $column) {
      if (!is_object($column)) {
        continue;
      }
      $columnData = (array) $column;

      $fieldName =
        isset($columnData["Field"]) && is_string($columnData["Field"]) ? $columnData["Field"] : "";

      $type =
        isset($columnData["Type"]) && is_string($columnData["Type"]) ? $columnData["Type"] : "";

      $key = isset($columnData["Key"]) && is_string($columnData["Key"]) ? $columnData["Key"] : "";

      $extra =
        isset($columnData["Extra"]) && is_string($columnData["Extra"]) ? $columnData["Extra"] : "";

      $columns[$fieldName] = [
        "type" => $this->abbreviateType($type),
        "null" => ($columnData["Null"] ?? "") === "YES",
        "key" => $this->abbreviateKey($key),
        "extra" => $this->abbreviateExtra($extra),
        "default" => $columnData["Default"] ?? null,
      ];
    }

    // Get indexes
    $indexes = [];
    try {
      $indexesRaw = DB::select("SHOW INDEX FROM `{$tableName}`");
      foreach ($indexesRaw as $index) {
        if (!is_object($index)) {
          continue;
        }
        $indexData = (array) $index;

        $keyName =
          isset($indexData["Key_name"]) && is_string($indexData["Key_name"])
            ? $indexData["Key_name"]
            : "";

        $columnName =
          isset($indexData["Column_name"]) && is_string($indexData["Column_name"])
            ? $indexData["Column_name"]
            : "";

        $nonUnique = isset($indexData["Non_unique"])
          ? $indexData["Non_unique"] === 1 || $indexData["Non_unique"] === "1"
          : true;

        $seqInIndex = 1;
        if (isset($indexData["Seq_in_index"]) && is_numeric($indexData["Seq_in_index"])) {
          $seqInIndex = (int) $indexData["Seq_in_index"];
        }

        if ($keyName !== "" && !isset($indexes[$keyName])) {
          $indexes[$keyName] = [
            "unique" => !$nonUnique,
            "cols" => [],
          ];
        }

        if ($keyName !== "") {
          $indexes[$keyName]["cols"][$seqInIndex - 1] = $columnName;
        }
      }

      foreach ($indexes as &$index) {
        ksort($index["cols"]);
        $index["cols"] = array_values($index["cols"]);
      }
    } catch (Exception $e) {
      $this->error(
        "Warning: Could not retrieve indexes for table {$tableName}: " . $e->getMessage(),
      );
    }

    // Get foreign keys with cascade information
    $foreignKeys = [];
    try {
      $foreignKeysRaw = DB::select("
        SELECT 
          kcu.CONSTRAINT_NAME as cn,
          kcu.COLUMN_NAME as col,
          kcu.REFERENCED_TABLE_NAME as ref_t,
          kcu.REFERENCED_COLUMN_NAME as ref_c,
          rc.DELETE_RULE as del_rule,
          rc.UPDATE_RULE as upd_rule
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
          ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
          AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_NAME = '{$tableName}'
          AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
          AND kcu.CONSTRAINT_SCHEMA = DATABASE()
      ");

      foreach ($foreignKeysRaw as $fk) {
        if (!is_object($fk)) {
          continue;
        }
        $fkArray = (array) $fk;

        $constraintName = isset($fkArray["cn"]) && is_string($fkArray["cn"]) ? $fkArray["cn"] : "";

        $col = isset($fkArray["col"]) && is_string($fkArray["col"]) ? $fkArray["col"] : "";

        $refTable =
          isset($fkArray["ref_t"]) && is_string($fkArray["ref_t"]) ? $fkArray["ref_t"] : "";

        $refCol = isset($fkArray["ref_c"]) && is_string($fkArray["ref_c"]) ? $fkArray["ref_c"] : "";

        $deleteRule =
          isset($fkArray["del_rule"]) && is_string($fkArray["del_rule"])
            ? $fkArray["del_rule"]
            : "RESTRICT";

        $updateRule =
          isset($fkArray["upd_rule"]) && is_string($fkArray["upd_rule"])
            ? $fkArray["upd_rule"]
            : "RESTRICT";

        if ($constraintName !== "") {
          $foreignKeys[$constraintName] = [
            "col" => $col,
            "ref" => $refTable . "." . $refCol,
            "on_delete" => $deleteRule,
            "on_update" => $updateRule,
          ];
        }
      }
    } catch (Exception $e) {
      $this->error(
        "Warning: Could not retrieve foreign keys for table {$tableName}: " . $e->getMessage(),
      );
    }

    // Get check constraints (MySQL 8.0.16+)
    $checkConstraints = [];
    try {
      $checkConstraintsRaw = DB::select("
        SELECT 
          cc.CONSTRAINT_NAME as name,
          cc.CHECK_CLAUSE as clause
        FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS cc
        JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
          ON cc.CONSTRAINT_SCHEMA = tc.CONSTRAINT_SCHEMA
          AND cc.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
        WHERE tc.TABLE_NAME = '{$tableName}'
          AND tc.CONSTRAINT_SCHEMA = DATABASE()
          AND tc.CONSTRAINT_TYPE = 'CHECK'
      ");

      foreach ($checkConstraintsRaw as $check) {
        if (!is_object($check)) {
          continue;
        }
        $checkArray = (array) $check;

        $constraintName =
          isset($checkArray["name"]) && is_string($checkArray["name"]) ? $checkArray["name"] : "";

        $clause =
          isset($checkArray["clause"]) && is_string($checkArray["clause"])
            ? $checkArray["clause"]
            : "";

        if ($constraintName !== "" && $clause !== "") {
          // Clean up the clause for compactness
          $clause = $this->cleanCheckClause($clause);
          $checkConstraints[$constraintName] = $clause;
        }
      }
    } catch (Exception $e) {
      $this->error(
        "Warning: Could not retrieve check constraints for table {$tableName}: " . $e->getMessage(),
      );
    }

    return [
      "columns" => $columns,
      "indexes" => $indexes,
      "foreign_keys" => $foreignKeys,
      "check_constraints" => $checkConstraints,
    ];
  }

  /**
   * Clean up check constraint clause for readability
   */
  private function cleanCheckClause(string $clause): string
  {
    // Remove excessive parentheses and backticks for readability
    $clause = str_replace("`", "", $clause);
    // Remove redundant outer parentheses if present
    if (mb_substr($clause, 0, 1) === "(" && mb_substr($clause, -1) === ")") {
      $clause = mb_substr($clause, 1, -1);
    }
    // Normalize whitespace
    $clause = preg_replace("/\s+/", " ", mb_trim($clause)) ?? $clause;

    return $clause;
  }

  /**
   * Export as ultra-compact JSON with glossary by default
   *
   * @param array<string, array{
   *   columns: array<string, array{type: string, null: bool, key: string, extra: string, default: mixed}>,
   *   indexes: array<string, array{unique: bool, cols: array<int, string>}>,
   *   foreign_keys: array<string, array{col: string, ref: string, on_delete: string, on_update: string}>,
   *   check_constraints: array<string, string>
   * }> $tables
   */
  private function exportAsCompact(array $tables, bool $includeGlossary = true): string
  {
    $result = [];

    // Add glossary as first property by default - updated to include cascade abbreviations
    if ($includeGlossary) {
      $result["_g"] =
        "Types:vc=varchar,big=bigint,int=integer,tiny=tinyint,small=smallint,med=mediumint,dec=decimal,dt=datetime,ts=timestamp,bool=boolean|Mods:u=unsigned,?=nullable,:P=primary,:U=unique,:I=index,+AI=auto_increment,+SG=stored_generated,+VG=virtual_generated,+DG=default_generated,+CT=current_timestamp,+OU=on_update_current_timestamp,=X=default|Keys:c=columns,pk=primary_key,i=indexes(!=unique),fk=foreign_keys,ck=check_constraints|Cascades:C=CASCADE,R=RESTRICT,S=SET NULL,N=NO ACTION,D=SET DEFAULT";
    }

    foreach ($tables as $tableName => $schema) {
      $t = ["c" => []];

      // Compact columns
      foreach ($schema["columns"] as $colName => $col) {
        $c = $col["type"];
        if ($col["null"]) {
          $c .= "?";
        }
        if ($col["key"] !== "") {
          $c .= ":" . $col["key"];
        }
        if ($col["extra"] !== "") {
          $c .= "+" . $col["extra"];
        }
        if ($col["default"] !== null) {
          $c .= "=" . $this->compactValue($col["default"]);
        }
        $t["c"][$colName] = $c;
      }

      // Compact indexes
      foreach ($schema["indexes"] as $idxName => $idx) {
        if ($idxName === "PRIMARY") {
          $t["pk"] = $idx["cols"];
        } else {
          if (!isset($t["i"])) {
            $t["i"] = [];
          }
          $t["i"][$idxName] = ($idx["unique"] ? "!" : "") . implode(",", $idx["cols"]);
        }
      }

      // Compact foreign keys with cascade info
      foreach ($schema["foreign_keys"] as $fkName => $fk) {
        if (!isset($t["fk"])) {
          $t["fk"] = [];
        }

        // Build compact FK representation
        $fkCompact = $fk["ref"];

        // Add cascade rules if they're not the default (RESTRICT)
        $cascades = [];
        if ($fk["on_delete"] !== "RESTRICT") {
          $cascades[] = "d" . $this->abbreviateCascadeRule($fk["on_delete"]);
        }
        if ($fk["on_update"] !== "RESTRICT") {
          $cascades[] = "u" . $this->abbreviateCascadeRule($fk["on_update"]);
        }

        if (count($cascades) > 0) {
          $fkCompact .= "~" . implode(",", $cascades);
        }

        $t["fk"][$fk["col"]] = $fkCompact;
      }

      // Compact check constraints
      if (count($schema["check_constraints"]) > 0) {
        $t["ck"] = $schema["check_constraints"];
      }

      $result[$tableName] = $t;
    }

    $json = json_encode($result);

    return $json !== false ? $json : "{}";
  }

  /**
   * Export as full JSON (original format for comparison)
   *
   * @param array<string, array{
   *   columns: array<string, array{type: string, null: bool, key: string, extra: string, default: mixed}>,
   *   indexes: array<string, array{unique: bool, cols: array<int, string>}>,
   *   foreign_keys: array<string, array{col: string, ref: string, on_delete: string, on_update: string}>,
   *   check_constraints: array<string, string>
   * }> $tables
   */
  private function exportAsFull(array $tables): string
  {
    $json = json_encode(["tables" => $tables], JSON_PRETTY_PRINT);

    return $json !== false ? $json : "{}";
  }

  /**
   * Abbreviate cascade rules for compact format
   */
  private function abbreviateCascadeRule(string $rule): string
  {
    return match ($rule) {
      "CASCADE" => "C",
      "RESTRICT" => "R",
      "SET NULL" => "S",
      "NO ACTION" => "N",
      "SET DEFAULT" => "D",
      default => $rule,
    };
  }

  /**
   * Abbreviate MySQL types for compactness
   */
  private function abbreviateType(string $type): string
  {
    $abbrev = [
      "varchar" => "vc",
      "integer" => "int",
      "bigint" => "big",
      "tinyint" => "tiny",
      "smallint" => "small",
      "mediumint" => "med",
      "mediumtext" => "medtext",
      "decimal" => "dec",
      "datetime" => "dt",
      "timestamp" => "ts",
      "boolean" => "bool",
      "unsigned" => "u",
    ];

    $result = $type;
    foreach ($abbrev as $long => $short) {
      $result = str_replace($long, $short, $result);
    }

    return $result;
  }

  /**
   * Abbreviate key types - I for Index is clearer than M for MUL
   */
  private function abbreviateKey(string $key): string
  {
    return match ($key) {
      "PRI" => "P",
      "UNI" => "U",
      "MUL" => "I", // Index is clearer than M (MUL)
      default => "",
    };
  }

  /**
   * Abbreviate extra column info
   */
  private function abbreviateExtra(string $extra): string
  {
    $abbrev = [
      "auto_increment" => "AI",
      "on update current_timestamp" => "OU",
      "current_timestamp" => "CT",
      "DEFAULT_GENERATED" => "DG",
      "STORED GENERATED" => "SG",
      "VIRTUAL GENERATED" => "VG",
    ];

    return $abbrev[$extra] ?? $extra;
  }

  /**
   * Compact default values
   */
  private function compactValue(mixed $value): string
  {
    if ($value === null) {
      return "null";
    }
    if ($value === "CURRENT_TIMESTAMP") {
      return "NOW";
    }
    if (is_numeric($value)) {
      return (string) $value;
    }
    if (is_string($value)) {
      return '"' . addslashes($value) . '"';
    }
    if (is_bool($value)) {
      return $value ? "true" : "false";
    }
    if (is_array($value) || is_object($value)) {
      $json = json_encode($value);

      return $json !== false ? $json : '""';
    }

    return '""';
  }
}
