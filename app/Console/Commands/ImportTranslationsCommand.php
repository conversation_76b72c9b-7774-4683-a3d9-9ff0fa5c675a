<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use RuntimeException;
use <PERSON>tie\TranslationLoader\LanguageLine;

final class ImportTranslationsCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = "app:translations-import {--force : Overwrite existing translations}";

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = "Import translations from language files to database";

  /**
   * Execute the console command.
   *
   * @throws RuntimeException
   */
  public function handle(): int
  {
    $locales = Config::get("translatable.locales", []);
    $forceUpdate = $this->option("force");

    if (!is_array($locales)) {
      $this->error("Invalid translatable.locales configuration");

      return Command::FAILURE;
    }

    // Confirm force update
    if ($forceUpdate) {
      $this->warn(
        "⚠️  FORCE MODE: This will overwrite ALL existing translations in the database with values from files!",
      );

      if (!$this->confirm("Are you sure you want to continue?")) {
        $this->info("Import cancelled.");

        return Command::SUCCESS;
      }
    }

    $importCount = 0;
    $updatedCount = 0;
    $langPath = lang_path();

    $this->info(
      $forceUpdate
        ? "Starting translation import (FORCE MODE - will overwrite existing)..."
        : "Starting translation import (preserving existing)...",
    );

    $bar = $this->output->createProgressBar(count($locales));

    // Use a transaction to ensure atomicity
    DB::transaction(function () use (
      $locales,
      $langPath,
      &$importCount,
      &$updatedCount,
      $bar,
      $forceUpdate,
    ) {
      foreach ($locales as $locale) {
        if (!is_string($locale)) {
          continue;
        }

        $bar->advance();

        // Import JSON file for this locale
        $jsonPath = $langPath . "/" . $locale . ".json";

        if (!File::exists($jsonPath)) {
          continue;
        }

        $jsonContent = File::get($jsonPath);
        $translations = json_decode($jsonContent, true);

        if (!is_array($translations)) {
          continue;
        }

        // Import JSON translations (group = * for JSON files)
        foreach ($translations as $key => $value) {
          if (!is_string($key) || !is_string($value)) {
            continue;
          }

          // Use lockForUpdate to prevent concurrent modifications
          $languageLine = LanguageLine::query()
            ->where("group", "*")
            ->where("key", $key)
            ->lockForUpdate()
            ->first();

          if ($languageLine === null) {
            // Create new translation key
            $languageLine = new LanguageLine();
            $languageLine->group = "*";
            $languageLine->key = $key;
            $languageLine->text = [$locale => $value];
            $languageLine->save();
            $importCount++;
          } else {
            // For existing keys
            $text = $languageLine->text ?? [];

            if ($forceUpdate) {
              // Force mode: always update
              $text[$locale] = $value;
              $languageLine->text = $text;
              $languageLine->save();
              $updatedCount++;
            } elseif (!isset($text[$locale])) {
              // Normal mode: only add if locale doesn't exist
              $text[$locale] = $value;
              $languageLine->text = $text;
              $languageLine->save();
              $updatedCount++;
            }
            // else: Normal mode and locale exists - skip update
          }
        }
      }
    });

    $bar->finish();
    $this->newLine(2);

    $this->info("✓ Import complete!");
    $this->info("  - {$importCount} new translation keys added");

    if ($forceUpdate) {
      $this->info("  - {$updatedCount} translations overwritten (force mode)");
    } else {
      $this->info("  - {$updatedCount} missing locales filled in for existing keys");
    }

    return Command::SUCCESS;
  }
}
