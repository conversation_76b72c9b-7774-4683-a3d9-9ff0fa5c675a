<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Helpers\Assert;
use App\Models\User;
use Error;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Spatie\Permission\Models\Role;

use function Laravel\Prompts\confirm;
use function Laravel\Prompts\error;
use function Laravel\Prompts\info;
use function Laravel\Prompts\search;
use function Laravel\Prompts\select;
use function Laravel\Prompts\text;
use function Laravel\Prompts\warning;

final class ManageRolesCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'app:manage-roles 
                            {user? : The user ID or email}
                            {--guard=web : The guard name}';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = "Manage user roles - assign or remove roles interactively";

  /**
   * Execute the console command.
   *
   * @throws Exception
   * @throws Error
   */
  public function handle(): int
  {
    $userIdentifier = $this->argument("user");
    $guard = $this->option("guard");

    // If user not provided, ask interactively
    if ($userIdentifier === null) {
      $user = $this->askForUser();
      if ($user === null) {
        return Command::FAILURE;
      }
    } else {
      // Find user by ID or email
      $user = is_numeric($userIdentifier)
        ? User::find($userIdentifier)
        : User::where("email", $userIdentifier)->first();

      if ($user === null) {
        error("User not found: {$userIdentifier}");

        return Command::FAILURE;
      }
    }

    // Show current user info
    info("Selected user: {$user->name} ({$user->email}) - ID: {$user->id}");

    // Show current roles
    $currentRoles = $user->getRoleNames()->toArray();

    if (count($currentRoles) > 0) {
      info("Current roles: " . implode(", ", $currentRoles));
    } else {
      warning("User has no roles assigned.");
    }

    // Ask what action to perform
    $action = $this->askForAction($currentRoles);
    if ($action === null) {
      return Command::FAILURE;
    }

    // Handle the action
    if ($action === "assign") {
      return $this->assignRole($user, (string) $guard);
    }

    return $this->removeRole($user, $currentRoles);
  }

  /**
   * Ask for user interactively using search.
   */
  private function askForUser(): ?User
  {
    try {
      $userId = search(
        label: "Search for a user by name or email:",
        placeholder: "Start typing to search...",
        options: function (string $value): array {
          if ($value === "") {
            return [];
          }

          $users = User::where("name", "like", "%{$value}%")
            ->orWhere("email", "like", "%{$value}%")
            ->limit(10)
            ->get()
            ->mapWithKeys(
              fn($user) => [
                $user->id => "{$user->name} ({$user->email})",
              ],
            )
            ->toArray();

          Assert::stringArray($users);

          return $users;
        },
        hint: "Type at least 1 character to search",
      );

      if (is_string($userId) && $userId === "") {
        error("No user selected.");

        return null;
      }

      return User::find($userId);
    } catch (InvalidArgumentException $e) {
      error("No user selected.");

      return null;
    }
  }

  /**
   * Ask what action to perform.
   *
   * @param  array<mixed>  $currentRoles
   */
  private function askForAction(array $currentRoles): ?string
  {
    $options = ["assign" => "→ Assign a new role"];

    if (count($currentRoles) > 0) {
      $options["remove"] = "← Remove an existing role";
    }

    $options["cancel"] = "✕ Cancel";

    $choice = select(label: "What would you like to do?", options: $options, default: "assign");

    return $choice === "cancel" ? null : (string) $choice;
  }

  /**
   * Assign a role to the user.
   *
   * @throws Exception
   * @throws Error
   */
  private function assignRole(User $user, string $guard): int
  {
    // Get all available roles
    $existingRoles = Role::where("guard_name", $guard)->pluck("name")->toArray();

    if (count($existingRoles) === 0) {
      $roleName = text(
        label: "No roles exist. Enter name for new role:",
        placeholder: "e.g., admin, editor, user",
        required: true,
        validate: fn(string $value) => match (true) {
          mb_strlen($value) < 2 => "Role name must be at least 2 characters.",
          str_contains($value, " ") => "Role name cannot contain spaces.",
          default => null,
        },
      );
    } else {
      $options = [];
      foreach ($existingRoles as $role) {
        Assert::string($role);
        $options[$role] = $role;
      }
      $options["__new__"] = "→ Create new role";

      $choice = select(
        label: "Select a role to assign:",
        options: $options,
        hint: "Choose a role for this user",
      );

      if ($choice === "__new__") {
        $roleName = text(
          label: "Enter name for new role:",
          placeholder: "e.g., admin, editor, user",
          required: true,
          validate: fn(string $value) => match (true) {
            mb_strlen($value) < 2 => "Role name must be at least 2 characters.",
            str_contains($value, " ") => "Role name cannot contain spaces.",
            default => null,
          },
        );
      } else {
        $roleName = $choice;
      }
    }

    try {
      DB::beginTransaction();

      // Check if role exists
      $role = Role::where("name", $roleName)->where("guard_name", $guard)->first();

      if ($role === null) {
        $role = Role::create(["name" => $roleName, "guard_name" => $guard]);
        info("Role '{$roleName}' created successfully.");
      }

      $user->assignRole($roleName);

      DB::commit();

      info("Role '{$roleName}' assigned to user: {$user->email}");

      return Command::SUCCESS;
    } catch (Exception $e) {
      DB::rollBack();
      error("Failed to assign role: {$e->getMessage()}");

      return Command::FAILURE;
    }
  }

  /**
   * Remove a role from the user.
   *
   * @param  array<mixed>  $currentRoles
   *
   * @throws Exception
   * @throws Error
   */
  private function removeRole(User $user, array $currentRoles): int
  {
    if (count($currentRoles) === 0) {
      error("User has no roles to remove.");

      return Command::FAILURE;
    }

    // Ensure we have a proper string array for the select options
    $roleOptions = [];
    foreach ($currentRoles as $role) {
      Assert::string($role);
      $roleOptions[$role] = $role;
    }

    $roleToRemove = select(
      label: "Select role to remove:",
      options: $roleOptions,
      hint: "Select the role you want to remove from this user",
    );

    if (
      !confirm("Are you sure you want to remove the '{$roleToRemove}' role from {$user->name}?")
    ) {
      warning("Operation cancelled.");

      return Command::SUCCESS;
    }

    try {
      DB::beginTransaction();

      $user->removeRole($roleToRemove);

      DB::commit();

      info("Role '{$roleToRemove}' removed from user: {$user->email}");

      return Command::SUCCESS;
    } catch (Exception $e) {
      DB::rollBack();
      error("Failed to remove role: {$e->getMessage()}");

      return Command::FAILURE;
    }
  }
}
