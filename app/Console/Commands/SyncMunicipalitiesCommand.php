<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\MunicipalityService;
use Illuminate\Console\Command;
use RuntimeException;

final class SyncMunicipalitiesCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = "app:sync-municipalities";

  /**
   * The console command description.
   */
  protected $description = "Command description";

  /**
   * Execute the console command.
   */
  public function handle(MunicipalityService $service): int
  {
    $this->info("Starting municipality sync...");

    try {
      $service->syncFromApi();
      $this->info("Sync completed successfully");

      return self::SUCCESS;
    } catch (RuntimeException $e) {
      $this->error($e->getMessage());

      return self::FAILURE;
    }
  }
}
