<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use RuntimeException;
use Spatie\TranslationLoader\LanguageLine;

final class ExportTranslationsCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = "app:translations-export";

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = "Export database translations back to language files";

  /**
   * Execute the console command.
   *
   * @throws RuntimeException
   */
  public function handle(): int
  {
    $locales = Config::get("translatable.locales", []);

    if (!is_array($locales)) {
      $this->error("Invalid translatable.locales configuration");

      return Command::FAILURE;
    }

    $langPath = lang_path();

    $this->info("Exporting translations from database to files...");

    foreach ($locales as $locale) {
      if (!is_string($locale)) {
        continue;
      }
      $this->exportLocale($locale, $langPath);
    }

    $this->info("✓ Export complete!");

    return Command::SUCCESS;
  }

  /**
   * Export translations for a specific locale.
   *
   * @throws RuntimeException
   */
  protected function exportLocale(string $locale, string $langPath): void
  {
    // Get all JSON translations from database (group = *)
    $translations = LanguageLine::where("group", "*")
      ->orderBy("key")
      ->get()
      ->mapWithKeys(function ($line) use ($locale) {
        $text = $line->text;
        $translation = $text[$locale] ?? null;

        // Skip if this locale doesn't have a translation
        if ($translation === null || $translation === "") {
          return [];
        }

        return [$line->key => $translation];
      })
      ->filter()
      ->toArray();

    if (count($translations) === 0) {
      $this->warn("No translations found for locale: {$locale}");

      return;
    }

    // Write to JSON file
    $jsonPath = $langPath . "/" . $locale . ".json";

    // Build JSON manually with 2-space indentation
    $json = "{\n";
    $items = [];
    foreach ($translations as $key => $value) {
      $items[] =
        "  " .
        json_encode($key) .
        ": " .
        json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    $json .= implode(",\n", $items);
    $json .= "\n}";

    File::put($jsonPath, $json);

    $this->info("Exported {$locale}: " . count($translations) . " translations");
  }
}
