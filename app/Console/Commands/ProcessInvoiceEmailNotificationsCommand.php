<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Mail\InvoiceDetailsDigest;
use App\Models\Company;
use App\Models\InvoiceNotificationLog;
use App\Settings\InvoiceSettings;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use InvalidArgumentException;
use JsonException;
use RuntimeException;

final class ProcessInvoiceEmailNotificationsCommand extends Command
{
  protected $signature = "app:process-invoice-email-notifications";

  protected $description = "Process and send invoice detail update notifications";

  /**
   * Execute the console command.
   *
   * @return Command::SUCCESS|Command::FAILURE
   *
   * @throws InvalidArgumentException
   * @throws RuntimeException
   */
  public function handle(): int
  {
    $lock = Cache::lock("invoice-notifications", 300);

    if ($lock->get() === false) {
      $this->info("Another instance is already running.");

      return Command::FAILURE;
    }

    try {
      $settings = app(InvoiceSettings::class);
      $recipientEmail = $settings->notification_email;

      if ($recipientEmail === "") {
        $this->error("No invoice notification email configured in settings.");

        return Command::FAILURE;
      }

      $this->info("Processing invoice updates for notifications to: {$recipientEmail}");

      $notificationLogs = $this->loadNotificationLogs();

      $eligibleCompanies = Company::where(function ($query) {
        $query
          ->where("applying_for_scope1_2_mark", true)
          ->orWhere("applying_for_scope1_3_mark", true);
      })
        ->whereNotNull("e_invoice_address")
        ->where("e_invoice_address", "!=", "")
        ->whereNotNull("e_invoice_operator")
        ->where("e_invoice_operator", "!=", "")
        ->whereNotNull("e_invoice_reference")
        ->where("e_invoice_reference", "!=", "")
        ->whereNotNull("e_invoice_contact_name")
        ->where("e_invoice_contact_name", "!=", "")
        ->whereNotNull("e_invoice_contact_email")
        ->where("e_invoice_contact_email", "!=", "");

      $processed = $eligibleCompanies->count();

      // Analyze all companies and extract results functionally
      $results = $eligibleCompanies
        ->lazy(1000)
        ->map(
          fn(Company $company) => [
            "company" => $company,
            "analysis" => $this->analyzeCompany($company, $settings, $notificationLogs),
          ],
        )
        ->collect();

      $companiesWithUpdates = $this->extractCompaniesToNotify($results);
      $pendingUpdates = $this->extractPendingUpdates($results);

      if ($companiesWithUpdates->isEmpty()) {
        // No notifications to send, but we should still update pending states
        if ($pendingUpdates->isNotEmpty()) {
          DB::transaction(function () use ($pendingUpdates) {
            $pendingUpdates->each(fn($update) => $this->applyPendingUpdate($update));
          });
        }

        $this->info("Processed {$processed} companies, no updates to notify");

        return Command::SUCCESS;
      }

      // Send notifications and update all states atomically
      DB::transaction(function () use ($recipientEmail, $companiesWithUpdates, $pendingUpdates) {
        // Update states for companies being notified
        $companiesWithUpdates->each(fn(Company $company) => $this->markCompanyAsEmailed($company));

        // Update pending states for companies not being notified
        $pendingUpdates->each(fn($update) => $this->applyPendingUpdate($update));

        // Queue the email
        $result = Mail::to($recipientEmail)
          ->locale(App::getLocale())
          ->queue(new InvoiceDetailsDigest($companiesWithUpdates));

        $isValidResult = match (Config::string("queue.default")) {
          "sync" => $result === 0,
          "database" => is_int($result) && $result > 0,
          default => throw new RuntimeException(
            "Unsupported queue driver: " . Config::string("queue.default"),
          ),
        };

        if (!$isValidResult) {
          throw new RuntimeException("Failed to queue notification email");
        }
      });

      $this->info(
        "Processed {$processed} companies, sent digest email with {$companiesWithUpdates->count()} updates",
      );

      return Command::SUCCESS;
    } finally {
      $lock->release();
    }
  }

  /**
   * @return Collection<int, InvoiceNotificationLog>
   */
  private function loadNotificationLogs(): Collection
  {
    $companyIds = Company::where(function ($query) {
      $query
        ->where("applying_for_scope1_2_mark", true)
        ->orWhere("applying_for_scope1_3_mark", true);
    })
      ->whereNotNull("e_invoice_address")
      ->where("e_invoice_address", "!=", "")
      ->whereNotNull("e_invoice_operator")
      ->where("e_invoice_operator", "!=", "")
      ->whereNotNull("e_invoice_reference")
      ->where("e_invoice_reference", "!=", "")
      ->whereNotNull("e_invoice_contact_name")
      ->where("e_invoice_contact_name", "!=", "")
      ->whereNotNull("e_invoice_contact_email")
      ->where("e_invoice_contact_email", "!=", "")
      ->pluck("id");

    return InvoiceNotificationLog::whereIn("company_id", $companyIds)->get()->keyBy("company_id");
  }

  /**
   * @param  Collection<int, array{company: Company, analysis: array{notify: bool, pendingUpdate: array{id: int, hash: string, marked_at: \Carbon\CarbonImmutable, create: bool}|null}}>  $results
   * @return Collection<int, Company>
   */
  private function extractCompaniesToNotify(Collection $results): Collection
  {
    return $results
      ->filter(fn($result) => $result["analysis"]["notify"])
      ->map(fn($result) => $result["company"])
      ->values();
  }

  /**
   * @param  Collection<int, array{company: Company, analysis: array{notify: bool, pendingUpdate: array{id: int, hash: string, marked_at: \Carbon\CarbonImmutable, create: bool}|null}}>  $results
   * @return Collection<int, array{id: int, hash: string, marked_at: \Carbon\CarbonImmutable, create: bool}>
   */
  private function extractPendingUpdates(Collection $results): Collection
  {
    return $results->map(fn($result) => $result["analysis"]["pendingUpdate"])->filter()->values();
  }

  /**
   * Analyze a company and determine if it should be notified, without modifying state
   *
   * @param  Collection<int, InvoiceNotificationLog>  $notificationLogs
   * @return array{notify: bool, pendingUpdate: array{id: int, hash: string, marked_at: \Carbon\CarbonImmutable, create: bool}|null}
   *
   * @throws JsonException
   */
  private function analyzeCompany(
    Company $company,
    InvoiceSettings $settings,
    Collection $notificationLogs,
  ): array {
    // Verify company still wants notifications (either scope)
    if (!$company->applying_for_scope1_2_mark && !$company->applying_for_scope1_3_mark) {
      return ["notify" => false, "pendingUpdate" => null];
    }

    $currentHash = $this->generateInvoiceHash($company);
    $tracking = $notificationLogs->get($company->id);

    // If no tracking exists yet, create the pending update data
    if ($tracking === null) {
      return [
        "notify" => false,
        "pendingUpdate" => [
          "id" => $company->id,
          "hash" => $currentHash,
          "marked_at" => now()->toImmutable(),
          "create" => true,
        ],
      ];
    }

    // Check if we should include in digest for a stable pending change
    if ($this->hasStablePendingChange($tracking, $currentHash, $settings)) {
      return ["notify" => true, "pendingUpdate" => null];
    }

    // Determine what pending update is needed
    $pendingUpdate = null;

    if ($currentHash !== $tracking->last_emailed_hash) {
      if ($tracking->pending_hash === null) {
        // Need to mark as pending
        $pendingUpdate = [
          "id" => $tracking->id,
          "hash" => $currentHash,
          "marked_at" => now()->toImmutable(),
          "create" => false,
        ];
      } elseif (
        $tracking->pending_hash !== $currentHash &&
        $tracking->pending_marked_at !== null &&
        $tracking->pending_marked_at->lt(now()->subHours($settings->stability_threshold_hours))
      ) {
        // Need to reset to new pending
        $pendingUpdate = [
          "id" => $tracking->id,
          "hash" => $currentHash,
          "marked_at" => now()->toImmutable(),
          "create" => false,
        ];
      }
    }

    return ["notify" => false, "pendingUpdate" => $pendingUpdate];
  }

  /**
   * Apply a pending update to the database
   *
   * @param  array{id: int, hash: string, marked_at: \Carbon\CarbonImmutable, create: bool}  $update
   */
  private function applyPendingUpdate(array $update): void
  {
    if ($update["create"]) {
      $tracking = new InvoiceNotificationLog();
      $tracking->company_id = $update["id"];
      $tracking->pending_hash = $update["hash"];
      $tracking->pending_marked_at = $update["marked_at"];
      $tracking->save();
    } else {
      InvoiceNotificationLog::where("id", $update["id"])->update([
        "pending_hash" => $update["hash"],
        "pending_marked_at" => $update["marked_at"]->toDateTimeString(),
      ]);
    }
  }

  /**
   * @throws JsonException
   */
  private function markCompanyAsEmailed(Company $company): void
  {
    $currentHash = $this->generateInvoiceHash($company);

    $tracking = InvoiceNotificationLog::where("company_id", $company->id)->first();

    if ($tracking !== null) {
      $tracking->last_emailed_hash = $currentHash;
      $tracking->last_emailed_at = now()->toImmutable();
      $tracking->pending_hash = null;
      $tracking->pending_marked_at = null;
      $tracking->save();
    }
  }

  private function hasStablePendingChange(
    InvoiceNotificationLog $tracking,
    string $currentHash,
    InvoiceSettings $settings,
  ): bool {
    return $tracking->pending_hash !== null &&
      $tracking->pending_marked_at !== null &&
      $tracking->pending_marked_at->lt(now()->subHours($settings->stability_threshold_hours)) &&
      $currentHash === $tracking->pending_hash;
  }

  /**
   * @throws JsonException
   */
  private function generateInvoiceHash(Company $company): string
  {
    $data = [
      "applying_for_scope1_2_mark" => $company->applying_for_scope1_2_mark,
      "applying_for_scope1_3_mark" => $company->applying_for_scope1_3_mark,
      "e_invoice_address" => mb_trim($company->e_invoice_address ?? ""),
      "e_invoice_operator" => mb_trim($company->e_invoice_operator ?? ""),
      "e_invoice_reference" => mb_trim($company->e_invoice_reference ?? ""),
      "e_invoice_contact_name" => mb_trim($company->e_invoice_contact_name ?? ""),
      "e_invoice_contact_email" => mb_trim($company->e_invoice_contact_email ?? ""),
      "e_invoice_additional_info" => mb_trim($company->e_invoice_additional_info ?? ""),
    ];

    return hash("sha256", json_encode($data, JSON_THROW_ON_ERROR));
  }
}
