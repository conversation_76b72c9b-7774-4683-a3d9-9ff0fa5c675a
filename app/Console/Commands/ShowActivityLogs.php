<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\ActivityLog;
use Illuminate\Console\Command;

class ShowActivityLogs extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'activity:show {--limit=20 : Number of logs to show} {--event= : Filter by event type}';

    /**
     * The console command description.
     */
    protected $description = 'Show recent activity logs';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $query = ActivityLog::with(['user', 'company'])
            ->orderBy('occurred_at', 'desc');

        if ($event = $this->option('event')) {
            $query->where('event', $event);
        }

        $logs = $query->limit((int) $this->option('limit'))->get();

        if ($logs->isEmpty()) {
            $this->info('No activity logs found.');
            return 0;
        }

        $headers = ['Time', 'Event', 'User', 'Company', 'Description', 'IP'];
        $rows = [];

        foreach ($logs as $log) {
            $rows[] = [
                $log->occurred_at->format('Y-m-d H:i:s'),
                $log->event->value,
                $log->user?->email ?? 'N/A',
                $log->company?->name ?? 'N/A',
                $log->description,
                $log->ip_address ?? 'N/A',
            ];
        }

        $this->table($headers, $rows);

        return 0;
    }
}
