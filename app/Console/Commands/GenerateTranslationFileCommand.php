<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Filesystem\Filesystem;
use RuntimeException;
use Symfony\Component\Finder\Exception\DirectoryNotFoundException;
use Symfony\Component\Finder\Finder;

/**
 * Generates translation files by scanning the project for all strings in __() functions.
 */
final class GenerateTranslationFileCommand extends Command
{
  /**
   * The regular expression pattern to find all translation strings.
   */
  private const TRANSLATION_PATTERN = '/\b__\(\s*[\'"]([^\'"]+)[\'"]\s*[,\)]/';

  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'translations:generate 
                            {--locale=en : The locale to generate translations for}
                            {--source-locale=sv : The source locale of your application}
                            {--output=lang : The output directory for translation files}
                            {--format=json : The output format (json or php)}
                            {--merge : Merge with existing translation file}
                            {--directories=* : The directories to scan (default: app,resources)}';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = "Generate translation files by scanning for all strings in __() functions";

  /**
   * The filesystem instance.
   */
  private Filesystem $filesystem;

  /**
   * Create a new command instance.
   */
  public function __construct(Filesystem $filesystem)
  {
    parent::__construct();
    $this->filesystem = $filesystem;
  }

  /**
   * Execute the console command.
   *
   * @throws DirectoryNotFoundException
   * @throws RuntimeException
   * @throws FileNotFoundException
   */
  public function handle(): int
  {
    $locale = (string) $this->option("locale");
    $sourceLocale = (string) $this->option("source-locale");
    $format = (string) $this->option("format");
    $outputDir = (string) $this->option("output");
    $shouldMerge = $this->option("merge");
    $directories = $this->option("directories");

    // Default directories if none provided
    if (count($directories) === 0) {
      $directories = ["app", "resources"];
    }

    // Filter out null values and cast to strings
    $directories = array_values(
      array_filter(
        array_map(fn($dir) => is_string($dir) ? $dir : null, $directories),
        fn($dir): bool => $dir !== null,
      ),
    );

    $this->info("Scanning directories: " . implode(", ", $directories));

    $translationStrings = $this->scanDirectories($directories);

    if (count($translationStrings) === 0) {
      $this->warn("No translation strings found.");

      return Command::SUCCESS;
    }

    $this->info(sprintf("Found %d unique translation strings.", count($translationStrings)));

    // For the source locale, use the string as both key and value
    if ($locale === $sourceLocale || !$this->hasEmptyValues($translationStrings)) {
      $keys = array_keys($translationStrings);
      $result = array_combine($keys, $keys);
      $translationStrings = $result;
    }

    $this->generateTranslationFile($translationStrings, $locale, $format, $outputDir, $shouldMerge);

    return Command::SUCCESS;
  }

  /**
   * Check if the translation array has empty values.
   *
   * @param  array<string, string>  $translations
   */
  private function hasEmptyValues(array $translations): bool
  {
    foreach ($translations as $value) {
      if ($value !== "") {
        return false;
      }
    }

    return true;
  }

  /**
   * Scan the specified directories for translation strings.
   *
   * @param  array<int, string>  $directories
   * @return array<string, string>
   *
   * @throws DirectoryNotFoundException
   * @throws RuntimeException
   */
  private function scanDirectories(array $directories): array
  {
    $translationStrings = [];

    $finder = new Finder();
    $finder
      ->in(array_map(fn(string $dir): string => base_path($dir), $directories))
      ->name("*.{php,blade.php}") // Also scan blade templates
      ->files();

    foreach ($finder as $file) {
      $content = $file->getContents();

      // Find all __() calls
      $matches = [];
      $result = preg_match_all(self::TRANSLATION_PATTERN, $content, $matches);
      if ($result !== false && $result > 0) {
        foreach ($matches[1] as $string) {
          $translationStrings[$string] = "";
        }
      }
    }

    ksort($translationStrings);

    return $translationStrings;
  }

  /**
   * Generate the translation file.
   *
   * @param  array<string, string>  $translationStrings
   *
   * @throws RuntimeException
   * @throws FileNotFoundException
   */
  private function generateTranslationFile(
    array $translationStrings,
    string $locale,
    string $format,
    string $outputDir,
    bool $shouldMerge,
  ): void {
    $outputPath = base_path($outputDir);

    if (!$this->filesystem->isDirectory($outputPath)) {
      $this->filesystem->makeDirectory($outputPath, 0755, true);
    }

    // Handle merging with existing translations
    if ($shouldMerge) {
      $existingTranslations = $this->loadExistingTranslations($outputPath, $locale, $format);
      $translationStrings = array_merge($existingTranslations, $translationStrings);
    }

    if ($format === "json") {
      $outputFile = "{$outputPath}/{$locale}.json";
      $content = json_encode($translationStrings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

      if ($content === false) {
        throw new RuntimeException("Failed to encode translation strings to JSON");
      }
    } else {
      $outputFile = "{$outputPath}/{$locale}.php";
      $arrayContent = var_export($translationStrings, true);
      $content = "<?php\n\ndeclare(strict_types=1);\n\nreturn {$arrayContent};\n";
    }

    $this->filesystem->put($outputFile, $content);
    $this->info("Translation file generated: {$outputFile}");
  }

  /**
   * Load existing translations from file.
   *
   * @return array<string, string>
   *
   * @throws FileNotFoundException
   */
  private function loadExistingTranslations(
    string $outputPath,
    string $locale,
    string $format,
  ): array {
    $filePath = $format === "json" ? "{$outputPath}/{$locale}.json" : "{$outputPath}/{$locale}.php";

    if (!$this->filesystem->exists($filePath)) {
      return [];
    }

    if ($format === "json") {
      $content = $this->filesystem->get($filePath);
      $translations = json_decode($content, true);

      if (json_last_error() !== JSON_ERROR_NONE || !is_array($translations)) {
        $this->warn("Could not parse existing JSON translation file: {$filePath}");

        return [];
      }

      // Ensure all keys and values are strings
      $stringTranslations = [];
      foreach ($translations as $key => $value) {
        if (is_string($key) && is_string($value)) {
          $stringTranslations[$key] = $value;
        }
      }

      return $stringTranslations;
    }
    $translations = require $filePath;
    if (!is_array($translations)) {
      return [];
    }

    // Ensure all keys and values are strings
    $stringTranslations = [];
    foreach ($translations as $key => $value) {
      if (is_string($key) && is_string($value)) {
        $stringTranslations[$key] = $value;
      }
    }

    return $stringTranslations;
  }
}
