<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\IndustryClassificationService;
use Illuminate\Console\Command;
use RuntimeException;

final class SyncIndustryClassificationsCommand extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = "app:sync-industry-classifications";

  /**
   * The console command description.
   */
  protected $description = "Sync industry classifications from external API";

  public function handle(IndustryClassificationService $service): int
  {
    $this->info("Starting industry classification sync...");

    try {
      $service->syncFromApi();
      $this->info("Sync completed successfully");

      return self::SUCCESS;
    } catch (RuntimeException $e) {
      $this->error($e->getMessage());

      return self::FAILURE;
    }
  }
}
