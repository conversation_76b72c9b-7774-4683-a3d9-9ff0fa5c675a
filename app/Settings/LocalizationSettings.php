<?php

declare(strict_types=1);

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

final class LocalizationSettings extends Settings
{
  /**
   * Available locales for the application.
   *
   * @var array<string, string>
   */
  public array $available_locales;

  /**
   * Default application locale.
   */
  public string $default_locale;

  /**
   * Get the settings group.
   */
  public static function group(): string
  {
    return "localization";
  }
}
