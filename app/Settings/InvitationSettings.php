<?php

declare(strict_types=1);

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

final class InvitationSettings extends Settings
{
  public int $tokenExpirationMinutes;

  public int $maxIpAttempts;

  public int $ipTimeWindowMinutes;

  public int $maxEmailAttempts;

  public int $emailTimeWindowMinutes;

  public int $maxVerificationAttempts;

  public int $verificationTimeWindowMinutes;

  public static function group(): string
  {
    return "invitation";
  }
}
