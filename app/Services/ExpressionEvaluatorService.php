<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\CalculationDefinition;
use LogicException;
use Symfony\Component\ExpressionLanguage\SyntaxError;

/**
 * Service for evaluating predicate expressions against calculation definitions
 * Now uses the specialized PredicateExpressionService
 */
final class ExpressionEvaluatorService
{
  private PredicateExpressionService $predicateExpressionService;

  public function __construct()
  {
    $this->predicateExpressionService = new PredicateExpressionService();
  }

  /**
   * Evaluate a predicate expression against a calculation definition
   *
   * @return bool The evaluation result
   *
   * @throws SyntaxError If expression syntax is invalid
   * @throws LogicException
   */
  public function evaluateDefinition(string $expression, CalculationDefinition $definition): bool
  {
    return $this->predicateExpressionService->evaluateDefinition($expression, $definition);
  }

  /**
   * Validate an expression without evaluating it
   *
   * @param  string  $expression  The expression to validate
   * @return bool True if valid, false otherwise
   *
   * @throws LogicException
   */
  public function validateExpression(string $expression): bool
  {
    return $this->predicateExpressionService->validateExpression($expression);
  }
}
