<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\CompoundUnit;
use App\Models\Unit;
use App\Models\UnitConversion;
use App\Settings\UnitSettings;
use Brick\Math\BigDecimal;
use Illuminate\Database\Eloquent\Collection;
use RuntimeException;

final class UnitService
{
  /**
   * Convert a value from one unit to another
   *
   * @param  string  $value  The value to convert
   * @param  int  $fromUnitId  The source unit ID
   * @param  int  $toUnitId  The target unit ID
   * @return string|null The converted value, or null if conversion not possible
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws \Brick\Math\Exception\MathException
   */
  public function convertValue(string $value, int $fromUnitId, int $toUnitId): ?string
  {
    // If units are already the same, no conversion needed
    if ($fromUnitId === $toUnitId) {
      return $value;
    }

    // Find conversion path using breadth-first search
    $path = $this->findConversionPath($fromUnitId, $toUnitId);

    if ($path === null || count($path) < 2) {
      return null;
    }

    // Apply all conversions in the path
    $result = BigDecimal::of($value);

    $pathCount = count($path);
    for ($i = 0; $i < $pathCount - 1; $i++) {
      $fromId = $path[$i] ?? null;
      $toId = $path[$i + 1] ?? null;

      if ($fromId === null || $toId === null) {
        return null;
      }

      $conversion = UnitConversion::query()
        ->where("from_unit_id", $fromId)
        ->where("to_unit_id", $toId)
        ->first();

      if ($conversion === null || $conversion->conversion_factor === null) {
        return null;
      }

      $result = $result->multipliedBy(BigDecimal::of($conversion->conversion_factor));
    }

    return (string) $result;
  }

  /**
   * Find a standard unit by symbol, with fallback
   *
   * @param  string  $standardSymbol  The standard unit symbol to find
   * @param  array<int, int>  $fallbackUnitIds  Array of fallback unit IDs to try
   * @return Unit|null The unit, or null if not found
   */
  public function findStandardUnit(string $standardSymbol, array $fallbackUnitIds = []): ?Unit
  {
    $standardUnit = Unit::query()->where("symbol", $standardSymbol)->first();

    // Check that we have a Unit instance
    if (!($standardUnit instanceof Unit)) {
      $standardUnit = null;
    }

    // If found, return it
    if ($standardUnit !== null) {
      return $standardUnit;
    }

    // Try fallback IDs
    foreach ($fallbackUnitIds as $unitId) {
      $unit = Unit::find($unitId);
      if ($unit instanceof Unit) {
        return $unit;
      }
    }

    // No unit found
    return null;
  }

  /**
   * Get all simple units
   *
   * @return Collection<int, Unit>
   */
  public function getAllUnits(): Collection
  {
    return Unit::orderBy("symbol")->get();
  }

  /**
   * Get all compound units with their related units
   *
   * @return Collection<int, CompoundUnit>
   */
  public function getAllCompoundUnits(): Collection
  {
    return CompoundUnit::with(["numeratorUnit", "denominatorUnit"])->get();
  }

  /**
   * Get the default unit from settings
   *
   * @return Unit The default unit
   *
   * @throws RuntimeException if default unit not found
   */
  public function getDefaultUnit(): Unit
  {
    $settings = app(UnitSettings::class);
    $defaultUnitId = $settings->default_unit_id;

    if ($defaultUnitId === null) {
      throw new RuntimeException("Default unit ID not configured in settings.");
    }

    $unit = Unit::find($defaultUnitId);

    if (!$unit instanceof Unit) {
      throw new RuntimeException("Default unit with ID {$defaultUnitId} not found in database.");
    }

    return $unit;
  }

  /**
   * Get the default compound unit from settings
   *
   * @return CompoundUnit The default compound unit
   *
   * @throws RuntimeException if default compound unit not found
   */
  public function getDefaultCompoundUnit(): CompoundUnit
  {
    $settings = app(UnitSettings::class);
    $defaultCompoundUnitId = $settings->default_compound_unit_id;

    if ($defaultCompoundUnitId === null) {
      throw new RuntimeException("Default compound unit ID not configured in settings.");
    }

    $compoundUnit = CompoundUnit::find($defaultCompoundUnitId);

    if (!$compoundUnit instanceof CompoundUnit) {
      throw new RuntimeException(
        "Default compound unit with ID {$defaultCompoundUnitId} not found in database.",
      );
    }

    return $compoundUnit;
  }

  /**
   * Find a conversion path between two units using breadth-first search
   *
   * @param  int  $fromUnitId  The source unit ID
   * @param  int  $toUnitId  The target unit ID
   * @return list<int>|null List of unit IDs representing the path, or null if no path exists
   */
  private function findConversionPath(int $fromUnitId, int $toUnitId): ?array
  {
    // Build adjacency list of all conversions
    $conversions = UnitConversion::query()->whereNotNull("conversion_factor")->get();

    $adjacencyList = [];
    foreach ($conversions as $conversion) {
      if (!isset($adjacencyList[$conversion->from_unit_id])) {
        $adjacencyList[$conversion->from_unit_id] = [];
      }
      $adjacencyList[$conversion->from_unit_id][] = $conversion->to_unit_id;
    }

    // BFS to find shortest path
    $queue = [[$fromUnitId]];
    $visited = [$fromUnitId => true];

    while (count($queue) > 0) {
      $path = array_shift($queue);
      $currentUnit = end($path);

      // Check if we've reached the target
      if ($currentUnit === $toUnitId) {
        return $path;
      }

      // Explore neighbors
      if (isset($adjacencyList[$currentUnit])) {
        foreach ($adjacencyList[$currentUnit] as $neighbor) {
          if (!isset($visited[$neighbor])) {
            $visited[$neighbor] = true;
            $newPath = $path;
            $newPath[] = $neighbor;
            $queue[] = $newPath;
          }
        }
      }
    }

    // No path found
    return null;
  }
}
