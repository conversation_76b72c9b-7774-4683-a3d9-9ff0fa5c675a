<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\MetricDefinition;
use App\Models\MetricValue;
use Brick\Math\BigDecimal;
use Illuminate\Database\Eloquent\Collection;
use RuntimeException;

final class MetricValueService
{
  public function __construct(
    private CompanyContextService $companyContextService,
    private YearService $yearService,
  ) {}

  /**
   * Load metric values for a collection of definitions and all specified years
   *
   * @param  Collection<int, MetricDefinition>  $definitions  Definitions collection
   * @param  list<int>  $years  Years to load values for
   * @return array{values: array<int, array<int, string|null>>, sources: array<int, array<int, string|null>>}
   *
   * @throws RuntimeException
   */
  public function loadValuesForYears(Collection $definitions, array $years): array
  {
    $result = [
      "values" => [],
      "sources" => [],
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return $result;
    }

    $companyId = $company->id;

    // Get Year models for the requested years
    $yearModels = $this->yearService->getYearsByValues($years);

    foreach ($definitions as $definition) {
      $definitionId = $definition->id;

      // Initialize arrays for each definition
      $result["values"][$definitionId] = [];
      $result["sources"][$definitionId] = [];

      // Get all values for this definition across years
      $yearIds = $yearModels->pluck("id")->toArray();

      $valuesQuery = MetricValue::query()
        ->where("metric_definition_id", $definitionId)
        ->where("company_id", $companyId)
        ->whereIn("year_id", $yearIds)
        ->with("year");

      $values = $valuesQuery->get();

      // Create a map of year_id to value
      $valuesByYearId = $values->keyBy("year_id");

      // Fill in values for each year
      foreach ($years as $yearValue) {
        $yearModel = $yearModels->get($yearValue);

        if ($yearModel === null) {
          // Year doesn't exist in database
          $result["values"][$definitionId][$yearValue] = null;
          $result["sources"][$definitionId][$yearValue] = null;

          continue;
        }

        $metricValue = $valuesByYearId->get($yearModel->id);

        if ($metricValue !== null) {
          // Company-specific value exists for this year
          $stringValue =
            $metricValue->value !== null
              ? (string) BigDecimal::of($metricValue->value)->stripTrailingZeros()
              : null;
          $result["values"][$definitionId][$yearValue] = $stringValue;
          $result["sources"][$definitionId][$yearValue] = $metricValue->source ?? null;
        } else {
          // No value at all
          $result["values"][$definitionId][$yearValue] = null;
          $result["sources"][$definitionId][$yearValue] = null;
        }
      }
    }

    return $result;
  }

  /**
   * Save a metric value for a specific definition, company, and year
   *
   * @param  int  $definitionId  The metric definition ID
   * @param  string|null  $value  The value to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws RuntimeException
   */
  public function saveValue(int $definitionId, ?string $value, int $year): array
  {
    $result = [
      "success" => false,
      "message" => "",
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      $result["message"] = __("metrics.errors.company_context_missing");

      return $result;
    }

    $companyId = $company->id;

    // Get the Year model
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      $result["message"] = __("metrics.errors.year_not_available", ["year" => $year]);

      return $result;
    }

    // Handle empty values
    if ($value === null || $value === "") {
      $existingValue = MetricValue::query()
        ->where("metric_definition_id", $definitionId)
        ->where("company_id", $companyId)
        ->where("year_id", $yearModel->id)
        ->first();

      if ($existingValue !== null) {
        $existingValue->value = null;
        $existingValue->save();
      }

      $result["success"] = true;
      $result["message"] = __("metrics.messages.value_deleted");

      return $result;
    }

    // Validate non-empty values
    if (BigDecimal::of($value)->isLessThan(BigDecimal::zero())) {
      $result["message"] = __("validations.value.not_negative");

      return $result;
    }

    // Check if a value for this definition, company, and year already exists
    $existingValue = MetricValue::query()
      ->where("metric_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();

    if ($existingValue !== null) {
      // Update existing value
      $existingValue->value = $value;
      $existingValue->save();
    } else {
      // Create new value
      MetricValue::create([
        "metric_definition_id" => $definitionId,
        "company_id" => $companyId,
        "year_id" => $yearModel->id,
        "value" => $value,
        "source" => "",
      ]);
    }

    $result["success"] = true;
    $result["message"] = __("metrics.messages.value_updated");

    return $result;
  }

  /**
   * Save a source for a specific metric definition, company, and year
   *
   * @param  int  $definitionId  The metric definition ID
   * @param  string  $source  The source to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws RuntimeException
   */
  public function saveSource(int $definitionId, string $source, int $year): array
  {
    $result = [
      "success" => false,
      "message" => "",
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      $result["message"] = __("metrics.errors.company_context_missing");

      return $result;
    }

    $companyId = $company->id;

    // Get the Year model
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      $result["message"] = __("metrics.errors.year_not_available", ["year" => $year]);

      return $result;
    }

    // Check if a value for this definition, company, and year already exists
    $existingValue = MetricValue::query()
      ->where("metric_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();

    if ($existingValue !== null) {
      // Update existing value's source
      $existingValue->source = $source;
      $existingValue->save();
    } else {
      // Create new value with source
      if ($source !== "") {
        MetricValue::create([
          "metric_definition_id" => $definitionId,
          "company_id" => $companyId,
          "year_id" => $yearModel->id,
          "value" => null,
          "source" => $source,
        ]);
      }
    }

    $result["success"] = true;
    $result["message"] = __("metrics.messages.source_updated");

    return $result;
  }
}
