<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\InputMethod;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionYear;
use App\Models\Category;
use App\Models\CompoundUnit;
use App\Models\Grouping;
use App\Models\Scope;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use RuntimeException;

final class CalculationDefinitionService
{
  public function __construct(
    private CompanyContextService $companyContextService,
    private YearService $yearService,
    private UnitService $unitService,
  ) {}

  /**
   * Get template definition IDs for a category
   *
   * @param  int  $categoryId
   * @return array<int>
   */
  public function getTemplateDefinitionIds(int $categoryId): array
  {
    $category = Category::find($categoryId);

    if ($category === null) {
      return [];
    }

    // Get template IDs through the relationship
    $ids = $category->templateDefinitions()->pluck("calculation_definitions.id")->toArray();

    Assert::intArray($ids);

    return $ids;
  }

  /**
   * Create definitions based on category templates
   *
   * @param  int  $categoryId
   * @param  int  $scopeId
   * @param  int  $groupingId
   * @param  int  $year
   * @return int The number of definitions created
   *
   * @throws RuntimeException
   * @throws \Exception
   * @throws \Error
   */
  public function createFromCategoryTemplates(
    int $categoryId,
    int $scopeId,
    int $groupingId,
    int $year,
  ): int {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      throw new RuntimeException(__("Yrityskonteksti puuttuu"));
    }

    $templateIds = $this->getTemplateDefinitionIds($categoryId);

    if (count($templateIds) === 0) {
      // No templates defined, create single empty row (current behavior)
      $this->createDefinition(
        __("Uusi päästökerroin"),
        $year,
        $scopeId,
        $categoryId,
        $groupingId,
        null,
        null,
      );

      return 1;
    }

    // Get the template definitions (global definitions with company_id = null)
    $templates = CalculationDefinition::with("translations")
      ->whereIn("id", $templateIds)
      ->whereNull("company_id")
      ->with("years")
      ->get();

    if ($templates->isEmpty()) {
      throw new RuntimeException(__("Mallimäärittelyjä ei löytynyt"));
    }

    $creationId = null;

    // Generate a creation ID if these rows should be grouped
    if ($templates->count() > 1) {
      $creationId = Str::uuid7()->toString();
    }

    return DB::transaction(function () use (
      $templates,
      $company,
      $scopeId,
      $categoryId,
      $groupingId,
      $year,
      $creationId,
    ) {
      $creationCount = 0;
      $yearModel = $this->yearService->find($year);

      foreach ($templates as $index => $template) {
        $customName = $template->translate()->data_name ?? null;

        // Create new definition based on template
        $definitionData = [
          "company_id" => $company->id,
          "scope_id" => $scopeId,
          "category_id" => $categoryId,
          "grouping_id" => $groupingId,
          "custom_name" => $customName ?? __("Uusi kulutustieto"),
          "data_unit_id" => $template->data_unit_id ?? $this->unitService->getDefaultUnit()->id,
          "emission_factor_compound_unit_id" =>
            $template->emission_factor_compound_unit_id ??
            $this->unitService->getDefaultCompoundUnit()->id,
          "input_method" => $template->input_method ?? InputMethod::MANUAL,
          "data_formula" => $template->data_formula,
          "option_set_id" => $template->option_set_id, // Just copy the reference!
          "hide_from_data_page" => $template->hide_from_data_page,
          "hide_from_emission_factor_page" => $template->hide_from_emission_factor_page,
          "hide_from_results_page" => $template->hide_from_results_page,
          "tag" => $template->tag, // Copy from template
        ];

        // Add link_id for grouping if creating multiple definitions
        if ($creationId !== null) {
          $definitionData["link_id"] = $creationId;
        }

        $newDefinition = CalculationDefinition::create($definitionData);

        // Get template's year-specific data if it exists
        $templateYear = $template->years->where("year", $year)->first();

        $yearData = [
          "emission_factor_default_source" => "",
        ];

        if ($templateYear !== null) {
          $pivot = $templateYear->getAttribute("pivot");
          if ($pivot !== null) {
            Assert::instanceOf($pivot, CalculationDefinitionYear::class);

            $yearData["emission_factor_default_value"] = $pivot->emission_factor_default_value;
            $yearData["emission_factor_default_source"] =
              $pivot->emission_factor_default_source ?? "";
          }
        }

        $newDefinition->years()->attach($yearModel, $yearData);

        $creationCount++;
      }

      return $creationCount;
    });
  }

  /**
   * Create a new company-specific definition
   *
   * @param  string  $customName  The name for the company-specific definition
   * @param  int  $year  The year for the definition
   * @param  int  $scopeId  The scope ID
   * @param  int  $categoryId  The category ID
   * @param  int  $groupingId  The grouping ID
   * @param  int|null  $dataUnitId  The data unit ID (will use default if null)
   * @param  int|null  $emissionFactorCompoundUnitId  The emission factor compound unit ID (will use default if null)
   * @return void
   *
   * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
   * @throws RuntimeException
   */
  private function createDefinition(
    string $customName,
    int $year,
    int $scopeId,
    int $categoryId,
    int $groupingId,
    ?int $dataUnitId = null,
    ?int $emissionFactorCompoundUnitId = null,
  ): void {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      throw new RuntimeException(__("Yrityskonteksti puuttuu"));
    }

    $companyId = $company->id;

    // If no data unit ID provided, get a default unit
    if ($dataUnitId === null) {
      $dataUnitId = $this->unitService->getDefaultUnit()->id;
    }

    // If no compound unit ID provided, get a default compound unit
    if ($emissionFactorCompoundUnitId === null) {
      $emissionFactorCompoundUnitId = $this->unitService->getDefaultCompoundUnit()->id;
    }

    // Create the definition
    $definitionData = [
      "company_id" => $companyId,
      "data_unit_id" => $dataUnitId,
      "emission_factor_compound_unit_id" => $emissionFactorCompoundUnitId,
      "scope_id" => $scopeId,
      "category_id" => $categoryId,
      "grouping_id" => $groupingId,
      "custom_name" => $customName,
      "input_method" => InputMethod::MANUAL,
      "hide_from_data_page" => false,
      "hide_from_emission_factor_page" => false,
      "hide_from_results_page" => false,
    ];

    $definition = CalculationDefinition::create($definitionData);

    $definition->save();
    $definition->refresh();

    $yearModel = $this->yearService->find($year);

    $definition->years()->attach($yearModel, [
      "emission_factor_default_source" => "",
    ]);
  }

  /**
   * Update an existing calculation definition
   *
   * @param  int  $definitionId  The definition ID to update
   * @param  string  $customName  The name for the definition
   * @param  int  $scopeId  The scope ID
   * @param  int  $categoryId  The category ID
   * @param  int  $groupingId  The grouping ID
   * @param  int|null  $dataUnitId  The data unit ID (will use default if null)
   * @param  int|null  $emissionFactorCompoundUnitId  The emission factor compound unit ID (will use default if null)
   * @return CalculationDefinition The updated definition
   *
   * @throws RuntimeException
   * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
   */
  public function updateDefinition(
    int $definitionId,
    string $customName,
    int $scopeId,
    int $categoryId,
    int $groupingId,
    ?int $dataUnitId = null,
    ?int $emissionFactorCompoundUnitId = null,
  ): CalculationDefinition {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      throw new RuntimeException(__("Yrityskonteksti puuttuu"));
    }

    $companyId = $company->id;

    // If no data unit ID provided, get a default unit
    if ($dataUnitId === null) {
      $dataUnitId = $this->unitService->getDefaultUnit()->id;
    }

    // If no compound unit ID provided, get a default compound unit
    if ($emissionFactorCompoundUnitId === null) {
      $emissionFactorCompoundUnitId = $this->unitService->getDefaultCompoundUnit()->id;
    }

    // Find the definition and ensure it belongs to the current company
    $definition = CalculationDefinition::where("id", $definitionId)
      ->where("company_id", $companyId)
      ->first();

    if ($definition === null) {
      throw new \Illuminate\Database\Eloquent\ModelNotFoundException(
        __("Määrittelyä ei löydy tai siihen ei ole oikeutta"),
      );
    }

    if ($definition->company_id === null) {
      throw new RuntimeException(__("Globaaleja määrittelyjä ei voi muokata"));
    }

    // Update the non-translatable fields
    $definition->data_unit_id = $dataUnitId;
    $definition->emission_factor_compound_unit_id = $emissionFactorCompoundUnitId;
    $definition->scope_id = $scopeId;
    $definition->category_id = $categoryId;
    $definition->grouping_id = $groupingId;
    $definition->custom_name = $customName;

    $definition->save();

    return $definition;
  }

  /**
   * Get all calculation definitions for a company for a specific year
   * Sorts the definitions so that global ones appear first, followed by company-specific ones
   *
   * @param  int  $year  The year to get definitions for
   * @param  int  $scopeId  The scope ID to filter by
   * @return Collection<int, CalculationDefinition>
   *
   * @throws RuntimeException
   */
  public function getDefinitions(int $year, int $scopeId): Collection
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return new Collection();
    }

    $companyId = $company->id;

    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return new Collection();
    }

    $definitions = CalculationDefinition::with([
      "dataUnit.translation",
      "dataUnit.translations",
      "emissionFactorCompoundUnit.numeratorUnit.translations",
      "emissionFactorCompoundUnit.denominatorUnit.translations",
      "years" => function (Relation $query) use ($year) {
        $query->where("year", $year);
      },
      "scope",
      "category.translation",
      "grouping.translation",
      "translation",
      "translations",
      "optionSet.options.translations",
      "optionSet.options.yearValues.year",
    ])
      ->whereHas("years", function ($query) use ($year) {
        $query->where("year", $year);
      })
      ->where(function ($query) use ($companyId) {
        $query->where("company_id", $companyId)->orWhereNull("company_id"); // Include global definitions
      })
      ->where("scope_id", $scopeId)
      ->orderBy("sort_order")
      ->get();

    // Sort the collection so that global definitions (company_id is null) come first,
    // followed by company-specific definitions (company_id is not null)
    $sorted = $definitions->sort(function (CalculationDefinition $a, CalculationDefinition $b) {
      // First, sort by company_id (null comes first)
      if ($a->company_id === null && $b->company_id !== null) {
        return -1;
      }
      if ($a->company_id !== null && $b->company_id === null) {
        return 1;
      }

      // If both are global or both are company-specific, sort by sort_order
      $sortOrderComparison = $a->sort_order <=> $b->sort_order;

      // If sort_order is the same, use id as tie-breaker
      if ($sortOrderComparison === 0) {
        return $a->id <=> $b->id;
      }

      return $sortOrderComparison;
    });

    return $sorted->values(); // Reset keys and return
  }

  /**
   * Get all categories for a specific scope
   *
   * @param  int  $scopeId  The scope ID
   * @return Collection<int, Category>
   *
   * @throws RuntimeException
   */
  public function getCategoriesForScope(int $scopeId): Collection
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return new Collection();
    }

    $companyId = $company->id;

    // First try to find categories that have definitions in this scope
    $categoriesWithDefinitions = CalculationDefinition::where("scope_id", $scopeId)
      ->where(function ($query) use ($companyId) {
        $query->where("company_id", $companyId)->orWhereNull("company_id");
      })
      ->whereNotNull("category_id")
      ->distinct()
      ->pluck("category_id");

    return Category::with("translations")
      ->whereIn("categories.id", $categoriesWithDefinitions)
      ->orderBy("sort_order")
      ->get();
  }

  /**
   * Get linked definitions for a given definition
   *
   * @param  int  $definitionId  The definition ID to check
   * @return Collection<int, CalculationDefinition>
   *
   * @throws RuntimeException
   */
  public function getLinkedDefinitions(int $definitionId): Collection
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return new Collection();
    }

    $companyId = $company->id;

    $definition = CalculationDefinition::where("id", $definitionId)
      ->where("company_id", $companyId)
      ->first();

    if ($definition === null || $definition->link_id === null) {
      return new Collection();
    }

    // Get all definitions with the same link_id (including the original)
    return CalculationDefinition::where("link_id", $definition->link_id)
      ->where("company_id", $companyId)
      ->get();
  }

  /**
   * Delete a company-specific calculation definition
   * If the definition is linked, deletes all linked definitions
   *
   * @param  int  $definitionId  The definition ID to delete
   * @return int The number of definitions deleted
   *
   * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
   * @throws RuntimeException
   */
  public function deleteDefinition(int $definitionId): int
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      throw new RuntimeException(__("Yrityskonteksti puuttuu"));
    }

    $companyId = $company->id;

    return DB::transaction(function () use ($definitionId, $companyId) {
      $definition = CalculationDefinition::where("id", $definitionId)
        ->where("company_id", $companyId)
        ->lockForUpdate()
        ->firstOrFail();

      $deletedCount = 0;

      if ($definition->link_id !== null) {
        // Delete all linked definitions
        $linkedDefinitions = CalculationDefinition::where("link_id", $definition->link_id)
          ->where("company_id", $companyId)
          ->lockForUpdate()
          ->get();

        foreach ($linkedDefinitions as $linkedDef) {
          $linkedDef->dataValues()->delete();
          $linkedDef->emissionFactorValues()->delete();
          $linkedDef->years()->detach();
          $linkedDef->delete();
          $deletedCount++;
        }
      } else {
        // Delete single definition
        $definition->dataValues()->delete();
        $definition->emissionFactorValues()->delete();
        $definition->years()->detach();
        $definition->delete();
        $deletedCount = 1;
      }

      return $deletedCount;
    });
  }

  /**
   * Get all scopes that have definitions for the current company and year
   *
   * @param  int  $year  The year to check
   * @return Collection<int, Scope>
   *
   * @throws RuntimeException
   */
  public function getAvailableScopes(int $year): Collection
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return new Collection();
    }

    $companyId = $company->id;

    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return new Collection();
    }

    // Get all scopes that have definitions for this company and year
    $scopeIds = CalculationDefinition::whereHas("years", function ($query) use ($year) {
      $query->where("year", $year);
    })
      ->where(function ($query) use ($companyId) {
        $query->where("company_id", $companyId)->orWhereNull("company_id");
      })
      ->whereNotNull("scope_id")
      ->distinct()
      ->pluck("scope_id");

    return Scope::whereIn("id", $scopeIds)->orderBy("number")->get();
  }

  /**
   * Get all compound units
   *
   * @return Collection<int, CompoundUnit>
   */
  public function getCompoundUnits(): Collection
  {
    return CompoundUnit::with(["numeratorUnit", "denominatorUnit"])->get();
  }

  /**
   * Get all groupings for a specific scope
   *
   * @param  int  $scopeId  The scope ID
   * @return Collection<int, Grouping>
   *
   * @throws RuntimeException
   */
  public function getGroupingsForScope(int $scopeId): Collection
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return new Collection();
    }

    $companyId = $company->id;

    $groupingsWithDefinitions = CalculationDefinition::where("scope_id", $scopeId)
      ->where(function ($query) use ($companyId) {
        $query->where("company_id", $companyId)->orWhereNull("company_id");
      })
      ->distinct()
      ->pluck("grouping_id");

    return Grouping::with("translations")
      ->whereIn("groupings.id", $groupingsWithDefinitions)
      ->orderBy("sort_order")
      ->get();
  }
}
