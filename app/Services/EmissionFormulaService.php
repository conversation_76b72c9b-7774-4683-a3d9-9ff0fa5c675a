<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\FormulaException;
use LogicException;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\ExpressionLanguage\Node\ConstantNode;
use Symfony\Component\ExpressionLanguage\Node\FunctionNode;
use Symfony\Component\ExpressionLanguage\Node\Node;
use Symfony\Component\ExpressionLanguage\Parser;
use Symfony\Component\ExpressionLanguage\SyntaxError;

/**
 * Service for handling emission calculation formulas
 * Extends base expression service with data() and kerroin() functions
 * This service is stateless - a new ExpressionLanguage instance is created for each evaluation
 */
final class EmissionFormulaService extends BaseExpressionService
{
  /**
   * Evaluate a formula with the provided context
   *
   * @param array{data_values: array<int, string>, emission_factors: array<int, string>} $dataContext
   * @param array<string, mixed> $variables Additional variables (e.g., year)
   * @return mixed
   * @throws SyntaxError
   * @throws FormulaException
   * @throws LogicException
   */
  public function evaluateWithContext(
    string $expression,
    array $dataContext,
    array $variables = [],
  ): mixed {
    // Create a fresh ExpressionLanguage instance with all functions registered
    $expressionLanguage = $this->createExpressionLanguageWithDataFunctions($dataContext);

    // Evaluate the expression with the fresh instance
    return $expressionLanguage->evaluate($expression, $variables);
  }

  /**
   * Validate a formula without executing it
   *
   * @return array{valid: bool, error: string|null, references: array<int, string>}
   * @throws LogicException
   */
  public function validateFormula(string $formula): array
  {
    try {
      // Create a minimal ExpressionLanguage instance for validation
      $expressionLanguage = $this->createExpressionLanguage();
      $this->registerEmissionFunctions($expressionLanguage, [
        "data_values" => [],
        "emission_factors" => [],
      ]);

      $expressionLanguage->parse($formula, ["year"]);

      $referencedIds = $this->extractReferencedDefinitionIds($formula);

      $references = [];
      foreach ($referencedIds["data"] as $id) {
        $references[] = "data({$id})";
      }
      foreach ($referencedIds["kerroin"] as $id) {
        $references[] = "kerroin({$id})";
      }

      return [
        "valid" => true,
        "error" => null,
        "references" => $references,
      ];
    } catch (SyntaxError $e) {
      return [
        "valid" => false,
        "error" => __("Kaavan syntaksi on virheellinen"),
        "references" => [],
      ];
    }
  }

  /**
   * Extract referenced definition IDs from a formula
   *
   * @return array{data: array<int>, kerroin: array<int>}
   * @throws LogicException
   */
  public function extractReferencedDefinitionIds(string $formula): array
  {
    try {
      // Create a minimal ExpressionLanguage instance for parsing
      $expressionLanguage = $this->createExpressionLanguage();
      $this->registerEmissionFunctions($expressionLanguage, [
        "data_values" => [],
        "emission_factors" => [],
      ]);

      $parsed = $expressionLanguage->parse(
        $formula,
        [],
        Parser::IGNORE_UNKNOWN_VARIABLES | Parser::IGNORE_UNKNOWN_FUNCTIONS,
      );

      $dataIds = [];
      $kerroinIds = [];
      $this->extractFunctionCalls($parsed->getNodes(), $dataIds, "data");
      $this->extractFunctionCalls($parsed->getNodes(), $kerroinIds, "kerroin");

      return [
        "data" => array_unique($dataIds),
        "kerroin" => array_unique($kerroinIds),
      ];
    } catch (SyntaxError $e) {
      return ["data" => [], "kerroin" => []];
    }
  }

  /**
   * Create an ExpressionLanguage instance with data functions registered
   *
   * @param array{data_values: array<int, string>, emission_factors: array<int, string>} $dataContext
   * @throws LogicException
   */
  public function createExpressionLanguageWithDataFunctions(array $dataContext): ExpressionLanguage
  {
    $expressionLanguage = $this->createExpressionLanguage();
    $this->registerEmissionFunctions($expressionLanguage, $dataContext);
    return $expressionLanguage;
  }

  /**
   * Register emission-specific functions with the given context
   *
   * @param array{data_values: array<int, string>, emission_factors: array<int, string>} $dataContext
   * @throws LogicException
   */
  private function registerEmissionFunctions(
    ExpressionLanguage $expressionLanguage,
    array $dataContext,
  ): void {
    // Register data() function for accessing data values
    $expressionLanguage->register(
      "data",
      function (mixed $id): string {
        return sprintf("data(%s)", is_scalar($id) ? (string) $id : "0");
      },
      function (array $arguments, mixed $id) use ($dataContext): string {
        if (!is_numeric($id)) {
          throw new FormulaException("data() function requires numeric ID");
        }

        $definitionId = (int) $id;

        // Use the data context
        if (isset($dataContext["data_values"][$definitionId])) {
          return $dataContext["data_values"][$definitionId];
        }

        return "0";
      },
    );

    // Register kerroin() function for accessing emission factors
    $expressionLanguage->register(
      "kerroin",
      function (mixed $id): string {
        return sprintf("kerroin(%s)", is_scalar($id) ? (string) $id : "0");
      },
      function (array $arguments, mixed $id) use ($dataContext): string {
        if (!is_numeric($id)) {
          throw new FormulaException("kerroin() function requires numeric ID");
        }

        $definitionId = (int) $id;

        if (!isset($dataContext["emission_factors"][$definitionId])) {
          return "0";
        }

        return $dataContext["emission_factors"][$definitionId];
      },
    );
  }

  /**
   * Extract function calls from AST nodes using iterative approach
   *
   * @param array<int> $ids
   */
  private function extractFunctionCalls(Node $node, array &$ids, string $functionName): void
  {
    $nodesToProcess = [$node];

    while (count($nodesToProcess) !== 0) {
      $currentNode = array_shift($nodesToProcess);

      // Check if this is the function we're looking for
      // @phpstan-ignore-next-line instanceof.internalClass
      if ($currentNode instanceof FunctionNode) {
        $nodeName = $currentNode->attributes["name"] ?? null;
        if ($nodeName === $functionName) {
          $argumentsNode = $currentNode->nodes["arguments"] ?? null;
          if ($argumentsNode instanceof Node) {
            $argNodes = $argumentsNode->nodes;
            foreach ($argNodes as $argNode) {
              // @phpstan-ignore-next-line instanceof.internalClass
              if ($argNode instanceof ConstantNode) {
                $value = $argNode->attributes["value"] ?? null;
                if (is_numeric($value)) {
                  $ids[] = (int) $value;
                }
              }
            }
          }
        }
      }

      // Add child nodes to the processing queue
      $childNodes = $currentNode->nodes ?? [];
      foreach ($childNodes as $childNode) {
        if ($childNode instanceof Node) {
          $nodesToProcess[] = $childNode;
        }
      }
    }
  }
}
