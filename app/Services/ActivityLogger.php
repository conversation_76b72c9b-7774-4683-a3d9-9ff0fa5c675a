<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\ActivityEvent;
use App\Models\ActivityLog;
use App\Models\Company;
use App\Models\User;
use Illuminate\Support\Facades\Request;

final class ActivityLogger
{
    /**
     * Log an activity event.
     */
    public function log(
        ActivityEvent $event,
        string $description,
        ?User $user = null,
        ?Company $company = null,
        array $metadata = []
    ): void {
        ActivityLog::create([
            'user_id' => $user?->id,
            'company_id' => $company?->id,
            'event' => $event,
            'description' => $description,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'metadata' => $metadata,
            'occurred_at' => now(),
        ]);
    }

    /**
     * Log a magic link sent event.
     */
    public function logMagicLinkSent(string $email, ?Company $company = null): void
    {
        $this->log(
            ActivityEvent::MAGIC_LINK_SENT,
            "Magic link sent to {$email}",
            null,
            $company,
            [
                'email' => $email,
                'company_name' => $company?->name,
            ]
        );
    }

    /**
     * Log a magic link clicked event.
     */
    public function logMagicLinkClicked(User $user, ?Company $company = null): void
    {
        $this->log(
            ActivityEvent::MAGIC_LINK_CLICKED,
            "Magic link clicked by {$user->email}",
            $user,
            $company,
            [
                'email' => $user->email,
                'company_name' => $company?->name,
            ]
        );
    }

    /**
     * Log a successful login event.
     */
    public function logLoginSuccess(User $user, ?Company $company = null, array $metadata = []): void
    {
        $this->log(
            ActivityEvent::LOGIN_SUCCESS,
            "User {$user->email} logged in successfully",
            $user,
            $company,
            array_merge([
                'email' => $user->email,
                'company_name' => $company?->name,
            ], $metadata)
        );
    }

    /**
     * Log a failed login event.
     */
    public function logLoginFailure(string $reason, ?User $user = null, ?Company $company = null): void
    {
        $this->log(
            ActivityEvent::LOGIN_FAILURE,
            "Login failed: {$reason}",
            $user,
            $company,
            [
                'reason' => $reason,
                'email' => $user?->email,
                'company_name' => $company?->name,
            ]
        );
    }

    /**
     * Log a logout event.
     */
    public function logLogout(User $user): void
    {
        $this->log(
            ActivityEvent::LOGOUT,
            "User {$user->email} logged out",
            $user,
            null,
            [
                'email' => $user->email,
            ]
        );
    }
}
