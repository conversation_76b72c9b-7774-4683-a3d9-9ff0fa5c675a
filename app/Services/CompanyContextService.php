<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Company;
use Illuminate\Support\Facades\Auth;
use RuntimeException;

final class CompanyContextService
{
  /**
   * Get the current company for the authenticated user
   *
   * @return Company|null The selected company or null if none selected
   *
   * @throws RuntimeException
   */
  public function getCurrentCompany(): ?Company
  {
    $user = Auth::user();

    if ($user === null) {
      return null;
    }

    if ($user->selected_company_id === null) {
      // No selected company, try to get user's first company
      $firstCompany = $user->companies()->first();

      if ($firstCompany !== null) {
        // Update the user's selected company
        $user->update(["selected_company_id" => $firstCompany->id]);

        return $firstCompany;
      }

      return null;
    }

    return Company::find($user->selected_company_id);
  }

  /**
   * Get the current company ID for the authenticated user
   *
   * @return int|null The selected company ID or null if none selected
   *
   * @throws RuntimeException
   */
  public function getCurrentCompanyId(): ?int
  {
    $company = $this->getCurrentCompany();

    return $company?->id;
  }

  /**
   * Set the current company for the authenticated user
   *
   * @param  Company  $company  The company to set as selected
   * @return bool Whether the update was successful
   *
   * @throws RuntimeException
   */
  public function setCurrentCompany(Company $company): bool
  {
    $user = Auth::user();

    if ($user === null) {
      return false;
    }

    // Verify the user has access to this company
    if (!$user->companies()->where("companies.id", $company->id)->exists()) {
      return false;
    }

    return $user->update(["selected_company_id" => $company->id]);
  }
}
