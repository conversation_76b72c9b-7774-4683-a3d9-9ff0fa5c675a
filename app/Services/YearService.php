<?php

declare(strict_types=1);

namespace App\Services;

use App\Helpers\Assert;
use App\Models\Year;
use Illuminate\Support\Collection;
use RuntimeException;

final class YearService
{
  /**
   * Get all available years
   *
   * @return list<int>
   */
  public function getAllYears(): array
  {
    $years = Year::whereNotNull("published")->orderBy("year")->pluck("year")->all();

    Assert::list($years);
    Assert::intArray($years);

    return $years;
  }

  /**
   * Get the current year (most recent year in the database)
   *
   * @throws RuntimeException if no years exist
   */
  public function getCurrentYear(): int
  {
    $latestYear = Year::whereNotNull("published")->orderBy("year", "desc")->first();

    if ($latestYear === null) {
      throw new RuntimeException("No published years defined in the system");
    }

    return $latestYear->year;
  }

  /**
   * Find a year record
   *
   * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
   */
  public function find(int $year): Year
  {
    return Year::where("year", $year)->whereNotNull("published")->firstOrFail();
  }

  /**
   * Get a year model by its year value
   */
  public function getYearByValue(int $year): ?Year
  {
    return Year::with("translations")->where("year", $year)->whereNotNull("published")->first();
  }

  /**
   * Find a year record by ID
   */
  public function findById(int $id): ?Year
  {
    return Year::whereNotNull("published")->find($id);
  }

  /**
   * Get the latest year model
   */
  public function getLatestYear(): ?Year
  {
    return Year::whereNotNull("published")->orderBy("year", "desc")->first();
  }

  /**
   * Get multiple year models by their year values
   *
   * @param  list<int>  $years
   * @return Collection<int, Year>
   */
  public function getYearsByValues(array $years): Collection
  {
    return Year::whereIn("year", $years)->whereNotNull("published")->get()->keyBy("year");
  }
}
