<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\MetricDefinition;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\Relation;

final class MetricDefinitionService
{
  public function __construct(private YearService $yearService) {}

  /**
   * Get all metric definitions for a specific year
   *
   * @param  int  $year  The year to get definitions for
   * @return Collection<int, MetricDefinition>
   */
  public function getDefinitionsForYear(int $year): Collection
  {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return new Collection();
    }

    return MetricDefinition::withTranslation()
      ->with([
        "translation",
        "unit.translations",
        "years" => function (Relation $query) use ($year) {
          $query->where("year", $year);
        },
      ])
      ->whereHas("years", function ($query) use ($year) {
        $query->where("year", $year);
      })
      ->get();
  }
}
