<?php

declare(strict_types=1);

namespace App\Services;

use App\DataTransferObjects\SelectOption as SelectOptionDTO;
use App\Enums\SelectOptionType;
use App\Models\SelectOption;

final class SelectOptionService
{
  /**
   * Get select options by type formatted for select elements
   *
   * @return list<SelectOptionDTO>
   */
  public function getSelectOptions(SelectOptionType $type): array
  {
    return array_values(
      SelectOption::with("translation")
        ->where("type", $type)
        ->orderBy("sort_order")
        ->get()
        ->map(
          fn(SelectOption $option) => new SelectOptionDTO(
            $option->id,
            $option->translate()->label ?? "",
          ),
        )
        ->all(),
    );
  }
}
