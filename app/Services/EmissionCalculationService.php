<?php

declare(strict_types=1);

namespace App\Services;

use App\DataTransferObjects\Failure;
use App\DataTransferObjects\Result;
use App\DataTransferObjects\Success;
use App\DataTransferObjects\TotalEmissions;
use App\Enums\InputMethod;
use App\Exceptions\FormulaException;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionOptionYearValue;
use App\Models\CalculationDefinitionYear;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use App\Models\Unit;
use App\Models\Year;
use App\Services\EmissionFormulaService;
use App\Settings\UnitSettings;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use LogicException;
use RuntimeException;
use Throwable;

final class EmissionCalculationService
{
  private EmissionFormulaService $emissionFormulaService;

  public function __construct(
    private CompanyContextService $companyContextService,
    private UnitService $unitService,
    private YearService $yearService,
  ) {
    $this->emissionFormulaService = new EmissionFormulaService();
  }

  /**
   * Determine the target unit for all results
   * Either from settings or by finding a common unit that all results can be converted to
   *
   * @param Collection<int, CalculationDefinition> $definitions
   * @return Unit The determined unit
   * @throws \RuntimeException if no common unit can be found
   * @throws \Brick\Math\Exception\MathException
   */
  public function determineTargetUnit(Collection $definitions): Unit
  {
    // First, check if there's a configured result unit
    $unitSettings = app(UnitSettings::class);
    if ($unitSettings->result_unit_id !== null && $unitSettings->result_unit_id > 0) {
      $unit = Unit::with("translations")->find($unitSettings->result_unit_id);
      if ($unit !== null) {
        return $unit;
      }
    }

    // Collect all natural units from definitions
    $naturalUnits = [];
    foreach ($definitions as $definition) {
      $compoundUnit = $definition->emissionFactorCompoundUnit;
      if ($compoundUnit !== null && $compoundUnit->numerator_unit_id !== null) {
        $naturalUnits[] = $compoundUnit->numerator_unit_id;
      }
    }

    $naturalUnits = array_unique($naturalUnits);

    if (count($naturalUnits) === 0) {
      throw new \RuntimeException("No emission units found in calculation definitions");
    }

    // If there's only one natural unit, use it
    if (count($naturalUnits) === 1) {
      $unitId = reset($naturalUnits);
      $unit = Unit::with("translations")->find($unitId);
      if ($unit !== null) {
        return $unit;
      }
    }

    $units = Unit::with("translations")->whereIn("id", $naturalUnits)->get()->keyBy("id");

    // Build a conversion matrix to avoid repeated conversion attempts
    $conversionMatrix = [];
    foreach ($naturalUnits as $sourceId) {
      foreach ($naturalUnits as $targetId) {
        if ($sourceId === $targetId) {
          $conversionMatrix[$sourceId][$targetId] = true;
          continue;
        }

        // Cache the conversion check
        $testValue = $this->unitService->convertValue("1", $sourceId, $targetId);
        $conversionMatrix[$sourceId][$targetId] = $testValue !== null;
      }
    }

    // Find a unit that all other units can be converted to
    foreach ($naturalUnits as $candidateTargetId) {
      $canConvertAll = true;

      foreach ($naturalUnits as $sourceUnitId) {
        if (!($conversionMatrix[$sourceUnitId][$candidateTargetId] ?? false)) {
          $canConvertAll = false;
          break;
        }
      }

      if ($canConvertAll && isset($units[$candidateTargetId])) {
        return $units[$candidateTargetId];
      }
    }

    // No common unit found - provide helpful error message
    $unitNames = $units
      ->map(fn($u) => $u->translate()->symbol ?? "ID:{$u->id}")
      ->values()
      ->implode(", ");

    throw new \RuntimeException(
      "Cannot find a common unit for all emissions. Incompatible units detected: {$unitNames}. " .
        "Please configure a result unit in settings or ensure all emission factors use compatible units.",
    );
  }

  /**
   * Calculate results for a specific year
   *
   * @param  Collection<int, CalculationDefinition>  $definitions
   * @return array<int, Result>
   *
   * @throws LogicException
   * @throws RuntimeException
   * @throws FormulaException
   */
  public function calculateResultsForYear(int $year, Collection $definitions): array
  {
    $results = [];

    $company = $this->companyContextService->getCurrentCompany();
    if ($company === null) {
      return $results;
    }

    $companyId = $company->id;

    $yearModel = $this->yearService->getYearByValue($year);
    if ($yearModel === null) {
      return $results;
    }

    // Determine the target unit for all results
    $targetUnit = $this->determineTargetUnit($definitions);

    $dataContext = $this->buildDataContextOptimized($definitions, $companyId, $yearModel, $year);

    // Pass data values, emission factors, and definitions array for formula evaluation
    $formulaContext = [
      "data_values" => $dataContext["data_values"],
      "emission_factors" => $dataContext["emission_factors"],
      "definitions" => $dataContext["definitions_array"],
    ];

    $computedResult = $this->calculateComputedValues($definitions, $formulaContext, $year);
    $computedValues = $computedResult["values"];
    $formulaErrors = $computedResult["errors"];

    foreach ($definitions as $definition) {
      $definitionId = $definition->id ?? 0;

      // Handle formula errors
      if (isset($formulaErrors[$definitionId])) {
        $results[$definitionId] = new Failure(error: $formulaErrors[$definitionId]);
        continue;
      }

      // Path 1: Formula exists - use the computed value
      if (isset($computedValues[$definitionId])) {
        $results[$definitionId] = $this->formatComputedResult(
          $definition,
          $computedValues[$definitionId],
          $targetUnit,
        );
      }
      // Path 2: No formula - use manual data × emission factor
      else {
        $dataValue = $dataContext["data_value_objects"][$definitionId] ?? null;
        $factorValue = $dataContext["emission_factors"][$definitionId] ?? null;

        $results[$definitionId] = $this->calculateManualResult(
          $definition,
          $dataValue,
          $factorValue,
          $targetUnit,
        );
      }
    }

    return $results;
  }

  /**
   * Extract referenced definition IDs from a formula
   *
   * @return array{data: array<int>, kerroin: array<int>}
   * @throws LogicException
   */
  public function extractReferencedDefinitionIds(string $formula): array
  {
    return $this->emissionFormulaService->extractReferencedDefinitionIds($formula);
  }

  /**
   * Calculate results for multiple years
   *
   * @param  array<int>  $years
   * @param  Collection<int, CalculationDefinition>  $definitions
   * @return array{results: array<int, array<int, Result>>, unit: Unit}
   * @throws LogicException
   * @throws RuntimeException
   * @throws FormulaException
   */
  public function calculateResultsForYears(array $years, Collection $definitions): array
  {
    // Determine target unit once for all years
    $targetUnit = $this->determineTargetUnit($definitions);

    $allResults = [];

    foreach ($years as $year) {
      $allResults[$year] = $this->calculateResultsForYear($year, $definitions);
    }

    return [
      "results" => $allResults,
      "unit" => $targetUnit,
    ];
  }

  /**
   * Validate a formula without executing it
   *
   * @param  string  $formula  The formula to validate
   * @return array{valid: bool, error: string|null, references: array<int, string>}
   * @throws LogicException
   */
  public function validateFormula(string $formula): array
  {
    return $this->emissionFormulaService->validateFormula($formula);
  }

  /**
   * Get the total emissions for a specific year
   *
   * @param  array<int, Result>  $calculatedResults  The calculation results
   * @param  Unit  $targetUnit  The unit for the totals
   * @return TotalEmissions The total emissions
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws \Brick\Math\Exception\MathException
   */
  public function getTotalEmissionsForYear(
    array $calculatedResults,
    Unit $targetUnit,
  ): TotalEmissions {
    $total = BigDecimal::of("0.0");

    foreach ($calculatedResults as $result) {
      if ($result instanceof Success) {
        $total = $total->plus(BigDecimal::of($result->value));
      }
    }

    // Use the provided unit symbol
    $unitSymbol = $targetUnit->translate()->symbol ?? "";

    return new TotalEmissions(value: (string) $total, unitSymbol: $unitSymbol);
  }

  /**
   * Get total emissions for all years
   *
   * @param  array<int, array<int, Result>>  $allResults  All calculation results
   * @param  Unit  $targetUnit  The unit for the totals
   * @return array<int, TotalEmissions> Total emissions for each year
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws \Brick\Math\Exception\MathException
   */
  public function getTotalEmissionsForYears(array $allResults, Unit $targetUnit): array
  {
    $allTotals = [];

    foreach ($allResults as $year => $yearResults) {
      $allTotals[$year] = $this->getTotalEmissionsForYear($yearResults, $targetUnit);
    }

    return $allTotals;
  }

  /**
   * Build data context for all definitions with optimized batch loading
   *
   * @param  Collection<int, CalculationDefinition>  $definitions
   * @return array{data_values: array<int, string>, data_value_objects: array<int, DataValue>, emission_factors: array<int, string>, definitions_array: array<int, array<string, mixed>>}
   * @throws LogicException
   */
  private function buildDataContextOptimized(
    Collection $definitions,
    int $companyId,
    Year $yearModel,
    int $year,
  ): array {
    $dataValues = [];
    $dataValueObjects = [];
    $emissionFactors = [];
    $definitionsArray = [];

    $definitionIdsToLoad = [];

    foreach ($definitions as $definition) {
      $definitionIdsToLoad[] = $definition->id;
    }

    // Extract referenced IDs from formulas
    foreach ($definitions as $definition) {
      if ($definition->data_formula !== null && $definition->data_formula !== "") {
        $refs = $this->extractReferencedDefinitionIds($definition->data_formula);

        foreach ($refs["data"] as $refId) {
          if (!in_array($refId, $definitionIdsToLoad, true)) {
            $definitionIdsToLoad[] = $refId;
          }
        }
        foreach ($refs["kerroin"] as $refId) {
          if (!in_array($refId, $definitionIdsToLoad, true)) {
            $definitionIdsToLoad[] = $refId;
          }
        }
      }
    }

    // Always load all company definitions for the definitions array
    $allCompanyDefinitions = CalculationDefinition::with(["years", "translations"])
      ->where(function ($query) use ($companyId) {
        $query->where("company_id", $companyId)->orWhereNull("company_id");
      })
      ->get();

    foreach ($allCompanyDefinitions as $def) {
      if (!in_array($def->id, $definitionIdsToLoad, true)) {
        $definitionIdsToLoad[] = $def->id;
      }
    }

    $definitionIdsToLoad = array_unique($definitionIdsToLoad);
    $mainDefinitionIds = $definitions->pluck("id")->filter()->values()->all();
    Assert::intArray($mainDefinitionIds);

    // Load all data values for the definitions
    $dataValueCollection = DataValue::whereIn("data_definition_id", $definitionIdsToLoad)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->get();

    // Store actual DataValue objects for main definitions (needed for unit checking)
    foreach ($mainDefinitionIds as $mainId) {
      $dataValue = $dataValueCollection->where("data_definition_id", $mainId)->first();
      if ($dataValue !== null && $dataValue->value !== null) {
        $dataValueObjects[$mainId] = $dataValue;
      }
    }

    // Store only string values for all definitions (for formula evaluation)
    foreach ($dataValueCollection as $dataValue) {
      if ($dataValue->value !== null) {
        $dataValues[$dataValue->data_definition_id] = $dataValue->value;
      }
    }

    // Load emission factors for ALL definitions (not just main ones, since formulas might reference them)
    $emissionFactorCollection = EmissionFactorValue::whereIn(
      "emission_factor_definition_id",
      $definitionIdsToLoad,
    )
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->get();

    // Also need to get default emission factors for all definitions
    $allDefinitions = CalculationDefinition::with(["years", "translations"])
      ->whereIn("id", $definitionIdsToLoad)
      ->get();

    // Collect all option IDs that might need year values
    $optionIds = [];
    foreach ($allDefinitions as $definition) {
      if ($definition->input_method === InputMethod::SELECT) {
        $selectedEmissionFactors = $emissionFactorCollection
          ->where("emission_factor_definition_id", $definition->id)
          ->whereNotNull("calculation_definition_option_id");

        foreach ($selectedEmissionFactors as $ef) {
          if ($ef->calculation_definition_option_id !== null) {
            $optionIds[] = $ef->calculation_definition_option_id;
          }
        }
      }
    }

    // Load all option year values in one query
    $optionYearValues = [];
    if (count($optionIds) !== 0) {
      $optionYearValues = CalculationDefinitionOptionYearValue::whereIn(
        "calculation_definition_option_id",
        $optionIds,
      )
        ->where("year_id", $yearModel->id)
        ->get()
        ->keyBy("calculation_definition_option_id");
    }

    foreach ($allDefinitions as $definition) {
      $definitionId = $definition->id;

      $factorValue = null;

      // Check if this is a select-type input
      if ($definition->input_method === InputMethod::SELECT) {
        // For select type, find the emission factor with an option ID (which indicates the selected option)
        $selectedEmissionFactor = $emissionFactorCollection
          ->where("emission_factor_definition_id", $definitionId)
          ->whereNotNull("calculation_definition_option_id")
          ->first();

        if (
          $selectedEmissionFactor !== null &&
          $selectedEmissionFactor->calculation_definition_option_id !== null
        ) {
          // Look up the actual emission factor value for this option
          $optionYearValue =
            $optionYearValues[$selectedEmissionFactor->calculation_definition_option_id] ?? null;

          if ($optionYearValue !== null && $optionYearValue->value !== null) {
            $factorValue = $optionYearValue->value;
          }
        }
      } else {
        // For manual type, get the emission factor without an option ID
        $emissionFactorValue = $emissionFactorCollection
          ->where("emission_factor_definition_id", $definitionId)
          ->whereNull("calculation_definition_option_id")
          ->first();

        if ($emissionFactorValue !== null && $emissionFactorValue->value !== null) {
          $factorValue = $emissionFactorValue->value;
        }
      }

      // Fall back to definition default if no value found
      if ($factorValue === null) {
        $yearRelation = $definition->years->where("year", $year)->first();
        if ($yearRelation !== null) {
          $yearPivot = $yearRelation->getAttribute("pivot");
          Assert::instanceOf($yearPivot, CalculationDefinitionYear::class);
          $pivotValue = $yearPivot->emission_factor_default_value;
          if ($pivotValue !== null) {
            $factorValue = $pivotValue;
          }
        }
      }

      if ($factorValue !== null) {
        $emissionFactors[$definitionId] = $factorValue;
      }

      // Build the definitions array for use in formulas
      $definitionsArray[$definitionId] = [
        "id" => $definitionId,
        "tag" => $definition->tag,
        "link_id" => $definition->link_id,
        "scope_id" => $definition->scope_id,
        "category_id" => $definition->category_id,
        "grouping_id" => $definition->grouping_id,
        "custom_name" => $definition->custom_name,
        "data_value" => $dataValues[$definitionId] ?? null,
        "emission_factor" => $emissionFactors[$definitionId] ?? null,
        "input_method" => $definition->input_method->value,
        "data_formula" => $definition->data_formula,
        "data_unit_id" => $definition->data_unit_id,
        "emission_factor_compound_unit_id" => $definition->emission_factor_compound_unit_id,
        "hide_from_data_page" => $definition->hide_from_data_page ?? false,
        "hide_from_emission_factor_page" => $definition->hide_from_emission_factor_page ?? false,
        "hide_from_results_page" => $definition->hide_from_results_page ?? false,
        "sort_order" => $definition->sort_order ?? 0,
      ];

      // Add translated names if available
      $translation = $definition->translate();
      if ($translation !== null) {
        $definitionsArray[$definitionId]["data_name"] = $translation->data_name;
        $definitionsArray[$definitionId]["emission_factor_name"] =
          $translation->emission_factor_name;
        $definitionsArray[$definitionId]["result_name"] = $translation->result_name;
      }
    }

    return [
      "data_values" => $dataValues,
      "data_value_objects" => $dataValueObjects,
      "emission_factors" => $emissionFactors,
      "definitions_array" => $definitionsArray,
    ];
  }

  /**
   * Calculate computed values for all definitions with formulas
   * All formulas calculate the complete emission value
   *
   * @param  Collection<int, CalculationDefinition>  $definitions
   * @param  array{data_values: array<int, string>, emission_factors: array<int, string>, definitions: array<int, array<string, mixed>>}  $formulaContext
   * @return array{values: array<int, BigDecimal>, errors: array<int, string>}
   */
  private function calculateComputedValues(
    Collection $definitions,
    array $formulaContext,
    int $year,
  ): array {
    $computedValues = [];
    $errors = [];

    foreach ($definitions as $definition) {
      if ($definition->data_formula === null || $definition->data_formula === "") {
        continue;
      }

      $definitionId = $definition->id ?? 0;

      $formulaResult = $this->evaluateFormulaSecure($definition, $formulaContext, $year);

      if ($formulaResult["success"] && $formulaResult["value"] !== null) {
        $computedValues[$definitionId] = $formulaResult["value"];
      } elseif ($formulaResult["error"] !== null) {
        $errors[$definitionId] = $formulaResult["error"];
      }
    }

    return [
      "values" => $computedValues,
      "errors" => $errors,
    ];
  }

  /**
   * Evaluate a formula with security measures
   * All formulas calculate the complete emission value
   *
   * @param  array{data_values: array<int, string>, emission_factors: array<int, string>, definitions: array<int, array<string, mixed>>}  $formulaContext
   * @return array{success: bool, value: BigDecimal|null, error: string|null}
   */
  private function evaluateFormulaSecure(
    CalculationDefinition $definition,
    array $formulaContext,
    int $year,
  ): array {
    try {
      $formula = $definition->data_formula;
      if (!is_string($formula) || $formula === "") {
        return ["success" => false, "value" => null, "error" => __("Kaava puuttuu tai on tyhjä")];
      }

      try {
        $rawResult = $this->emissionFormulaService->evaluateWithContext($formula, $formulaContext, [
          "year" => $year,
          "definitions" => $formulaContext["definitions"],
        ]);
      } catch (Throwable $e) {
        report($e);
        return [
          "success" => false,
          "value" => null,
          "error" => __("Virhe kaavan suorituksessa"),
        ];
      }

      if (!is_scalar($rawResult)) {
        return [
          "success" => false,
          "value" => null,
          "error" => __("Kaava palautti virheellisen arvon"),
        ];
      }

      $resultString = (string) $rawResult;

      if (!is_numeric($resultString)) {
        return [
          "success" => false,
          "value" => null,
          "error" => __("Kaava palautti ei-numeerisen arvon"),
        ];
      }

      try {
        $decimal = BigDecimal::of($resultString);
      } catch (\Brick\Math\Exception\MathException $e) {
        return [
          "success" => false,
          "value" => null,
          "error" => __("Virhe desimaaliluvun käsittelyssä"),
        ];
      }

      return ["success" => true, "value" => $decimal, "error" => null];
    } catch (Throwable $e) {
      report($e);
      return [
        "success" => false,
        "value" => null,
        "error" => __("Odottamaton virhe kaavan arvioinnissa"),
      ];
    }
  }

  /**
   * Format a computed result with unit conversion
   * Used for all formula-calculated emission values
   */
  private function formatComputedResult(
    CalculationDefinition $definition,
    BigDecimal $computedValue,
    Unit $targetUnit,
  ): Result {
    try {
      $compoundUnit = $definition->emissionFactorCompoundUnit;

      // If no compound unit is configured, return raw value
      if ($compoundUnit === null) {
        return new Success(
          value: (string) $computedValue->toScale(2, RoundingMode::HALF_UP)->stripTrailingZeros(),
        );
      }

      // Get the natural result unit (numerator of the emission factor unit)
      $naturalResultUnitId = $compoundUnit->numerator_unit_id ?? null;

      if ($naturalResultUnitId === null) {
        return new Success(
          value: (string) $computedValue->toScale(2, RoundingMode::HALF_UP)->stripTrailingZeros(),
        );
      }

      // Get the target unit ID
      $targetUnitId = $targetUnit->id;

      // Apply unit conversion if natural unit differs from target unit
      $finalResult = $computedValue;
      if ($targetUnitId !== $naturalResultUnitId) {
        // Convert the value from natural unit to target unit
        $convertedValue = $this->unitService->convertValue(
          (string) $computedValue,
          $naturalResultUnitId,
          $targetUnitId,
        );

        if ($convertedValue === null) {
          return new Failure(error: __("Yksikkömuunnos epäonnistui"));
        }

        $finalResult = BigDecimal::of($convertedValue);
      }

      return new Success(
        value: (string) $finalResult->toScale(2, RoundingMode::HALF_UP)->stripTrailingZeros(),
      );
    } catch (Exception $e) {
      report($e);
      return new Failure(error: __("Odottamaton virhe laskennassa"));
    }
  }

  /**
   * Calculate result from manual data entry (no formula)
   * Manual data is multiplied by emission factor with unit compatibility check
   */
  private function calculateManualResult(
    CalculationDefinition $definition,
    ?DataValue $dataValue,
    ?string $factorValue,
    Unit $targetUnit,
  ): Result {
    try {
      $dataValueDecimal = null;
      if ($dataValue !== null && $dataValue->value !== null) {
        $dataValueDecimal = BigDecimal::of($dataValue->value);
      }

      // If there's no data value, return 0 regardless of emission factor
      if ($dataValueDecimal === null) {
        return new Success(value: "0.0");
      }

      // If there's a data value but no emission factor, show error
      if ($factorValue === null) {
        return new Failure(error: __("Päästökerroin puuttuu eikä oletuskerrointa ole määritetty"));
      }

      $compoundUnit = $definition->emissionFactorCompoundUnit;
      if ($compoundUnit === null) {
        return new Failure(error: __("Yhdistelmäyksikön konfiguraatio puuttuu"));
      }

      $dataUnitId = $definition->data_unit_id ?? null;
      $factorDenominatorUnitId = $compoundUnit->denominator_unit_id ?? null;

      if ($dataUnitId !== $factorDenominatorUnitId) {
        return new Failure(
          error: __(
            "Yksiköt eivät ole yhteensopivia. Datayksikkö ei vastaa kertoimen nimittäjäyksikköä.",
          ),
        );
      }

      $rawResult = $dataValueDecimal->multipliedBy(BigDecimal::of($factorValue));

      $naturalResultUnitId = $compoundUnit->numerator_unit_id ?? null;

      if ($naturalResultUnitId === null) {
        return new Failure(
          error: __(
            "Luonnollisen tulosyksikön tunniste puuttuu yhdistelmäyksikön konfiguraatiosta",
          ),
        );
      }

      // Get the target unit ID
      $targetUnitId = $targetUnit->id;

      $finalResult = $rawResult;
      if ($targetUnitId !== $naturalResultUnitId) {
        $convertedValue = $this->unitService->convertValue(
          (string) $rawResult,
          $naturalResultUnitId,
          $targetUnitId,
        );

        if ($convertedValue === null) {
          return new Failure(error: __("Yksikkömuunnos epäonnistui"));
        }

        $finalResult = BigDecimal::of($convertedValue);
      }

      return new Success(
        value: (string) $finalResult->toScale(2, RoundingMode::HALF_UP)->stripTrailingZeros(),
      );
    } catch (Exception $e) {
      report($e);

      return new Failure(error: __("Odottamaton virhe laskennassa"));
    }
  }
}
