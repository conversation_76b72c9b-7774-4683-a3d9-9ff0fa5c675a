<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Company;
use App\Models\GroupingState;
use App\Models\Scope;
use Illuminate\Support\Facades\DB;
use RuntimeException;

final class GroupingStateService
{
  /**
   * Available states for groupings
   */
  private const STATE_HIDDEN = "hidden";

  public function __construct(private readonly YearService $yearService) {}

  /**
   * Load grouping states for a company and year
   *
   * @return array{hiddenGroupings: array<string, bool>, selectOptionIds: array<string, int|null>, customReasons: array<string, string>}
   */
  public function loadGroupingStates(Company $company, int $year): array
  {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return [
        "hiddenGroupings" => [],
        "selectOptionIds" => [],
        "customReasons" => [],
      ];
    }

    $states = GroupingState::where("company_id", $company->id)
      ->where("year_id", $yearModel->id)
      ->where("state", self::STATE_HIDDEN)
      ->get();

    $hiddenGroupings = [];
    $selectOptionIds = [];
    $customReasons = [];

    foreach ($states as $state) {
      $key = $state->scope_id . "." . $state->grouping_id;
      $hiddenGroupings[$key] = true;

      $selectOptionIds[$key] = $state->select_option_id;
      $customReasons[$key] = $state->custom_reason ?? "";
    }

    return [
      "hiddenGroupings" => $hiddenGroupings,
      "selectOptionIds" => $selectOptionIds,
      "customReasons" => $customReasons,
    ];
  }

  /**
   * Save grouping state
   *
   * @throws RuntimeException
   */
  public function saveGroupingState(
    Company $company,
    int $year,
    int $scopeId,
    int $groupingId,
    bool $isHidden,
    ?int $selectOptionId = null,
    ?string $customReason = null,
  ): void {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      throw new RuntimeException("Year {$year} not found");
    }

    if ($isHidden) {
      $this->validateScopeAllowsHiding($scopeId);
    }

    DB::transaction(function () use (
      $company,
      $yearModel,
      $scopeId,
      $groupingId,
      $isHidden,
      $selectOptionId,
      $customReason,
    ) {
      if (!$isHidden) {
        // If showing the grouping, delete any existing state record
        GroupingState::where("company_id", $company->id)
          ->where("year_id", $yearModel->id)
          ->where("scope_id", $scopeId)
          ->where("grouping_id", $groupingId)
          ->delete();
      } else {
        // If hiding the grouping, create or update the state record
        GroupingState::updateOrCreate(
          [
            "company_id" => $company->id,
            "year_id" => $yearModel->id,
            "scope_id" => $scopeId,
            "grouping_id" => $groupingId,
          ],
          [
            "state" => self::STATE_HIDDEN,
            "select_option_id" => $selectOptionId,
            "custom_reason" => $customReason,
          ],
        );
      }
    });
  }

  /**
   * Update the reason for a hidden grouping
   *
   * @throws RuntimeException
   */
  public function updateReason(
    Company $company,
    int $year,
    int $scopeId,
    int $groupingId,
    ?int $selectOptionId,
    ?string $customReason,
  ): void {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      throw new RuntimeException("Year {$year} not found");
    }

    $this->validateScopeAllowsHiding($scopeId);

    GroupingState::where("company_id", $company->id)
      ->where("year_id", $yearModel->id)
      ->where("scope_id", $scopeId)
      ->where("grouping_id", $groupingId)
      ->lockForUpdate()
      ->update([
        "select_option_id" => $selectOptionId,
        "custom_reason" => $customReason,
      ]);
  }

  /**
   * Check if a scope allows hiding groupings
   *
   * @throws RuntimeException
   */
  private function validateScopeAllowsHiding(int $scopeId): void
  {
    $scope = Scope::find($scopeId);

    if ($scope === null) {
      throw new RuntimeException("Scope {$scopeId} not found");
    }

    if (!$scope->allow_grouping_hiding) {
      throw new RuntimeException("Scope {$scope->number} does not allow hiding groupings");
    }
  }
}
