<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\CalculationDefinition;
use LogicException;
use <PERSON>ymfony\Component\ExpressionLanguage\SyntaxError;

/**
 * Service for evaluating predicate expressions against calculation definitions
 * Used for filtering and matching definitions based on their properties
 * This service is stateless - a new ExpressionLanguage instance is created for each evaluation
 */
final class PredicateExpressionService extends BaseExpressionService
{
  /**
   * Evaluate a predicate expression against a calculation definition
   *
   * @return bool The evaluation result
   *
   * @throws SyntaxError If expression syntax is invalid
   * @throws LogicException
   */
  public function evaluateDefinition(string $expression, CalculationDefinition $definition): bool
  {
    $context = [
      "definition_id" => $definition->id,
      "grouping_id" => $definition->grouping_id,
      "category_id" => $definition->category_id,
    ];

    // Create a fresh ExpressionLanguage instance for evaluation
    $expressionLanguage = $this->createExpressionLanguage();
    $result = $expressionLanguage->evaluate($expression, $context);

    return (bool) $result;
  }

  /**
   * Validate an expression without evaluating it
   *
   * @param  string  $expression  The expression to validate
   * @param  array<string>  $allowedVariables  Optional list of allowed variables
   * @return bool True if valid, false otherwise
   * @throws LogicException
   */
  public function validateExpression(string $expression, array $allowedVariables = []): bool
  {
    try {
      if (count($allowedVariables) === 0) {
        $allowedVariables = ["definition_id", "grouping_id", "category_id"];
      }

      // Create a fresh ExpressionLanguage instance for validation
      $expressionLanguage = $this->createExpressionLanguage();
      $expressionLanguage->parse($expression, $allowedVariables);
      return true;
    } catch (SyntaxError $e) {
      return false;
    }
  }

  /**
   * Get the error message for an invalid expression
   *
   * @param  string  $expression  The expression to validate
   * @param  array<string>  $allowedVariables  Optional list of allowed variables
   * @return string|null The error message, or null if valid
   * @throws LogicException
   */
  public function getValidationError(string $expression, array $allowedVariables = []): ?string
  {
    try {
      if (count($allowedVariables) === 0) {
        $allowedVariables = ["definition_id", "grouping_id", "category_id"];
      }

      // Create a fresh ExpressionLanguage instance for validation
      $expressionLanguage = $this->createExpressionLanguage();
      $expressionLanguage->parse($expression, $allowedVariables);
      return null;
    } catch (SyntaxError $e) {
      return $e->getMessage();
    }
  }
}
