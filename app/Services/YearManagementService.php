<?php

declare(strict_types=1);

namespace App\Services;

use App\Helpers\Assert;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use Illuminate\Support\Carbon;
use RuntimeException;

final class YearManagementService
{
  public function __construct(
    private CompanyContextService $companyContextService,
    private Carbon $dateTime,
  ) {}

  /**
   * Get the current year
   */
  public function getCurrentYear(): int
  {
    return $this->dateTime->year;
  }

  /**
   * Load available years with data for DataValue model
   *
   * @return non-empty-list<int> Array of years
   *
   * @throws RuntimeException
   */
  public function loadDataValueYears(): array
  {
    $currentYear = $this->getCurrentYear();
    $companyId = $this->companyContextService->getCurrentCompanyId();

    if ($companyId === null) {
      return [$currentYear];
    }

    $yearsArray = DataValue::query()
      ->where("company_id", $companyId)
      ->select("year")
      ->distinct()
      ->orderBy("year")
      ->pluck("year")
      ->all();

    Assert::intArray($yearsArray);
    Assert::list($yearsArray);

    // If no years are found, use current year
    if ($yearsArray === []) {
      $yearsArray = [$currentYear];
    }

    // Make sure current year is included
    if (!in_array($currentYear, $yearsArray, true)) {
      $yearsArray[] = $currentYear;
      sort($yearsArray);
    }

    return $yearsArray;
  }

  /**
   * Load available years with data for EmissionFactorValue model
   *
   * @return array<int, int> Array of years
   *
   * @throws RuntimeException
   */
  public function loadEmissionFactorYears(): array
  {
    $currentYear = $this->getCurrentYear();
    $companyId = $this->companyContextService->getCurrentCompanyId();

    if ($companyId === null) {
      return [$currentYear];
    }

    $yearsArray = EmissionFactorValue::query()
      ->where("company_id", $companyId)
      ->select("year")
      ->distinct()
      ->orderBy("year")
      ->pluck("year")
      ->all();

    Assert::intKeyedArray($yearsArray);
    Assert::intArray($yearsArray);

    // If no years are found, use current year
    if ($yearsArray === []) {
      $yearsArray = [$currentYear];
    }

    // Make sure current year is included
    if (!in_array($currentYear, $yearsArray, true)) {
      $yearsArray[] = $currentYear;
      sort($yearsArray);
    }

    return $yearsArray;
  }

  /**
   * Combine years from both DataValue and EmissionFactorValue models
   *
   * @return list<int> List of unique years
   *
   * @throws RuntimeException
   */
  public function combineDataAndEmissionYears(): array
  {
    $currentYear = $this->getCurrentYear();
    $companyId = $this->companyContextService->getCurrentCompanyId();

    if ($companyId === null) {
      return [$currentYear];
    }

    $dataYearsCollection = DataValue::query()
      ->where("company_id", $companyId)
      ->select("year")
      ->distinct()
      ->orderBy("year")
      ->pluck("year");

    $factorYearsCollection = EmissionFactorValue::query()
      ->where("company_id", $companyId)
      ->select("year")
      ->distinct()
      ->orderBy("year")
      ->pluck("year");

    $allYears = $dataYearsCollection->merge($factorYearsCollection);

    $yearsArray = $allYears->unique()->sort()->all();

    Assert::intArray($yearsArray);
    Assert::list($yearsArray);

    // If no years are found, use current year
    if ($yearsArray === []) {
      $yearsArray = [$currentYear];
    }

    // Make sure current year is included
    if (!in_array($currentYear, $yearsArray, true)) {
      $yearsArray[] = $currentYear;
      sort($yearsArray);
    }

    return $yearsArray;
  }
}
