<?php

declare(strict_types=1);

namespace App\Services;

use App\DataTransferObjects\SelectOption;
use App\Helpers\Assert;
use App\Models\Municipality;
use App\Settings\UrlSettings;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use RuntimeException;

final class MunicipalityService
{
  /** @var non-empty-string */
  private readonly string $baseUrl;

  /** @var non-empty-list<string> */
  private readonly array $locales;

  /**
   * @throws RuntimeException
   */
  public function __construct(private readonly UrlSettings $urlSettings)
  {
    Assert::nonEmptyString($this->urlSettings->municipality_api_url);

    $this->baseUrl = $this->urlSettings->municipality_api_url;

    $locales = Config::array("translatable.locales", ["fi", "sv", "en"]);

    Assert::stringArray($locales);
    Assert::nonEmptyList($locales);

    $this->locales = $locales;
  }

  /**
   * @throws RuntimeException
   */
  public function syncFromApi(): void
  {
    $errors = [];
    $processed = 0;

    foreach ($this->locales as $locale) {
      $response = Http::get($this->baseUrl, [
        "lang" => $locale,
        "content" => "data",
        "meta" => "max",
        "format" => "json",
      ]);

      if (!$response->successful()) {
        throw new RuntimeException(
          "API request failed for locale {$locale} with status: " . $response->status(),
        );
      }

      $data = $response->json();

      if (!is_array($data)) {
        throw new RuntimeException("Invalid response format for locale {$locale} - expected array");
      }

      DB::transaction(function () use ($data, $locale, &$errors, &$processed): void {
        foreach ($data as $index => $item) {
          if (!is_array($item)) {
            $errors[] = "Item at index {$index} is not an array";

            continue;
          }

          if (!isset($item["localId"]) || !is_string($item["localId"])) {
            $errors[] = "Item at index {$index} missing or invalid localId";

            continue;
          }

          if (!isset($item["code"]) || !is_string($item["code"])) {
            $errors[] = "Item {$item["localId"]} missing or invalid code";

            continue;
          }

          $names = $item["classificationItemNames"] ?? [];

          if (!is_array($names)) {
            $errors[] = "Item {$item["localId"]} has invalid classificationItemNames";

            continue;
          }

          $municipality = Municipality::firstOrCreate(
            ["local_id" => $item["localId"]],
            ["code" => $item["code"]],
          );

          $municipality->load("translations");

          foreach ($names as $nameIndex => $nameItem) {
            if (!is_array($nameItem)) {
              $errors[] = "Item {$item["localId"]} name at index {$nameIndex} is not an array";

              continue;
            }

            if (!isset($nameItem["lang"]) || !is_string($nameItem["lang"])) {
              $errors[] = "Item {$item["localId"]} name at index {$nameIndex} missing lang";

              continue;
            }

            if (!isset($nameItem["name"]) || !is_string($nameItem["name"])) {
              $errors[] = "Item {$item["localId"]} name at index {$nameIndex} missing name";

              continue;
            }

            if ($nameItem["lang"] === $locale) {
              $municipality->translateOrNew($locale)->name = $nameItem["name"];
              $municipality->save();
              break;
            }
          }

          $processed++;
        }
      });
    }

    if (count($errors) > 0) {
      throw new RuntimeException(
        sprintf("Sync completed with %d errors. First error: %s", count($errors), $errors[0]),
      );
    }
  }

  /**
   * @return list<SelectOption>
   */
  public function getSelectOptions(): array
  {
    $result = [];

    $municipalities = Municipality::query()->with("translation")->orderBy("code")->get();

    foreach ($municipalities as $item) {
      $translation = $item->translate();
      $name = $translation !== null ? $translation->name : "";

      $result[] = new SelectOption(value: $item->id, label: $name);
    }

    return $result;
  }
}
