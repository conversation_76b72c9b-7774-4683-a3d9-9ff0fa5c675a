<?php

declare(strict_types=1);

namespace App\Services;

use App\Mail\LoginLink;
use App\Models\LoginToken;
use App\Models\User;
use App\Settings\AuthenticationSettings;
use App\Values\SplitToken;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use InvalidArgumentException;
use RuntimeException;
use SensitiveParameter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class AuthenticationService
{
  public function __construct(private readonly AuthenticationSettings $settings) {}

  /**
   * Validate that a login token exists and is valid without consuming it.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return bool True if token is valid
   */
  public function validateToken(string $selector, #[SensitiveParameter] string $validator): bool
  {
    try {
      $splitToken = SplitToken::fromParts($selector, $validator);
    } catch (InvalidArgumentException) {
      return false;
    }

    $loginToken = LoginToken::where("selector", $splitToken->selector())
      ->where("expires_at", ">", now())
      ->first();

    if ($loginToken === null) {
      return false;
    }

    return hash_equals($loginToken->token, $splitToken->hashedValidator());
  }

  /**
   * Verify a token and perform login.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return bool True if verification was successful
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  public function login(string $selector, #[SensitiveParameter] string $validator): bool
  {
    $ipKey = "verify-token-ip:" . hash("sha256", Request::ip() ?? "");

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("auth.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

    $splitToken = SplitToken::fromParts($selector, $validator);

    return DB::transaction(function () use ($splitToken) {
      $loginToken = LoginToken::where("selector", $splitToken->selector())
        ->where("expires_at", ">", now())
        ->lockForUpdate()
        ->first();

      if ($loginToken === null) {
        return false;
      }

      if (!hash_equals($loginToken->token, $splitToken->hashedValidator())) {
        return false;
      }

      $user = $loginToken->user;

      if ($user === null) {
        return false;
      }

      // Verify the user's email if not already verified
      if ($user->email_verified_at === null) {
        $user->email_verified_at = now();
        $user->save();
      }

      $currentUser = Auth::user();
      $isAlreadyLoggedIn = $currentUser !== null;
      $isSameUser = $isAlreadyLoggedIn && $currentUser->id === $user->id;

      // Only login if not already logged in as this user
      if (!$isSameUser) {
        // If logged in as a different user, log them out first
        if ($isAlreadyLoggedIn) {
          $this->logout();
        }

        // Now log in as the token's user
        Auth::login($user);
      }

      // Delete the token as it's been used
      $loginToken->delete();

      return true;
    });
  }

  /**
   * @throws RuntimeException
   */
  public function logout(): void
  {
    Auth::logout();
    Session::invalidate();
    Session::regenerateToken();
  }

  /**
   * Send a magic link to the specified user.
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   */
  public function sendMagicLink(string $email): void
  {
    $ipKey = "login-ip:" . hash("sha256", Request::ip() ?? "");
    $emailKey = "login-email:" . hash("sha256", $email);

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxIpAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("auth.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    if (RateLimiter::tooManyAttempts($emailKey, $this->settings->maxEmailAttempts)) {
      $seconds = RateLimiter::availableIn($emailKey);
      $message = __("auth.errors.link_already_sent", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->ipTimeWindowMinutes * 60);
    RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

    $user = User::where("email", $email)->first();

    if ($user === null) {
      $user = DB::transaction(function () use ($email) {
        $user = User::create([
          "name" => $email,
          "email" => $email,
          "password" => Hash::make(Str::random(64)),
        ]);

        $user->assignRole("basic");

        // Process any accepted invitations for this email
        $invitationService = App::make(CompanyInvitationService::class);
        $invitationService->processAcceptedInvitations($user);

        return $user;
      });
    }

    $splitToken = $this->generateToken($user);
    $magicLink = $this->generateMagicLink($splitToken);

    Mail::to($user->email)->queue(
      new LoginLink($magicLink, $this->settings->tokenExpirationMinutes),
    );
  }

  /**
   * Generate a login token for the given user.
   */
  private function generateToken(User $user): SplitToken
  {
    return DB::transaction(function () use ($user) {
      // Delete any existing tokens for this user
      LoginToken::where("user_id", $user->id)->delete();

      $splitToken = SplitToken::generate();

      LoginToken::create([
        "user_id" => $user->id,
        "selector" => $splitToken->selector(),
        "token" => $splitToken->hashedValidator(),
        "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
      ]);

      return $splitToken;
    });
  }

  /**
   * Generate a magic link URL from a token.
   */
  private function generateMagicLink(SplitToken $token): string
  {
    return route("login.verify", [
      "selector" => $token->selector(),
      "validator" => $token->validator(),
    ]);
  }
}
