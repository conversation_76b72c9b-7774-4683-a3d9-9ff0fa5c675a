<?php

declare(strict_types=1);

namespace App\Services;

use App\Mail\CompanyInvitation;
use App\Mail\LoginLink;
use App\Models\Company;
use App\Models\LoginToken;
use App\Models\User;
use App\Services\ActivityLogger;
use App\Settings\AuthenticationSettings;
use App\Values\SplitToken;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use InvalidArgumentException;
use RuntimeException;
use SensitiveParameter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class AuthenticationService
{
    public function __construct(
        private readonly AuthenticationSettings $settings,
        private readonly ActivityLogger $activityLogger
    ) {}

    /**
     * Verify a token and perform its associated action.
     *
     * @param  string  $token  The token to verify
     * @return bool True if verification was successful
     *
     * @throws TooManyRequestsHttpException
     * @throws RuntimeException
     * @throws InvalidArgumentException
     */
    public function login(#[SensitiveParameter] string $token): bool
    {
        $ipKey = "verify-token-ip:" . hash("sha256", Request::ip() ?? "");

        if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
            $seconds = RateLimiter::availableIn($ipKey);
            $message = __("Liian monta yritystä. Yritä uudelleen :minutes minuutin kuluttua.", [
                "minutes" => ceil($seconds / 60),
            ]);

            $this->activityLogger->logLoginFailure("Too many verification attempts");
            throw new TooManyRequestsHttpException($seconds, $message);
        }

        RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

        try {
            $splitToken = SplitToken::fromString($token);
        } catch (InvalidArgumentException $e) {
            $this->activityLogger->logLoginFailure("Invalid token format");
            throw $e;
        }

        return DB::transaction(function () use ($splitToken) {

            $selector = $splitToken->selector();

            $loginToken = LoginToken::where("selector", $selector)
                ->where("expires_at", ">", now())
                ->lockForUpdate()
                ->first();

            if ($loginToken === null) {
                $this->activityLogger->logLoginFailure("Token not found or expired: " . $selector);
                return false;
            }

            if (!hash_equals($loginToken->token, $splitToken->hashedValidator())) {
                $this->activityLogger->logLoginFailure("Invalid token validator", $loginToken->user);
                return false;
            }

            $user = $loginToken->user;

            if ($user === null) {
                $this->activityLogger->logLoginFailure("User not found for token");
                return false;
            }

            // Check if user agent is present - block login if missing
            $userAgent = Request::userAgent();
            if (empty($userAgent)) {
                $this->activityLogger->logLoginFailure("Missing user agent", $user, $loginToken->company);
                return false;
            }

            // Log that the magic link was clicked
            $this->activityLogger->logMagicLinkClicked($user, $loginToken->company);

            // Verify the user's email if not already verified
            if ($user->email_verified_at === null) {
                $user->email_verified_at = now();
                $user->save();
            }

            // If this token is associated with a company, add the user to it
            if ($loginToken->company_id !== null) {
                $user->companies()->attach($loginToken->company_id, ["is_primary" => false]);
            }

            $currentUser = Auth::user();
            $isAlreadyLoggedIn = $currentUser !== null;
            $isSameUser = $isAlreadyLoggedIn && $currentUser->id === $user->id;

            // Only login if not already logged in as this user
            if (!$isSameUser) {
                // If logged in as a different user, log them out first
                if ($isAlreadyLoggedIn) {
                    $this->logout();
                }

                // Now log in as the token's user
                Auth::login($user);
            }

            // Delete the token as it's been used
            $loginToken->delete();

            // Log successful login
            $this->activityLogger->logLoginSuccess($user, $loginToken->company, [
                'login_method' => 'magic_link',
                'was_already_logged_in' => $isSameUser,
                'email_verified' => $user->email_verified_at !== null,
            ]);

            return true;
        });
    }

    /**
     * @throws RuntimeException
     */
    public function logout(): void
    {
        $user = Auth::user();

        Auth::logout();
        Session::invalidate();
        Session::regenerateToken();

        // Log logout if we had a user
        if ($user instanceof User) {
            $this->activityLogger->logLogout($user);
        }
    }

    /**
     * Send a magic link to the specified user.
     *
     * @throws TooManyRequestsHttpException
     * @throws RuntimeException
     */
    public function sendMagicLink(string $email, ?Company $company = null): void
    {
        $ipKey = "login-ip:" . hash("sha256", Request::ip() ?? "");
        $emailKey = "login-email:" . hash("sha256", $email);

        if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxIpAttempts)) {
            $seconds = RateLimiter::availableIn($ipKey);
            $message = __("Liian monta yritystä. Yritä uudelleen :minutes minuutin kuluttua.", [
                "minutes" => ceil($seconds / 60),
            ]);

            throw new TooManyRequestsHttpException($seconds, $message);
        }

        if (RateLimiter::tooManyAttempts($emailKey, $this->settings->maxEmailAttempts)) {
            $seconds = RateLimiter::availableIn($emailKey);
            $message = __(
                "Kirjautumislinkki on jo lähetetty tälle sähköpostiosoitteelle. Voit pyytää uuden linkin :minutes minuutin kuluttua.",
                [
                    "minutes" => ceil($seconds / 60),
                ],
            );

            throw new TooManyRequestsHttpException($seconds, $message);
        }

        RateLimiter::hit($ipKey, $this->settings->ipTimeWindowMinutes * 60);
        RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

        $user = User::where("email", $email)->first();

        if ($user === null) {
            $user = DB::transaction(function () use ($email) {
                $user = User::create([
                    "name" => $email,
                    "email" => $email,
                    "password" => Hash::make(Str::random(64)),
                ]);

                $user->assignRole("basic");

                return $user;
            });
        }

        $splitToken = $this->generateToken($user, $company);
        $magicLink = $this->generateMagicLink($splitToken);

        $mailable =
            $company === null
            ? new LoginLink($magicLink, $this->settings->tokenExpirationMinutes)
            : new CompanyInvitation(
                $magicLink,
                $company->name,
                $this->settings->tokenExpirationMinutes,
            );

        Mail::to($user->email)->queue($mailable);

        // Log that magic link was sent
        $this->activityLogger->logMagicLinkSent($email, $company);
    }

    /**
     * Generate a login token for the given user.
     */
    private function generateToken(User $user, ?Company $company): SplitToken
    {
        return DB::transaction(function () use ($user, $company) {
            // Delete any existing tokens for this user
            LoginToken::where("user_id", $user->id)->delete();

            $splitToken = SplitToken::generate();

            LoginToken::create([
                "user_id" => $user->id,
                "company_id" => $company?->id,
                "selector" => $splitToken->selector(),
                "token" => $splitToken->hashedValidator(),
                "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
            ]);

            return $splitToken;
        });
    }

    /**
     * Generate a magic link URL from a token.
     */
    private function generateMagicLink(SplitToken $token): string
    {
        return route("login.verify", ["token" => (string) $token]);
    }
}
