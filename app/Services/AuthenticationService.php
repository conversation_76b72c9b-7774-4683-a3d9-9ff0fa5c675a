<?php

declare(strict_types=1);

namespace App\Services;

use App\Helpers\Assert;
use App\Mail\LoginLink;
use App\Models\LoginToken;
use App\Models\User;
use App\Services\ActivityLogger;
use App\Settings\AuthenticationSettings;
use App\Values\SplitToken;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use RuntimeException;
use SensitiveParameter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class AuthenticationService
{
    public function __construct(
        private readonly AuthenticationSettings $settings,
        private readonly ActivityLogger $activityLogger
    ) {}

    /**
     * Validate that a login token exists and is valid without consuming it.
     *
     * @param  string  $selector  The token selector
     * @param  string  $validator  The token validator
     * @return bool True if token is valid
     */
    public function validateToken(string $selector, #[SensitiveParameter] string $validator): bool
    {
        try {
            $splitToken = SplitToken::fromParts($selector, $validator);
        } catch (InvalidArgumentException) {
            return false;
        }

        $loginToken = LoginToken::where("selector", $splitToken->selector())
            ->where("expires_at", ">", now())
            ->first();

        if ($loginToken === null) {
            return false;
        }

        return hash_equals($loginToken->token, $splitToken->hashedValidator());
    }

    /**
     * Verify a token and perform login.
     *
     * @param  string  $selector  The token selector
     * @param  string  $validator  The token validator
     * @return bool True if verification was successful
     *
     * @throws TooManyRequestsHttpException
     * @throws RuntimeException
     * @throws InvalidArgumentException
     */
    public function login(string $selector, #[SensitiveParameter] string $validator): bool
    {
        $ipKey = "verify-token-ip:" . hash("sha256", Request::ip() ?? "");

        if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
            $seconds = RateLimiter::availableIn($ipKey);
            $message = __("auth.errors.rate_limit", [
                "minutes" => ceil($seconds / 60),
            ]);

            $this->activityLogger->logLoginFailure("Too many verification attempts");
            throw new TooManyRequestsHttpException($seconds, $message);
        }

        RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

        $splitToken = SplitToken::fromParts($selector, $validator);

        return DB::transaction(function () use ($splitToken) {
            $selector = $splitToken->selector();

            $loginToken = LoginToken::where("selector", $selector)
                ->where("expires_at", ">", now())
                ->lockForUpdate()
                ->first();

            if ($loginToken === null) {
                $this->activityLogger->logLoginFailure("Token not found or expired: " . $selector);
                return false;
            }

            if (!hash_equals($loginToken->token, $splitToken->hashedValidator())) {
                $this->activityLogger->logLoginFailure("Invalid token validator", $loginToken->user);
                return false;
            }

            $user = $loginToken->user;

            if ($user === null) {
                $this->activityLogger->logLoginFailure("User not found for token");
                return false;
            }

            // Log that the magic link was clicked
            $this->activityLogger->logMagicLinkClicked($user, $loginToken->company);

            // Verify the user's email if not already verified
            if ($user->email_verified_at === null) {
                $user->email_verified_at = now();
                $user->save();
            }

            $currentUser = Auth::user();
            $isAlreadyLoggedIn = $currentUser !== null;
            $isSameUser = $isAlreadyLoggedIn && $currentUser->id === $user->id;

            // Only login if not already logged in as this user
            if (!$isSameUser) {
                // If logged in as a different user, log them out first
                if ($isAlreadyLoggedIn) {
                    $this->logout();
                }

                // Now log in as the token's user
                Auth::login($user);
            }

            // Delete the token as it's been used
            $loginToken->delete();

            // Log successful login
            $this->activityLogger->logLoginSuccess($user, $loginToken->company, [
                'login_method' => 'magic_link',
                'was_already_logged_in' => $isSameUser,
                'email_verified' => $user->email_verified_at !== null,
            ]);

            return true;
        });
    }

    /**
     * @throws RuntimeException
     */
    public function logout(): void
    {
        $user = Auth::user();

        Auth::logout();
        Session::invalidate();
        Session::regenerateToken();

        // Log logout if we had a user
        if ($user instanceof User) {
            $this->activityLogger->logLogout($user);
        }
    }

    /**
     * Send a magic link to the specified user.
     *
     * @throws TooManyRequestsHttpException
     * @throws RuntimeException
     */
    public function sendMagicLink(string $email): void
    {
        $ipKey = "login-ip:" . hash("sha256", Request::ip() ?? "");
        $emailKey = "login-email:" . hash("sha256", $email);

        if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxIpAttempts)) {
            $seconds = RateLimiter::availableIn($ipKey);
            $message = __("auth.errors.rate_limit", [
                "minutes" => ceil($seconds / 60),
            ]);

            throw new TooManyRequestsHttpException($seconds, $message);
        }

        if (RateLimiter::tooManyAttempts($emailKey, $this->settings->maxEmailAttempts)) {
            $seconds = RateLimiter::availableIn($emailKey);
            $message = __("auth.errors.link_already_sent", [
                "minutes" => ceil($seconds / 60),
            ]);

            throw new TooManyRequestsHttpException($seconds, $message);
        }

        RateLimiter::hit($ipKey, $this->settings->ipTimeWindowMinutes * 60);
        RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

        $user = User::where("email", $email)->first();

        if ($user === null) {
            $user = DB::transaction(function () use ($email) {
                $user = User::create([
                    "name" => $email,
                    "email" => $email,
                    "password" => Hash::make(Str::random(64)),
                ]);

                $user->assignRole("basic");

                return $user;
            });
        }

        $splitToken = $this->generateToken($user);
        $magicLink = $this->generateMagicLink($splitToken);

        Mail::to($user->email)
            ->locale(App::getLocale())
            ->queue(new LoginLink($magicLink, $this->settings->tokenExpirationMinutes));

        // Log that magic link was sent
        $this->activityLogger->logMagicLinkSent($email);
    }

    /**
     * Generate a login token for the given user.
     */
    private function generateToken(User $user): SplitToken
    {
        return DB::transaction(function () use ($user) {
            // Delete any existing tokens for this user
            LoginToken::where("user_id", $user->id)->delete();

            $splitToken = SplitToken::generate();

            LoginToken::create([
                "user_id" => $user->id,
                "selector" => $splitToken->selector(),
                "token" => $splitToken->hashedValidator(),
                "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
            ]);

            return $splitToken;
        });
    }

    /**
     * Generate a magic link URL from a token.
     * @throws \RuntimeException
     */
    private function generateMagicLink(SplitToken $token): string
    {
        $url = LaravelLocalization::getLocalizedURL(
            null,
            route("login.verify", [
                "selector" => $token->selector(),
                "validator" => $token->validator(),
            ]),
        );

        Assert::string($url);

        return $url;
    }
}
