<?php

declare(strict_types=1);

namespace App\Services;

use App\DataTransferObjects\InvitationAcceptanceResult;
use App\DataTransferObjects\InvitationValidationResult;
use App\Mail\CompanyInvitation as CompanyInvitationMail;
use App\Models\Company;
use App\Models\CompanyInvitation;
use App\Models\User;
use App\Settings\InvitationSettings;
use App\Values\SplitToken;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use InvalidArgumentException;
use RuntimeException;
use SensitiveParameter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class CompanyInvitationService
{
  public function __construct(private readonly InvitationSettings $settings) {}

  /**
   * Validate an invitation without changing its state.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   */
  public function validateInvitation(
    string $selector,
    #[SensitiveParameter] string $validator,
  ): InvitationValidationResult {
    try {
      $splitToken = SplitToken::fromParts($selector, $validator);
    } catch (InvalidArgumentException) {
      return InvitationValidationResult::invalid();
    }

    $invitation = CompanyInvitation::where("selector", $splitToken->selector())
      ->where("expires_at", ">", now())
      ->where("status", "pending")
      ->with("company")
      ->first();

    if ($invitation === null) {
      return InvitationValidationResult::invalid();
    }

    if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
      return InvitationValidationResult::invalid();
    }

    if ($invitation->company === null) {
      return InvitationValidationResult::invalid();
    }

    return InvitationValidationResult::valid(
      $invitation->company->name,
      $invitation->invitee_email,
    );
  }

  /**
   * Send an invitation to join a company.
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   */
  public function inviteToCompany(string $email, Company $company): void
  {
    $ipKey = "invite-ip:" . hash("sha256", Request::ip() ?? "");
    $emailKey = "invite-email:" . hash("sha256", $email);

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxIpAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    if (RateLimiter::tooManyAttempts($emailKey, $this->settings->maxEmailAttempts)) {
      $seconds = RateLimiter::availableIn($emailKey);
      $message = __("companies.errors.invitation_already_sent", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->ipTimeWindowMinutes * 60);
    RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

    $splitToken = $this->generateInvitationToken($email, $company);
    $invitationLink = $this->generateInvitationLink($splitToken);

    Mail::to($email)->queue(
      new CompanyInvitationMail(
        $invitationLink,
        $company->name,
        $this->settings->tokenExpirationMinutes,
      ),
    );
  }

  /**
   * Accept an invitation.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return InvitationAcceptanceResult The result of the acceptance attempt
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  public function acceptInvitation(
    string $selector,
    #[SensitiveParameter] string $validator,
  ): InvitationAcceptanceResult {
    $ipKey = "accept-invitation-ip:" . hash("sha256", Request::ip() ?? "");

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

    $splitToken = SplitToken::fromParts($selector, $validator);

    return DB::transaction(function () use ($splitToken) {
      $invitation = CompanyInvitation::where("selector", $splitToken->selector())
        ->where("expires_at", ">", now())
        ->where("status", "pending")
        ->lockForUpdate()
        ->first();

      if ($invitation === null) {
        return InvitationAcceptanceResult::failure();
      }

      if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
        return InvitationAcceptanceResult::failure();
      }

      // Mark invitation as accepted
      $invitation->update([
        "status" => "accepted",
        "decided_at" => now(),
      ]);

      // Check if user exists
      $user = User::where("email", $invitation->invitee_email)->first();

      if ($user !== null) {
        // User exists - add them to the company immediately
        if (!$user->companies()->where("companies.id", $invitation->company_id)->exists()) {
          $user->companies()->attach($invitation->company_id, ["is_primary" => false]);
        }

        return InvitationAcceptanceResult::successForExistingUser();
      }

      // User doesn't exist, they'll be added to the company when they register
      return InvitationAcceptanceResult::successForNewUser();
    });
  }

  /**
   * Decline an invitation.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return bool True if successfully declined
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  public function declineInvitation(string $selector, #[SensitiveParameter] string $validator): bool
  {
    $ipKey = "decline-invitation-ip:" . hash("sha256", Request::ip() ?? "");

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

    $splitToken = SplitToken::fromParts($selector, $validator);

    return DB::transaction(function () use ($splitToken) {
      $invitation = CompanyInvitation::where("selector", $splitToken->selector())
        ->where("expires_at", ">", now())
        ->where("status", "pending")
        ->lockForUpdate()
        ->first();

      if ($invitation === null) {
        return false;
      }

      if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
        return false;
      }

      // Mark invitation as declined
      $invitation->update([
        "status" => "declined",
        "decided_at" => now(),
      ]);

      return true;
    });
  }

  /**
   * Process accepted invitations for a newly registered user.
   * This should be called after user registration.
   */
  public function processAcceptedInvitations(User $user): void
  {
    DB::transaction(function () use ($user) {
      $acceptedInvitations = CompanyInvitation::where("invitee_email", $user->email)
        ->where("status", "accepted")
        ->lockForUpdate()
        ->get();

      foreach ($acceptedInvitations as $invitation) {
        if (!$user->companies()->where("companies.id", $invitation->company_id)->exists()) {
          $user->companies()->attach($invitation->company_id, ["is_primary" => false]);
        }
      }
    });
  }

  /**
   * Generate an invitation token for the given email and company.
   */
  private function generateInvitationToken(string $email, Company $company): SplitToken
  {
    return DB::transaction(function () use ($email, $company) {
      // Delete any existing pending invitations for this email and company
      CompanyInvitation::where("invitee_email", $email)
        ->where("company_id", $company->id)
        ->where("status", "pending")
        ->delete();

      $splitToken = SplitToken::generate();

      $currentUser = Auth::user();

      if ($currentUser === null) {
        throw new RuntimeException("User must be authenticated to send invitations");
      }

      CompanyInvitation::create([
        "company_id" => $company->id,
        "inviter_user_id" => $currentUser->id,
        "invitee_email" => $email,
        "selector" => $splitToken->selector(),
        "token" => $splitToken->hashedValidator(),
        "status" => "pending",
        "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
      ]);

      return $splitToken;
    });
  }

  /**
   * Generate an invitation link URL from a token.
   */
  private function generateInvitationLink(SplitToken $token): string
  {
    return route("invitation.accept", [
      "selector" => $token->selector(),
      "validator" => $token->validator(),
    ]);
  }
}
