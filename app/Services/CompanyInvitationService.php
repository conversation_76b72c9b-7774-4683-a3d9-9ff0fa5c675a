<?php

declare(strict_types=1);

namespace App\Services;

use App\DataTransferObjects\InvitationAcceptanceResult;
use App\DataTransferObjects\InvitationValidationResult;
use App\Helpers\Assert;
use App\Mail\CompanyInvitation as CompanyInvitationMail;
use App\Models\Company;
use App\Models\CompanyInvitation;
use App\Models\User;
use App\Settings\InvitationSettings;
use App\Values\SplitToken;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use RuntimeException;
use SensitiveParameter;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class CompanyInvitationService
{
  public function __construct(private readonly InvitationSettings $settings) {}

  /**
   * Validate an invitation without changing its state.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   */
  public function validateInvitation(
    string $selector,
    #[SensitiveParameter] string $validator,
  ): InvitationValidationResult {
    try {
      $splitToken = SplitToken::fromParts($selector, $validator);
    } catch (InvalidArgumentException) {
      return InvitationValidationResult::invalid();
    }

    $invitation = CompanyInvitation::where("selector", $splitToken->selector())
      ->where("expires_at", ">", now())
      ->where("status", "pending")
      ->with("company")
      ->first();

    if ($invitation === null) {
      return InvitationValidationResult::invalid();
    }

    if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
      return InvitationValidationResult::invalid();
    }

    if ($invitation->company === null) {
      return InvitationValidationResult::invalid();
    }

    return InvitationValidationResult::valid(
      $invitation->company->name,
      $invitation->invitee_email,
    );
  }

  /**
   * Send an invitation to join a company.
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   */
  public function inviteToCompany(string $email, Company $company): void
  {
    $ipKey = "invite-ip:" . hash("sha256", Request::ip() ?? "");
    $emailKey = "invite-email:" . hash("sha256", $email);

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxIpAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    if (RateLimiter::tooManyAttempts($emailKey, $this->settings->maxEmailAttempts)) {
      $seconds = RateLimiter::availableIn($emailKey);
      $message = __("companies.errors.invitation_already_sent", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->ipTimeWindowMinutes * 60);
    RateLimiter::hit($emailKey, $this->settings->emailTimeWindowMinutes * 60);

    // Find or create the user account first
    $user = DB::transaction(function () use ($email) {
      $user = User::where("email", $email)->first();

      if ($user === null) {
        $user = User::create([
          "name" => $email,
          "email" => $email,
          "password" => Hash::make(Str::random(64)),
        ]);

        $user->assignRole("basic");
      }

      return $user;
    });

    $splitToken = $this->generateInvitationToken($user, $company);
    $invitationLink = $this->generateInvitationLink($splitToken);

    Mail::to($email)
      ->locale(App::getLocale())
      ->queue(
        new CompanyInvitationMail(
          $invitationLink,
          $company->name,
          $this->settings->tokenExpirationMinutes,
        ),
      );
  }

  /**
   * Accept an invitation.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return InvitationAcceptanceResult The result of the acceptance attempt
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  public function acceptInvitation(
    string $selector,
    #[SensitiveParameter] string $validator,
  ): InvitationAcceptanceResult {
    $ipKey = "accept-invitation-ip:" . hash("sha256", Request::ip() ?? "");

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

    $splitToken = SplitToken::fromParts($selector, $validator);

    return DB::transaction(function () use ($splitToken) {
      $invitation = CompanyInvitation::where("selector", $splitToken->selector())
        ->where("expires_at", ">", now())
        ->where("status", "pending")
        ->with("invitee")
        ->lockForUpdate()
        ->first();

      if ($invitation === null) {
        return InvitationAcceptanceResult::failure();
      }

      if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
        return InvitationAcceptanceResult::failure();
      }

      // The user already exists because they were created when the invitation was sent
      Assert::notNull($invitation->invitee);
      $user = $invitation->invitee;

      // Add the user to the company
      if (!$user->companies()->where("companies.id", $invitation->company_id)->exists()) {
        $user->companies()->attach($invitation->company_id, ["is_primary" => false]);
      }

      // Mark the invitation as accepted
      $invitation->update([
        "status" => "accepted",
        "decided_at" => now(),
      ]);

      // Determine if this is their first login
      $isNewUser = $user->email_verified_at === null;

      if ($isNewUser) {
        return InvitationAcceptanceResult::successForNewUser();
      } else {
        return InvitationAcceptanceResult::successForExistingUser();
      }
    });
  }

  /**
   * Decline an invitation.
   *
   * @param  string  $selector  The token selector
   * @param  string  $validator  The token validator
   * @return bool True if successfully declined
   *
   * @throws TooManyRequestsHttpException
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  public function declineInvitation(string $selector, #[SensitiveParameter] string $validator): bool
  {
    $ipKey = "decline-invitation-ip:" . hash("sha256", Request::ip() ?? "");

    if (RateLimiter::tooManyAttempts($ipKey, $this->settings->maxVerificationAttempts)) {
      $seconds = RateLimiter::availableIn($ipKey);
      $message = __("companies.errors.rate_limit", [
        "minutes" => ceil($seconds / 60),
      ]);

      throw new TooManyRequestsHttpException($seconds, $message);
    }

    RateLimiter::hit($ipKey, $this->settings->verificationTimeWindowMinutes * 60);

    $splitToken = SplitToken::fromParts($selector, $validator);

    return DB::transaction(function () use ($splitToken) {
      $invitation = CompanyInvitation::where("selector", $splitToken->selector())
        ->where("expires_at", ">", now())
        ->where("status", "pending")
        ->lockForUpdate()
        ->first();

      if ($invitation === null) {
        return false;
      }

      if (!hash_equals($invitation->token, $splitToken->hashedValidator())) {
        return false;
      }

      // Mark invitation as declined
      $invitation->update([
        "status" => "declined",
        "decided_at" => now(),
      ]);

      return true;
    });
  }

  /**
   * Generate an invitation token for the given user and company.
   */
  private function generateInvitationToken(User $user, Company $company): SplitToken
  {
    return DB::transaction(function () use ($user, $company) {
      $splitToken = SplitToken::generate();

      $currentUser = Auth::user();

      if ($currentUser === null) {
        throw new RuntimeException("User must be authenticated to send invitations");
      }

      CompanyInvitation::create([
        "company_id" => $company->id,
        "inviter_user_id" => $currentUser->id,
        "invitee_email" => $user->email,
        "invitee_user_id" => $user->id,
        "selector" => $splitToken->selector(),
        "token" => $splitToken->hashedValidator(),
        "status" => "pending",
        "expires_at" => now()->addMinutes($this->settings->tokenExpirationMinutes),
      ]);

      return $splitToken;
    });
  }

  /**
   * Generate an invitation link URL from a token.
   * @throws \RuntimeException
   */
  private function generateInvitationLink(SplitToken $token): string
  {
    $url = LaravelLocalization::getLocalizedURL(
      null,
      route("invitation.accept", [
        "selector" => $token->selector(),
        "validator" => $token->validator(),
      ]),
    );

    Assert::string($url);

    return $url;
  }
}
