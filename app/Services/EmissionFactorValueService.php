<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\InputMethod;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionYear;
use App\Models\EmissionFactorValue;
use App\Models\Year;
use Brick\Math\BigDecimal;
use Brick\Math\Exception\DivisionByZeroException;
use Brick\Math\Exception\NumberFormatException;
use Brick\Math\Exception\RoundingNecessaryException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use RuntimeException;

final class EmissionFactorValueService
{
  public function __construct(
    private CompanyContextService $companyContextService,
    private YearService $yearService,
  ) {}

  /**
   * Load values for a collection of detfinitions and all specified years
   *
   * @param  Collection<int, CalculationDefinition>  $definitions  Definitions collection
   * @param  list<int>  $years  Years to load values for
   * @return array{values: array<int, array<int, string|null>>, isDefault: array<int, array<int, bool>>, sources: array<int, array<int, string|null>>}
   *
   * @throws RuntimeException
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   */
  public function loadValuesForYears(Collection $definitions, array $years): array
  {
    $result = [
      "values" => [],
      "isDefault" => [],
      "sources" => [],
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return $result;
    }

    $companyId = $company->id;

    // Get Year models for the requested years
    $yearModels = $this->yearService->getYearsByValues($years);
    $yearIds = $yearModels->pluck("id")->toArray();
    $definitionIds = $definitions->pluck("id")->toArray();

    // Ensure all definitions have their years relationship loaded
    $definitions->loadMissing("years");

    // Load ALL values for ALL definitions in a SINGLE query
    $allValues = EmissionFactorValue::query()
      ->whereIn("emission_factor_definition_id", $definitionIds)
      ->where("company_id", $companyId)
      ->whereIn("year_id", $yearIds)
      ->with(["year", "selectedOption"])
      ->get();

    // Group values by definition_id for efficient access
    $valuesByDefinition = $allValues->groupBy("emission_factor_definition_id");

    // Now process each definition without additional queries
    foreach ($definitions as $definition) {
      $definitionId = $definition->id;

      // Initialize arrays for each definition
      $result["values"][$definitionId] = [];
      $result["isDefault"][$definitionId] = [];
      $result["sources"][$definitionId] = [];

      // Get values for this definition from our pre-loaded collection
      $definitionValues = $valuesByDefinition->get($definitionId);

      if ($definitionValues === null) {
        // No values exist for this definition - leave empty (don't populate with defaults)
        foreach ($years as $yearValue) {
          // Check if default exists to set isDefault flag
          $yearPivotModel = $definition->years->where("year", $yearValue)->first();
          $hasDefault = false;

          if ($yearPivotModel !== null) {
            $pivot = $yearPivotModel->getAttribute("pivot");
            Assert::instanceOf($pivot, CalculationDefinitionYear::class);

            if ($pivot->emission_factor_default_value !== null) {
              $inputMethod = $definition->input_method ?? InputMethod::MANUAL;
              // Only MANUAL inputs can have defaults
              $hasDefault = $inputMethod === InputMethod::MANUAL;
            }
          }

          // Don't populate the value, just set flags
          $result["values"][$definitionId][$yearValue] = null;
          $result["isDefault"][$definitionId][$yearValue] = $hasDefault;
          $result["sources"][$definitionId][$yearValue] = null;
        }

        continue; // Skip to next definition
      }

      // Create a map of year_id to value for this definition
      $valuesByYearId = $definitionValues->keyBy("year_id");

      // Fill in values for each year
      foreach ($years as $yearValue) {
        $yearModel = $yearModels->get($yearValue);

        if ($yearModel === null) {
          // Year doesn't exist in database
          $result["values"][$definitionId][$yearValue] = null;
          $result["isDefault"][$definitionId][$yearValue] = false;
          $result["sources"][$definitionId][$yearValue] = null;

          continue;
        }

        $emissionFactorValue = $valuesByYearId->get($yearModel->id);

        if ($emissionFactorValue !== null) {
          // Company-specific record exists for this year
          $inputMethod = $definition->input_method;

          if (
            $inputMethod === InputMethod::SELECT &&
            $emissionFactorValue->calculation_definition_option_id !== null
          ) {
            // For SELECT inputs, return the option ID for form binding
            $stringValue = (string) $emissionFactorValue->calculation_definition_option_id;
          } else {
            // For MANUAL inputs or if no option is selected, use the stored value
            // Handle case where value is null (source-only record)
            $stringValue = null;
            if ($emissionFactorValue->value !== null) {
              $stringValue = (string) BigDecimal::of(
                $emissionFactorValue->value,
              )->stripTrailingZeros();
            }
          }

          // Set the values - stringValue might be null for source-only records
          $result["values"][$definitionId][$yearValue] = $stringValue;

          // If we have a record but no value, check if there's a default
          if ($stringValue === null) {
            $yearPivotModel = $definition->years->where("year", $yearValue)->first();
            $hasDefault = false;

            if ($yearPivotModel !== null) {
              $pivot = $yearPivotModel->getAttribute("pivot");
              Assert::instanceOf($pivot, CalculationDefinitionYear::class);

              if (
                $pivot->emission_factor_default_value !== null &&
                $inputMethod === InputMethod::MANUAL
              ) {
                $hasDefault = true;
              }
            }
            $result["isDefault"][$definitionId][$yearValue] = $hasDefault;
          } else {
            // We have an actual value, so it's not using default
            $result["isDefault"][$definitionId][$yearValue] = false;
          }

          $result["sources"][$definitionId][$yearValue] = $emissionFactorValue->source ?? null;
        } else {
          // No company-specific record exists
          // Check if default exists to set isDefault flag
          $yearPivotModel = $definition->years->where("year", $yearValue)->first();
          if ($yearPivotModel !== null) {
            $pivot = $yearPivotModel->getAttribute("pivot");
            Assert::instanceOf($pivot, CalculationDefinitionYear::class);

            if ($pivot->emission_factor_default_value !== null) {
              // Check input method
              $inputMethod = $definition->input_method ?? InputMethod::MANUAL;

              if ($inputMethod === InputMethod::SELECT) {
                // No default values for SELECT inputs
                $result["values"][$definitionId][$yearValue] = null;
                $result["isDefault"][$definitionId][$yearValue] = false;
                $result["sources"][$definitionId][$yearValue] = null;
              } else {
                // Don't populate value, just mark as having default
                $result["values"][$definitionId][$yearValue] = null;
                $result["isDefault"][$definitionId][$yearValue] = true;
                $result["sources"][$definitionId][$yearValue] = null;
              }
            } else {
              // No default value
              $result["values"][$definitionId][$yearValue] = null;
              $result["isDefault"][$definitionId][$yearValue] = false;
              $result["sources"][$definitionId][$yearValue] = null;
            }
          } else {
            // No pivot model found
            $result["values"][$definitionId][$yearValue] = null;
            $result["isDefault"][$definitionId][$yearValue] = false;
            $result["sources"][$definitionId][$yearValue] = null;
          }
        }
      }
    }

    return $result;
  }

  /**
   * Find a value for a specific definition, company and year
   */
  public function findValue(int $definitionId, int $companyId, int $year): ?EmissionFactorValue
  {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return null;
    }

    return EmissionFactorValue::query()
      ->where("emission_factor_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();
  }

  /**
   * Save a value for a specific definition, company, and year
   *
   * @param  int  $definitionId  The definition ID
   * @param  string|null  $value  The value to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   * @throws RuntimeException
   */
  public function saveValue(int $definitionId, ?string $value, int $year): array
  {
    return DB::transaction(function () use ($definitionId, $value, $year) {
      $result = [
        "success" => false,
        "message" => "",
      ];

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $result["message"] = __("Yrityskonteksti puuttuu");
        return $result;
      }

      $companyId = $company->id;

      $yearModel = $this->yearService->getYearByValue($year);

      if ($yearModel === null) {
        $result["message"] = __("Vuosi :year ei ole käytettävissä", ["year" => $year]);

        return $result;
      }

      // Handle empty values
      if ($value === null || $value === "") {
        $existingValue = EmissionFactorValue::query()
          ->where("emission_factor_definition_id", $definitionId)
          ->where("company_id", $companyId)
          ->where("year_id", $yearModel->id)
          ->first();

        if ($existingValue !== null) {
          $existingValue->value = null;
          $existingValue->calculation_definition_option_id = null;
          $existingValue->save();
        }

        $result["message"] = __("Arvo poistettu");
        $result["success"] = true;

        return $result;
      }

      // Get the definition to check its input method
      $definition = CalculationDefinition::with(["optionSet.options.yearValues"])->find(
        $definitionId,
      );

      if ($definition === null) {
        $result["message"] = __("Määrittelyä ei löydy");
        return $result;
      }

      // Get input method
      $inputMethod = $definition->input_method ?? InputMethod::MANUAL;

      // Variables to store the actual value to save and the selected option ID
      $actualValue = null;
      $selectedOptionId = null;

      if ($inputMethod === InputMethod::SELECT) {
        // For SELECT, the $value is the option ID
        $selectedOption = $definition->optionSet?->options->find($value);

        if ($selectedOption === null) {
          $result["message"] = __("Valittu arvo ei ole kelvollinen");
          return $result;
        }

        // Find the year-specific value from the loaded relationship
        $yearValue = $selectedOption->yearValues->where("year_id", $yearModel->id)->first();

        if ($yearValue === null || $yearValue->value === null) {
          $result["message"] = __("Tälle vuodelle ei ole määritetty arvoa");
          return $result;
        }

        // For SELECT inputs: set option_id, leave value as NULL
        $actualValue = null;
        $selectedOptionId = $selectedOption->id;
      } elseif ($inputMethod === InputMethod::MANUAL) {
        // For MANUAL input method, ensure the value is a valid number and not negative
        $decimalValue = BigDecimal::of($value);
        if ($decimalValue->isLessThan(BigDecimal::zero())) {
          $result["message"] = __("Arvo ei voi olla alle nollan");
          return $result;
        }

        // For MANUAL inputs: set value, leave option_id as NULL
        $actualValue = (string) $decimalValue->stripTrailingZeros();
        $selectedOptionId = null;
      }

      // Check if a value for this definition, company, and year already exists
      $existingValue = EmissionFactorValue::query()
        ->where("emission_factor_definition_id", $definitionId)
        ->where("company_id", $companyId)
        ->where("year_id", $yearModel->id)
        ->first();

      if ($existingValue !== null) {
        // Update existing value
        $existingValue->value = $actualValue;
        $existingValue->calculation_definition_option_id = $selectedOptionId;
        $existingValue->save();
      } else {
        // Create new value
        EmissionFactorValue::create([
          "emission_factor_definition_id" => $definitionId,
          "company_id" => $companyId,
          "year_id" => $yearModel->id,
          "value" => $actualValue,
          "calculation_definition_option_id" => $selectedOptionId,
          "source" => "",
        ]);
      }

      $result["success"] = true;
      $result["message"] = __("Arvo päivitetty onnistuneesti");

      return $result;
    });
  }

  /**
   * Save a source for a specific definition, company, and year
   *
   * @param  int  $definitionId  The definition ID
   * @param  string  $source  The source to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws RuntimeException
   */
  public function saveSource(int $definitionId, string $source, int $year): array
  {
    return DB::transaction(function () use ($definitionId, $source, $year) {
      $result = [
        "success" => false,
        "message" => "",
      ];

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $result["message"] = __("Yrityskonteksti puuttuu");

        return $result;
      }

      $companyId = $company->id;

      // Get the Year model
      $yearModel = $this->yearService->getYearByValue($year);

      if ($yearModel === null) {
        $result["message"] = __("Vuosi :year ei ole käytettävissä", ["year" => $year]);

        return $result;
      }

      // Check if a value for this definition, company, and year already exists
      $existingValue = EmissionFactorValue::query()
        ->where("emission_factor_definition_id", $definitionId)
        ->where("company_id", $companyId)
        ->where("year_id", $yearModel->id)
        ->first();

      if ($existingValue !== null) {
        // Update existing value's source
        $existingValue->source = $source;
        $existingValue->save();
      } else {
        // Create a new record with just the source
        // Both MANUAL and SELECT inputs can have source-only records
        EmissionFactorValue::create([
          "emission_factor_definition_id" => $definitionId,
          "company_id" => $companyId,
          "year_id" => $yearModel->id,
          "value" => null, // Null for source-only records
          "calculation_definition_option_id" => null, // Null for source-only records
          "source" => $source,
        ]);
      }

      $result["success"] = true;
      $result["message"] = __("Lähde päivitetty onnistuneesti");

      return $result;
    });
  }
}
