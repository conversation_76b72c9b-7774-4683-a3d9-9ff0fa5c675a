<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\FormulaException;
use App\Helpers\Assert;
use Brick\Math\BigDecimal;
use Brick\Math\BigInteger;
use Brick\Math\RoundingMode;
use Exception;
use LogicException;
use <PERSON>ymfony\Component\ExpressionLanguage\ExpressionLanguage;

/**
 * Base service for expression language functionality
 * Provides Laravel Collection-style functions and math operations
 */
abstract class BaseExpressionService
{
  protected ?ExpressionLanguage $expressionLanguage = null;

  /**
   * Create a new ExpressionLanguage instance with base functions registered
   *
   * @throws LogicException
   */
  protected function createExpressionLanguage(): ExpressionLanguage
  {
    $expressionLanguage = new ExpressionLanguage();

    $this->disableDangerousFunctions($expressionLanguage);
    $this->registerMathFunctions($expressionLanguage);
    $this->registerArrayFunctions($expressionLanguage);
    $this->registerHelperFunctions($expressionLanguage);
    $this->registerCollectionFunctions($expressionLanguage);

    $this->expressionLanguage = $expressionLanguage;

    return $expressionLanguage;
  }

  /**
   * Disable potentially dangerous built-in functions
   *
   * @throws LogicException
   */
  protected function disableDangerousFunctions(ExpressionLanguage $expressionLanguage): void
  {
    // Override built-in constant() function to disable it
    $expressionLanguage->register(
      "constant",
      function (mixed $value): string {
        return "null";
      },
      function (array $arguments, mixed $value): null {
        throw new FormulaException("Function 'constant' is disabled");
      },
    );

    // Override built-in enum() function to disable it
    $expressionLanguage->register(
      "enum",
      function (mixed $value): string {
        return "null";
      },
      function (array $arguments, mixed $value): null {
        throw new FormulaException("Function 'enum' is disabled");
      },
    );
  }

  /**
   * Register Laravel Collection-style functions
   *
   * @throws LogicException
   */
  protected function registerCollectionFunctions(ExpressionLanguage $expressionLanguage): void
  {
    // Filter arrays by field and value (Laravel Collection style)
    $expressionLanguage->register(
      "where",
      function (mixed $array, string $field, mixed $operator = null, mixed $value = null): string {
        if (func_num_args() === 3) {
          return sprintf(
            "where(array, %s, %s)",
            var_export($field, true),
            var_export($operator, true),
          );
        }

        return sprintf(
          "where(array, %s, %s, %s)",
          var_export($field, true),
          var_export($operator, true),
          var_export($value, true),
        );
      },
      function (
        array $arguments,
        mixed $array,
        string $field,
        mixed $operator = null,
        mixed $value = null,
      ): array {
        if (!is_array($array)) {
          return [];
        }

        // Check number of arguments passed to determine behavior
        $numArgs = 2; // Start with array and field
        if ($operator !== null) {
          $numArgs++;
        }
        if ($value !== null) {
          $numArgs++;
        }

        // Check if operator is actually an operator string
        $validOperators = ["=", "==", "===", "!=", "!==", ">", ">=", "<", "<="];

        if ($numArgs === 3 && !in_array($operator, $validOperators, true)) {
          // We have 2 logical arguments: field, value (operator is actually the value)
          $value = $operator;
          $operator = "==";
        } elseif ($numArgs === 3 && in_array($operator, $validOperators, true)) {
          // We have field, operator, but no value - value is implicitly null
          $value = null;
        }
        // Otherwise we have 3 logical arguments: field, operator, value

        $result = [];
        foreach ($array as $key => $item) {
          $fieldValue = null;

          if (is_array($item)) {
            $fieldValue = array_key_exists($field, $item) ? $item[$field] : null;
          } elseif (is_object($item)) {
            if (property_exists($item, $field)) {
              // @phpstan-ignore-next-line property.dynamicName
              $fieldValue = $item->{$field};
            } else {
              $fieldValue = null;
            }
          } else {
            // If field doesn't exist, treat as null
            $fieldValue = null;
          }

          $match = match ($operator) {
            // @phpstan-ignore-next-line equal.notAllowed
            "=", "==" => $fieldValue ==
              $value, // Intentional loose comparison for Laravel compatibility
            "===" => $fieldValue === $value,
            // @phpstan-ignore-next-line notEqual.notAllowed
            "!=" => $fieldValue != $value, // Intentional loose comparison for Laravel compatibility
            "!==" => $fieldValue !== $value,
            ">" => $fieldValue > $value,
            ">=" => $fieldValue >= $value,
            "<" => $fieldValue < $value,
            "<=" => $fieldValue <= $value,
            default => false,
          };

          if ($match) {
            $result[$key] = $item;
          }
        }

        return $result;
      },
    );

    // Get first item where field matches value (Laravel Collection style)
    $expressionLanguage->register(
      "firstWhere",
      function (mixed $array, string $field, mixed $operator = null, mixed $value = null): string {
        if (func_num_args() === 3) {
          return sprintf(
            "firstWhere(array, %s, %s)",
            var_export($field, true),
            var_export($operator, true),
          );
        }

        return sprintf(
          "firstWhere(array, %s, %s, %s)",
          var_export($field, true),
          var_export($operator, true),
          var_export($value, true),
        );
      },
      function (
        array $arguments,
        mixed $array,
        string $field,
        mixed $operator = null,
        mixed $value = null,
      ): mixed {
        if (!is_array($array)) {
          return null;
        }

        // Check number of arguments passed to determine behavior
        $numArgs = 2; // Start with array and field
        if ($operator !== null) {
          $numArgs++;
        }
        if ($value !== null) {
          $numArgs++;
        }

        // Check if operator is actually an operator string
        $validOperators = ["=", "==", "===", "!=", "!==", ">", ">=", "<", "<="];

        if ($numArgs === 3 && !in_array($operator, $validOperators, true)) {
          // We have 2 logical arguments: field, value (operator is actually the value)
          $value = $operator;
          $operator = "==";
        } elseif ($numArgs === 3 && in_array($operator, $validOperators, true)) {
          // We have field, operator, but no value - value is implicitly null
          $value = null;
        }
        // Otherwise we have 3 logical arguments: field, operator, value

        foreach ($array as $item) {
          $fieldValue = null;

          if (is_array($item)) {
            $fieldValue = array_key_exists($field, $item) ? $item[$field] : null;
          } elseif (is_object($item)) {
            if (property_exists($item, $field)) {
              // @phpstan-ignore-next-line property.dynamicName
              $fieldValue = $item->{$field};
            } else {
              continue;
            }
          } else {
            continue;
          }

          $match = match ($operator) {
            // @phpstan-ignore-next-line equal.notAllowed
            "=", "==" => $fieldValue ==
              $value, // Intentional loose comparison for Laravel compatibility
            "===" => $fieldValue === $value,
            // @phpstan-ignore-next-line notEqual.notAllowed
            "!=" => $fieldValue != $value, // Intentional loose comparison for Laravel compatibility
            "!==" => $fieldValue !== $value,
            ">" => $fieldValue > $value,
            ">=" => $fieldValue >= $value,
            "<" => $fieldValue < $value,
            "<=" => $fieldValue <= $value,
            default => false,
          };

          if ($match) {
            return $item;
          }
        }

        return null;
      },
    );

    // Get array keys
    $expressionLanguage->register(
      "keys",
      function (mixed $array): string {
        return "keys(array)";
      },
      function (array $arguments, mixed $array): array {
        if (!is_array($array)) {
          return [];
        }

        return array_keys($array);
      },
    );

    // Get array values (reindex) - Laravel Collection style
    $expressionLanguage->register(
      "values",
      function (mixed $array): string {
        return "values(array)";
      },
      function (array $arguments, mixed $array): array {
        if (!is_array($array)) {
          return [];
        }

        return array_values($array);
      },
    );

    // Group array by field value (Laravel Collection style)
    $expressionLanguage->register(
      "groupBy",
      function (mixed $array, string $field): string {
        return sprintf("groupBy(array, %s)", var_export($field, true));
      },
      function (array $arguments, mixed $array, string $field): array {
        if (!is_array($array)) {
          return [];
        }

        $grouped = [];
        foreach ($array as $key => $item) {
          $groupKey = null;

          if (is_array($item)) {
            $groupKey = array_key_exists($field, $item) ? $item[$field] : null;
          } elseif (is_object($item)) {
            if (property_exists($item, $field)) {
              // @phpstan-ignore-next-line property.dynamicName
              $groupKey = $item->{$field};
            } else {
              $groupKey = null;
            }
          } else {
            $groupKey = "null";
          }

          if ($groupKey === null) {
            $groupKey = "null";
          }

          if (!is_scalar($groupKey)) {
            $groupKey = json_encode($groupKey);
          }

          $grouped[$groupKey][$key] = $item;
        }

        return $grouped;
      },
    );

    // Map expression over array (needs expression string for complex operations)
    $expressionLanguage->register(
      "map",
      function (mixed $array, string $expression): string {
        return sprintf("map(array, '%s')", $expression);
      },
      function (array $arguments, mixed $array, string $expression): array {
        if (!is_array($array)) {
          return [];
        }

        if ($this->expressionLanguage === null) {
          $this->createExpressionLanguage();
        }

        Assert::notNull($this->expressionLanguage);

        $results = [];
        foreach ($array as $key => $item) {
          $localArgs = array_merge($arguments, [
            "_" => $item,
            "_key" => $key,
          ]);

          try {
            $results[] = $this->expressionLanguage->evaluate($expression, $localArgs);
          } catch (Exception $e) {
            throw new FormulaException("Error in map expression: " . $e->getMessage());
          }
        }

        return $results;
      },
    );

    // Flatten one level of array nesting
    $expressionLanguage->register(
      "flatten",
      function (mixed $array): string {
        return "flatten(array)";
      },
      function (array $arguments, mixed $array): array {
        if (!is_array($array)) {
          return [];
        }

        $result = [];
        foreach ($array as $item) {
          if (is_array($item)) {
            $result = array_merge($result, $item);
          } else {
            $result[] = $item;
          }
        }

        return $result;
      },
    );

    // Get field value from object/array (Laravel Collection style)
    $expressionLanguage->register(
      "get",
      function (mixed $target, string $field, mixed $default = null): string {
        if ($default === null) {
          return sprintf("get(target, %s)", var_export($field, true));
        }

        return sprintf("get(target, %s, %s)", var_export($field, true), var_export($default, true));
      },
      function (array $arguments, mixed $target, string $field, mixed $default = null): mixed {
        if (is_array($target)) {
          return array_key_exists($field, $target) ? $target[$field] : $default;
        }
        if (is_object($target)) {
          if (property_exists($target, $field)) {
            // @phpstan-ignore-next-line property.dynamicName
            return $target->{$field};
          }
        }

        return $default;
      },
    );
  }

  /**
   * Register mathematical functions with BigDecimal support
   *
   * @throws LogicException
   */
  protected function registerMathFunctions(ExpressionLanguage $expressionLanguage): void
  {
    // BigDecimal-based addition function for precision
    $expressionLanguage->register(
      "add",
      function (mixed ...$values): string {
        $args = array_map(fn($v) => is_scalar($v) ? (string) $v : "0", $values);

        return sprintf("add(%s)", implode(", ", $args));
      },
      function (array $arguments, mixed ...$values): string {
        if (count($values) === 0) {
          throw new FormulaException("add() function requires at least one value");
        }

        $result = BigDecimal::of("0");
        foreach ($values as $index => $value) {
          if (!is_numeric($value)) {
            throw new FormulaException(
              "add() function argument at position {$index} is not numeric",
            );
          }
          $result = $result->plus(BigDecimal::of((string) $value));
        }

        return (string) $result;
      },
    );

    // BigDecimal-based subtraction function for precision
    $expressionLanguage->register(
      "subtract",
      function (mixed $a, mixed $b): string {
        $aStr = is_scalar($a) ? (string) $a : "0";
        $bStr = is_scalar($b) ? (string) $b : "0";

        return sprintf("subtract(%s, %s)", $aStr, $bStr);
      },
      function (array $arguments, mixed $a, mixed $b): string {
        if (!is_numeric($a) || !is_numeric($b)) {
          throw new FormulaException("subtract() function requires numeric arguments");
        }

        $result = BigDecimal::of((string) $a)->minus(BigDecimal::of((string) $b));

        return (string) $result;
      },
    );

    // BigDecimal-based multiplication function for precision
    $expressionLanguage->register(
      "multiply",
      function (mixed ...$values): string {
        $args = array_map(fn($v) => is_scalar($v) ? (string) $v : "0", $values);

        return sprintf("multiply(%s)", implode(", ", $args));
      },
      function (array $arguments, mixed ...$values): string {
        if (count($values) === 0) {
          throw new FormulaException("multiply() function requires at least one value");
        }

        $result = BigDecimal::of("1");
        foreach ($values as $index => $value) {
          if (!is_numeric($value)) {
            throw new FormulaException(
              "multiply() function argument at position {$index} is not numeric",
            );
          }
          $result = $result->multipliedBy(BigDecimal::of((string) $value));
        }

        return (string) $result;
      },
    );

    // BigDecimal-based division function for precision
    $expressionLanguage->register(
      "divide",
      function (mixed $a, mixed $b, mixed $scale = 10): string {
        $aStr = is_scalar($a) ? (string) $a : "0";
        $bStr = is_scalar($b) ? (string) $b : "1";
        $scaleStr = is_scalar($scale) ? (string) $scale : "10";

        return sprintf("divide(%s, %s, %s)", $aStr, $bStr, $scaleStr);
      },
      function (array $arguments, mixed $a, mixed $b, mixed $scale = 10): string {
        if (!is_numeric($a) || !is_numeric($b)) {
          throw new FormulaException("divide() function requires numeric arguments");
        }

        $divisor = BigDecimal::of((string) $b);
        if ($divisor->isZero()) {
          throw new FormulaException("Division by zero in divide() function");
        }

        $scaleInt = is_numeric($scale) ? (int) $scale : 10;
        $scaleInt = min(max($scaleInt, 0), 20);

        $result = BigDecimal::of((string) $a)->dividedBy(
          $divisor,
          $scaleInt,
          RoundingMode::HALF_UP,
        );

        return (string) $result;
      },
    );

    // BigDecimal-based sum function
    $expressionLanguage->register(
      "sum",
      function (mixed ...$values): string {
        $args = array_map(fn($v) => is_scalar($v) ? (string) $v : "0", $values);

        return sprintf("sum(%s)", implode(", ", $args));
      },
      function (array $arguments, mixed ...$values): string {
        // Handle if first argument is an array
        if (count($values) === 1 && isset($values[0]) && is_array($values[0])) {
          $values = $values[0];
        }

        if (count($values) === 0) {
          return "0";
        }

        $result = BigDecimal::of("0");
        foreach ($values as $value) {
          if (!is_numeric($value)) {
            // Skip non-numeric values in sum
            continue;
          }
          $result = $result->plus(BigDecimal::of((string) $value));
        }

        return (string) $result;
      },
    );

    // Absolute value
    $expressionLanguage->register(
      "abs",
      function (mixed $value): string {
        $valueStr = is_scalar($value) ? (string) $value : "0";

        return sprintf("abs(%s)", $valueStr);
      },
      function (array $arguments, mixed $value): string {
        if (!is_numeric($value)) {
          throw new FormulaException("abs() function requires numeric argument");
        }

        $decimal = BigDecimal::of($value);
        $result = $decimal->abs();

        return (string) $result;
      },
    );

    // Rounding
    $expressionLanguage->register(
      "round",
      function (mixed $value, mixed $precision = 0): string {
        $valueStr = is_scalar($value) ? (string) $value : "0";
        $precisionStr = is_scalar($precision) ? (string) $precision : "0";

        return sprintf("round(%s, %s)", $valueStr, $precisionStr);
      },
      function (array $arguments, mixed $value, mixed $precision = 0): string {
        if (!is_numeric($value)) {
          throw new FormulaException("round() function requires numeric value");
        }
        if (!is_numeric($precision)) {
          throw new FormulaException("round() function requires numeric precision");
        }

        $precisionBigInt = BigInteger::of($precision);

        if ($precisionBigInt->isLessThan(BigInteger::zero())) {
          $precisionBigInt = BigInteger::zero();
        }
        if ($precisionBigInt->isGreaterThan(BigInteger::of("10"))) {
          $precisionBigInt = BigInteger::of("10");
        }

        $intPrecision = $precisionBigInt->toInt();

        $decimal = BigDecimal::of($value);
        $result = $decimal->toScale($intPrecision, RoundingMode::HALF_UP);

        return (string) $result;
      },
    );

    // Power
    $expressionLanguage->register(
      "pow",
      function (mixed $base, mixed $exponent): string {
        $baseStr = is_scalar($base) ? (string) $base : "0";
        $exponentStr = is_scalar($exponent) ? (string) $exponent : "0";

        return sprintf("pow(%s, %s)", $baseStr, $exponentStr);
      },
      function (array $arguments, mixed $base, mixed $exponent): string {
        if (!is_numeric($base) || !is_numeric($exponent)) {
          throw new FormulaException("pow() function requires numeric arguments");
        }

        $baseDecimal = BigDecimal::of($base);
        $expBigInt = BigInteger::of($exponent);
        $expInt = $expBigInt->toInt();

        if (abs($expInt) > 100) {
          throw new FormulaException("pow() function exponent too large (max 100)");
        }

        // Handle negative exponents: x^(-n) = 1/(x^n)
        if ($expInt < 0) {
          $positiveExpInt = abs($expInt);
          $positiveResult = $baseDecimal->power($positiveExpInt);
          if ($positiveResult->isZero()) {
            throw new FormulaException("pow() function: division by zero (base^exponent = 0)");
          }
          $result = BigDecimal::of("1")->dividedBy($positiveResult, 10, RoundingMode::HALF_UP);
        } else {
          $result = $baseDecimal->power($expInt);
        }

        return (string) $result;
      },
    );

    // Square root
    $expressionLanguage->register(
      "sqrt",
      function (mixed $value): string {
        $valueStr = is_scalar($value) ? (string) $value : "0";

        return sprintf("sqrt(%s)", $valueStr);
      },
      function (array $arguments, mixed $value): string {
        if (!is_numeric($value)) {
          throw new FormulaException("sqrt() function requires numeric argument");
        }

        $decimal = BigDecimal::of((string) $value);

        if ($decimal->isNegative()) {
          throw new FormulaException(
            "sqrt() function cannot calculate square root of negative number",
          );
        }

        if ($decimal->isZero()) {
          return "0";
        }

        // Safety check for extreme values
        $max = BigDecimal::of("10")->power(50); // 10^50
        $min = BigDecimal::of("1")->dividedBy(
          BigDecimal::of("10")->power(50),
          100,
          RoundingMode::HALF_UP,
        ); // 10^-50

        if ($decimal->isGreaterThan($max) || $decimal->isLessThan($min)) {
          throw new FormulaException("sqrt() function input out of reasonable range");
        }

        // Newton's method for square root
        $guess = $decimal;
        $two = BigDecimal::of("2");

        while (true) {
          $newGuess = $guess
            ->plus($decimal->dividedBy($guess, 20, RoundingMode::HALF_UP))
            ->dividedBy($two, 20, RoundingMode::HALF_UP);

          if (
            $newGuess
              ->toScale(10, RoundingMode::HALF_UP)
              ->isEqualTo($guess->toScale(10, RoundingMode::HALF_UP))
          ) {
            break;
          }

          $guess = $newGuess;
        }

        return (string) $guess->toScale(10, RoundingMode::HALF_UP);
      },
    );

    // Min function
    $expressionLanguage->register(
      "min",
      function (...$values): string {
        $valueStrs = array_map(fn(mixed $v): string => is_scalar($v) ? (string) $v : "0", $values);

        return sprintf("min(%s)", implode(", ", $valueStrs));
      },
      function (array $arguments, ...$values): mixed {
        if (count($values) === 0) {
          return null;
        }

        $numericValues = array_filter($values, fn(mixed $v): bool => is_numeric($v));

        if (count($numericValues) === 0) {
          return null;
        }

        return min($numericValues);
      },
    );

    // Max function
    $expressionLanguage->register(
      "max",
      function (...$values): string {
        $valueStrs = array_map(fn(mixed $v): string => is_scalar($v) ? (string) $v : "0", $values);

        return sprintf("max(%s)", implode(", ", $valueStrs));
      },
      function (array $arguments, ...$values): mixed {
        if (count($values) === 0) {
          return null;
        }

        $numericValues = array_filter($values, fn(mixed $v): bool => is_numeric($v));

        if (count($numericValues) === 0) {
          return null;
        }

        return max($numericValues);
      },
    );
  }

  /**
   * Register array and collection functions
   *
   * @throws LogicException
   */
  protected function registerArrayFunctions(ExpressionLanguage $expressionLanguage): void
  {
    // Register in_array() to check if a value exists in an array
    $expressionLanguage->register(
      "in_array",
      function (mixed $needle, mixed $haystack): string {
        $needleStr = is_scalar($needle) ? (string) $needle : "null";
        if (is_array($haystack)) {
          $haystackStr =
            "[" .
            implode(
              ", ",
              array_map(fn(mixed $v): string => is_scalar($v) ? (string) $v : "null", $haystack),
            ) .
            "]";
        } else {
          $haystackStr = "[]";
        }

        return sprintf("in_array(%s, %s)", $needleStr, $haystackStr);
      },
      function (array $arguments, mixed $needle, mixed $haystack): bool {
        if (!is_array($haystack)) {
          return false;
        }

        return in_array($needle, $haystack, true);
      },
    );

    // Register count() function for arrays
    $expressionLanguage->register(
      "count",
      function (mixed $value): string {
        return "count(array)";
      },
      function (array $arguments, mixed $value): int {
        if (is_array($value)) {
          return count($value);
        }

        return 0;
      },
    );

    // Register range() function to create ranges of numbers
    $expressionLanguage->register(
      "range",
      function (mixed $start, mixed $end, mixed $step = 1): string {
        $startStr = is_scalar($start) ? (string) $start : "0";
        $endStr = is_scalar($end) ? (string) $end : "0";
        $stepStr = is_scalar($step) ? (string) $step : "1";

        return sprintf("range(%s, %s, %s)", $startStr, $endStr, $stepStr);
      },
      function (array $arguments, mixed $start, mixed $end, mixed $step = 1): array {
        if (!is_numeric($start) || !is_numeric($end) || !is_numeric($step)) {
          return [];
        }

        // Convert to BigInteger for precise integer math
        $bigStart = BigInteger::of($start);
        $bigEnd = BigInteger::of($end);
        $bigStep = BigInteger::of($step);

        if ($bigStep->isZero()) {
          return [];
        }

        // Calculate size for safety check
        $difference = $bigEnd->minus($bigStart)->abs();
        $stepAbs = $bigStep->abs();
        $size = $difference->dividedBy($stepAbs)->plus(BigInteger::one());

        // Limit range size for safety
        $maxSize = BigInteger::of(1000);
        if ($size->isGreaterThan($maxSize)) {
          return [];
        }

        $result = [];

        if ($bigStep->isPositive()) {
          $current = $bigStart;
          while ($current->isLessThanOrEqualTo($bigEnd)) {
            $result[] = $current->toInt();
            $current = $current->plus($bigStep);
          }
        } else {
          $current = $bigStart;
          while ($current->isGreaterThanOrEqualTo($bigEnd)) {
            $result[] = $current->toInt();
            $current = $current->plus($bigStep);
          }
        }

        return $result;
      },
    );
  }

  /**
   * Register helper functions
   *
   * @throws LogicException
   */
  protected function registerHelperFunctions(ExpressionLanguage $expressionLanguage): void
  {
    // Register between() helper for checking if a value is between two numbers
    $expressionLanguage->register(
      "between",
      function (mixed $value, mixed $min, mixed $max): string {
        $valueStr = is_scalar($value) ? (string) $value : "0";
        $minStr = is_scalar($min) ? (string) $min : "0";
        $maxStr = is_scalar($max) ? (string) $max : "0";

        return sprintf("between(%s, %s, %s)", $valueStr, $minStr, $maxStr);
      },
      function (array $arguments, mixed $value, mixed $min, mixed $max): bool {
        if (!is_numeric($value) || !is_numeric($min) || !is_numeric($max)) {
          return false;
        }

        // Use BigDecimal for precise comparisons
        $decimalValue = BigDecimal::of((string) $value);
        $decimalMin = BigDecimal::of((string) $min);
        $decimalMax = BigDecimal::of((string) $max);

        return $decimalValue->isGreaterThanOrEqualTo($decimalMin) &&
          $decimalValue->isLessThanOrEqualTo($decimalMax);
      },
    );
  }
}
