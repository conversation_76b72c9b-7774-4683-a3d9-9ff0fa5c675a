<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\CalculationDefinition;
use App\Models\DataValue;
use Brick\Math\BigDecimal;
use Illuminate\Database\Eloquent\Collection;
use RuntimeException;

final class DataValueService
{
  public function __construct(
    private CompanyContextService $companyContextService,
    private YearService $yearService,
  ) {}

  /**
   * Load values for a collection of definitions and all specified years
   *
   * @param  Collection<int, CalculationDefinition>  $definitions  Definitions collection
   * @param  list<int>  $years  Years to load values for
   * @return array{values: array<int, array<int, string|null>>, sources: array<int, array<int, string|null>>}
   *
   * @throws RuntimeException
   */
  public function loadValuesForYears(Collection $definitions, array $years): array
  {
    $result = [
      "values" => [],
      "sources" => [],
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return $result;
    }

    $companyId = $company->id;

    // Get Year models for the requested years
    $yearModels = $this->yearService->getYearsByValues($years);
    $yearIds = $yearModels->pluck("id")->toArray();
    $definitionIds = $definitions->pluck("id")->toArray();

    // Load ALL values for ALL definitions in a SINGLE query
    $allValues = DataValue::query()
      ->whereIn("data_definition_id", $definitionIds)
      ->where("company_id", $companyId)
      ->whereIn("year_id", $yearIds)
      ->with("year")
      ->get();

    // Group values by definition_id for efficient access
    $valuesByDefinition = $allValues->groupBy("data_definition_id");

    // Now process each definition without additional queries
    foreach ($definitions as $definition) {
      $definitionId = $definition->id;

      // Initialize arrays for each definition
      $result["values"][$definitionId] = [];
      $result["sources"][$definitionId] = [];

      // Get values for this definition from our pre-loaded collection
      $definitionValues = $valuesByDefinition->get($definitionId);

      if ($definitionValues === null) {
        // No values exist for this definition - fill all years with null
        foreach ($years as $yearValue) {
          $result["values"][$definitionId][$yearValue] = null;
          $result["sources"][$definitionId][$yearValue] = null;
        }

        continue; // Skip to next definition
      }

      // Create a map of year_id to value for this definition
      $valuesByYearId = $definitionValues->keyBy("year_id");

      // Fill in values for each year
      foreach ($years as $yearValue) {
        $yearModel = $yearModels->get($yearValue);

        if ($yearModel === null) {
          // Year doesn't exist in database
          $result["values"][$definitionId][$yearValue] = null;
          $result["sources"][$definitionId][$yearValue] = null;

          continue;
        }
        $dataValue = $valuesByYearId->get($yearModel->id);

        if ($dataValue !== null) {
          // Company-specific value exists for this year
          $stringValue =
            $dataValue->value !== null
              ? (string) BigDecimal::of($dataValue->value)->stripTrailingZeros()
              : null;
          $result["values"][$definitionId][$yearValue] = $stringValue;
          $result["sources"][$definitionId][$yearValue] = $dataValue->source ?? null;
        } else {
          // No value at all
          $result["values"][$definitionId][$yearValue] = null;
          $result["sources"][$definitionId][$yearValue] = null;
        }
      }
    }

    return $result;
  }

  /**
   * Find a value for a specific definition, company and year
   */
  public function findValue(int $definitionId, int $companyId, int $year): ?DataValue
  {
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      return null;
    }

    return DataValue::query()
      ->where("data_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();
  }

  /**
   * Save a value for a specific definition, company, and year
   *
   * @param  int  $definitionId  The definition ID
   * @param  string|null  $value  The value to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws RuntimeException
   */
  public function saveValue(int $definitionId, ?string $value, int $year): array
  {
    $result = [
      "success" => false,
      "message" => "",
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      $result["message"] = __("Yrityskonteksti puuttuu");
      return $result;
    }

    $companyId = $company->id;

    // Get the Year model
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      $result["message"] = __("Vuosi :year ei ole käytettävissä", ["year" => $year]);
      return $result;
    }

    // Handle empty values
    if ($value === null || $value === "") {
      $existingValue = DataValue::query()
        ->where("data_definition_id", $definitionId)
        ->where("company_id", $companyId)
        ->where("year_id", $yearModel->id)
        ->first();

      if ($existingValue !== null) {
        $existingValue->value = null;
        $existingValue->save();
      }

      $result["success"] = true;
      $result["message"] = __("Arvo poistettu");

      return $result;
    }

    // Validate non-empty values
    if (BigDecimal::of($value)->isLessThan(BigDecimal::zero())) {
      $result["message"] = __("Arvo ei voi olla alle nollan");
      return $result;
    }

    // Check if a value for this definition, company, and year already exists
    $existingValue = DataValue::query()
      ->where("data_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();

    if ($existingValue !== null) {
      // Update existing value
      $existingValue->value = $value;
      $existingValue->save();
    } else {
      // Create new value
      DataValue::create([
        "data_definition_id" => $definitionId,
        "company_id" => $companyId,
        "year_id" => $yearModel->id,
        "value" => $value,
        "source" => "",
      ]);
    }

    $result["success"] = true;
    $result["message"] = __("Arvo päivitetty onnistuneesti");

    return $result;
  }

  /**
   * Save a source for a specific definition, company, and year
   *
   * @param  int  $definitionId  The definition ID
   * @param  string  $source  The source to save
   * @param  int  $year  The year
   * @return array{success: bool, message: string}
   *
   * @throws RuntimeException
   */
  public function saveSource(int $definitionId, string $source, int $year): array
  {
    $result = [
      "success" => false,
      "message" => "",
    ];

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      $result["message"] = __("Yrityskonteksti puuttuu");

      return $result;
    }

    $companyId = $company->id;

    // Get the Year model
    $yearModel = $this->yearService->getYearByValue($year);

    if ($yearModel === null) {
      $result["message"] = __("Vuosi :year ei ole käytettävissä", ["year" => $year]);

      return $result;
    }

    // Check if a value for this definition, company, and year already exists
    $existingValue = DataValue::query()
      ->where("data_definition_id", $definitionId)
      ->where("company_id", $companyId)
      ->where("year_id", $yearModel->id)
      ->first();

    if ($existingValue !== null) {
      // Update existing value's source
      $existingValue->source = $source;
      $existingValue->save();
    } else {
      // Create new value with source
      if ($source !== "") {
        DataValue::create([
          "data_definition_id" => $definitionId,
          "company_id" => $companyId,
          "year_id" => $yearModel->id,
          "value" => null,
          "source" => $source,
        ]);
      }
    }

    $result["success"] = true;
    $result["message"] = __("Lähde päivitetty onnistuneesti");

    return $result;
  }
}
