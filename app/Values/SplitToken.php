<?php

declare(strict_types=1);

namespace App\Values;

use Illuminate\Support\Str;
use InvalidArgumentException;
use Ramsey\Uuid\Uuid;
use SensitiveParameter;

final readonly class SplitToken
{
  private function __construct(
    private string $selector,
    #[SensitiveParameter] private string $validator,
  ) {}

  /**
   * Create a new token with UUID7 selector and random validator.
   */
  public static function generate(): self
  {
    $selector = Uuid::uuid7()->toString();
    $validator = Str::random(64);

    return new self($selector, $validator);
  }

  /**
   * Create a token from separate selector and validator.
   *
   * @throws InvalidArgumentException When the selector or validator format is invalid
   */
  public static function fromParts(string $selector, #[SensitiveParameter] string $validator): self
  {
    if (!Uuid::isValid($selector)) {
      throw new InvalidArgumentException("Invalid token format: selector is not a valid UUID");
    }

    if (preg_match('/^[a-zA-Z0-9]{64}$/', $validator) !== 1) {
      throw new InvalidArgumentException("Invalid token format: validator has invalid format");
    }

    return new self($selector, $validator);
  }

  /**
   * Get the selector part of the token.
   */
  public function selector(): string
  {
    return $this->selector;
  }

  /**
   * Get the validator part of the token.
   */
  public function validator(): string
  {
    return $this->validator;
  }

  /**
   * Get the hashed validator.
   */
  public function hashedValidator(): string
  {
    return hash("sha256", $this->validator);
  }
}
