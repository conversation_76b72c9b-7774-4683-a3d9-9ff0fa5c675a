<?php

declare(strict_types=1);

namespace App\Values;

use Illuminate\Support\Str;
use InvalidArgumentException;
use Ramsey\Uuid\Uuid;
use SensitiveParameter;

final readonly class SplitToken
{
  private function __construct(
    private string $selector,
    #[SensitiveParameter] private string $validator,
  ) {}

  /**
   * Convert the token to a string when used as a string.
   */
  public function __toString(): string
  {
    return $this->toString();
  }

  /**
   * Create a new token with UUID7 selector and random validator.
   *
   * @throws InvalidArgumentException
   */
  public static function generate(): self
  {
    $selector = Uuid::uuid7()->toString();
    $validator = Str::random(64);

    return self::fromString("$selector.$validator");
  }

  /**
   * Parse a token string into a SplitToken object.
   *
   * @throws InvalidArgumentException When the token format is invalid
   */
  public static function fromString(#[SensitiveParameter] string $tokenString): self
  {
    $parts = explode(".", $tokenString, 2);

    if (count($parts) !== 2) {
      throw new InvalidArgumentException("Invalid token format: missing delimiter");
    }

    [$selector, $validator] = $parts;

    if (!Uuid::isValid($selector)) {
      throw new InvalidArgumentException("Invalid token format: selector is not a valid UUID");
    }

    if (preg_match('/^[a-zA-Z0-9]{64}$/', $validator) !== 1) {
      throw new InvalidArgumentException("Invalid token format: validator has invalid format");
    }

    return new self($selector, $validator);
  }

  /**
   * Get the selector part of the token.
   */
  public function selector(): string
  {
    return $this->selector;
  }

  /**
   * Get the validator part of the token.
   */
  public function validator(): string
  {
    return $this->validator;
  }

  /**
   * Get the hashed validator.
   */
  public function hashedValidator(): string
  {
    return hash("sha256", $this->validator);
  }

  /**
   * Convert the token to a string.
   */
  public function toString(): string
  {
    return $this->selector . "." . $this->validator;
  }
}
