<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Support\Facades\Session;
use Illuminate\View\Component;
use Illuminate\View\View;
use RuntimeException;
use Stringable;

final class FlashMessage extends Component
{
  public ?string $message = null;

  public ?string $type = null;

  /**
   * Create a new component instance.
   *
   * @throws RuntimeException
   */
  public function __construct()
  {
    $this->initializeFromSession();
  }

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.flash-message");
  }

  /**
   * Initialize message and type from session flash data.
   *
   * @throws RuntimeException
   */
  private function initializeFromSession(): void
  {
    $flashTypes = ["success", "error", "warning", "info"];

    foreach ($flashTypes as $flashType) {
      $message = $this->getStringFromSession($flashType);
      if ($message !== null) {
        $this->message = $message;
        $this->type = $flashType;
        break;
      }
    }
  }

  /**
   * Get a string value from session or null if not a valid string.
   *
   * @throws RuntimeException
   */
  private function getStringFromSession(string $key): ?string
  {
    if (!Session::has($key)) {
      return null;
    }

    $value = Session::get($key);

    if (is_string($value)) {
      return $value;
    }

    if ($value instanceof Stringable) {
      return (string) $value;
    }

    return null;
  }
}
