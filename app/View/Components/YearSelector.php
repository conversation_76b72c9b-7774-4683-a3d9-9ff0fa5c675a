<?php

declare(strict_types=1);
// app/View/Components/YearSelector.php

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class YearSelector extends Component
{
  /**
   * Create a new component instance.
   *
   * @param  list<int>  $years
   */
  public function __construct(
    public array $years,
    public int $currentYear,
    public string $routeName = "data",
  ) {}

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.year-selector");
  }
}
