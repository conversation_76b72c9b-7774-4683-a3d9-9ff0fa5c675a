<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class FormTextarea extends Component
{
  /**
   * The textarea id.
   */
  public string $id;

  /**
   * The textarea label.
   */
  public string $label;

  /**
   * The error message.
   */
  public ?string $error;

  /**
   * Whether the textarea has an error.
   */
  public bool $hasError;

  /**
   * The textarea placeholder text.
   */
  public string $placeholder;

  /**
   * The number of rows for the textarea.
   */
  public int $rows;

  /**
   * Create a new component instance.
   */
  public function __construct(
    string $id,
    string $label,
    ?string $error = null,
    ?string $placeholder = null,
    int $rows = 4,
  ) {
    $this->id = $id;
    $this->label = $label;
    $this->error = $error;
    $this->hasError = $error !== null && $error !== "";
    $this->placeholder = $placeholder ?? "";
    $this->rows = $rows;
  }

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.form-textarea");
  }
}
