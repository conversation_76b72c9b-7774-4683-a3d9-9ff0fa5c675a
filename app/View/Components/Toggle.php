<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class Toggle extends Component
{
  /**
   * Mapping of color to CSS classes.
   *
   * @var array<string, string>
   */
  protected const COLOR_CLASSES = [
    "blue" => "bg-blue-900",
    "gray" => "bg-gray-600",
  ];

  /**
   * The toggle input ID.
   */
  public string $id;

  /**
   * The toggle label text.
   */
  public ?string $label;

  /**
   * The position of the label.
   */
  public string $labelPosition;

  /**
   * The color of the toggle.
   */
  public string $color;

  /**
   * Whether the toggle is disabled.
   */
  public bool $disabled;

  /**
   * The error message.
   */
  public ?string $error;

  /**
   * Whether the input has an error.
   */
  public bool $hasError;

  /**
   * Create a new component instance.
   */
  public function __construct(
    string $id,
    ?string $label = null,
    string $labelPosition = "right",
    string $color = "blue",
    bool $disabled = false,
    ?string $error = null,
  ) {
    $this->id = $id;
    $this->label = $label;
    $this->labelPosition = $labelPosition;
    $this->color = $color;
    $this->disabled = $disabled;
    $this->error = $error;
    $this->hasError = $error !== null && $error !== "";
  }

  /**
   * Get the color class for the toggle.
   */
  public function colorClass(): string
  {
    return self::COLOR_CLASSES[$this->color] ?? self::COLOR_CLASSES["blue"];
  }

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.toggle");
  }
}
