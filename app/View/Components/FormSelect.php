<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class FormSelect extends Component
{
  /**
   * The select id.
   */
  public string $id;

  /**
   * The select label.
   */
  public string $label;

  /**
   * The select options.
   *
   * @var array<int, \App\DataTransferObjects\SelectOption>
   */
  public array $options;

  /**
   * The error message.
   */
  public ?string $error;

  /**
   * Whether the select has an error.
   */
  public bool $hasError;

  /**
   * The select placeholder text.
   */
  public string $placeholder;

  /**
   * Create a new component instance.
   *
   * @param  array<int, \App\DataTransferObjects\SelectOption>  $options
   */
  public function __construct(
    string $id,
    string $label,
    array $options = [],
    ?string $error = null,
    ?string $placeholder = null,
  ) {
    $this->id = $id;
    $this->label = $label;
    $this->options = $options;
    $this->error = $error;
    $this->hasError = $error !== null && $error !== "";
    $this->placeholder = $placeholder ?? "Valitse...";
  }

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.form-select");
  }
}
