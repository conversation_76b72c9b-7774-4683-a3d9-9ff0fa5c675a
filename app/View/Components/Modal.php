<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Illuminate\View\ComponentSlot;

final class Modal extends Component
{
  public ?ComponentSlot $slot = null;

  public ?ComponentSlot $header = null;

  public ?ComponentSlot $footer = null;

  /**
   * Create a new component instance.
   */
  public function __construct(public bool $show = false, public string $onBackdropClick = "") {}

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.modal");
  }
}
