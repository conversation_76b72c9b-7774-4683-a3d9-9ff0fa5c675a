<?php

declare(strict_types=1);

namespace App\View\Components;

use App\Helpers\Assert;
use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class MonthDaySelector extends Component
{
  public string $id;

  public string $label;

  /**
   * @var non-empty-list<string>
   */
  public array $monthNames;

  /**
   * The error message.
   */
  public ?string $error;

  /**
   * Whether the input has an error.
   */
  public bool $hasError;

  /**
   * Create a new component instance.
   *
   * @throws InvalidFormatException
   */
  public function __construct(string $id, string $label, ?string $error = null)
  {
    $this->id = $id;
    $this->label = $label;
    $this->error = $error;
    $this->hasError = $error !== null && $error !== "";

    for ($i = 1; $i <= 12; $i++) {
      $carbon = Carbon::create(null, $i, 1);
      Assert::notNull($carbon);
      $this->monthNames[] = $carbon->isoFormat("MMMM");
    }
  }

  public function render(): View
  {
    return view("components.month-day-selector");
  }
}
