<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class FormInput extends Component
{
  /**
   * The input id.
   */
  public string $id;

  /**
   * The input label.
   */
  public string $label;

  /**
   * The input type.
   */
  public string $type;

  /**
   * The error message.
   */
  public ?string $error;

  /**
   * Whether the input has an error.
   */
  public bool $hasError;

  /**
   * The input placeholder text.
   */
  public string $placeholder;

  /**
   * Create a new component instance.
   */
  public function __construct(
    string $id,
    string $label,
    string $type = "text",
    ?string $error = null,
    ?string $placeholder = null,
  ) {
    $this->id = $id;
    $this->label = $label;
    $this->type = $type;
    $this->error = $error;
    $this->hasError = $error !== null && $error !== "";
    $this->placeholder = $placeholder ?? "";
  }

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.form-input");
  }
}
