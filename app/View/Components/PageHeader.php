<?php

declare(strict_types=1);

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

final class PageHeader extends Component
{
  /**
   * Create the component instance.
   */
  public function __construct(public string $title = "", public ?string $description = null) {}

  /**
   * Get the view / contents that represent the component.
   */
  public function render(): View
  {
    return view("components.page-header");
  }
}
