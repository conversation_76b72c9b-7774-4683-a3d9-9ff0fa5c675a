<?php

declare(strict_types=1);

namespace App\Rules;

use Brick\Math\BigNumber;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

final class ValidBigDecimal implements ValidationRule
{
  public function validate(string $attribute, mixed $value, Closure $fail): void
  {
    if (
      !is_string($value) &&
      !is_int($value) &&
      !is_float($value) &&
      !($value instanceof BigNumber)
    ) {
      $fail(__("validations.valid_number"));

      return;
    }

    try {
      \Brick\Math\BigDecimal::of($value);
    } catch (\Brick\Math\Exception\MathException $e) {
      $fail(__("validations.valid_number"));
    }
  }
}
