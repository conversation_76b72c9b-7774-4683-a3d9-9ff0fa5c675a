<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Settings\InstructionsSettings;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\ComponentContainer;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

/**
 * @property ComponentContainer $form
 */
final class ManageInstructions extends Page
{
  /** @var array{content_fi: string, content_en: string, content_sv: string} */
  public ?array $data = null;

  protected static ?string $navigationIcon = "heroicon-o-document-text";

  protected static string $view = "filament.pages.manage-instructions";

  public static function getNavigationLabel(): string
  {
    return __("admin.instructions.user_guide");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.settings");
  }

  public function getTitle(): string
  {
    return __("admin.instructions.title");
  }

  public function mount(): void
  {
    $settings = app(InstructionsSettings::class);

    $this->form->fill([
      "content_fi" => $settings->content_fi ?? "",
      "content_en" => $settings->content_en ?? "",
      "content_sv" => $settings->content_sv ?? "",
    ]);
  }

  public function form(Form $form): Form
  {
    return $form
      ->schema([
        Forms\Components\Tabs::make(__("admin.instructions.translations"))->tabs([
          Forms\Components\Tabs\Tab::make(__("admin.languages.finnish"))
            ->icon("heroicon-o-flag")
            ->schema([
              Forms\Components\RichEditor::make("content_fi")
                ->label(__("admin.instructions.fields.content"))
                ->default("")
                ->fileAttachmentsDisk("public")
                ->fileAttachmentsDirectory("instructions/fi")
                ->fileAttachmentsVisibility("public"),
            ]),
          Forms\Components\Tabs\Tab::make(__("admin.languages.english"))
            ->icon("heroicon-o-flag")
            ->schema([
              Forms\Components\RichEditor::make("content_en")
                ->label(__("admin.instructions.fields.content"))
                ->default("")
                ->fileAttachmentsDisk("public")
                ->fileAttachmentsDirectory("instructions/en")
                ->fileAttachmentsVisibility("public"),
            ]),
          Forms\Components\Tabs\Tab::make(__("admin.languages.swedish"))
            ->icon("heroicon-o-flag")
            ->schema([
              Forms\Components\RichEditor::make("content_sv")
                ->label(__("admin.instructions.fields.content"))
                ->default("")
                ->fileAttachmentsDisk("public")
                ->fileAttachmentsDirectory("instructions/sv")
                ->fileAttachmentsVisibility("public"),
            ]),
        ]),
      ])
      ->statePath("data");
  }

  public function save(): void
  {
    $data = $this->form->getState();

    $settings = app(InstructionsSettings::class);

    // Get values with empty string as default
    $content_fi = $data["content_fi"] ?? "";
    $content_en = $data["content_en"] ?? "";
    $content_sv = $data["content_sv"] ?? "";

    // Ensure they are strings (convert null to empty string)
    $content_fi = is_string($content_fi) ? $content_fi : "";
    $content_en = is_string($content_en) ? $content_en : "";
    $content_sv = is_string($content_sv) ? $content_sv : "";

    $settings->content_fi = $content_fi;
    $settings->content_en = $content_en;
    $settings->content_sv = $content_sv;
    $settings->save();

    Notification::make()->success()->title(__("admin.instructions.messages.updated"))->send();
  }

  protected function getHeaderActions(): array
  {
    return [
      Action::make("save")->label(__("admin.instructions.actions.save_changes"))->action("save"),
    ];
  }
}
