<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Helpers\Assert;
use App\Models\Company;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use Filament\Support\Colors\Color;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use RuntimeException;

final class AuditLogBreakdownWidget extends ChartWidget
{
  public ?string $filter = "30days";

  protected static ?string $heading = null;

  protected static ?int $sort = 30;

  protected int|string|array $columnSpan = 1;

  /**
   * @throws RuntimeException
   */
  public static function canView(): bool
  {
    $user = Auth::user();

    return $user !== null && $user->hasPermissionTo("view any company audit logs");
  }

  public function getHeading(): string
  {
    return __("Muutokset kohteittain");
  }

  protected function getData(): array
  {
    $startDate = match ($this->filter) {
      "today" => Carbon::today(),
      "7days" => Carbon::today()->subDays(6),
      "30days" => Carbon::today()->subDays(29),
      "3months" => Carbon::today()->subMonths(3),
      "6months" => Carbon::today()->subMonths(6),
      "year" => Carbon::today()->subYear(),
      "all" => null,
      default => Carbon::today()->subDays(29),
    };

    $query = DB::table("audit_logs")
      ->select("auditable_type", DB::raw("COUNT(*) as count"))
      ->groupBy("auditable_type");

    if ($startDate !== null) {
      $query->where("occurred_at", ">=", $startDate);
    }

    $data = $query->get();

    $labels = [];
    $counts = [];
    $colors = [];

    foreach ($data as $item) {
      Assert::string($item->auditable_type);
      $labels[] = $this->getModelTypeLabel($item->auditable_type);
      Assert::int($item->count);
      $counts[] = $item->count;
      $colors[] = $this->getColorForType($item->auditable_type);
    }

    return [
      "datasets" => [
        [
          "data" => $counts,
          "backgroundColor" => $colors,
          "borderWidth" => 0,
        ],
      ],
      "labels" => $labels,
    ];
  }

  protected function getType(): string
  {
    return "doughnut";
  }

  /**
   * @return array<string, mixed>
   */
  protected function getOptions(): array
  {
    return [
      "plugins" => [
        "legend" => [
          "display" => true,
          "position" => "bottom",
        ],
      ],
      "scales" => [
        "x" => [
          "display" => false,
        ],
        "y" => [
          "display" => false,
        ],
      ],
      "maintainAspectRatio" => true,
      "responsive" => true,
      "aspectRatio" => 1.5,
    ];
  }

  /**
   * @return array<string, string>
   */
  protected function getFilters(): array
  {
    return [
      "today" => __("Tänään"),
      "7days" => __("7 päivää"),
      "30days" => __("30 päivää"),
      "3months" => __("3 kuukautta"),
      "6months" => __("6 kuukautta"),
      "year" => __("Vuosi"),
      "all" => __("Kaikki"),
    ];
  }

  private function getModelTypeLabel(string $modelClass): string
  {
    return match ($modelClass) {
      Company::class => __("Yritys"),
      DataValue::class => __("Data"),
      EmissionFactorValue::class => __("Päästökerroin"),
      default => __("Muu"),
    };
  }

  private function getColorForType(string $modelClass): string
  {
    return match ($modelClass) {
      Company::class => "rgb(" . Color::Blue[500] . ")",
      DataValue::class => "rgb(" . Color::Green[500] . ")",
      EmissionFactorValue::class => "rgb(" . Color::Amber[500] . ")",
      default => "rgb(" . Color::Gray[500] . ")",
    };
  }
}
