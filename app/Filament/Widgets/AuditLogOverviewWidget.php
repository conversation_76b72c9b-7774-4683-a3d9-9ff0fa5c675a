<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Helpers\Assert;
use App\Models\AuditLog;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use RuntimeException;

final class AuditLogOverviewWidget extends BaseWidget
{
  protected static ?int $sort = 10;

  protected int|string|array $columnSpan = "full";

  /**
   * @throws RuntimeException
   */
  public static function canView(): bool
  {
    $user = Auth::user();

    return $user !== null && $user->hasPermissionTo("view any company audit logs");
  }

  /**
   * @return array<int, Stat>
   *
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\MathException
   * @throws InvalidArgumentException
   */
  protected function getStats(): array
  {
    $today = Carbon::today();
    $yesterday = Carbon::yesterday();

    // Today's changes
    $todayCount = AuditLog::whereDate("occurred_at", $today)->count();
    $yesterdayCount = AuditLog::whereDate("occurred_at", $yesterday)->count();

    $todayChange = BigDecimal::zero();
    if ($yesterdayCount > 0) {
      $todayChange = BigDecimal::of($todayCount - $yesterdayCount)
        ->dividedBy($yesterdayCount, 3, RoundingMode::HALF_UP)
        ->multipliedBy(100)
        ->toScale(1, RoundingMode::HALF_UP);
    }

    // This week's changes
    $thisWeek = AuditLog::whereBetween("occurred_at", [
      $today->copy()->startOfWeek(),
      $today->copy()->endOfWeek(),
    ])->count();

    $lastWeek = AuditLog::whereBetween("occurred_at", [
      $today->copy()->subWeek()->startOfWeek(),
      $today->copy()->subWeek()->endOfWeek(),
    ])->count();

    $weekChange = BigDecimal::zero();
    if ($lastWeek > 0) {
      $weekChange = BigDecimal::of($thisWeek - $lastWeek)
        ->dividedBy($lastWeek, 3, RoundingMode::HALF_UP)
        ->multipliedBy(100)
        ->toScale(1, RoundingMode::HALF_UP);
    }

    // This month's changes
    $thisMonth = AuditLog::where("occurred_at", ">=", $today->copy()->startOfMonth())->count();

    $lastMonth = AuditLog::whereBetween("occurred_at", [
      $today->copy()->subMonth()->startOfMonth(),
      $today->copy()->subMonth()->endOfMonth(),
    ])->count();

    $monthChange = BigDecimal::zero();
    if ($lastMonth > 0) {
      $monthChange = BigDecimal::of($thisMonth - $lastMonth)
        ->dividedBy($lastMonth, 3, RoundingMode::HALF_UP)
        ->multipliedBy(100)
        ->toScale(1, RoundingMode::HALF_UP);
    }

    // Most active company this month
    $mostActiveCompanyData = DB::table("audit_logs")
      ->selectRaw("company_id, COUNT(*) as total_count")
      ->where("occurred_at", ">=", $today->copy()->startOfMonth())
      ->groupBy("company_id")
      ->orderByDesc("total_count")
      ->first();

    $mostActiveCompanyName = __("audit.stats.no_changes");
    $mostActiveCompanyCount = 0;

    if ($mostActiveCompanyData !== null) {
      // Cast to array to access properties safely
      $data = (array) $mostActiveCompanyData;
      if (isset($data["company_id"]) && isset($data["total_count"])) {
        $companyId = $data["company_id"];
        Assert::int($companyId);
        $company = \App\Models\Company::query()->find($companyId);
        if ($company instanceof \App\Models\Company) {
          $mostActiveCompanyName = $company->name;
        } else {
          $mostActiveCompanyName = __("audit.unknown");
        }
        Assert::int($data["total_count"]);
        $mostActiveCompanyCount = $data["total_count"];
      }
    }

    return [
      Stat::make(__("audit.stats.changes_today"), $todayCount)
        ->description(
          !$todayChange->isZero()
            ? ($todayChange->isPositive() ? "+" : "") . $todayChange . "% eiliseen"
            : __("audit.stats.no_change_from_yesterday"),
        )
        ->descriptionIcon(
          $todayChange->isPositive()
            ? "heroicon-m-arrow-trending-up"
            : ($todayChange->isNegative()
              ? "heroicon-m-arrow-trending-down"
              : "heroicon-m-minus"),
        )
        ->color(
          $todayChange->isPositive() ? "success" : ($todayChange->isNegative() ? "danger" : "gray"),
        )
        ->chart($this->getLastSevenDaysChart()),

      Stat::make(__("audit.stats.changes_this_week"), $thisWeek)
        ->description(
          !$weekChange->isZero()
            ? ($weekChange->isPositive() ? "+" : "") . $weekChange . "% edelliseen"
            : __("audit.stats.no_change_from_previous"),
        )
        ->descriptionIcon(
          $weekChange->isPositive()
            ? "heroicon-m-arrow-trending-up"
            : ($weekChange->isNegative()
              ? "heroicon-m-arrow-trending-down"
              : "heroicon-m-minus"),
        )
        ->color(
          $weekChange->isPositive() ? "success" : ($weekChange->isNegative() ? "danger" : "gray"),
        ),

      Stat::make(__("audit.stats.changes_this_month"), $thisMonth)
        ->description(
          !$monthChange->isZero()
            ? ($monthChange->isPositive() ? "+" : "") . $monthChange . "% edelliseen"
            : __("audit.stats.no_change_from_previous"),
        )
        ->descriptionIcon(
          $monthChange->isPositive()
            ? "heroicon-m-arrow-trending-up"
            : ($monthChange->isNegative()
              ? "heroicon-m-arrow-trending-down"
              : "heroicon-m-minus"),
        )
        ->color(
          $monthChange->isPositive() ? "success" : ($monthChange->isNegative() ? "danger" : "gray"),
        ),

      Stat::make(
        __("audit.stats.most_active_company"),
        $mostActiveCompanyCount > 0
          ? mb_substr((string) $mostActiveCompanyName, 0, 15) .
            (mb_strlen((string) $mostActiveCompanyName) > 15 ? "..." : "")
          : "-",
      )
        ->description(
          $mostActiveCompanyCount > 0
            ? $mostActiveCompanyCount . " muutosta tässä kuussa"
            : __("audit.stats.no_changes_this_month"),
        )
        ->descriptionIcon("heroicon-m-building-office")
        ->color("primary")
        ->extraAttributes([
          "title" => $mostActiveCompanyName,
        ]),
    ];
  }

  /**
   * @return array<int, int>
   */
  private function getLastSevenDaysChart(): array
  {
    $data = [];

    for ($i = 6; $i >= 0; $i--) {
      $date = Carbon::today()->subDays($i);
      $count = AuditLog::whereDate("occurred_at", $date)->count();
      $data[] = $count;
    }

    return $data;
  }
}
