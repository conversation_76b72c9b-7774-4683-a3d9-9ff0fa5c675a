<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Enums\AuditEvent;
use App\Enums\ScalarType;
use App\Helpers\Assert;
use App\Models\AuditLog;
use App\Models\CalculationDefinition;
use App\Models\Company;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Auth;
use RuntimeException;

final class AuditLogWidget extends BaseWidget
{
  protected int|string|array $columnSpan = "full";

  protected static ?int $sort = 40;

  /**
   * Determine if the widget should be displayed
   *
   * @throws RuntimeException
   */
  public static function canView(): bool
  {
    $user = Auth::user();

    return $user !== null && $user->hasPermissionTo("view any company audit logs");
  }

  public function getTableHeading(): string
  {
    return __("audit.title");
  }

  public function table(Table $table): Table
  {
    return $table
      ->query($this->getQuery())
      ->columns($this->getColumns())
      ->filters($this->getFilters())
      ->actions([])
      ->bulkActions([])
      ->paginated([10, 25, 50, 100])
      ->defaultPaginationPageOption(25)
      ->emptyStateHeading(__("audit.empty.no_history"))
      ->emptyStateDescription(__("audit.empty.no_records"))
      ->striped()
      ->defaultSort("occurred_at", "desc");
  }

  /**
   * @return Builder<AuditLog>
   */
  protected function getQuery(): Builder
  {
    return AuditLog::query()->with([
      "company",
      "user",
      "auditable" => function (Relation $morphTo) {
        Assert::instanceOf($morphTo, MorphTo::class);

        return $morphTo
          ->morphWith([
            DataValue::class => ["calculationDefinition.translations", "year"],
            EmissionFactorValue::class => ["calculationDefinition.translations", "year"],
            Company::class => [],
            CalculationDefinition::class => ["translations"],
          ])
          ->withTrashed();
      },
    ]);
  }

  /**
   * @return array<int, Tables\Columns\Column>
   */
  protected function getColumns(): array
  {
    return [
      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("company.name")
        ->label(__("audit.fields.company"))
        ->searchable()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("user.email")
        ->label(__("audit.fields.changed_by"))
        ->default(__("audit.unknown"))
        ->searchable()
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("event")
        ->label(__("audit.fields.action"))
        ->formatStateUsing(
          fn(AuditEvent $state): string => match ($state->value) {
            "created" => __("audit.actions.created"),
            "updated" => __("audit.actions.updated"),
            "deleted" => __("audit.actions.deleted"),
            "soft_deleted" => __("audit.status.hidden"),
            "force_deleted" => __("audit.actions.permanently_deleted"),
            "restored" => __("audit.actions.restored"),
          },
        )
        ->badge()
        ->color(
          fn(AuditEvent $state): string => match ($state->value) {
            "created" => "success",
            "updated" => "info",
            "deleted" => "danger",
            "force_deleted" => "danger",
            "soft_deleted" => "warning",
            "restored" => "success",
          },
        )
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("auditable_type")
        ->label(__("audit.fields.target"))
        ->formatStateUsing(fn(string $state): string => $this->getModelTypeLabel($state))
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
        "model_description",
      )
        ->label(__("audit.fields.name"))
        ->getStateUsing(function (AuditLog $record): string {
          if ($record->auditable === null) {
            return __("audit.unknown");
          }

          return $this->getAuditTargetDescription($record->auditable);
        })
        ->wrap()
        ->sortable()
        ->searchable(
          query: function (Builder $query, string $search): Builder {
            return $query->where(function (Builder $query) use ($search) {
              $query->orWhereHasMorph(
                "auditable",
                [DataValue::class, EmissionFactorValue::class, CalculationDefinition::class],
                function (Builder $query, string $type) use ($search) {
                  if ($type === DataValue::class || $type === EmissionFactorValue::class) {
                    $query->whereHas("calculationDefinition.translations", function (
                      Builder $query,
                    ) use ($search) {
                      $query
                        ->where("data_name", "like", "%{$search}%")
                        ->orWhere("emission_factor_name", "like", "%{$search}%");
                    });
                  } elseif ($type === CalculationDefinition::class) {
                    $query
                      ->where("custom_name", "like", "%{$search}%")
                      ->orWhereHas("translations", function (Builder $query) use ($search) {
                        $query
                          ->where("data_name", "like", "%{$search}%")
                          ->orWhere("emission_factor_name", "like", "%{$search}%")
                          ->orWhere("result_name", "like", "%{$search}%");
                      });
                  }
                },
              );
            });
          },
        )
        ->tooltip(function (AuditLog $record): ?string {
          if (
            $record->auditable !== null &&
            method_exists($record->auditable, "trashed") &&
            $record->auditable->trashed()
          ) {
            return __("audit.status.target_hidden");
          }

          return null;
        }),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("field_name")
        ->label(__("audit.fields.field"))
        ->formatStateUsing(
          fn(string $state, AuditLog $record): string => $this->getFieldLabel(
            $record->auditable_type,
            $state,
          ),
        )
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("old_value")
        ->label(__("audit.values.old"))
        ->getStateUsing(
          fn(AuditLog $record): string => $this->formatFieldValue(
            $record->old_value,
            $record->field_type,
          ),
        )
        ->wrap()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("new_value")
        ->label(__("audit.values.new"))
        ->getStateUsing(
          fn(AuditLog $record): string => $this->formatFieldValue(
            $record->new_value,
            $record->field_type,
          ),
        )
        ->wrap()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("occurred_at")
        ->label(__("audit.fields.change_time"))
        ->dateTime("H.i - d.m.Y")
        ->sortable()
        ->alignEnd(),
    ];
  }

  /**
   * @return array<int, Tables\Filters\BaseFilter>
   */
  protected function getFilters(): array
  {
    return [
      Tables\Filters\SelectFilter::make("company_id")
        ->label(__("audit.fields.company"))
        ->relationship("company", "name")
        ->searchable()
        ->preload(),

      Tables\Filters\SelectFilter::make("user_id")
        ->label(__("audit.fields.user"))
        ->relationship("user", "email")
        ->searchable()
        ->preload(),

      Tables\Filters\SelectFilter::make("event")
        ->label(__("audit.fields.action"))
        ->options([
          AuditEvent::CREATED->value => __("audit.actions.created"),
          AuditEvent::UPDATED->value => __("audit.actions.updated"),
          AuditEvent::DELETED->value => __("audit.actions.deleted"),
          AuditEvent::SOFT_DELETED->value => __("audit.status.hidden"),
          AuditEvent::FORCE_DELETED->value => __("audit.actions.permanently_deleted"),
          AuditEvent::RESTORED->value => __("audit.actions.restored"),
        ])
        ->multiple(),

      Tables\Filters\SelectFilter::make("auditable_type")
        ->label(__("audit.fields.target"))
        ->options([
          Company::class => __("audit.fields.company"),
          DataValue::class => __("audit.types.data"),
          EmissionFactorValue::class => __("audit.types.emission_factor"),
          CalculationDefinition::class => __("audit.fields.row"),
        ]),

      Tables\Filters\Filter::make("occurred_at")
        ->label(__("audit.fields.time_range"))
        ->form([
          Forms\Components\DatePicker::make("from")->label(__("audit.fields.starting_from")),
          Forms\Components\DatePicker::make("to")->label(__("audit.fields.ending")),
        ])
        ->query(function (Builder $query, array $data): Builder {
          $fromDate = null;
          $toDate = null;

          if (isset($data["from"]) && is_string($data["from"]) && $data["from"] !== "") {
            $fromDate = $data["from"];
          }

          if (isset($data["to"]) && is_string($data["to"]) && $data["to"] !== "") {
            $toDate = $data["to"];
          }

          return $query
            ->when(
              $fromDate !== null,
              fn(Builder $query) => $query->whereDate("occurred_at", ">=", $fromDate),
            )
            ->when(
              $toDate !== null,
              fn(Builder $query) => $query->whereDate("occurred_at", "<=", $toDate),
            );
        }),
    ];
  }

  /**
   * @throws RuntimeException
   */
  private function getModelTypeLabel(string $modelClass): string
  {
    return match ($modelClass) {
      Company::class => __("audit.fields.company"),
      DataValue::class => __("audit.types.data"),
      EmissionFactorValue::class => __("audit.types.emission_factor"),
      CalculationDefinition::class => __("audit.fields.row"),
      default => throw new RuntimeException("No label found for model class: {$modelClass}"),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getAuditTargetDescription(Model $auditable): string
  {
    return match (get_class($auditable)) {
      Company::class => sprintf("%s", $auditable->name ?? __("audit.unknown")),

      DataValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->data_name ?? (string) __("audit.unknown"),
        $auditable->year->year ?? (string) __("audit.unknown"),
      ),

      EmissionFactorValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->emission_factor_name ??
          (string) __("audit.unknown"),
        $auditable->year->year ?? (string) __("audit.unknown"),
      ),

      CalculationDefinition::class => sprintf(
        "%s",
        $auditable->custom_name ??
          ($auditable->translate()->data_name ??
            ($auditable->translate()->emission_factor_name ??
              ($auditable->translate()->result_name ?? (string) __("audit.unknown")))),
      ),

      default => throw new RuntimeException(
        "No target description found for model class: " . get_class($auditable),
      ),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getFieldLabel(string $modelClass, string $field): string
  {
    return match ($modelClass) {
      Company::class => match ($field) {
        "name" => __("audit.fields.company_name"),
        "business_id" => __("audit.fields.business_id"),
        "fiscal_start_month" => __("audit.fields.fiscal_start_month"),
        "fiscal_start_day" => __("audit.fields.fiscal_start_day"),
        "fiscal_end_month" => __("audit.fields.fiscal_end_month"),
        "fiscal_end_day" => __("audit.fields.fiscal_end_day"),
        "consent_for_data_examination" => __("audit.fields.data_viewing_permission"),
        "applying_for_mark" => __("audit.fields.applying_badge"), // Legacy field
        "applying_for_scope1_2_mark" => __("audit.fields.applying_scope_1_2"),
        "applying_for_scope3_mark" => __(
          "audit.fields.applying_scope_3",
        ), // Legacy field before rename
        "applying_for_scope1_3_mark" => __("audit.fields.applying_scope_1_3"),
        "industry_classification_id" => __("audit.fields.industry"),
        "municipality_id" => __("audit.fields.municipality"),
        "revenue_range_id" => __("audit.fields.revenue"),
        "employee_count_range_id" => __("audit.fields.employee_count"),
        "e_invoice_address" => __("audit.fields.einvoice_address"),
        "e_invoice_operator" => __("audit.fields.einvoice_operator"),
        "e_invoice_reference" => __("audit.fields.reference"),
        "e_invoice_contact_name" => __("audit.fields.contact_name"),
        "e_invoice_contact_email" => __("audit.fields.contact_email"),
        "e_invoice_additional_info" => __("audit.fields.additional_info"),
        "terms_of_service_accepted_at" => __("audit.fields.terms_accepted"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in Company model",
        ),
      },
      DataValue::class => match ($field) {
        "value" => __("audit.fields.value"),
        "source" => __("audit.fields.source"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in DataValue model",
        ),
      },
      EmissionFactorValue::class => match ($field) {
        "value" => __("audit.fields.value"),
        "source" => __("audit.fields.source"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in EmissionFactorValue model",
        ),
      },
      CalculationDefinition::class => match ($field) {
        "custom_name" => __("audit.fields.name"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in CalculationDefinition model",
        ),
      },
      default => throw new RuntimeException("No labels found for model class: {$modelClass}"),
    };
  }

  /**
   * Format field value for display
   */
  private function formatFieldValue(?string $value, ScalarType $fieldType): string
  {
    if ($value === null) {
      return __("common.dash");
    }

    // Use type information for proper formatting
    if ($fieldType === ScalarType::BOOLEAN) {
      return $value === "1" ? __("common.yes") : __("common.no");
    }

    return $value;
  }
}
