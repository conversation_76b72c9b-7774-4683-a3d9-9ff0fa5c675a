<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Enums\AuditEvent;
use App\Enums\ScalarType;
use App\Helpers\Assert;
use App\Models\AuditLog;
use App\Models\CalculationDefinition;
use App\Models\Company;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Auth;
use RuntimeException;

final class AuditLogWidget extends BaseWidget
{
  protected int|string|array $columnSpan = "full";

  protected static ?int $sort = 40;

  /**
   * Determine if the widget should be displayed
   *
   * @throws RuntimeException
   */
  public static function canView(): bool
  {
    $user = Auth::user();

    return $user !== null && $user->hasPermissionTo("view any company audit logs");
  }

  public function getTableHeading(): string
  {
    return __("Muutoshistoria");
  }

  public function table(Table $table): Table
  {
    return $table
      ->query($this->getQuery())
      ->columns($this->getColumns())
      ->filters($this->getFilters())
      ->actions([])
      ->bulkActions([])
      ->paginated([10, 25, 50, 100])
      ->defaultPaginationPageOption(25)
      ->emptyStateHeading(__("Ei muutoshistoriaa"))
      ->emptyStateDescription(__("Muutoshistoriaa ei ole vielä tallennettu."))
      ->striped()
      ->defaultSort("occurred_at", "desc");
  }

  /**
   * @return Builder<AuditLog>
   */
  protected function getQuery(): Builder
  {
    return AuditLog::query()->with([
      "company",
      "user",
      "auditable" => function (Relation $morphTo) {
        Assert::instanceOf($morphTo, MorphTo::class);

        return $morphTo
          ->morphWith([
            DataValue::class => ["calculationDefinition.translations", "year"],
            EmissionFactorValue::class => ["calculationDefinition.translations", "year"],
            Company::class => [],
            CalculationDefinition::class => ["translations"],
          ])
          ->withTrashed();
      },
    ]);
  }

  /**
   * @return array<int, Tables\Columns\Column>
   */
  protected function getColumns(): array
  {
    return [
      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("company.name")
        ->label(__("Yritys"))
        ->searchable()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("user.email")
        ->label(__("Muutoksen tekijä"))
        ->default(__("Tuntematon"))
        ->searchable()
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("event")
        ->label(__("Toiminto"))
        ->formatStateUsing(
          fn(AuditEvent $state): string => match ($state->value) {
            "created" => __("Luotu"),
            "updated" => __("Päivitetty"),
            "deleted" => __("Poistettu"),
            "soft_deleted" => __("Piilotettu"),
            "force_deleted" => __("Poistettu pysyvästi"),
            "restored" => __("Palautettu"),
          },
        )
        ->badge()
        ->color(
          fn(AuditEvent $state): string => match ($state->value) {
            "created" => "success",
            "updated" => "info",
            "deleted" => "danger",
            "force_deleted" => "danger",
            "soft_deleted" => "warning",
            "restored" => "success",
          },
        )
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("auditable_type")
        ->label(__("Kohde"))
        ->formatStateUsing(fn(string $state): string => $this->getModelTypeLabel($state))
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
        "model_description",
      )
        ->label(__("Nimi"))
        ->getStateUsing(function (AuditLog $record): string {
          if ($record->auditable === null) {
            return __("Tuntematon");
          }

          return $this->getAuditTargetDescription($record->auditable);
        })
        ->wrap()
        ->sortable()
        ->searchable(
          query: function (Builder $query, string $search): Builder {
            return $query->where(function (Builder $query) use ($search) {
              $query->orWhereHasMorph(
                "auditable",
                [DataValue::class, EmissionFactorValue::class, CalculationDefinition::class],
                function (Builder $query, string $type) use ($search) {
                  if ($type === DataValue::class || $type === EmissionFactorValue::class) {
                    $query->whereHas("calculationDefinition.translations", function (
                      Builder $query,
                    ) use ($search) {
                      $query
                        ->where("data_name", "like", "%{$search}%")
                        ->orWhere("emission_factor_name", "like", "%{$search}%");
                    });
                  } elseif ($type === CalculationDefinition::class) {
                    $query
                      ->where("custom_name", "like", "%{$search}%")
                      ->orWhereHas("translations", function (Builder $query) use ($search) {
                        $query
                          ->where("data_name", "like", "%{$search}%")
                          ->orWhere("emission_factor_name", "like", "%{$search}%")
                          ->orWhere("result_name", "like", "%{$search}%");
                      });
                  }
                },
              );
            });
          },
        )
        ->tooltip(function (AuditLog $record): ?string {
          if (
            $record->auditable !== null &&
            method_exists($record->auditable, "trashed") &&
            $record->auditable->trashed()
          ) {
            return __("Tämä kohde on piilotettu");
          }

          return null;
        }),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("field_name")
        ->label(__("Tieto"))
        ->formatStateUsing(
          fn(string $state, AuditLog $record): string => $this->getFieldLabel(
            $record->auditable_type,
            $state,
          ),
        )
        ->sortable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("old_value")
        ->label(__("Vanha"))
        ->getStateUsing(
          fn(AuditLog $record): string => $this->formatFieldValue(
            $record->old_value,
            $record->field_type,
          ),
        )
        ->wrap()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("new_value")
        ->label(__("Uusi"))
        ->getStateUsing(
          fn(AuditLog $record): string => $this->formatFieldValue(
            $record->new_value,
            $record->field_type,
          ),
        )
        ->wrap()
        ->sortable()
        ->toggleable(),

      \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("occurred_at")
        ->label(__("Muutoksen ajankohta"))
        ->dateTime("H.i - d.m.Y")
        ->sortable()
        ->alignEnd(),
    ];
  }

  /**
   * @return array<int, Tables\Filters\BaseFilter>
   */
  protected function getFilters(): array
  {
    return [
      Tables\Filters\SelectFilter::make("company_id")
        ->label(__("Yritys"))
        ->relationship("company", "name")
        ->searchable()
        ->preload(),

      Tables\Filters\SelectFilter::make("user_id")
        ->label(__("Käyttäjä"))
        ->relationship("user", "email")
        ->searchable()
        ->preload(),

      Tables\Filters\SelectFilter::make("event")
        ->label(__("Toiminto"))
        ->options([
          AuditEvent::CREATED->value => __("Luotu"),
          AuditEvent::UPDATED->value => __("Päivitetty"),
          AuditEvent::DELETED->value => __("Poistettu"),
          AuditEvent::SOFT_DELETED->value => __("Piilotettu"),
          AuditEvent::FORCE_DELETED->value => __("Poistettu pysyvästi"),
          AuditEvent::RESTORED->value => __("Palautettu"),
        ])
        ->multiple(),

      Tables\Filters\SelectFilter::make("auditable_type")
        ->label(__("Kohde"))
        ->options([
          Company::class => __("Yritys"),
          DataValue::class => __("Data"),
          EmissionFactorValue::class => __("Päästökerroin"),
          CalculationDefinition::class => __("Rivi"),
        ]),

      Tables\Filters\Filter::make("occurred_at")
        ->label(__("Aikaväli"))
        ->form([
          Forms\Components\DatePicker::make("from")->label(__("Alkaen")),
          Forms\Components\DatePicker::make("to")->label(__("Päättyen")),
        ])
        ->query(function (Builder $query, array $data): Builder {
          $fromDate = null;
          $toDate = null;

          if (isset($data["from"]) && is_string($data["from"]) && $data["from"] !== "") {
            $fromDate = $data["from"];
          }

          if (isset($data["to"]) && is_string($data["to"]) && $data["to"] !== "") {
            $toDate = $data["to"];
          }

          return $query
            ->when(
              $fromDate !== null,
              fn(Builder $query) => $query->whereDate("occurred_at", ">=", $fromDate),
            )
            ->when(
              $toDate !== null,
              fn(Builder $query) => $query->whereDate("occurred_at", "<=", $toDate),
            );
        }),
    ];
  }

  /**
   * @throws RuntimeException
   */
  private function getModelTypeLabel(string $modelClass): string
  {
    return match ($modelClass) {
      Company::class => __("Yritys"),
      DataValue::class => __("Data"),
      EmissionFactorValue::class => __("Päästökerroin"),
      CalculationDefinition::class => __("Rivi"),
      default => throw new RuntimeException("No label found for model class: {$modelClass}"),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getAuditTargetDescription(Model $auditable): string
  {
    return match (get_class($auditable)) {
      Company::class => sprintf("%s", $auditable->name ?? __("Tuntematon")),

      DataValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->data_name ?? (string) __("Tuntematon"),
        $auditable->year->year ?? (string) __("Tuntematon"),
      ),

      EmissionFactorValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->emission_factor_name ??
          (string) __("Tuntematon"),
        $auditable->year->year ?? (string) __("Tuntematon"),
      ),

      CalculationDefinition::class => sprintf(
        "%s",
        $auditable->custom_name ??
          ($auditable->translate()->data_name ??
            ($auditable->translate()->emission_factor_name ??
              ($auditable->translate()->result_name ?? (string) __("Tuntematon")))),
      ),

      default => throw new RuntimeException(
        "No target description found for model class: " . get_class($auditable),
      ),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getFieldLabel(string $modelClass, string $field): string
  {
    return match ($modelClass) {
      Company::class => match ($field) {
        "name" => __("Yrityksen nimi"),
        "business_id" => __("Y-tunnus"),
        "fiscal_start_month" => __("Tilikauden alku (kk)"),
        "fiscal_start_day" => __("Tilikauden alku (pv)"),
        "fiscal_end_month" => __("Tilikauden loppu (kk)"),
        "fiscal_end_day" => __("Tilikauden loppu (pv)"),
        "consent_for_data_examination" => __("Oikeus tarkastella dataa"),
        "applying_for_scope1_2_mark" => __("Haen Scope 1 & 2 merkkiä"),
        "applying_for_scope3_mark" => __("Haen Scope 3 merkkiä"),
        "industry_classification_id" => __("Toimiala"),
        "municipality_id" => __("Kunta"),
        "revenue_range_id" => __("Liikevaihto"),
        "employee_count_range_id" => __("Henkilöstön määrä"),
        "e_invoice_address" => __("Verkkolaskuosoite"),
        "e_invoice_operator" => __("Verkkolaskuoperaattori"),
        "e_invoice_reference" => __("Viite"),
        "e_invoice_contact_name" => __("Yhteyshenkilön nimi"),
        "e_invoice_contact_email" => __("Yhteyshenkilön sähköposti"),
        "e_invoice_additional_info" => __("Lisätiedot"),
        "terms_of_service_accepted_at" => __("Käyttöehdot hyväksytty"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in Company model",
        ),
      },
      DataValue::class => match ($field) {
        "value" => __("Arvo"),
        "source" => __("Lähde"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in DataValue model",
        ),
      },
      EmissionFactorValue::class => match ($field) {
        "value" => __("Arvo"),
        "source" => __("Lähde"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in EmissionFactorValue model",
        ),
      },
      CalculationDefinition::class => match ($field) {
        "custom_name" => __("Nimi"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in CalculationDefinition model",
        ),
      },
      default => throw new RuntimeException("No labels found for model class: {$modelClass}"),
    };
  }

  /**
   * Format field value for display
   */
  private function formatFieldValue(?string $value, ScalarType $fieldType): string
  {
    if ($value === null) {
      return __("—");
    }

    // Use type information for proper formatting
    if ($fieldType === ScalarType::BOOLEAN) {
      return $value === "1" ? __("Kyllä") : __("Ei");
    }

    return $value;
  }
}
