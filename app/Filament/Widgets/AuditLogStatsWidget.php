<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Helpers\Assert;
use Filament\Support\Colors\Color;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use RuntimeException;

final class AuditLogStatsWidget extends ChartWidget
{
  public ?string $filter = "30days";

  protected static ?string $heading = null;

  protected static ?int $sort = 20;

  protected int|string|array $columnSpan = 1;

  /**
   * @throws RuntimeException
   */
  public static function canView(): bool
  {
    $user = Auth::user();

    return $user !== null && $user->hasPermissionTo("view any company audit logs");
  }

  public function getHeading(): string
  {
    return __("Muutosten määrä");
  }

  /**
   * @throws InvalidArgumentException
   */
  protected function getData(): array
  {
    $startDate = match ($this->filter) {
      "today" => Carbon::today(),
      "7days" => Carbon::today()->subDays(6),
      "30days" => Carbon::today()->subDays(29),
      "3months" => Carbon::today()->subMonths(3),
      "6months" => Carbon::today()->subMonths(6),
      "year" => Carbon::today()->subYear(),
      "all" => null,
      default => Carbon::today()->subDays(29),
    };

    $data = $this->getChartData($startDate);

    $counts = [];
    $labels = [];

    foreach ($data as $item) {
      $counts[] = $item["count"];
      $labels[] = $item["label"];
    }

    return [
      "datasets" => [
        [
          "label" => __("Muutokset"),
          "data" => $counts,
          "borderColor" => "rgb(" . Color::Blue[500] . ")",
          "backgroundColor" => "rgba(" . Color::Blue[500] . ", 0.1)",
          "borderWidth" => 2,
          "tension" => 0.4,
        ],
      ],
      "labels" => $labels,
    ];
  }

  protected function getType(): string
  {
    return "line";
  }

  /**
   * @return array<string, mixed>
   */
  protected function getOptions(): array
  {
    return [
      "plugins" => [
        "legend" => [
          "display" => false,
        ],
      ],
      "scales" => [
        "y" => [
          "beginAtZero" => true,
          "ticks" => [
            "stepSize" => 1,
          ],
        ],
      ],
      "maintainAspectRatio" => true,
      "responsive" => true,
      "aspectRatio" => 1.5,
    ];
  }

  /**
   * @return array<string, string>
   */
  protected function getFilters(): array
  {
    return [
      "today" => __("Tänään"),
      "7days" => __("7 päivää"),
      "30days" => __("30 päivää"),
      "3months" => __("3 kuukautta"),
      "6months" => __("6 kuukautta"),
      "year" => __("Vuosi"),
      "all" => __("Kaikki"),
    ];
  }

  /**
   * @return array<int, array{count: int, label: string}>
   *
   * @throws InvalidArgumentException
   */
  private function getChartData(?Carbon $startDate): array
  {
    if ($startDate === null) {
      // For "all" filter, get the earliest date from the database
      $earliestDate = DB::table("audit_logs")->min("occurred_at");

      if ($earliestDate === null) {
        return [];
      }

      Assert::string($earliestDate);
      $startDate = Carbon::parse($earliestDate)->startOfDay();
    }

    $endDate = Carbon::today()->endOfDay();
    $daysDiff = (int) $startDate->diffInDays($endDate);

    $result = [];

    // Determine grouping based on date range
    // For 0 days (today only), show hours
    // For 1-30 days, show days
    // For 31-90 days, show weeks
    // For 91+ days, show months
    if ($daysDiff === 0 && $this->filter === "today") {
      // Group by hour for today filter only
      $results = DB::table("audit_logs")
        ->selectRaw("DATE_FORMAT(occurred_at, '%H:00') as hour_label, COUNT(*) as total_count")
        ->where("occurred_at", ">=", $startDate)
        ->groupBy("hour_label")
        ->orderBy("hour_label")
        ->get();

      // Create array with all hours
      $hourCounts = [];
      foreach ($results as $item) {
        Assert::string($item->hour_label);
        Assert::int($item->total_count);
        $hourCounts[$item->hour_label] = $item->total_count;
      }

      // Fill in missing hours
      for ($hour = 0; $hour < 24; $hour++) {
        $label = sprintf("%02d:00", $hour);
        $result[] = [
          "count" => $hourCounts[$label] ?? 0,
          "label" => $label,
        ];
      }
    } elseif ($daysDiff <= 30) {
      // Group by day for up to 30 days
      $results = DB::table("audit_logs")
        ->selectRaw("DATE(occurred_at) as date_value, COUNT(*) as total_count")
        ->where("occurred_at", ">=", $startDate)
        ->groupBy("date_value")
        ->orderBy("date_value")
        ->get();

      // Create array with counts indexed by date
      $dateCounts = [];
      foreach ($results as $item) {
        Assert::string($item->date_value);
        Assert::int($item->total_count);
        $dateCounts[$item->date_value] = $item->total_count;
      }

      // Fill in all days in the range
      $currentDate = $startDate->copy();
      while ($currentDate <= $endDate) {
        $dateKey = $currentDate->format("Y-m-d");
        $result[] = [
          "count" => $dateCounts[$dateKey] ?? 0,
          "label" => $currentDate->format("d.m"),
        ];
        $currentDate->addDay();
      }
    } elseif ($daysDiff <= 90) {
      // Group by week for up to 3 months
      $results = DB::table("audit_logs")
        ->selectRaw(
          "YEARWEEK(occurred_at, 3) as week_value, COUNT(*) as total_count, MIN(occurred_at) as min_date",
        )
        ->where("occurred_at", ">=", $startDate)
        ->groupBy("week_value")
        ->orderBy("week_value")
        ->get();

      // Create array with counts indexed by week
      $weekCounts = [];
      foreach ($results as $item) {
        Assert::int($item->week_value);
        Assert::int($item->total_count);
        $weekCounts[(string) $item->week_value] = $item->total_count;
      }

      // Fill in all weeks in the range
      $currentDate = $startDate->copy()->startOfWeek(Carbon::MONDAY);
      while ($currentDate <= $endDate) {
        // Use ISO week year and week number to match MySQL's YEARWEEK(..., 3)
        $yearWeek = $currentDate->isoWeekYear() . sprintf("%02d", $currentDate->isoWeek());
        $result[] = [
          "count" => $weekCounts[$yearWeek] ?? 0,
          "label" => __("Viikko") . " " . $currentDate->isoWeek(),
        ];
        $currentDate->addWeek();
      }
    } else {
      // Group by month for longer periods
      $results = DB::table("audit_logs")
        ->selectRaw("DATE_FORMAT(occurred_at, '%Y-%m') as month_value, COUNT(*) as total_count")
        ->where("occurred_at", ">=", $startDate)
        ->groupBy("month_value")
        ->orderBy("month_value")
        ->get();

      // Create array with counts indexed by month
      $monthCounts = [];
      foreach ($results as $item) {
        Assert::string($item->month_value);
        Assert::int($item->total_count);
        $monthCounts[$item->month_value] = $item->total_count;
      }

      // Fill in all months in the range
      $currentDate = $startDate->copy()->startOfMonth();
      while ($currentDate <= $endDate) {
        $monthKey = $currentDate->format("Y-m");
        $result[] = [
          "count" => $monthCounts[$monthKey] ?? 0,
          "label" => $currentDate->translatedFormat("M 'y"),
        ];
        $currentDate->addMonth();
      }
    }

    return $result;
  }
}
