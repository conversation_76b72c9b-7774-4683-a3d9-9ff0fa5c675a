<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\SelectOptionResource\Pages;
use App\Filament\Resources\SelectOptionResource\Pages\ListSelectOptions;
use App\Models\SelectOption;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class SelectOptionResource extends Resource
{
  use Translatable;

  protected static ?string $model = SelectOption::class;

  protected static ?string $navigationIcon = "heroicon-o-list-bullet";

  public static function getModelLabel(): string
  {
    return __("admin.options.select_option");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.options.select_options");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.settings");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Section::make(__("admin.common.basic_info"))->schema([
        Select::make("type")
          ->label(__("admin.options.fields.type"))
          ->options([
            "revenue_range" => __("admin.options.fields.revenue"),
            "employee_count_range" => __("admin.options.fields.employee_count"),
            "grouping_hiding_reason" => __("admin.options.fields.grouping_hiding_reason"),
          ])
          ->required()
          ->searchable()
          ->disabledOn("edit"),

        TextInput::make("sort_order")
          ->label(__("admin.common.fields.order"))
          ->numeric()
          ->default(0)
          ->required(),

        TextInput::make("label")
          ->label(__("admin.options.fields.title"))
          ->required()
          ->maxLength(255),
      ]),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->defaultPaginationPageOption(50)
      ->modifyQueryUsing(
        fn(Builder $query, ListSelectOptions $livewire, SelectOptionResource $resource) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          ])
          ->orderBy("type")
          ->orderBy("sort_order"),
      )
      ->columns([
        Tables\Columns\TextColumn::make("type")
          ->label(__("admin.options.fields.type"))
          ->formatStateUsing(
            fn(string $state): string => match ($state) {
              "revenue_range" => __("admin.options.fields.revenue"),
              "employee_count_range" => __("admin.options.fields.employee_count"),
              "grouping_hiding_reason" => __("admin.options.fields.grouping_hiding_reason"),
              default => $state,
            },
          )
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("admin.options.fields.title"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("admin.common.fields.order"))
          ->numeric()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->label(__("admin.common.deleted"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\SelectFilter::make("type")
          ->label(__("admin.options.fields.type"))
          ->options([
            "revenue_range" => __("admin.options.fields.revenue"),
            "employee_count_range" => __("admin.options.fields.employee_count"),
            "grouping_hiding_reason" => __("admin.options.fields.grouping_hiding_reason"),
          ])
          ->multiple(),

        Tables\Filters\TrashedFilter::make()->label(__("admin.common.trashed")),
      ])
      ->actions([
        Tables\Actions\EditAction::make(),
        Tables\Actions\DeleteAction::make(),
        Tables\Actions\RestoreAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("type", "asc")
      ->groups([
        Tables\Grouping\Group::make("type")
          ->label(__("admin.options.fields.type"))
          ->getTitleFromRecordUsing(
            fn(SelectOption $record): string => match ($record->type) {
              "revenue_range" => __("admin.options.fields.revenue"),
              "employee_count_range" => __("admin.options.fields.employee_count"),
              "grouping_hiding_reason" => __("admin.options.fields.grouping_hiding_reason"),
              default => $record->type,
            },
          )
          ->collapsible(),
      ])
      ->emptyStateHeading(__("admin.options.empty.no_options"))
      ->emptyStateDescription(__("admin.options.help.create_new"));
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListSelectOptions::route("/"),
      "create" => Pages\CreateSelectOption::route("/create"),
      "edit" => Pages\EditSelectOption::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<SelectOption>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
