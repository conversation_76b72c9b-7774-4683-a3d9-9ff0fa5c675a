<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\SelectOptionResource\Pages;
use App\Filament\Resources\SelectOptionResource\Pages\ListSelectOptions;
use App\Models\SelectOption;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class SelectOptionResource extends Resource
{
  use Translatable;

  protected static ?string $model = SelectOption::class;

  protected static ?string $navigationIcon = "heroicon-o-list-bullet";

  public static function getModelLabel(): string
  {
    return __("Valintavaihtoehto");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Valintavaihtoehdot");
  }

  public static function getNavigationGroup(): string
  {
    return __("Asetukset");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Section::make(__("Perustiedot"))->schema([
        Select::make("type")
          ->label(__("Tyyppi"))
          ->options([
            "revenue_range" => __("Liikevaihto"),
            "employee_count_range" => __("Henkilöstömäärä"),
            "grouping_hiding_reason" => __("Ryhmittelyn piilotuksen syy"),
          ])
          ->required()
          ->searchable()
          ->disabledOn("edit"),

        TextInput::make("sort_order")->label(__("Järjestys"))->numeric()->default(0)->required(),

        TextInput::make("label")->label(__("Nimike"))->required()->maxLength(255),
      ]),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->defaultPaginationPageOption(50)
      ->modifyQueryUsing(
        fn(Builder $query, ListSelectOptions $livewire, SelectOptionResource $resource) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          ])
          ->orderBy("type")
          ->orderBy("sort_order"),
      )
      ->columns([
        Tables\Columns\TextColumn::make("type")
          ->label(__("Tyyppi"))
          ->formatStateUsing(
            fn(string $state): string => match ($state) {
              "revenue_range" => __("Liikevaihto"),
              "employee_count_range" => __("Henkilöstömäärä"),
              "grouping_hiding_reason" => __("Ryhmittelyn piilotuksen syy"),
              default => $state,
            },
          )
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("Nimike"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("Järjestys"))
          ->numeric()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->label(__("Poistettu"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\SelectFilter::make("type")
          ->label(__("Tyyppi"))
          ->options([
            "revenue_range" => __("Liikevaihto"),
            "employee_count_range" => __("Henkilöstömäärä"),
            "grouping_hiding_reason" => __("Ryhmittelyn piilotuksen syy"),
          ])
          ->multiple(),

        Tables\Filters\TrashedFilter::make()->label(__("Poistetut")),
      ])
      ->actions([
        Tables\Actions\EditAction::make(),
        Tables\Actions\DeleteAction::make(),
        Tables\Actions\RestoreAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("type", "asc")
      ->groups([
        Tables\Grouping\Group::make("type")
          ->label(__("Tyyppi"))
          ->getTitleFromRecordUsing(
            fn(SelectOption $record): string => match ($record->type) {
              "revenue_range" => __("Liikevaihto"),
              "employee_count_range" => __("Henkilöstömäärä"),
              "grouping_hiding_reason" => __("Ryhmittelyn piilotuksen syy"),
              default => $record->type,
            },
          )
          ->collapsible(),
      ])
      ->emptyStateHeading(__("Ei valintavaihtoehtoja"))
      ->emptyStateDescription(__("Luo uusi valintavaihtoehto yllä olevalla painikkeella"));
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListSelectOptions::route("/"),
      "create" => Pages\CreateSelectOption::route("/create"),
      "edit" => Pages\EditSelectOption::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<SelectOption>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
