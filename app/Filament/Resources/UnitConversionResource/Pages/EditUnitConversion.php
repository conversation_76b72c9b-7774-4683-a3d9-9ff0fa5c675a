<?php

declare(strict_types=1);

namespace App\Filament\Resources\UnitConversionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\UnitConversionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditUnitConversion extends EditRecord
{
  use Translatable;

  protected static string $resource = UnitConversionResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make()];
  }
}
