<?php

declare(strict_types=1);

namespace App\Filament\Resources\UnitConversionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\UnitConversionResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateUnitConversion extends CreateRecord
{
  use Translatable;

  protected static string $resource = UnitConversionResource::class;
}
