<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\CategoryResource\Pages;
use App\Filament\Resources\CategoryResource\Pages\ListCategories;
use App\Models\CalculationDefinition;
use App\Models\Category;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

final class CategoryResource extends Resource
{
  use Translatable;

  protected static ?string $model = Category::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function form(Form $form): Form
  {
    return $form->schema([
      Section::make(__("admin.common.basic_info"))->schema([
        TextInput::make("title")->label(__("admin.categories.fields.name")),
        TextInput::make("description")->label(__("admin.categories.fields.description")),
        Toggle::make("hide_total")
          ->label(__("admin.categories.help.hide_total_row"))
          ->default(false),
      ]),

      Section::make(__("admin.categories.sections.definition_templates"))
        ->description(__("admin.categories.help.select_definitions_to_copy"))
        ->schema([
          Select::make("templateDefinitions")
            ->label(__("admin.categories.fields.template_definitions"))
            ->relationship(
              "templateDefinitions",
              "id",
              fn(Builder $query) => $query
                ->with(["translations", "scope", "category.translations"])
                ->whereNull("company_id"),
            )
            ->multiple()
            ->searchable(false) // Disable default search
            ->getSearchResultsUsing(function (string $search) {
              $searchLower = mb_strtolower($search);

              return CalculationDefinition::query()
                ->with(["translations", "scope", "category.translations"])
                ->whereNull("company_id")
                ->where(function ($query) use ($search, $searchLower) {
                  $query
                    ->whereHas("translations", function ($q) use ($searchLower) {
                      $q->whereRaw("LOWER(data_name) LIKE ?", ["%{$searchLower}%"]);
                    })
                    // Search in scope number (e.g., "Scope 2")
                    ->orWhereHas("scope", function ($q) use ($search) {
                      $q->where("number", "like", "%{$search}%");
                    })
                    // Search in category name (shown in parentheses)
                    ->orWhereHas("category.translations", function ($q) use ($searchLower) {
                      $q->whereRaw("LOWER(title) LIKE ?", ["%{$searchLower}%"]);
                    });
                })
                ->limit(50)
                ->get()
                ->mapWithKeys(function ($definition) {
                  $scopeNumber = $definition->scope->number ?? "?";
                  $name =
                    $definition->translate()->emission_factor_name ?? "ID: " . $definition->id;
                  $categoryName = $definition->category?->translate()->title ?? "";

                  $label = "Scope {$scopeNumber}: {$name}";
                  if ($categoryName !== "") {
                    $label .= " ({$categoryName})";
                  }

                  return [$definition->id => $label];
                })
                ->toArray();
            })
            ->preload()
            ->getOptionLabelFromRecordUsing(function (CalculationDefinition $record) {
              $scopeNumber = $record->scope->number ?? "?";
              $name = $record->translate()->data_name ?? "ID: " . $record->id;
              $categoryName = $record->category?->translate()->title ?? "";

              $label = "Scope {$scopeNumber}: {$name}";
              if ($categoryName !== "") {
                $label .= " ({$categoryName})";
              }

              return $label;
            })
            ->helperText(__("admin.categories.help.leave_empty_for_single"))
            ->hint(__("admin.categories.help.select_multiple_for_group"))
            ->columnSpanFull(),
        ])
        ->collapsed(),
    ]);
  }

  public static function getModelLabel(): string
  {
    return __("admin.categories.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.categories.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.categories.fields.grouping");
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(Builder $query, ListCategories $livewire, CategoryResource $resource) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          ])
          ->orderBy("sort_order"),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.title",
        )
          ->label(__("admin.categories.fields.name"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.description",
        )
          ->label(__("admin.categories.fields.description"))
          ->sortable(),
        Tables\Columns\ToggleColumn::make("hide_total")
          ->label(__("admin.categories.fields.hide_total"))
          ->sortable(),
        Tables\Columns\TextColumn::make("template_definition_ids")
          ->label(__("admin.categories.fields.templates"))
          ->badge()
          ->formatStateUsing(
            fn($state) => is_array($state)
              ? count($state) . " " . __("admin.categories.units.template")
              : "0 " . __("admin.categories.units.template"),
          )
          ->color(fn($state) => is_array($state) && count($state) > 0 ? "success" : "gray"),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("admin.categories.fields.order"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        //
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ])
      ->reorderable("sort_order")
      ->defaultSort("sort_order");
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListCategories::route("/"),
      "create" => Pages\CreateCategory::route("/create"),
      "edit" => Pages\EditCategory::route("/{record}/edit"),
    ];
  }
}
