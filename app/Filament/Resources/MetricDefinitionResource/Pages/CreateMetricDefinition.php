<?php

declare(strict_types=1);

namespace App\Filament\Resources\MetricDefinitionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\MetricDefinitionResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateMetricDefinition extends CreateRecord
{
  use Translatable;

  protected static string $resource = MetricDefinitionResource::class;
}
