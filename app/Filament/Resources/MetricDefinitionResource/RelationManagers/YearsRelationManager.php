<?php

declare(strict_types=1);

namespace App\Filament\Resources\MetricDefinitionResource\RelationManagers;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns\Translatable;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class YearsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "years";

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("Vuosimäärittelyt");
  }

  public static function getModelLabel(): string
  {
    return __("Vuosimäärittely");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Vuosimäärittelyt");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\TextInput::make("year")
        ->required()
        ->numeric()
        ->label(__("Vuosi"))
        ->disabled(),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("year")
      ->columns([
        Tables\Columns\TextColumn::make("year")->label(__("Vuosi"))->sortable()->searchable(),
        Tables\Columns\TextColumn::make("help_text")
          ->label(__("Ohjeteksti"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState($record, "help_text"),
          )
          ->limit(100)
          ->tooltip(fn(Year $record) => $this->getTranslatableColumnState($record, "help_text"))
          ->searchable(),
        Tables\Columns\TextColumn::make("pivot.created_at")
          ->label(__("Luotu"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        Tables\Columns\TextColumn::make("pivot.updated_at")
          ->label(__("Päivitetty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([])
      ->headerActions([
        $this->configureAttachActionForTranslatablePivot(
          Tables\Actions\AttachAction::make()
            ->preloadRecordSelect()
            ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("year", "desc"))
            ->form(
              /**
               * @return array<Forms\Components\Component>
               */
              fn(Tables\Actions\AttachAction $action): array => [
                $action->getRecordSelect(),
                ...$this->getTranslatablePivotFormSchema(),
              ],
            ),
        ),
      ])
      ->actions([
        $this->configureEditActionForTranslatablePivot(
          Tables\Actions\EditAction::make()->form(
            /** @return array<Forms\Components\Component> */
            fn(): array => $this->getTranslatablePivotFormSchema(),
          ),
        ),
        Tables\Actions\DetachAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DetachBulkAction::make()]),
      ])
      ->defaultSort("year", "desc")
      ->emptyStateHeading(__("Ei vuosimäärittelyjä"))
      ->emptyStateDescription(__("Lisää vuosimäärittelyjä yllä olevalla painikkeella"))
      ->emptyStateIcon("heroicon-o-calendar");
  }

  /**
   * Get the form schema for translatable pivot fields
   *
   * @return array<Forms\Components\Component>
   */
  private function getTranslatablePivotFormSchema(): array
  {
    return [
      Forms\Components\Textarea::make("help_text")
        ->label(__("Ohjeteksti"))
        ->required()
        ->maxLength(65535)
        ->rows(4)
        ->default("")
        ->dehydrateStateUsing(fn($state) => $state ?? "") // Ensure empty string for text fields
        ->helperText(__("Anna ohjeteksti, joka auttaa käyttäjiä ymmärtämään mittarin merkityksen"))
        ->columnSpanFull(),
    ];
  }
}
