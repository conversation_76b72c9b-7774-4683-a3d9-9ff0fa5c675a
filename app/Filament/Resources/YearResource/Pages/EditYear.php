<?php

declare(strict_types=1);

namespace App\Filament\Resources\YearResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\YearResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditYear extends EditRecord
{
  use Translatable;

  protected static string $resource = YearResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make(), Actions\RestoreAction::make()];
  }
}
