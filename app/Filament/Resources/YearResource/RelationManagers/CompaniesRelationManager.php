<?php

declare(strict_types=1);

namespace App\Filament\Resources\YearResource\RelationManagers;

use App\Models\Company;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class CompaniesRelationManager extends RelationManager
{
  protected static string $relationship = "companies";

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("admin.companies.plural");
  }

  public static function getModelLabel(): string
  {
    return __("admin.companies.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.companies.plural");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("company_id")
        ->label(__("admin.companies.label"))
        ->options(Company::orderBy("name")->pluck("name", "id"))
        ->required()
        ->disabledOn("edit")
        ->searchable()
        ->preload(),

      Forms\Components\Section::make(__("admin.companies.programs.climate_programs"))
        ->schema([
          Forms\Components\Toggle::make("participates_in_climate_program")
            ->label(__("admin.companies.participation.climate_program"))
            ->default(false),

          Forms\Components\Toggle::make("participates_in_climate_community")
            ->label(__("admin.companies.participation.climate_community"))
            ->default(false),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.companies.labels.badges"))
        ->schema([
          Forms\Components\Toggle::make("awarded_scope1_2_mark")
            ->label(__("admin.companies.badges.granted_scope_1_2"))
            ->default(false),

          Forms\Components\Toggle::make("awarded_scope1_3_mark")
            ->label(__("admin.companies.badges.granted_scope_1_3"))
            ->default(false),
        ])
        ->columns(2),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("name")
      ->columns([
        Tables\Columns\TextColumn::make("name")
          ->label(__("admin.companies.label"))
          ->searchable()
          ->sortable(),

        Tables\Columns\TextColumn::make("business_id")
          ->label(__("admin.companies.fields.business_id"))
          ->searchable()
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_program")
          ->label(__("admin.companies.programs.climate_program"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_community")
          ->label(__("admin.companies.programs.climate_community"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.awarded_scope1_2_mark")
          ->label(__("admin.companies.scopes.scope_1_2"))
          ->boolean()
          ->trueIcon("heroicon-o-shield-check")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.awarded_scope1_3_mark")
          ->label(__("admin.companies.scopes.scope_1_3"))
          ->boolean()
          ->trueIcon("heroicon-o-shield-check")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\TextColumn::make("pivot.created_at")
          ->label(__("admin.common.added"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("pivot.updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("participates_in_climate_program")
          ->label(__("admin.companies.programs.climate_program"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.companies.participation.participating"))
          ->falseLabel(__("admin.companies.participation.not_participating"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("participates_in_climate_program", true),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_program",
              false,
            ),
          ),

        Tables\Filters\TernaryFilter::make("participates_in_climate_community")
          ->label(__("admin.companies.programs.climate_community"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.companies.participation.participating"))
          ->falseLabel(__("admin.companies.participation.not_participating"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              true,
            ),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              false,
            ),
          ),

        Tables\Filters\TernaryFilter::make("awarded_scope1_2_mark")
          ->label(__("admin.companies.badges.scope_1_2"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.companies.badges.granted"))
          ->falseLabel(__("admin.companies.badges.not_granted"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("awarded_scope1_2_mark", true),
            false: fn(Builder $query) => $query->wherePivot("awarded_scope1_2_mark", false),
          ),

        Tables\Filters\TernaryFilter::make("awarded_scope1_3_mark")
          ->label(__("admin.companies.badges.scope_1_3"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.companies.badges.granted"))
          ->falseLabel(__("admin.companies.badges.not_granted"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("awarded_scope1_3_mark", true),
            false: fn(Builder $query) => $query->wherePivot("awarded_scope1_3_mark", false),
          ),
      ])
      ->headerActions([
        Tables\Actions\AttachAction::make()
          ->label(__("admin.years.actions.add_company"))
          ->preloadRecordSelect()
          ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("name"))
          ->form(
            fn(Tables\Actions\AttachAction $action): array => [
              $action->getRecordSelect()->label(__("admin.companies.label")),

              Forms\Components\Section::make(__("admin.companies.programs.climate_programs"))
                ->schema([
                  Forms\Components\Toggle::make("participates_in_climate_program")
                    ->label(__("admin.companies.participation.climate_program"))
                    ->default(false),

                  Forms\Components\Toggle::make("participates_in_climate_community")
                    ->label(__("admin.companies.participation.climate_community"))
                    ->default(false),
                ])
                ->columns(2),

              Forms\Components\Section::make(__("admin.companies.labels.badges"))
                ->schema([
                  Forms\Components\Toggle::make("awarded_scope1_2_mark")
                    ->label(__("admin.companies.badges.granted_scope_1_2"))
                    ->default(false),

                  Forms\Components\Toggle::make("awarded_scope1_3_mark")
                    ->label(__("admin.companies.badges.granted_scope_1_3"))
                    ->default(false),
                ])
                ->columns(2),
            ],
          ),
      ])
      ->actions([
        Tables\Actions\EditAction::make()->form(
          fn(): array => [
            Forms\Components\Section::make(__("admin.companies.programs.climate_programs"))
              ->schema([
                Forms\Components\Toggle::make("participates_in_climate_program")->label(
                  __("admin.companies.participation.climate_program"),
                ),

                Forms\Components\Toggle::make("participates_in_climate_community")->label(
                  __("admin.companies.participation.climate_community"),
                ),
              ])
              ->columns(2),

            Forms\Components\Section::make(__("admin.companies.labels.badges"))
              ->schema([
                Forms\Components\Toggle::make("awarded_scope1_2_mark")->label(
                  __("admin.companies.badges.granted_scope_1_2"),
                ),

                Forms\Components\Toggle::make("awarded_scope1_3_mark")->label(
                  __("admin.companies.badges.granted_scope_1_3"),
                ),
              ])
              ->columns(2),
          ],
        ),
        Tables\Actions\DetachAction::make()->label(__("admin.common.delete")),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DetachBulkAction::make()->label(__("admin.common.delete_selected")),
        ]),
      ])
      ->defaultSort("name", "asc")
      ->emptyStateHeading(__("admin.years.empty.no_companies"))
      ->emptyStateDescription(__("admin.years.help.add_companies"))
      ->emptyStateIcon("heroicon-o-building-office");
  }
}
