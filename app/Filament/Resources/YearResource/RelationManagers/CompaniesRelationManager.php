<?php

declare(strict_types=1);

namespace App\Filament\Resources\YearResource\RelationManagers;

use App\Models\Company;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class CompaniesRelationManager extends RelationManager
{
  protected static string $relationship = "companies";

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("Yritykset");
  }

  public static function getModelLabel(): string
  {
    return __("Yritys");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Yritykset");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("company_id")
        ->label(__("Yritys"))
        ->options(Company::orderBy("name")->pluck("name", "id"))
        ->required()
        ->disabledOn("edit")
        ->searchable()
        ->preload(),

      Forms\Components\Section::make(__("Ilmasto-ohjelmat"))
        ->schema([
          Forms\Components\Toggle::make("participates_in_climate_program")
            ->label(__("Osallistuu ilmasto-ohjelmaan"))
            ->default(false),

          Forms\Components\Toggle::make("participates_in_climate_community")
            ->label(__("Osallistuu ilmastoyhteisöön"))
            ->default(false),
        ])
        ->columns(2),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("name")
      ->columns([
        Tables\Columns\TextColumn::make("name")->label(__("Yritys"))->searchable()->sortable(),

        Tables\Columns\TextColumn::make("business_id")
          ->label(__("Y-tunnus"))
          ->searchable()
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_program")
          ->label(__("Ilmasto-ohjelma"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_community")
          ->label(__("Ilmastoyhteisö"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\TextColumn::make("pivot.created_at")
          ->label(__("Lisätty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("pivot.updated_at")
          ->label(__("Päivitetty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("participates_in_climate_program")
          ->label(__("Ilmasto-ohjelma"))
          ->placeholder(__("Kaikki"))
          ->trueLabel(__("Osallistuu"))
          ->falseLabel(__("Ei osallistu"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("participates_in_climate_program", true),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_program",
              false,
            ),
          ),

        Tables\Filters\TernaryFilter::make("participates_in_climate_community")
          ->label(__("Ilmastoyhteisö"))
          ->placeholder(__("Kaikki"))
          ->trueLabel(__("Osallistuu"))
          ->falseLabel(__("Ei osallistu"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              true,
            ),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              false,
            ),
          ),
      ])
      ->headerActions([
        Tables\Actions\AttachAction::make()
          ->label(__("Lisää yritys"))
          ->preloadRecordSelect()
          ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("name"))
          ->form(
            fn(Tables\Actions\AttachAction $action): array => [
              $action->getRecordSelect()->label(__("Yritys")),

              Forms\Components\Section::make(__("Ilmasto-ohjelmat"))
                ->schema([
                  Forms\Components\Toggle::make("participates_in_climate_program")
                    ->label(__("Osallistuu ilmasto-ohjelmaan"))
                    ->default(false),

                  Forms\Components\Toggle::make("participates_in_climate_community")
                    ->label(__("Osallistuu ilmastoyhteisöön"))
                    ->default(false),
                ])
                ->columns(2),
            ],
          ),
      ])
      ->actions([
        Tables\Actions\EditAction::make()->form(
          fn(): array => [
            Forms\Components\Section::make(__("Ilmasto-ohjelmat"))
              ->schema([
                Forms\Components\Toggle::make("participates_in_climate_program")->label(
                  __("Osallistuu ilmasto-ohjelmaan"),
                ),

                Forms\Components\Toggle::make("participates_in_climate_community")->label(
                  __("Osallistuu ilmastoyhteisöön"),
                ),
              ])
              ->columns(2),
          ],
        ),
        Tables\Actions\DetachAction::make()->label(__("Poista")),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DetachBulkAction::make()->label(__("Poista valitut")),
        ]),
      ])
      ->defaultSort("name", "asc")
      ->emptyStateHeading(__("Ei yrityksiä"))
      ->emptyStateDescription(__("Lisää yrityksiä tälle vuodelle yllä olevalla painikkeella"))
      ->emptyStateIcon("heroicon-o-building-office");
  }
}
