<?php

declare(strict_types=1);

namespace App\Filament\Resources\ScopeResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\ScopeResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateScope extends CreateRecord
{
  use Translatable;

  protected static string $resource = ScopeResource::class;
}
