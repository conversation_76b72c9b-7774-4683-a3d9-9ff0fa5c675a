<?php

declare(strict_types=1);

namespace App\Filament\Resources\ScopeResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\ScopeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditScope extends EditRecord
{
  use Translatable;

  protected static string $resource = ScopeResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make()];
  }
}
