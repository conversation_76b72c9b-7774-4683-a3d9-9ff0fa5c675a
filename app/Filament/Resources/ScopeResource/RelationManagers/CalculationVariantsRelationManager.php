<?php

declare(strict_types=1);

namespace App\Filament\Resources\ScopeResource\RelationManagers;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns\Translatable;
use App\Filament\Resources\ScopeResource;
use App\Services\ExpressionEvaluatorService;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\HtmlString;
use RuntimeException;

final class CalculationVariantsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "calculationVariants";

  public static function getModelLabel(): string
  {
    return __("admin.scopes.variants.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.scopes.variants.plural");
  }

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("admin.scopes.variants.plural");
  }

  /**
   * @throws RuntimeException
   */
  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("admin.common.basic_info"))
        ->schema([
          Forms\Components\TextInput::make("label")
            ->label(__("admin.scopes.fields.name"))
            ->maxLength(255)
            ->required(),

          Forms\Components\Toggle::make("include_in_total")
            ->label(__("admin.scopes.variants.include_in_total"))
            ->helperText(__("admin.scopes.help.include_in_total"))
            ->default(true)
            ->inline(false),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("admin.scopes.sections.filtering"))
        ->description(__("admin.scopes.help.filter_expression"))
        ->schema([
          Forms\Components\Textarea::make("predicate")
            ->label(__("admin.scopes.fields.filter_expression"))
            ->placeholder(__("admin.scopes.help.leave_empty_for_all"))
            ->helperText(
              new HtmlString(
                '<div class="space-y-3">' .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.scopes.help.available_variables") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">definition_id</code> - ' .
                  __("admin.scopes.variables.definition_id") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">grouping_id</code> - ' .
                  __("admin.scopes.variables.grouping_id") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">category_id</code> - ' .
                  __("admin.scopes.variables.category_id") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.scopes.help.supported_operators") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  "<li>• " .
                  __(
                    'Vertailu: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">== != < > <= >=</code>',
                  ) .
                  "</li>" .
                  "<li>• " .
                  __(
                    'Loogiset: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">and or not</code> (tai <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">&& || !</code>)',
                  ) .
                  "</li>" .
                  "<li>• " .
                  __(
                    'Sulut: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">( )</code> ryhmittelyyn',
                  ) .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.scopes.help.supported_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(value, [array])</code> - ' .
                  __("admin.scopes.formulas.check_in_list") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(value, min, max)</code> - ' .
                  __("admin.scopes.formulas.check_between") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">min(a, b, ...)</code> - ' .
                  __("admin.scopes.formulas.min_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">max(a, b, ...)</code> - ' .
                  __("admin.scopes.formulas.max_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">count([array])</code> - ' .
                  __("admin.scopes.formulas.count_items") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">range(start, end, step)</code> - ' .
                  __("admin.scopes.formulas.create_range") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.scopes.help.examples") .
                  "</p>" .
                  '<ul class="text-sm space-y-1">' .
                  "<li>" .
                  __("admin.scopes.filters.specific_categories") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(category_id, [10, 20, 30])</code></li>' .
                  "<li>" .
                  __("admin.scopes.filters.id_range") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(definition_id, 100, 200)</code></li>' .
                  "<li>" .
                  __("admin.scopes.filters.no_specific_groups") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">not in_array(grouping_id, [5, 6])</code></li>' .
                  "<li>" .
                  __("admin.scopes.help.categories_10_50") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(category_id, range(10, 50))</code></li>' .
                  "<li>" .
                  __("admin.scopes.help.combination") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">(grouping_id == 1 and category_id > 10) or grouping_id == 3</code></li>' .
                  "</ul>" .
                  "</div>" .
                  "</div>",
              ),
            )
            ->rows(4)
            ->columnSpanFull()
            ->live(onBlur: true)
            ->afterStateUpdated(function (
              ?string $state,
              Forms\Components\Textarea $component,
            ): void {
              if ($state === null || $state === "") {
                return;
              }

              try {
                $evaluator = app(ExpressionEvaluatorService::class);
                $isValid = $evaluator->validateExpression($state);

                if (!$isValid) {
                  \Filament\Notifications\Notification::make()
                    ->title(__("admin.scopes.errors.invalid_filter_expression"))
                    ->body(__("admin.scopes.errors.expression_syntax"))
                    ->danger()
                    ->send();
                }
              } catch (Exception $e) {
                \Filament\Notifications\Notification::make()
                  ->title(__("admin.scopes.errors.expression_validation_failed"))
                  ->body(__("admin.scopes.errors.expression_check_system"))
                  ->danger()
                  ->send();
              }
            }),
        ])
        ->collapsed(fn(Forms\Get $get): bool => ($get("predicate") ?? "") === "")
        ->collapsible(),
    ]);
  }

  /**
   * @throws RuntimeException
   */
  public function table(Table $table): Table
  {
    $locale = ScopeResource::getActiveLocale();

    return $table
      ->modifyQueryUsing(
        fn(Builder $query) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $locale),
          ])
          ->orderBy("display_order"),
      )
      ->recordTitleAttribute("id")
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("id")
          ->label("ID")
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("admin.scopes.fields.name"))
          ->searchable()
          ->sortable(),

        Tables\Columns\ToggleColumn::make("include_in_total")
          ->label(__("admin.scopes.variants.in_total"))
          ->sortable()
          ->beforeStateUpdated(function ($record, $state) {
            \Filament\Notifications\Notification::make()
              ->title(
                $state
                  ? __("admin.scopes.messages.variant_added_to_total")
                  : __("admin.scopes.messages.variant_removed_from_total"),
              )
              ->success()
              ->send();
          }),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("predicate")
          ->label(__("admin.scopes.fields.filter_expression"))
          ->limit(50)
          ->tooltip(fn(?string $state): string => $state ?? __("admin.scopes.filters.no_filter"))
          ->placeholder(__("admin.scopes.filters.no_filter"))
          ->toggleable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "display_order",
        )
          ->label(__("admin.scopes.fields.order"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("include_in_total")
          ->label(__("admin.scopes.variants.in_total"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.scopes.variants.included"))
          ->falseLabel(__("admin.scopes.variants.not_included"))
          ->queries(
            true: fn(Builder $query) => $query->where("include_in_total", true),
            false: fn(Builder $query) => $query->where("include_in_total", false),
            blank: fn(Builder $query) => $query,
          ),
      ])
      ->headerActions([
        Tables\Actions\CreateAction::make()->mutateFormDataUsing(function (array $data): array {
          $relationship = $this->getRelationship();
          $maxOrder = $relationship->max("display_order");

          $data["display_order"] = is_numeric($maxOrder) ? ((int) $maxOrder) + 1 : 0;

          // Form default(true) handles this, but ensure it's set for safety
          if (!isset($data["include_in_total"])) {
            $data["include_in_total"] = true;
          }

          return $data;
        }),
      ])
      ->actions([
        $this->configureEditActionForTranslatable(Tables\Actions\EditAction::make()),
        Tables\Actions\DeleteAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ])
      ->reorderable("display_order")
      ->defaultSort("display_order");
  }
}
