<?php

declare(strict_types=1);

namespace App\Filament\Resources\ScopeResource\RelationManagers;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns\Translatable;
use App\Filament\Resources\ScopeResource;
use App\Services\ExpressionEvaluatorService;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\HtmlString;
use RuntimeException;

final class CalculationVariantsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "calculationVariants";

  public static function getModelLabel(): string
  {
    return __("Laskelmavariantti");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Laskelmavariantit");
  }

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("Laskelmavariantit");
  }

  /**
   * @throws RuntimeException
   */
  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("Perustiedot"))
        ->schema([
          Forms\Components\TextInput::make("label")->label(__("Nimi"))->maxLength(255)->required(),

          Forms\Components\Toggle::make("include_in_total")
            ->label(__("Sisällytä kokonaissummaan"))
            ->helperText(__("Määrittää lasketaanko tämä variantti mukaan kokonaispäästöihin"))
            ->default(true)
            ->inline(false),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("Suodatus"))
        ->description(
          __(
            "Määritä suodatuslauseke, joka rajaa mitä laskentamäärittelyjä tähän varianttiin sisällytetään",
          ),
        )
        ->schema([
          Forms\Components\Textarea::make("predicate")
            ->label(__("Suodatuslauseke"))
            ->placeholder(__("Jätä tyhjäksi sisällyttääksesi kaikki määritelmät"))
            ->helperText(
              new HtmlString(
                '<div class="space-y-3">' .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("Käytettävissä olevat muuttujat:") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">definition_id</code> - ' .
                  __("määritelmän ID") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">grouping_id</code> - ' .
                  __("ryhmittelyn ID") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">category_id</code> - ' .
                  __("kategorian ID") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("Tuetut operaattorit:") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  "<li>• " .
                  __(
                    'Vertailu: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">== != < > <= >=</code>',
                  ) .
                  "</li>" .
                  "<li>• " .
                  __(
                    'Loogiset: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">and or not</code> (tai <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">&& || !</code>)',
                  ) .
                  "</li>" .
                  "<li>• " .
                  __(
                    'Sulut: <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">( )</code> ryhmittelyyn',
                  ) .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("Tuetut funktiot:") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(value, [array])</code> - ' .
                  __("tarkista sisältyykö arvo listaan") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(value, min, max)</code> - ' .
                  __("tarkista onko arvo välillä") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">min(a, b, ...)</code> - ' .
                  __("pienin arvo") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">max(a, b, ...)</code> - ' .
                  __("suurin arvo") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">count([array])</code> - ' .
                  __("laske alkioiden määrä") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">range(start, end, step)</code> - ' .
                  __("luo numerosarja") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("Esimerkkejä:") .
                  "</p>" .
                  '<ul class="text-sm space-y-1">' .
                  "<li>" .
                  __("Tietyt kategoriat:") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(category_id, [10, 20, 30])</code></li>' .
                  "<li>" .
                  __("ID-väli:") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(definition_id, 100, 200)</code></li>' .
                  "<li>" .
                  __("Ei tiettyjä ryhmiä:") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">not in_array(grouping_id, [5, 6])</code></li>' .
                  "<li>" .
                  __("Kategoriat 10-50:") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(category_id, range(10, 50))</code></li>' .
                  "<li>" .
                  __("Yhdistelmä:") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">(grouping_id == 1 and category_id > 10) or grouping_id == 3</code></li>' .
                  "</ul>" .
                  "</div>" .
                  "</div>",
              ),
            )
            ->rows(4)
            ->columnSpanFull()
            ->live(onBlur: true)
            ->afterStateUpdated(function (
              ?string $state,
              Forms\Components\Textarea $component,
            ): void {
              if ($state === null || $state === "") {
                return;
              }

              try {
                $evaluator = app(ExpressionEvaluatorService::class);
                $isValid = $evaluator->validateExpression($state);

                if (!$isValid) {
                  \Filament\Notifications\Notification::make()
                    ->title(__("Virheellinen suodatuslauseke"))
                    ->body(__("Lausekkeen syntaksi on virheellinen"))
                    ->danger()
                    ->send();
                }
              } catch (Exception $e) {
                \Filament\Notifications\Notification::make()
                  ->title(__("Lausekkeen validointi epäonnistui"))
                  ->body(__("Järjestelmävirhe lausekkeen tarkistuksessa"))
                  ->danger()
                  ->send();
              }
            }),
        ])
        ->collapsed(fn(Forms\Get $get): bool => ($get("predicate") ?? "") === "")
        ->collapsible(),
    ]);
  }

  /**
   * @throws RuntimeException
   */
  public function table(Table $table): Table
  {
    $locale = ScopeResource::getActiveLocale();

    return $table
      ->modifyQueryUsing(
        fn(Builder $query) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $locale),
          ])
          ->orderBy("display_order"),
      )
      ->recordTitleAttribute("id")
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("id")
          ->label("ID")
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("Nimi"))
          ->searchable()
          ->sortable(),

        Tables\Columns\ToggleColumn::make("include_in_total")
          ->label(__("Kokonaissummassa"))
          ->sortable()
          ->beforeStateUpdated(function ($record, $state) {
            \Filament\Notifications\Notification::make()
              ->title(
                $state
                  ? __("Variantti lisätty kokonaissummaan")
                  : __("Variantti poistettu kokonaissummasta"),
              )
              ->success()
              ->send();
          }),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("predicate")
          ->label(__("Suodatuslauseke"))
          ->limit(50)
          ->tooltip(fn(?string $state): string => $state ?? __("Ei suodatusta"))
          ->placeholder(__("Ei suodatusta"))
          ->toggleable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "display_order",
        )
          ->label(__("Järjestys"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("include_in_total")
          ->label(__("Kokonaissummassa"))
          ->placeholder(__("Kaikki"))
          ->trueLabel(__("Sisällytetty"))
          ->falseLabel(__("Ei sisällytetty"))
          ->queries(
            true: fn(Builder $query) => $query->where("include_in_total", true),
            false: fn(Builder $query) => $query->where("include_in_total", false),
            blank: fn(Builder $query) => $query,
          ),
      ])
      ->headerActions([
        Tables\Actions\CreateAction::make()->mutateFormDataUsing(function (array $data): array {
          $relationship = $this->getRelationship();
          $maxOrder = $relationship->max("display_order");

          $data["display_order"] = is_numeric($maxOrder) ? ((int) $maxOrder) + 1 : 0;

          // Form default(true) handles this, but ensure it's set for safety
          if (!isset($data["include_in_total"])) {
            $data["include_in_total"] = true;
          }

          return $data;
        }),
      ])
      ->actions([Tables\Actions\EditAction::make(), Tables\Actions\DeleteAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ])
      ->reorderable("display_order")
      ->defaultSort("display_order");
  }
}
