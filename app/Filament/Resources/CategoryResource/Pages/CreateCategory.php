<?php

declare(strict_types=1);

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\CategoryResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateCategory extends CreateRecord
{
  use Translatable;

  protected static string $resource = CategoryResource::class;
}
