<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\LanguageLineResource\Pages;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Config;
use RuntimeException;
use Spatie\TranslationLoader\LanguageLine;

final class LanguageLineResource extends Resource
{
  protected static bool $shouldRegisterNavigation = false;

  protected static ?string $model = LanguageLine::class;

  protected static ?string $navigationIcon = "heroicon-o-language";

  protected static ?string $navigationGroup = "Asetukset";

  protected static ?string $navigationLabel = "Käännökset";

  protected static ?string $modelLabel = "Käännös";

  protected static ?string $pluralModelLabel = "Käännökset";

  protected static ?int $navigationSort = 90;

  public static function getNavigationGroup(): string
  {
    return __("Asetukset");
  }

  public static function getNavigationLabel(): string
  {
    return __("Käännökset");
  }

  public static function getModelLabel(): string
  {
    return __("Käännös");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Käännökset");
  }

  /**
   * @throws RuntimeException
   */
  public static function form(Form $form): Form
  {
    // Get available locales
    $locales = self::getAvailableLocales();
    $defaultLocale = Config::string("app.locale");

    return $form->schema([
      Forms\Components\Section::make(__("Käännöksen tiedot"))
        ->schema([
          Forms\Components\TextInput::make("group")
            ->label(__("Ryhmä"))
            ->required()
            ->maxLength(255)
            ->disabled(),

          Forms\Components\TextInput::make("key")
            ->label(__("Avain"))
            ->required()
            ->maxLength(255)
            ->disabled(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("Käännökset"))
        ->schema(self::getTranslationFields($locales, $defaultLocale))
        ->columns(1),
    ]);
  }

  /**
   * Get an array of available locales.
   *
   * @return array<string, string>
   */
  public static function getAvailableLocales(): array
  {
    $localizationSettings = app(\App\Settings\LocalizationSettings::class);
    $settingsLocales = $localizationSettings->available_locales;

    return $settingsLocales;
  }

  public static function table(Table $table): Table
  {
    $locales = self::getAvailableLocales();
    $currentLocale = app()->getLocale();
    $localeDisplay = $locales[$currentLocale] ?? ucfirst($currentLocale);

    return $table
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("key")
          ->label(__("Avain"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("group")
          ->label(__("Ryhmä"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "text.{$currentLocale}",
        )
          ->label(__("Käännös (:locale)", ["locale" => $localeDisplay]))
          ->searchable()
          ->sortable()
          ->limit(50)
          ->wrap(),
      ])
      ->filters([
        Tables\Filters\Filter::make("missing_translations")
          ->label(__("Puuttuvat käännökset"))
          ->query(function (Builder $query): Builder {
            $locale = app()->getLocale();

            return $query->where(function (Builder $query) use ($locale): void {
              $query
                ->whereRaw("JSON_EXTRACT(text, ?) IS NULL", ["$.{$locale}"])
                ->orWhereRaw("JSON_EXTRACT(text, ?) = 'null'", ["$.{$locale}"])
                ->orWhereRaw("JSON_EXTRACT(text, ?) = ''", ["$.{$locale}"]);
            });
          }),

        Tables\Filters\SelectFilter::make("group")
          ->label(__("Ryhmä"))
          ->options(function (): array {
            return LanguageLine::query()->distinct("group")->pluck("group", "group")->toArray();
          }),
      ])
      ->actions([Tables\Actions\EditAction::make()->label(__("Muokkaa"))])
      ->bulkActions([])
      ->defaultSort("key");
  }

  public static function getRelations(): array
  {
    return [];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListLanguageLines::route("/"),
      "edit" => Pages\EditLanguageLine::route("/{record}/edit"),
    ];
  }

  public static function getNavigationBadge(): ?string
  {
    $locale = app()->getLocale();

    $count = LanguageLine::query()
      ->where(function (Builder $query) use ($locale): void {
        $query
          ->whereRaw("JSON_EXTRACT(text, ?) IS NULL", ["$.{$locale}"])
          ->orWhereRaw("JSON_EXTRACT(text, ?) = 'null'", ["$.{$locale}"])
          ->orWhereRaw("JSON_EXTRACT(text, ?) = ''", ["$.{$locale}"]);
      })
      ->count();

    return $count > 0 ? (string) $count : null;
  }

  /**
   * Get the translation fields for the form.
   *
   * @param  array<string, string>  $locales
   * @return array<int, Forms\Components\Section>
   */
  protected static function getTranslationFields(array $locales, string $defaultLocale): array
  {
    $fields = [];

    foreach ($locales as $locale => $label) {
      $fields[] = Forms\Components\Section::make($label)
        ->schema([
          Forms\Components\Textarea::make("text.{$locale}")
            ->label($label)
            ->required(false)
            ->rows(3),
        ])
        ->compact();
    }

    return $fields;
  }
}
