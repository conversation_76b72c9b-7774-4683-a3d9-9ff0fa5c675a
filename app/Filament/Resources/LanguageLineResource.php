<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\LanguageLineResource\Pages;
use App\Helpers\Assert;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Config;
use RuntimeException;
use Spatie\TranslationLoader\LanguageLine;

final class LanguageLineResource extends Resource
{
  use Translatable;

  protected static ?string $model = LanguageLine::class;

  protected static ?string $navigationIcon = "heroicon-o-language";

  protected static ?int $navigationSort = 90;

  /**
   * @throws RuntimeException
   */
  public static function form(Form $form): Form
  {
    $locales = self::getAvailableLocales();

    return $form->schema([
      Forms\Components\Hidden::make("group")->default("*"),

      Forms\Components\Section::make(__("admin.translations.sections.details"))->schema([
        Forms\Components\TextInput::make("key")
          ->label(__("admin.translations.fields.key"))
          ->required()
          ->maxLength(255)
          ->disabled(),
      ]),

      Forms\Components\Section::make(__("admin.translations.sections.translations"))
        ->schema(self::getTranslationFields($locales))
        ->columns(1),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("key")
          ->label(__("admin.translations.fields.key"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("text")
          ->label(__("admin.translations.fields.translation"))
          ->formatStateUsing(function ($state, LanguageLine $record) {
            $locale = static::getActiveLocale();
            $text = $record->text;

            // The text property is always an array according to the model's PHPDoc
            // Check if the locale key exists and has a value
            if (!isset($text[$locale]) || $text[$locale] === "") {
              return "";
            }

            return $text[$locale];
          })
          ->searchable(
            query: function (Builder $query, string $search): Builder {
              $locale = static::getActiveLocale();

              return $query->whereRaw("LOWER(JSON_EXTRACT(text, ?)) LIKE LOWER(?)", [
                "$.{$locale}",
                "%{$search}%",
              ]);
            },
          )
          ->sortable()
          ->limit(50)
          ->wrap(),
      ])
      ->filters([
        Tables\Filters\Filter::make("missing_translations")
          ->label(__("admin.translations.filters.missing_translations"))
          ->query(function (Builder $query): Builder {
            $locale = static::getActiveLocale();

            return $query->where(function (Builder $query) use ($locale): void {
              $query
                ->whereRaw("JSON_EXTRACT(text, ?) IS NULL", ["$.{$locale}"])
                ->orWhereRaw("JSON_EXTRACT(text, ?) = 'null'", ["$.{$locale}"])
                ->orWhereRaw("JSON_EXTRACT(text, ?) = ''", ["$.{$locale}"]);
            });
          }),
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([])
      ->defaultSort("key");
  }

  public static function getRelations(): array
  {
    return [];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListLanguageLines::route("/"),
      "edit" => Pages\EditLanguageLine::route("/{record}/edit"),
    ];
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.settings");
  }

  public static function getNavigationLabel(): string
  {
    return __("admin.translations.plural");
  }

  public static function getModelLabel(): string
  {
    return __("admin.translations.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.translations.plural");
  }

  /**
   * Get an array of available locales.
   *
   * @return non-empty-list<string>
   *
   * @throws RuntimeException
   */
  public static function getAvailableLocales(): array
  {
    $locales = Config::array("translatable.locales");
    Assert::stringArray($locales);
    Assert::nonEmptyList($locales);

    return $locales;
  }

  /**
   * Get the translation fields for the form.
   *
   * @param  non-empty-list<string>  $locales
   * @return non-empty-list<Forms\Components\Component>
   */
  protected static function getTranslationFields(array $locales): array
  {
    $fields = [];

    foreach ($locales as $locale) {
      $fields[] = Forms\Components\Textarea::make("text.{$locale}")
        ->label(ucfirst($locale))
        ->rows(3);
    }

    return $fields;
  }
}
