<?php

namespace App\Filament\Resources\OptionSetResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\OptionSetResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOptionSet extends EditRecord
{
  use Translatable;

  protected static string $resource = OptionSetResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make()];
  }
}
