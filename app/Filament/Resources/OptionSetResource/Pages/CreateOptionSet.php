<?php

declare(strict_types=1);

namespace App\Filament\Resources\OptionSetResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\OptionSetResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateOptionSet extends CreateRecord
{
  use Translatable;

  protected static string $resource = OptionSetResource::class;
}
