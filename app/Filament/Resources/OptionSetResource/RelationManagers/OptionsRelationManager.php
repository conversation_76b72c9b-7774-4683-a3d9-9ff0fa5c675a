<?php

declare(strict_types=1);

namespace App\Filament\Resources\OptionSetResource\RelationManagers;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Models\CalculationDefinitionOption;
use App\Models\CalculationDefinitionOptionYearValue;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

final class OptionsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "options";

  public static function getModelLabel(): string
  {
    return __("admin.options.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.options.plural");
  }

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("admin.options.plural");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("admin.options.sections.option_details"))
        ->schema([
          Forms\Components\TextInput::make("label")
            ->label(__("admin.options.fields.option_name"))
            ->required()
            ->maxLength(255),

          Forms\Components\TextInput::make("sort_order")
            ->label(__("admin.options.fields.order"))
            ->numeric()
            ->default(0),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.options.sections.year_values"))
        ->schema([
          Forms\Components\Repeater::make("yearValues")
            ->relationship("yearValues")
            ->hiddenLabel()
            ->schema([
              Forms\Components\Select::make("year_id")
                ->label(__("admin.options.fields.year"))
                ->options(Year::orderBy("year", "desc")->pluck("year", "id"))
                ->required()
                ->disableOptionsWhenSelectedInSiblingRepeaterItems(),

              Forms\Components\TextInput::make("value")
                ->label(__("admin.options.fields.value"))
                ->rules([new \App\Rules\ValidBigDecimal()])
                ->required(),
            ])
            ->columns(2)
            ->defaultItems(0)
            ->addActionLabel(__("admin.options.actions.add_year_value")),
        ])
        ->collapsed(),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("id")
      ->modifyQueryUsing(
        fn(Builder $query) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", self::getActiveLocale()),
            "yearValues.year",
          ])
          ->orderBy("sort_order"),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("admin.options.fields.option_name"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("yearValues")
          ->label(__("admin.options.fields.values"))
          ->getStateUsing(function (CalculationDefinitionOption $record): string {
            $values = $record
              ->yearValues()
              ->with("year")
              ->get()
              ->sortByDesc(fn(CalculationDefinitionOptionYearValue $yv) => $yv->year->year ?? 0)
              ->map(
                fn(CalculationDefinitionOptionYearValue $yv) => $yv->year !== null
                  ? $yv->year->year . ": " . $yv->value
                  : "?: " . $yv->value,
              )
              ->take(3)
              ->join(", ");

            if ($record->yearValues()->count() > 3) {
              $values .= " ...";
            }

            return $values === "" ? "-" : $values;
          }),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("admin.options.fields.order"))
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->reorderable("sort_order")
      ->defaultSort("sort_order")
      ->actions([
        Tables\Actions\EditAction::make(),
        Tables\Actions\DeleteAction::make()->before(function (CalculationDefinitionOption $record) {
          // Delete all related year values before deleting the option
          $record->yearValues()->delete();
        }),
      ])
      ->headerActions([Tables\Actions\CreateAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ]);
  }
}
