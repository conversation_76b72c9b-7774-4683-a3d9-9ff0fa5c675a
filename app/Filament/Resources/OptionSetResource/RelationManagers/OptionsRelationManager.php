<?php

declare(strict_types=1);

namespace App\Filament\Resources\OptionSetResource\RelationManagers;

use App\Enums\InputMethod;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Models\CalculationDefinitionOption;
use App\Models\CalculationDefinitionOptionYearValue;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

final class OptionsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "options";

  public static function getModelLabel(): string
  {
    return __("Vaihtoehto");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Vaihtoehdot");
  }

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("Vaihtoehdot");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("Vaihtoehdon tiedot"))
        ->schema([
          Forms\Components\TextInput::make("label")
            ->label(__("Vaihtoehdon nimi"))
            ->required()
            ->maxLength(255),

          Forms\Components\TextInput::make("sort_order")
            ->label(__("Järjestys"))
            ->numeric()
            ->default(0),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("Vuosikohtaiset arvot"))
        ->schema([
          Forms\Components\Repeater::make("yearValues")
            ->relationship("yearValues")
            ->hiddenLabel()
            ->schema([
              Forms\Components\Select::make("year_id")
                ->label(__("Vuosi"))
                ->options(Year::orderBy("year", "desc")->pluck("year", "id"))
                ->required()
                ->disableOptionsWhenSelectedInSiblingRepeaterItems(),

              Forms\Components\TextInput::make("value")
                ->label(__("Arvo"))
                ->rules([new \App\Rules\ValidBigDecimal()])
                ->required(),
            ])
            ->columns(2)
            ->defaultItems(0)
            ->addActionLabel(__("Lisää vuosiarvo")),
        ])
        ->collapsed(),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("id")
      ->modifyQueryUsing(
        fn(Builder $query) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", self::getActiveLocale()),
            "yearValues.year",
          ])
          ->orderBy("sort_order"),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.label",
        )
          ->label(__("Vaihtoehdon nimi"))
          ->searchable()
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("yearValues")
          ->label(__("Arvot"))
          ->getStateUsing(function (CalculationDefinitionOption $record): string {
            $values = $record
              ->yearValues()
              ->with("year")
              ->get()
              ->sortByDesc(fn(CalculationDefinitionOptionYearValue $yv) => $yv->year->year ?? 0)
              ->map(
                fn(CalculationDefinitionOptionYearValue $yv) => $yv->year !== null
                  ? $yv->year->year . ": " . $yv->value
                  : "?: " . $yv->value,
              )
              ->take(3)
              ->join(", ");

            if ($record->yearValues()->count() > 3) {
              $values .= " ...";
            }

            return $values === "" ? "-" : $values;
          }),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("Järjestys"))
          ->sortable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->reorderable("sort_order")
      ->defaultSort("sort_order")
      ->actions([Tables\Actions\EditAction::make(), Tables\Actions\DeleteAction::make()])
      ->headerActions([Tables\Actions\CreateAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ]);
  }
}
