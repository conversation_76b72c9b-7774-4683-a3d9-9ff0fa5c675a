<?php

declare(strict_types=1);

namespace App\Filament\Resources\CompoundUnitResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\CompoundUnitResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditCompoundUnit extends EditRecord
{
  use Translatable;

  protected static string $resource = CompoundUnitResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make(), Actions\RestoreAction::make()];
  }
}
