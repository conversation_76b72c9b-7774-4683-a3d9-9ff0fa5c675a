<?php

declare(strict_types=1);

namespace App\Filament\Resources\CompoundUnitResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\CompoundUnitResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateCompoundUnit extends CreateRecord
{
  use Translatable;

  protected static string $resource = CompoundUnitResource::class;
}
