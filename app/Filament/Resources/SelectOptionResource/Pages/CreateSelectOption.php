<?php

declare(strict_types=1);

namespace App\Filament\Resources\SelectOptionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\SelectOptionResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateSelectOption extends CreateRecord
{
  use Translatable;

  protected static string $resource = SelectOptionResource::class;
}
