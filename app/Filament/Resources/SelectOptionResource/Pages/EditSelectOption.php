<?php

declare(strict_types=1);

namespace App\Filament\Resources\SelectOptionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\SelectOptionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditSelectOption extends EditRecord
{
  use Translatable;

  protected static string $resource = SelectOptionResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make()];
  }
}
