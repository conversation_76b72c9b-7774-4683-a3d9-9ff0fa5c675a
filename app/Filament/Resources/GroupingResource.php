<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\GroupingResource\Pages;
use App\Filament\Resources\GroupingResource\Pages\ListGroupings;
use App\Models\Grouping;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class GroupingResource extends Resource
{
  use Translatable;

  protected static ?string $model = Grouping::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function form(Form $form): Form
  {
    return $form->schema([TextInput::make("title")->label(__("Nimi"))]);
  }

  public static function getModelLabel(): string
  {
    return __("Ryhmittely");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Ryhmittelyt");
  }

  public static function getNavigationGroup(): string
  {
    return __("Jaottelu");
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(Builder $query, ListGroupings $livewire, GroupingResource $resource) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          ])
          ->orderBy("sort_order"),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.title",
        )
          ->label(__("Nimi"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("Järjestys"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->reorderable("sort_order")
      ->defaultSort("sort_order");
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListGroupings::route("/"),
      "create" => Pages\CreateGrouping::route("/create"),
      "edit" => Pages\EditGrouping::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Grouping>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
