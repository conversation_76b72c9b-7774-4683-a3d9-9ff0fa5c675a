<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\CompanyResource\Pages;
use App\Filament\Resources\CompanyResource\RelationManagers;
use App\Helpers\Assert;
use App\Models\Company;
use App\Models\IndustryClassification;
use App\Models\Municipality;
use App\Models\SelectOption;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

final class CompanyResource extends Resource
{
  use Translatable;

  protected static ?string $model = Company::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("Yritys");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Yritykset");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Hidden::make("terms_of_service_accepted_at")
        ->default(fn() => \Carbon\Carbon::createFromTimestamp(0))
        ->dehydrateStateUsing(fn($state) => $state ?? \Carbon\Carbon::createFromTimestamp(0)),

      Forms\Components\Section::make(__("Perustiedot"))
        ->schema([
          Forms\Components\TextInput::make("name")->required()->maxLength(255)->label(__("Nimi")),
          Forms\Components\TextInput::make("business_id")
            ->required()
            ->maxLength(9)
            ->label(__("Y-tunnus")),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("Sijainti ja toimiala"))
        ->schema([
          Forms\Components\Select::make("municipality_id")
            ->relationship("municipality", "id", fn(Builder $query) => $query->with("translations"))
            ->getOptionLabelFromRecordUsing(
              fn(
                Municipality $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("Kunta"))
            ->searchable()
            ->preload(),
          Forms\Components\Select::make("industry_classification_id")
            ->relationship(
              "industryClassification",
              "id",
              fn(Builder $query) => $query->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                IndustryClassification $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("Toimialaluokitus"))
            ->searchable()
            ->preload(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("Yrityksen koko"))
        ->schema([
          Forms\Components\Select::make("revenue_range_id")
            ->relationship(
              "revenueRange",
              "id",
              fn(Builder $query) => $query->where("type", "revenue_range")->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                SelectOption $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->label ?? "",
            )
            ->label(__("Liikevaihto"))
            ->preload(),
          Forms\Components\Select::make("employee_count_range_id")
            ->relationship(
              "employeeCountRange",
              "id",
              fn(Builder $query) => $query
                ->where("type", "employee_count_range")
                ->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                SelectOption $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->label ?? "",
            )
            ->label(__("Henkilöstömäärä"))
            ->preload(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("Tilikausi"))
        ->schema([
          Forms\Components\TextInput::make("fiscal_start_day")
            ->required()
            ->numeric()
            ->label(__("Tilikauden alkupäivä"))
            ->minValue(1)
            ->maxValue(31)
            ->default(1),
          Forms\Components\TextInput::make("fiscal_start_month")
            ->required()
            ->numeric()
            ->label(__("Tilikauden alkukuu"))
            ->minValue(1)
            ->maxValue(12)
            ->default(1),
          Forms\Components\TextInput::make("fiscal_end_day")
            ->required()
            ->numeric()
            ->label(__("Tilikauden loppupäivä"))
            ->minValue(1)
            ->maxValue(31)
            ->default(31),
          Forms\Components\TextInput::make("fiscal_end_month")
            ->required()
            ->numeric()
            ->label(__("Tilikauden loppukuu"))
            ->minValue(1)
            ->maxValue(12)
            ->default(12),
        ])
        ->columns(4),

      Forms\Components\Section::make(__("Suostumukset"))
        ->schema([
          Forms\Components\Toggle::make("consent_for_data_examination")
            ->required()
            ->label(__("Suostumus datan käsittelyyn"))
            ->default(false),
          Forms\Components\Toggle::make("applying_for_scope1_2_mark")
            ->required()
            ->label(__("Hakee Scope 1-2 merkkiä"))
            ->default(false)
            ->reactive()
            ->afterStateUpdated(function ($state, callable $set) {
              if ($state) {
                $set("applying_for_scope1_3_mark", false);
              }
            }),
          Forms\Components\Toggle::make("applying_for_scope1_3_mark")
            ->required()
            ->label(__("Hakee Scope 1-3 merkkiä"))
            ->default(false)
            ->reactive()
            ->afterStateUpdated(function ($state, callable $set) {
              if ($state) {
                $set("applying_for_scope1_2_mark", false);
              }
            }),
        ])
        ->columns(3),

      Forms\Components\Section::make(__("Laskutustiedot"))
        ->schema([
          Forms\Components\TextInput::make("e_invoice_contact_name")
            ->maxLength(255)
            ->label(__("Yhteyshenkilön nimi")),
          Forms\Components\TextInput::make("e_invoice_contact_email")
            ->email()
            ->maxLength(255)
            ->label(__("Yhteyshenkilön sähköposti")),
          Forms\Components\TextInput::make("e_invoice_address")
            ->maxLength(255)
            ->label(__("Verkkolaskuosoite")),
          Forms\Components\TextInput::make("e_invoice_operator")
            ->maxLength(255)
            ->label(__("Verkkolaskuoperaattori")),
          Forms\Components\TextInput::make("e_invoice_reference")
            ->maxLength(255)
            ->label(__("Viite")),
          Forms\Components\Textarea::make("e_invoice_additional_info")
            ->label(__("Lisätiedot"))
            ->rows(4)
            ->columnSpanFull(),
        ])
        ->columns(2),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->defaultPaginationPageOption(50)
      ->modifyQueryUsing(
        fn(Builder $query, CompanyResource $resource) => $query->with([
          "municipality.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "industryClassification.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "revenueRange.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "employeeCountRange.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("name")
          ->label(__("Nimi"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("business_id")
          ->label(__("Y-tunnus"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "municipality.translations.name",
        )
          ->label(__("Kunta"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "industryClassification.translations.name",
        )
          ->label(__("Toimiala"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "revenueRange.translations.label",
        )
          ->label(__("Liikevaihto"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "employeeCountRange.translations.label",
        )
          ->label(__("Henkilöstömäärä"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_start_day",
        )
          ->label(__("Tilikauden alkupäivä"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_start_month",
        )
          ->label(__("Tilikauden alkukuu"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_end_day",
        )
          ->label(__("Tilikauden loppupäivä"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_end_month",
        )
          ->label(__("Tilikauden loppukuu"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        Tables\Columns\IconColumn::make("consent_for_data_examination")
          ->label(__("Suostumus datan käsittelyyn"))
          ->boolean()
          ->sortable(),
        Tables\Columns\IconColumn::make("applying_for_scope1_2_mark")
          ->label(__("Hakee Scope 1-2 merkkiä"))
          ->boolean()
          ->sortable(),
        Tables\Columns\IconColumn::make("applying_for_scope1_3_mark")
          ->label(__("Hakee Scope 1-3 merkkiä"))
          ->boolean()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_contact_name",
        )
          ->label(__("Yhteyshenkilön nimi"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_contact_email",
        )
          ->label(__("Yhteyshenkilön sähköposti"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_address",
        )
          ->label(__("Verkkolaskuosoite"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_operator",
        )
          ->label(__("Verkkolaskuoperaattori"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_reference",
        )
          ->label(__("Viite"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_additional_info",
        )
          ->label(__("Lisätiedot"))
          ->limit(50)
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([
        Tables\Actions\Action::make("select")
          ->label(__("Avaa"))
          ->action(function (Company $record) {
            $user = Auth::user();
            Assert::notNull($user);
            $user->update([
              "selected_company_id" => $record->id,
            ]);

            return redirect()->to(Config::string("misc.auth_redirect_url"));
          })
          ->openUrlInNewTab(),
        Tables\Actions\EditAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [RelationManagers\YearsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListCompanies::route("/"),
      "create" => Pages\CreateCompany::route("/create"),
      "edit" => Pages\EditCompany::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Company>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
