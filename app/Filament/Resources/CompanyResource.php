<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\CompanyResource\Pages;
use App\Filament\Resources\CompanyResource\RelationManagers;
use App\Helpers\Assert;
use App\Models\Company;
use App\Models\IndustryClassification;
use App\Models\Municipality;
use App\Models\SelectOption;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

final class CompanyResource extends Resource
{
  use Translatable;

  protected static ?string $model = Company::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("admin.companies.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.companies.plural");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Hidden::make("terms_of_service_accepted_at")
        ->default(fn() => \Carbon\Carbon::createFromTimestamp(0))
        ->dehydrateStateUsing(fn($state) => $state ?? \Carbon\Carbon::createFromTimestamp(0)),

      Forms\Components\Section::make(__("admin.common.basic_info"))
        ->schema([
          Forms\Components\TextInput::make("name")
            ->required()
            ->maxLength(255)
            ->label(__("admin.companies.fields.name")),
          Forms\Components\TextInput::make("business_id")
            ->required()
            ->maxLength(9)
            ->label(__("admin.companies.fields.business_id")),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.companies.sections.location_industry"))
        ->schema([
          Forms\Components\Select::make("municipality_id")
            ->relationship("municipality", "id", fn(Builder $query) => $query->with("translations"))
            ->getOptionLabelFromRecordUsing(
              fn(
                Municipality $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("admin.companies.fields.municipality"))
            ->searchable()
            ->preload(),
          Forms\Components\Select::make("industry_classification_id")
            ->relationship(
              "industryClassification",
              "id",
              fn(Builder $query) => $query->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                IndustryClassification $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("admin.companies.fields.industry_classification"))
            ->searchable()
            ->preload(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.companies.sections.company_size"))
        ->schema([
          Forms\Components\Select::make("revenue_range_id")
            ->relationship(
              "revenueRange",
              "id",
              fn(Builder $query) => $query->where("type", "revenue_range")->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                SelectOption $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->label ?? "",
            )
            ->label(__("admin.companies.fields.revenue"))
            ->preload(),
          Forms\Components\Select::make("employee_count_range_id")
            ->relationship(
              "employeeCountRange",
              "id",
              fn(Builder $query) => $query
                ->where("type", "employee_count_range")
                ->with("translations"),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                SelectOption $record,
                Page $livewire,
                CompanyResource $resource,
              ) => $record->translate($resource::getActiveLocale())->label ?? "",
            )
            ->label(__("admin.companies.fields.employee_count"))
            ->preload(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.companies.sections.fiscal_year"))
        ->schema([
          Forms\Components\TextInput::make("fiscal_start_day")
            ->required()
            ->numeric()
            ->label(__("admin.companies.fields.fiscal_start_day"))
            ->minValue(1)
            ->maxValue(31)
            ->default(1),
          Forms\Components\TextInput::make("fiscal_start_month")
            ->required()
            ->numeric()
            ->label(__("admin.companies.fields.fiscal_start_month"))
            ->minValue(1)
            ->maxValue(12)
            ->default(1),
          Forms\Components\TextInput::make("fiscal_end_day")
            ->required()
            ->numeric()
            ->label(__("admin.companies.fields.fiscal_end_day"))
            ->minValue(1)
            ->maxValue(31)
            ->default(31),
          Forms\Components\TextInput::make("fiscal_end_month")
            ->required()
            ->numeric()
            ->label(__("admin.companies.fields.fiscal_end_month"))
            ->minValue(1)
            ->maxValue(12)
            ->default(12),
        ])
        ->columns(4),

      Forms\Components\Section::make(__("admin.companies.sections.consents"))
        ->schema([
          Forms\Components\Toggle::make("consent_for_data_examination")
            ->required()
            ->label(__("admin.companies.fields.data_processing_consent"))
            ->default(false),
          Forms\Components\Toggle::make("applying_for_scope1_2_mark")
            ->required()
            ->label(__("admin.companies.fields.applying_scope_1_2"))
            ->default(false)
            ->reactive()
            ->afterStateUpdated(function ($state, callable $set) {
              if ($state) {
                $set("applying_for_scope1_3_mark", false);
              }
            }),
          Forms\Components\Toggle::make("applying_for_scope1_3_mark")
            ->required()
            ->label(__("admin.companies.fields.applying_scope_1_3"))
            ->default(false)
            ->reactive()
            ->afterStateUpdated(function ($state, callable $set) {
              if ($state) {
                $set("applying_for_scope1_2_mark", false);
              }
            }),
        ])
        ->columns(3),

      Forms\Components\Section::make(__("admin.companies.sections.invoice_details"))
        ->schema([
          Forms\Components\TextInput::make("e_invoice_contact_name")
            ->maxLength(255)
            ->label(__("admin.companies.fields.contact_name")),
          Forms\Components\TextInput::make("e_invoice_contact_email")
            ->email()
            ->maxLength(255)
            ->label(__("admin.companies.fields.contact_email")),
          Forms\Components\TextInput::make("e_invoice_address")
            ->maxLength(255)
            ->label(__("admin.companies.fields.einvoice_address")),
          Forms\Components\TextInput::make("e_invoice_operator")
            ->maxLength(255)
            ->label(__("admin.companies.fields.einvoice_operator")),
          Forms\Components\TextInput::make("e_invoice_reference")
            ->maxLength(255)
            ->label(__("admin.companies.fields.reference")),
          Forms\Components\Textarea::make("e_invoice_additional_info")
            ->label(__("admin.companies.fields.additional_info"))
            ->rows(4)
            ->columnSpanFull(),
        ])
        ->columns(2),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->defaultPaginationPageOption(50)
      ->modifyQueryUsing(
        fn(Builder $query, CompanyResource $resource) => $query->with([
          "municipality.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "industryClassification.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "revenueRange.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "employeeCountRange.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("name")
          ->label(__("admin.companies.fields.name"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("business_id")
          ->label(__("admin.companies.fields.business_id"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "municipality.translations.name",
        )
          ->label(__("admin.companies.fields.municipality"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "industryClassification.translations.name",
        )
          ->label(__("admin.companies.fields.industry"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "revenueRange.translations.label",
        )
          ->label(__("admin.companies.fields.revenue"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "employeeCountRange.translations.label",
        )
          ->label(__("admin.companies.fields.employee_count"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_start_day",
        )
          ->label(__("admin.companies.fields.fiscal_start_day"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_start_month",
        )
          ->label(__("admin.companies.fields.fiscal_start_month"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_end_day",
        )
          ->label(__("admin.companies.fields.fiscal_end_day"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fiscal_end_month",
        )
          ->label(__("admin.companies.fields.fiscal_end_month"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        Tables\Columns\IconColumn::make("consent_for_data_examination")
          ->label(__("admin.companies.fields.data_processing_consent"))
          ->boolean()
          ->sortable(),
        Tables\Columns\IconColumn::make("applying_for_scope1_2_mark")
          ->label(__("admin.companies.fields.applying_scope_1_2"))
          ->boolean()
          ->sortable(),
        Tables\Columns\IconColumn::make("applying_for_scope1_3_mark")
          ->label(__("admin.companies.fields.applying_scope_1_3"))
          ->boolean()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_contact_name",
        )
          ->label(__("admin.companies.fields.contact_name"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_contact_email",
        )
          ->label(__("admin.companies.fields.contact_email"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_address",
        )
          ->label(__("admin.companies.fields.einvoice_address"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_operator",
        )
          ->label(__("admin.companies.fields.einvoice_operator"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_reference",
        )
          ->label(__("admin.companies.fields.reference"))
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "e_invoice_additional_info",
        )
          ->label(__("admin.companies.fields.additional_info"))
          ->limit(50)
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([
        Tables\Actions\Action::make("select")
          ->label(__("admin.companies.actions.open"))
          ->action(function (Company $record) {
            $user = Auth::user();
            Assert::notNull($user);
            $user->update([
              "selected_company_id" => $record->id,
            ]);

            return redirect()->to(Config::string("misc.auth_redirect_url"));
          })
          ->openUrlInNewTab(),
        Tables\Actions\EditAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [
      RelationManagers\YearsRelationManager::class,
      RelationManagers\UsersRelationManager::class,
    ];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListCompanies::route("/"),
      "create" => Pages\CreateCompany::route("/create"),
      "edit" => Pages\EditCompany::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Company>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
