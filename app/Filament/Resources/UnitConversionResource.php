<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\UnitConversionResource\Pages;
use App\Filament\Resources\UnitConversionResource\Pages\ListUnitConversions;
use App\Models\Unit;
use App\Models\UnitConversion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

final class UnitConversionResource extends Resource
{
  use Translatable;

  protected static ?string $model = UnitConversion::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("from_unit_id")
        ->relationship("fromUnit", "id", fn(Builder $query) => $query->with("translations"))
        ->required()
        ->getOptionLabelFromRecordUsing(
          fn(Unit $record, Page $livewire, UnitConversionResource $resource) => $record->translate(
            $resource::getActiveLocale(),
          )->name ?? "",
        )
        ->label(__("admin.units.fields.from_unit")),
      Forms\Components\Select::make("to_unit_id")
        ->relationship("toUnit", "id", fn(Builder $query) => $query->with("translations"))
        ->required()
        ->getOptionLabelFromRecordUsing(
          fn(Unit $record, Page $livewire, UnitConversionResource $resource) => $record->translate(
            $resource::getActiveLocale(),
          )->name ?? "",
        )
        ->label(__("admin.units.fields.to_unit")),
      Forms\Components\TextInput::make("conversion_factor")
        ->required()
        ->numeric()
        ->label(__("admin.units.fields.conversion_factor")),
    ]);
  }

  public static function getModelLabel(): string
  {
    return __("admin.units.conversion");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.units.conversions");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(
          Builder $query,
          ListUnitConversions $livewire,
          UnitConversionResource $resource,
        ) => $query->with([
          "fromUnit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "toUnit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "fromUnit.translations.name",
        )
          ->label(__("admin.units.fields.from_unit"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "toUnit.translations.name",
        )
          ->label(__("admin.units.fields.to_unit"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "conversion_factor",
        )
          ->label(__("admin.units.fields.conversion_factor"))
          ->numeric()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        //
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ]);
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListUnitConversions::route("/"),
      "create" => Pages\CreateUnitConversion::route("/create"),
      "edit" => Pages\EditUnitConversion::route("/{record}/edit"),
    ];
  }
}
