<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\InputMethod;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\CalculationDefinitionResource\Pages;
use App\Filament\Resources\CalculationDefinitionResource\Pages\ListCalculationDefinitions;
use App\Filament\Resources\CalculationDefinitionResource\RelationManagers\YearsRelationManager;
use App\Models\CalculationDefinition;
use App\Models\Category;
use App\Models\CompoundUnit;
use App\Models\Grouping;
use App\Models\OptionSet;
use App\Models\Unit;
use App\Services\EmissionCalculationService;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

final class CalculationDefinitionResource extends Resource
{
  use Translatable;

  protected static ?string $model = CalculationDefinition::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("admin.calculations.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.calculations.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      // Warning if this definition is referenced in other formulas
      Forms\Components\Placeholder::make("reference_warning")
        ->hiddenLabel()
        ->content(function (?CalculationDefinition $record): ?HtmlString {
          if ($record === null || $record->id === null) {
            return null;
          }

          // Check if this definition is referenced in any formulas
          $service = app(EmissionCalculationService::class);
          $referencingDefinitions = CalculationDefinition::whereNotNull("data_formula")
            ->with("translations")
            ->where("id", "!=", $record->id)
            ->get()
            ->filter(function (CalculationDefinition $def) use ($service, $record): bool {
              $formula = $def->data_formula;
              if ($formula === null) {
                return false;
              }
              $referencedIds = $service->extractReferencedDefinitionIds($formula);

              return in_array($record->id, $referencedIds["data"], true) ||
                in_array($record->id, $referencedIds["kerroin"], true);
            });

          if ($referencingDefinitions->isEmpty()) {
            return null;
          }

          $links = $referencingDefinitions
            ->map(function (CalculationDefinition $def): string {
              $translation = $def->translate();
              $name = $translation !== null ? $translation->data_name : null;
              $name = $name ?? "ID {$def->id}";
              $url = static::getUrl("edit", ["record" => $def]);

              return '<a href="' .
                $url .
                '" class="text-amber-800 underline hover:text-amber-900">' .
                e($name) .
                "</a>";
            })
            ->join(", ");

          return new HtmlString(
            '<div class="bg-amber-50 border border-amber-200 rounded-lg p-4">' .
              '<div class="flex">' .
              '<div class="flex-shrink-0">' .
              '<svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">' .
              '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />' .
              "</svg>" .
              "</div>" .
              '<div class="ml-3">' .
              '<h3 class="text-sm font-medium text-amber-800">' .
              __("admin.calculations.warnings.referenced_in_formulas") .
              "</h3>" .
              '<p class="mt-1 text-sm text-amber-700">' .
              __("admin.calculations.references.fields_using") .
              $links .
              "</p>" .
              '<p class="mt-1 text-sm text-amber-700">' .
              __("admin.calculations.warnings.affects_calculations") .
              "</p>" .
              "</div>" .
              "</div>" .
              "</div>",
          );
        })
        ->columnSpanFull(),

      Forms\Components\Section::make(__("admin.common.basic_info"))
        ->schema([
          Forms\Components\TextInput::make("data_name")
            ->label(__("admin.calculations.fields.data_name"))
            ->maxLength(255)
            ->required(),
          Forms\Components\TextInput::make("emission_factor_name")
            ->label(__("admin.calculations.fields.emission_factor_name"))
            ->maxLength(255)
            ->required(),
          Forms\Components\TextInput::make("result_name")
            ->label(__("admin.calculations.fields.result_name"))
            ->maxLength(255)
            ->required(),
          Forms\Components\TextInput::make("tag")
            ->label(__("admin.calculations.fields.identifier"))
            ->maxLength(255)
            ->helperText(__("admin.calculations.help.group_identifier")),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("admin.calculations.fields.grouping"))
        ->schema([
          Forms\Components\Select::make("scope_id")
            ->relationship("scope", "number")
            ->required()
            ->label(__("admin.calculations.fields.scope")),
          Forms\Components\Select::make("grouping_id")
            ->relationship("grouping", "id", fn(Builder $query) => $query->with("translations"))
            ->required()
            ->getOptionLabelFromRecordUsing(
              fn(
                Grouping $record,
                Page $livewire,
                CalculationDefinitionResource $resource,
              ) => $record->translate($resource::getActiveLocale())->title ?? "",
            )
            ->label(__("admin.calculations.fields.grouping")),
          Forms\Components\Select::make("category_id")
            ->relationship("category", "id", fn(Builder $query) => $query->with("translations"))
            ->required()
            ->getOptionLabelFromRecordUsing(
              fn(
                Category $record,
                Page $livewire,
                CalculationDefinitionResource $resource,
              ) => $record->translate($resource::getActiveLocale())->title ?? "",
            )
            ->label(__("admin.calculations.fields.category")),
        ])
        ->columns(3),

      Forms\Components\Section::make(__("admin.calculations.sections.units"))
        ->schema([
          Forms\Components\Select::make("data_unit_id")
            ->relationship("dataUnit", "id", fn(Builder $query) => $query->with("translations"))
            ->getOptionLabelFromRecordUsing(
              fn(
                Unit $record,
                Page $livewire,
                CalculationDefinitionResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("admin.calculations.fields.data_unit"))
            ->required(),
          Forms\Components\Select::make("emission_factor_compound_unit_id")
            ->relationship(
              "emissionFactorCompoundUnit",
              "id",
              fn(Builder $query) => $query->with([
                "numeratorUnit.translations",
                "denominatorUnit.translations",
              ]),
            )
            ->getOptionLabelFromRecordUsing(
              fn(
                CompoundUnit $record,
                Page $livewire,
                CalculationDefinitionResource $resource,
              ) => ($record->numeratorUnit?->translate($resource::getActiveLocale())->symbol ??
                "-") .
                "/" .
                ($record->denominatorUnit?->translate($resource::getActiveLocale())->symbol ?? "-"),
            )
            ->label(__("admin.calculations.fields.emission_factor_compound_unit"))
            ->required(),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.calculations.fields.input_type"))
        ->schema([
          Forms\Components\Select::make("input_method")
            ->label(__("admin.calculations.fields.input_type"))
            ->options([
              "manual" => __("admin.calculations.input_types.manual"),
              "select" => __("admin.calculations.input_types.select"),
            ])
            ->default("manual")
            ->required()
            ->live()
            ->helperText(
              __(
                'Valitse "Valintavalikko" jos haluat käyttäjien valitsevan ennalta määritetyistä vaihtoehdoista',
              ),
            ),

          // NEW: OptionSet selection field
          Forms\Components\Select::make("option_set_id")
            ->label(__("admin.calculations.fields.option_set"))
            ->relationship("optionSet", "name")
            ->searchable()
            ->preload()
            ->createOptionForm([
              Forms\Components\TextInput::make("name")
                ->label(__("admin.calculations.fields.name"))
                ->required()
                ->maxLength(255)
                ->helperText(__("admin.calculations.help.option_set_identifier")),
            ])
            ->editOptionForm([
              Forms\Components\TextInput::make("name")
                ->label(__("admin.calculations.fields.name"))
                ->required()
                ->maxLength(255)
                ->helperText(__("admin.calculations.help.option_set_identifier")),
            ])
            ->visible(fn(Get $get): bool => $get("input_method") === InputMethod::SELECT->value)
            ->required(fn(Get $get): bool => $get("input_method") === InputMethod::SELECT->value)
            ->helperText(__("admin.calculations.help.select_or_create_option_set"))
            ->hint(
              fn(?CalculationDefinition $record): ?HtmlString => $record !== null &&
              $record->option_set_id !== null
                ? new HtmlString(
                  '<a href="' .
                    OptionSetResource::getUrl("edit", ["record" => $record->option_set_id]) .
                    '" ' .
                    'class="text-primary-600 hover:text-primary-800 text-sm underline" target="_blank">' .
                    __("admin.calculations.actions.edit_options") .
                    " →" .
                    "</a>",
                )
                : null,
            ),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("admin.calculations.sections.formula_visibility"))
        ->description(__("admin.calculations.help.formula_description"))
        ->schema([
          // Page visibility controls
          Forms\Components\Fieldset::make(__("admin.calculations.sections.page_visibility"))
            ->schema([
              Forms\Components\Toggle::make("hide_from_data_page")
                ->label(__("admin.calculations.visibility.hide_data_page"))
                ->helperText(__("admin.calculations.help.hide_data_page"))
                ->inline(false),
              Forms\Components\Toggle::make("hide_from_emission_factor_page")
                ->label(__("admin.calculations.visibility.hide_emission_page"))
                ->helperText(__("admin.calculations.help.hide_emission_page"))
                ->inline(false),
              Forms\Components\Toggle::make("hide_from_results_page")
                ->label(__("admin.calculations.visibility.hide_results_page"))
                ->helperText(__("admin.calculations.help.hide_results_page"))
                ->inline(false),
            ])
            ->columns(3),

          Textarea::make("data_formula")
            ->label(__("admin.calculations.fields.formula"))
            ->placeholder(__("admin.calculations.placeholders.enter_formula"))
            ->helperText(
              new HtmlString(
                '<div class="space-y-3">' .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.data_fetch_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">data(id)</code> - ' .
                  __("admin.calculations.formulas.get_data_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">kerroin(id)</code> - ' .
                  __("admin.calculations.formulas.get_emission_factor") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.arithmetic") .
                  "</p>" .
                  '<p class="text-sm text-gray-600 mb-2">' .
                  __("admin.calculations.help.precision_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">add(...)</code> - ' .
                  __("admin.calculations.formulas.addition_full_precision") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">subtract(a, b)</code> - ' .
                  __("admin.calculations.formulas.subtraction_full_precision") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">multiply(...)</code> - ' .
                  __("admin.calculations.formulas.multiplication_full_precision") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">divide(a, b, desimaalit)</code> - ' .
                  __("admin.calculations.formulas.division_with_precision") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">sum(...)</code> - ' .
                  __("admin.calculations.formulas.sum_full_precision") .
                  "</li>" .
                  "</ul>" .
                  '<p class="text-sm text-gray-600 mt-3 mb-2">' .
                  __("admin.calculations.help.basic_operators") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">+ - * / %</code> - ' .
                  __("admin.calculations.formulas.basic_arithmetic") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">**</code> - ' .
                  __("admin.calculations.formulas.exponentiation") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.math_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">abs(x)</code> - ' .
                  __("admin.calculations.formulas.absolute_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">round(x, tarkkuus)</code> - ' .
                  __("admin.calculations.formulas.round_precision") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">pow(x, y)</code> - ' .
                  __("admin.calculations.formulas.power") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">sqrt(x)</code> - ' .
                  __("admin.calculations.formulas.square_root") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">min(...)</code> - ' .
                  __("admin.calculations.formulas.minimum_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">max(...)</code> - ' .
                  __("admin.calculations.formulas.maximum_value") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(x, min, max)</code> - ' .
                  __("admin.calculations.formulas.check_between_minmax") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.array_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">count(array)</code> - ' .
                  __("admin.calculations.formulas.count_array") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">in_array(arvo, taulukko)</code> - ' .
                  __("admin.calculations.formulas.check_in_array") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">range(alku, loppu, askel)</code> - ' .
                  __("admin.calculations.formulas.create_number_range") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">keys(array)</code> - ' .
                  __("admin.calculations.formulas.array_keys") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">values(array)</code> - ' .
                  __("admin.calculations.formulas.array_values") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">flatten(array)</code> - ' .
                  __("admin.calculations.formulas.flatten_arrays") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.data_processing_functions") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">where(array, kenttä, operaattori, arvo)</code> - ' .
                  __("admin.calculations.formulas.filter_by_field") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">where(array, kenttä, arvo)</code> - ' .
                  __("admin.calculations.formulas.filter_equals") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">firstWhere(array, kenttä, operaattori, arvo)</code> - ' .
                  __("admin.calculations.formulas.first_matching") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">groupBy(array, kenttä)</code> - ' .
                  __("admin.calculations.formulas.group_by_field") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">map(array, \'lauseke\')</code> - ' .
                  __("admin.calculations.formulas.map_transform") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">get(objekti, kenttä, oletus)</code> - ' .
                  __("admin.calculations.formulas.get_field_or_default") .
                  "</li>" .
                  "</ul>" .
                  '<p class="text-sm text-gray-600 mt-2">' .
                  __("admin.calculations.help.comparison_operators") .
                  "</p>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.comparison_logic") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">== != < > <= >=</code> - ' .
                  __("admin.calculations.formulas.comparison_operators") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">and or not</code> - ' .
                  __("admin.calculations.formulas.logical_operators") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">ehto ? arvo1 : arvo2</code> - ' .
                  __("admin.calculations.formulas.conditional") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.variables") .
                  "</p>" .
                  '<ul class="text-sm space-y-1 ml-4">' .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">year</code> - ' .
                  __("admin.calculations.formulas.current_year") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">{2023: 1.5, 2024: 2.0}[year]</code> - ' .
                  __("admin.calculations.formulas.yearly_values") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">1_000_000</code> - ' .
                  __("admin.calculations.formulas.underscores_in_numbers") .
                  "</li>" .
                  '<li>• <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">1.5e3</code> - ' .
                  __("admin.calculations.formulas.scientific_notation") .
                  "</li>" .
                  "</ul>" .
                  "</div>" .
                  "<div>" .
                  '<p class="font-semibold mb-1">' .
                  __("admin.calculations.help.common_formulas") .
                  "</p>" .
                  '<ul class="text-sm space-y-1">' .
                  "<li>" .
                  __("admin.calculations.help.simple_emission") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">multiply(data(10), kerroin(10))</code></li>' .
                  "<li>" .
                  __("admin.calculations.help.multiple_emissions") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">add(multiply(data(10), kerroin(10)), multiply(data(20), kerroin(20)))</code></li>' .
                  "<li>" .
                  __("admin.calculations.help.conditional") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">data(10) > 0 ? multiply(data(10), kerroin(10)) : 0</code></li>' .
                  "<li>" .
                  __("admin.calculations.help.year_adjustment") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">multiply(data(10), kerroin(10), {2023: 0.9, 2024: 0.95, 2025: 1.0}[year])</code></li>' .
                  "<li>" .
                  __("admin.calculations.help.percentage") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">multiply(data(10), 0.95, kerroin(10))</code></li>' .
                  "<li>" .
                  __("admin.calculations.help.check_between") .
                  ' <code class="text-xs bg-gray-100 px-1 py-0.5 rounded font-mono">between(data(10), 100, 500) ? data(10) : 100</code></li>' .
                  "</ul>" .
                  "</div>" .
                  "</div>",
              ),
            )
            ->rows(3)
            ->columnSpanFull()
            ->live(onBlur: true)
            ->afterStateUpdated(function (
              ?string $state,
              Textarea $component,
              Forms\Set $set,
            ): void {
              if ($state === null || $state === "") {
                $set("data_formula", null);

                return;
              }

              try {
                $service = app(EmissionCalculationService::class);
                $result = $service->validateFormula($state);

                if (!$result["valid"]) {
                  $set("data_formula", $state);
                  // Show validation error using Filament notifications
                  \Filament\Notifications\Notification::make()
                    ->title(__("admin.calculations.errors.invalid_formula"))
                    ->body($result["error"] ?? __("admin.calculations.errors.formula_syntax"))
                    ->danger()
                    ->send();
                } else {
                  // Show which data references are used
                  $references = $result["references"];
                  if (count($references) > 0) {
                    $refList = collect($references)->join(", ");
                    $component->hint(
                      __("admin.calculations.references.uses_references") . $refList,
                    );
                  }
                }
              } catch (Exception $e) {
                \Filament\Notifications\Notification::make()
                  ->title(__("admin.calculations.errors.formula_validation_failed"))
                  ->body(__("admin.calculations.errors.formula_check_system"))
                  ->danger()
                  ->send();
              }
            }),
        ])
        ->collapsed(fn(Get $get): bool => ($get("data_formula") ?? "") === "")
        ->collapsible(),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(
          Builder $query,
          ListCalculationDefinitions $livewire,
          CalculationDefinitionResource $resource,
        ) => $query
          ->with([
            "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
            "grouping.translations" => fn(Relation $q) => $q->where(
              "locale",
              $resource::getActiveLocale(),
            ),
            "category.translations" => fn(Relation $q) => $q->where(
              "locale",
              $resource::getActiveLocale(),
            ),
            "dataUnit.translations" => fn(Relation $q) => $q->where(
              "locale",
              $resource::getActiveLocale(),
            ),
            "emissionFactorCompoundUnit.numeratorUnit.translations" => fn(Relation $q) => $q->where(
              "locale",
              $resource::getActiveLocale(),
            ),
            "emissionFactorCompoundUnit.denominatorUnit.translations" => fn(
              Relation $q,
            ) => $q->where("locale", $resource::getActiveLocale()),
            "optionSet", // NEW: Load optionSet relationship
          ])
          ->orderBy("sort_order"),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("id")
          ->label("ID")
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.data_name",
        )
          ->label(__("admin.calculations.fields.data_name"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.emission_factor_name",
        )
          ->label(__("admin.calculations.fields.emission_factor_name"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.result_name",
        )
          ->label(__("admin.calculations.fields.result_name"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("scope.number")
          ->label(__("admin.calculations.fields.scope"))
          ->numeric()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "grouping.translations.title",
        )
          ->label(__("admin.calculations.fields.grouping"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "category.translations.title",
        )
          ->label(__("admin.calculations.fields.category"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("tag")
          ->label(__("admin.calculations.fields.identifier"))
          ->badge()
          ->color("info")
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "dataUnit.translations.name",
        )
          ->label(__("admin.calculations.fields.data_unit"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "emissionFactorCompoundUnit",
        )
          ->label(__("admin.calculations.fields.emission_factor_compound_unit"))
          ->getStateUsing(function (
            CalculationDefinition $record,
            ListCalculationDefinitions $livewire,
            CalculationDefinitionResource $resource,
          ) {
            $numerator =
              $record->emissionFactorCompoundUnit?->numeratorUnit?->translate(
                $resource::getActiveLocale(),
              )->symbol ?? "-";
            $denominator =
              $record->emissionFactorCompoundUnit?->denominatorUnit?->translate(
                $resource::getActiveLocale(),
              )->symbol ?? "-";

            return $numerator . "/" . $denominator;
          }),

        // UPDATED: Show optionSet instead of counting options
        Tables\Columns\TextColumn::make("optionSet.name")
          ->label(__("admin.calculations.fields.option_set"))
          ->badge()
          ->color("primary")
          ->placeholder("-")
          ->visible(fn() => true),

        Tables\Columns\IconColumn::make("input_method")
          ->label(__("admin.calculations.sections.input"))
          ->icon(
            fn(InputMethod $state): string => match ($state->value) {
              "select" => "heroicon-o-list-bullet",
              default => "heroicon-o-pencil",
            },
          )
          ->color(
            fn(InputMethod $state): string => match ($state->value) {
              "select" => "primary",
              default => "gray",
            },
          )
          ->sortable(),
        Tables\Columns\IconColumn::make("has_formula")
          ->label(__("admin.calculations.fields.formula"))
          ->getStateUsing(
            fn(CalculationDefinition $record): bool => ($record->data_formula ?? "") !== "",
          )
          ->boolean()
          ->trueIcon("heroicon-o-calculator")
          ->falseIcon("heroicon-o-minus")
          ->trueColor("primary")
          ->falseColor("gray"),

        // New visibility column showing which pages the definition appears on
        Tables\Columns\TextColumn::make("visibility")
          ->label(__("admin.calculations.sections.visibility"))
          ->getStateUsing(function (CalculationDefinition $record): string {
            $pages = [];
            // Handle potential null values as false (visible)
            if (!$record->hide_from_data_page) {
              $pages[] = "D";
            }
            if (!$record->hide_from_emission_factor_page) {
              $pages[] = "P";
            }
            if (!$record->hide_from_results_page) {
              $pages[] = "T";
            }

            return implode("/", $pages) !== "" ? implode("/", $pages) : "-";
          })
          ->badge()
          ->tooltip(function (CalculationDefinition $record): string {
            $visible = [];
            $hidden = [];

            // Handle potential null values as false (visible)
            if (!$record->hide_from_data_page) {
              $visible[] = __("admin.calculations.labels.data");
            } else {
              $hidden[] = __("admin.calculations.labels.data");
            }

            if (!$record->hide_from_emission_factor_page) {
              $visible[] = __("admin.calculations.types.emission_factor");
            } else {
              $hidden[] = __("admin.calculations.types.emission_factor");
            }

            if (!$record->hide_from_results_page) {
              $visible[] = __("admin.calculations.types.results");
            } else {
              $hidden[] = __("admin.calculations.types.results");
            }

            $tooltip = "";
            if (count($visible) > 0) {
              $tooltip .= __("admin.calculations.visibility.shown") . implode(", ", $visible);
            }
            if (count($hidden) > 0) {
              if ($tooltip !== "") {
                $tooltip .= "\n";
              }
              $tooltip .= __("admin.calculations.visibility.hidden") . implode(", ", $hidden);
            }

            return $tooltip;
          })
          ->color(function (CalculationDefinition $record): string {
            $visibleCount = 0;
            // Handle potential null values as false (visible)
            if (!$record->hide_from_data_page) {
              $visibleCount++;
            }
            if (!$record->hide_from_emission_factor_page) {
              $visibleCount++;
            }
            if (!$record->hide_from_results_page) {
              $visibleCount++;
            }

            return match ($visibleCount) {
              0 => "danger",
              1 => "warning",
              2 => "info",
              3 => "success",
            };
          })
          ->sortable(false), // Disable sorting for computed column

        Tables\Columns\IconColumn::make("is_referenced")
          ->label(__("admin.calculations.fields.reference"))
          ->getStateUsing(function (CalculationDefinition $record): bool {
            // Check if this definition is referenced in any formulas
            $service = app(EmissionCalculationService::class);
            $allDefinitions = CalculationDefinition::whereNotNull("data_formula")
              ->where("id", "!=", $record->id)
              ->get();

            foreach ($allDefinitions as $def) {
              if ($def->data_formula !== null) {
                $referencedIds = $service->extractReferencedDefinitionIds($def->data_formula);
                if (
                  in_array($record->id, $referencedIds["data"], true) ||
                  in_array($record->id, $referencedIds["kerroin"], true)
                ) {
                  return true;
                }
              }
            }

            return false;
          })
          ->boolean()
          ->trueIcon("heroicon-o-link")
          ->falseIcon(null)
          ->trueColor("info")
          ->tooltip(function (bool $state, CalculationDefinition $record): ?string {
            if (!$state) {
              return null;
            }

            // Find all definitions that reference this one
            $service = app(EmissionCalculationService::class);
            $referencingDefinitions = CalculationDefinition::whereNotNull("data_formula")
              ->with("translations")
              ->where("id", "!=", $record->id)
              ->get()
              ->filter(function (CalculationDefinition $def) use ($service, $record): bool {
                if ($def->data_formula === null) {
                  return false;
                }
                $referencedIds = $service->extractReferencedDefinitionIds($def->data_formula);

                return in_array($record->id, $referencedIds["data"], true) ||
                  in_array($record->id, $referencedIds["kerroin"], true);
              });

            $names = $referencingDefinitions
              ->map(function (CalculationDefinition $def): string {
                $translation = $def->translate();
                $name = $translation !== null ? $translation->data_name : null;

                return $name ?? "ID {$def->id}";
              })
              ->take(3)
              ->join(", ");

            if ($referencingDefinitions->count() > 3) {
              $names .=
                " " .
                __("admin.common.and_count_more", [
                  "count" => $referencingDefinitions->count() - 3,
                ]);
            }

            return __("admin.calculations.references.used_by") . $names;
          }),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("data_formula")
          ->label(__("admin.calculations.fields.formula"))
          ->limit(30)
          ->tooltip(fn(CalculationDefinition $record): ?string => $record->data_formula)
          ->toggleable(isToggledHiddenByDefault: true)
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("sort_order")
          ->label(__("admin.calculations.fields.order"))
          ->numeric()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([
        Tables\Actions\Action::make("view_references")
          ->label(__("admin.calculations.sections.references"))
          ->icon("heroicon-o-link")
          ->color("warning")
          ->visible(function (CalculationDefinition $record): bool {
            // Only show if this definition is referenced
            $service = app(EmissionCalculationService::class);

            return CalculationDefinition::whereNotNull("data_formula")
              ->where("id", "!=", $record->id)
              ->get()
              ->contains(function (CalculationDefinition $def) use ($service, $record): bool {
                if ($def->data_formula === null) {
                  return false;
                }
                $referencedIds = $service->extractReferencedDefinitionIds($def->data_formula);

                return in_array($record->id, $referencedIds["data"], true) ||
                  in_array($record->id, $referencedIds["kerroin"], true);
              });
          })
          ->modalHeading(
            fn(CalculationDefinition $record): string => __(
              "admin.calculations.references.definitions_referencing",
            ) .
              ($record->translate() !== null && $record->translate()->data_name !== null
                ? $record->translate()->data_name
                : "ID {$record->id}"),
          )
          ->modalContent(function (CalculationDefinition $record): HtmlString {
            $service = app(EmissionCalculationService::class);
            $referencingDefinitions = CalculationDefinition::whereNotNull("data_formula")
              ->with("translations")
              ->where("id", "!=", $record->id)
              ->get()
              ->filter(function (CalculationDefinition $def) use ($service, $record): bool {
                if ($def->data_formula === null) {
                  return false;
                }
                $referencedIds = $service->extractReferencedDefinitionIds($def->data_formula);

                return in_array($record->id, $referencedIds["data"], true) ||
                  in_array($record->id, $referencedIds["kerroin"], true);
              });

            $links = $referencingDefinitions
              ->map(function (CalculationDefinition $def): string {
                $translation = $def->translate();
                $name = $translation !== null ? $translation->data_name : null;
                $name = $name ?? "ID {$def->id}";
                $formula = $def->data_formula ?? "";
                $url = static::getUrl("edit", ["record" => $def]);

                return '<div class="mb-4 p-3 rounded-lg">' .
                  '<a href="' .
                  $url .
                  '" class="text-primary-600 hover:text-primary-800 font-medium">' .
                  e($name) .
                  "</a>" .
                  '<div class="mt-1">' .
                  '<code class="text-xs px-1 py-0.5 rounded font-mono">' .
                  e($formula) .
                  "</code>" .
                  "</div>" .
                  "</div>";
              })
              ->join("");

            if ($links === "") {
              return new HtmlString("<p>" . __("admin.calculations.references.none") . "</p>");
            }

            return new HtmlString(
              "<div>" .
                '<p class="mb-4 text-sm">' .
                __("admin.calculations.help.click_to_edit") .
                "</p>" .
                '<div class="max-h-96 overflow-y-auto">' .
                $links .
                "</div>" .
                "</div>",
            );
          })
          ->modalSubmitAction(false)
          ->modalCancelActionLabel(__("common.actions.close")),
        Tables\Actions\EditAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->reorderable("sort_order")
      ->defaultSort("sort_order");
  }

  public static function getRelations(): array
  {
    // REMOVED OptionsRelationManager since options are now managed through OptionSets
    return [YearsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListCalculationDefinitions::route("/"),
      "create" => Pages\CreateCalculationDefinition::route("/create"),
      "edit" => Pages\EditCalculationDefinition::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Model>
   */
  public static function getEloquentQuery(): Builder
  {
    $query = parent::getEloquentQuery()
      ->withoutGlobalScopes([SoftDeletingScope::class])
      ->with(["translations"]); // Always load translations

    return $query->whereNull("company_id");
  }
}
