<?php

declare(strict_types=1);

namespace App\Filament\Resources\GroupingResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\GroupingResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateGrouping extends CreateRecord
{
  use Translatable;

  protected static string $resource = GroupingResource::class;
}
