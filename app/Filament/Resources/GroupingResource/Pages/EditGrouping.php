<?php

declare(strict_types=1);

namespace App\Filament\Resources\GroupingResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns\Translatable;
use App\Filament\Resources\GroupingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

final class EditGrouping extends EditRecord
{
  use Translatable;

  protected static string $resource = GroupingResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\DeleteAction::make(), Actions\RestoreAction::make()];
  }
}
