<?php

declare(strict_types=1);

namespace App\Filament\Resources\CalculationDefinitionResource\RelationManagers;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns\Translatable;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class YearsRelationManager extends RelationManager
{
  use Translatable;

  protected static string $relationship = "years";

  public static function getModelLabel(): string
  {
    return __("admin.years.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.years.plural");
  }

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("admin.years.plural");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\TextInput::make("year")
        ->required()
        ->numeric()
        ->label(__("admin.years.label"))
        ->disabled(),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("year")
      ->columns([
        Tables\Columns\TextColumn::make("year")->label(__("admin.years.label"))->sortable(),
        Tables\Columns\TextColumn::make("emission_factor_default_value")
          ->label(__("admin.calculations.fields.emission_factor_default_value"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState(
              $record,
              "emission_factor_default_value",
            ),
          ),
        Tables\Columns\TextColumn::make("emission_factor_default_source")
          ->label(__("admin.calculations.fields.default_source"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState(
              $record,
              "emission_factor_default_source",
            ),
          )
          ->limit(50)
          ->tooltip(
            fn(Year $record) => $this->getTranslatableColumnState(
              $record,
              "emission_factor_default_source",
            ),
          ),
        Tables\Columns\TextColumn::make("data_help_text")
          ->label(__("admin.calculations.fields.data_instruction"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState($record, "data_help_text"),
          )
          ->limit(30)
          ->toggleable(),
        Tables\Columns\TextColumn::make("emission_factor_help_text")
          ->label(__("admin.calculations.fields.emission_factor_instruction"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState(
              $record,
              "emission_factor_help_text",
            ),
          )
          ->limit(30)
          ->toggleable(),
        Tables\Columns\TextColumn::make("result_help_text")
          ->label(__("admin.calculations.fields.result_instruction"))
          ->getStateUsing(
            fn(Year $record) => $this->getTranslatableColumnState($record, "result_help_text"),
          )
          ->limit(30)
          ->toggleable(),
        Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->headerActions([
        $this->configureAttachActionForTranslatablePivot(
          Tables\Actions\AttachAction::make()
            ->preloadRecordSelect()
            ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("year", "desc"))
            ->form(
              /**
               * @return array<Forms\Components\Component>
               */
              fn(Tables\Actions\AttachAction $action): array => [
                $action->getRecordSelect(),
                ...$this->getTranslatablePivotFormSchema(),
              ],
            ),
        ),
      ])
      ->actions([
        $this->configureEditActionForTranslatablePivot(
          Tables\Actions\EditAction::make()->form(
            /** @return array<Forms\Components\Component> */
            fn(): array => $this->getTranslatablePivotFormSchema(),
          ),
        ),
        Tables\Actions\DetachAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DetachBulkAction::make()]),
      ]);
  }

  /**
   * Get the form schema for translatable pivot fields
   *
   * @return array<Forms\Components\Component>
   */
  private function getTranslatablePivotFormSchema(): array
  {
    return [
      Forms\Components\TextInput::make("emission_factor_default_value")
        ->label(__("admin.calculations.fields.emission_factor_default_value"))
        ->numeric()
        ->default(null)
        ->dehydrateStateUsing(fn($state) => $state === "" ? null : $state) // Convert empty string to null for numeric
        ->helperText(__("admin.calculations.help.placeholder_value")),
      Forms\Components\Textarea::make("emission_factor_default_source")
        ->label(__("admin.calculations.fields.emission_factor_default_source"))
        ->rows(2)
        ->default("")
        ->dehydrateStateUsing(fn($state) => $state ?? "") // Ensure empty string for text fields
        ->helperText(__("admin.calculations.fields.default_source")),
      Forms\Components\Textarea::make("data_help_text")
        ->label(__("admin.calculations.fields.data_instruction_text"))
        ->rows(3)
        ->default("")
        ->dehydrateStateUsing(fn($state) => $state ?? "") // Ensure empty string for text fields
        ->helperText(__("admin.calculations.help.data_page_help")),
      Forms\Components\Textarea::make("emission_factor_help_text")
        ->label(__("admin.calculations.fields.emission_factor_instruction_text"))
        ->rows(3)
        ->default("")
        ->dehydrateStateUsing(fn($state) => $state ?? "") // Ensure empty string for text fields
        ->helperText(__("admin.calculations.help.emission_page_help")),
      Forms\Components\Textarea::make("result_help_text")
        ->label(__("admin.calculations.fields.result_instruction_text"))
        ->rows(3)
        ->default("")
        ->dehydrateStateUsing(fn($state) => $state ?? "") // Ensure empty string for text fields
        ->helperText(__("admin.calculations.help.results_page_help")),
    ];
  }
}
