<?php

declare(strict_types=1);

namespace App\Filament\Resources\CalculationDefinitionResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns\Translatable;
use App\Filament\Resources\CalculationDefinitionResource;
use Filament\Resources\Pages\CreateRecord;

final class CreateCalculationDefinition extends CreateRecord
{
  use Translatable;

  protected static string $resource = CalculationDefinitionResource::class;
}
