<?php

declare(strict_types=1);

namespace App\Filament\Resources\CompanyResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\ListRecords\Concerns\Translatable;
use App\Filament\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

final class ListCompanies extends ListRecords
{
  use Translatable;

  protected static string $resource = CompanyResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make(), Actions\CreateAction::make()];
  }
}
