<?php

declare(strict_types=1);

namespace App\Filament\Resources\CompanyResource\RelationManagers;

use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class YearsRelationManager extends RelationManager
{
  protected static string $relationship = "years";

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("Ilmasto-ohjelmat vuosittain");
  }

  public static function getModelLabel(): string
  {
    return __("Vuosi");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Vuodet");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("year_id")
        ->label(__("Vuosi"))
        ->options(Year::orderBy("year", "desc")->pluck("year", "id"))
        ->required()
        ->disabledOn("edit")
        ->searchable()
        ->preload(),

      Forms\Components\Section::make(__("Osallistumiset"))
        ->schema([
          Forms\Components\Toggle::make("participates_in_climate_program")->label(
            __("Osallistuu ilmasto-ohjelmaan"),
          ),

          Forms\Components\Toggle::make("participates_in_climate_community")->label(
            __("Osallistuu ilmastoyhteisöön"),
          ),
        ])
        ->columns(2),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("year")
      ->columns([
        Tables\Columns\TextColumn::make("year")->label(__("Vuosi"))->sortable()->searchable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_program")
          ->label(__("Ilmasto-ohjelma"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.participates_in_climate_community")
          ->label(__("Ilmastoyhteisö"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\TextColumn::make("pivot.created_at")
          ->label(__("Lisätty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("pivot.updated_at")
          ->label(__("Päivitetty"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("participates_in_climate_program")
          ->label(__("Ilmasto-ohjelma"))
          ->placeholder(__("Kaikki"))
          ->trueLabel(__("Osallistuu"))
          ->falseLabel(__("Ei osallistu"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("participates_in_climate_program", true),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_program",
              false,
            ),
          ),

        Tables\Filters\TernaryFilter::make("participates_in_climate_community")
          ->label(__("Ilmastoyhteisö"))
          ->placeholder(__("Kaikki"))
          ->trueLabel(__("Osallistuu"))
          ->falseLabel(__("Ei osallistu"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              true,
            ),
            false: fn(Builder $query) => $query->wherePivot(
              "participates_in_climate_community",
              false,
            ),
          ),
      ])
      ->headerActions([
        Tables\Actions\AttachAction::make()
          ->label(__("Lisää vuosi"))
          ->preloadRecordSelect()
          ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("year", "desc"))
          ->form(
            fn(Tables\Actions\AttachAction $action): array => [
              $action->getRecordSelect()->label(__("Vuosi")),

              Forms\Components\Section::make(__("Osallistumiset"))
                ->schema([
                  Forms\Components\Toggle::make("participates_in_climate_program")
                    ->label(__("Osallistuu ilmasto-ohjelmaan"))
                    ->default(false),

                  Forms\Components\Toggle::make("participates_in_climate_community")
                    ->label(__("Osallistuu ilmastoyhteisöön"))
                    ->default(false),
                ])
                ->columns(2),
            ],
          ),
      ])
      ->actions([
        Tables\Actions\EditAction::make()->form(
          fn(): array => [
            Forms\Components\Section::make(__("Osallistumiset"))
              ->schema([
                Forms\Components\Toggle::make("participates_in_climate_program")->label(
                  __("Osallistuu ilmasto-ohjelmaan"),
                ),

                Forms\Components\Toggle::make("participates_in_climate_community")->label(
                  __("Osallistuu ilmastoyhteisöön"),
                ),
              ])
              ->columns(2),
          ],
        ),
        Tables\Actions\DetachAction::make()->label(__("Poista")),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DetachBulkAction::make()->label(__("Poista valitut")),
        ]),
      ])
      ->defaultSort("year", "desc")
      ->emptyStateHeading(__("Ei vuositietoja"))
      ->emptyStateDescription(__("Lisää vuositietoja yllä olevalla painikkeella"))
      ->emptyStateIcon("heroicon-o-calendar");
  }
}
