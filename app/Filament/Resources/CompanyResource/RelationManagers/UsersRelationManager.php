<?php

declare(strict_types=1);

namespace App\Filament\Resources\CompanyResource\RelationManagers;

use App\Helpers\Assert;
use App\Models\Company;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

final class UsersRelationManager extends RelationManager
{
  protected static string $relationship = "users";

  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __("admin.companies.users.title");
  }

  public static function getModelLabel(): string
  {
    return __("admin.users.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.users.plural");
  }

  public function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("user_id")
        ->label(__("admin.users.label"))
        ->required()
        ->disabledOn("edit")
        ->searchable()
        ->getSearchResultsUsing(
          fn(string $search): array => User::where("email", "like", "%{$search}%")
            ->orderBy("email")
            ->limit(50)
            ->pluck("email", "id")
            ->toArray(),
        )
        ->getOptionLabelUsing(function ($value): ?string {
          $user = User::find($value);
          return $user instanceof User ? $user->email : null;
        }),

      Forms\Components\Section::make(__("admin.companies.users.settings"))->schema([
        Forms\Components\Toggle::make("is_primary")
          ->label(__("admin.companies.users.is_primary"))
          ->helperText(__("admin.companies.users.is_primary_help"))
          ->default(false),
      ]),
    ]);
  }

  public function table(Table $table): Table
  {
    return $table
      ->recordTitleAttribute("email")
      ->modifyQueryUsing(function (Builder $query) {
        $query->orderBy("company_user.is_primary", "desc")->orderBy("users.email", "asc");
      })
      ->columns([
        Tables\Columns\TextColumn::make("email")
          ->label(__("admin.users.email"))
          ->sortable()
          ->searchable()
          ->copyable(),

        Tables\Columns\IconColumn::make("email_verified_at")
          ->label(__("admin.users.email_verified"))
          ->boolean()
          ->trueIcon("heroicon-o-check-badge")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->getStateUsing(fn(User $record): bool => $record->email_verified_at !== null)
          ->sortable(),

        Tables\Columns\IconColumn::make("pivot.is_primary")
          ->label(__("admin.companies.users.primary"))
          ->boolean()
          ->trueIcon("heroicon-o-check-circle")
          ->falseIcon("heroicon-o-x-circle")
          ->trueColor("success")
          ->falseColor("gray")
          ->sortable(),

        Tables\Columns\TextColumn::make("pivot.created_at")
          ->label(__("admin.common.added"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("pivot.updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime("d.m.Y H:i")
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TernaryFilter::make("is_primary")
          ->label(__("admin.companies.users.primary_status"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.companies.users.primary_only"))
          ->falseLabel(__("admin.companies.users.non_primary"))
          ->queries(
            true: fn(Builder $query) => $query->wherePivot("is_primary", true),
            false: fn(Builder $query) => $query->wherePivot("is_primary", false),
          ),

        Tables\Filters\TernaryFilter::make("email_verified")
          ->label(__("admin.users.verification_status"))
          ->placeholder(__("admin.common.all"))
          ->trueLabel(__("admin.users.verified"))
          ->falseLabel(__("admin.users.unverified"))
          ->queries(
            true: fn(Builder $query) => $query->whereNotNull("email_verified_at"),
            false: fn(Builder $query) => $query->whereNull("email_verified_at"),
          ),
      ])
      ->headerActions([
        Tables\Actions\AttachAction::make()
          ->label(__("admin.companies.actions.add_user"))
          ->recordSelectOptionsQuery(fn(Builder $query) => $query->orderBy("email"))
          ->form(
            fn(Tables\Actions\AttachAction $action): array => [
              $action
                ->getRecordSelect()
                ->label(__("admin.users.label"))
                ->placeholder(__("admin.users.select_user"))
                ->searchable(),

              Forms\Components\Section::make(__("admin.companies.users.settings"))->schema([
                Forms\Components\Toggle::make("is_primary")
                  ->label(__("admin.companies.users.is_primary"))
                  ->helperText(__("admin.companies.users.is_primary_help"))
                  ->default(false),
              ]),
            ],
          )
          ->after(function (Model $record, RelationManager $livewire): void {
            Assert::instanceOf($record, User::class);

            // If user has no selected company, set this as their selected company
            if ($record->selected_company_id === null) {
              $company = $livewire->getOwnerRecord();
              Assert::instanceOf($company, Company::class);

              $record->selected_company_id = $company->id;
              $record->save();
            }
          }),
      ])
      ->actions([
        Tables\Actions\EditAction::make()
          ->form(
            fn(): array => [
              Forms\Components\Section::make(__("admin.companies.users.settings"))->schema([
                Forms\Components\Toggle::make("is_primary")
                  ->label(__("admin.companies.users.is_primary"))
                  ->helperText(__("admin.companies.users.is_primary_help")),
              ]),
            ],
          )
          ->mutateRecordDataUsing(function (User $record, RelationManager $livewire): array {
            $company = $livewire->getOwnerRecord();
            Assert::instanceOf($company, Company::class);

            // Get the pivot data for this user from the company relationship
            $userWithPivot = $company->users()->where("users.id", $record->id)->first();

            return [
              "is_primary" => $userWithPivot?->pivot->is_primary ?? false,
            ];
          })
          ->using(function (User $record, array $data, RelationManager $livewire): User {
            $company = $livewire->getOwnerRecord();
            Assert::instanceOf($company, Company::class);

            // Update this user's pivot data
            $company->users()->updateExistingPivot($record->id, [
              "is_primary" => $data["is_primary"] ?? false,
            ]);

            return $record;
          }),

        Tables\Actions\DetachAction::make()
          ->label(__("admin.common.remove"))
          ->requiresConfirmation()
          ->modalHeading(__("admin.companies.users.remove_confirmation"))
          ->modalDescription(__("admin.companies.users.remove_description"))
          ->before(function (RelationManager $livewire, User $record): ?bool {
            $company = $livewire->getOwnerRecord();
            Assert::instanceOf($company, Company::class);

            // Warn if removing primary user
            // Access pivot through the relationship
            $companyUser = $company->users()->where("users.id", $record->id)->first();

            if (
              $companyUser !== null &&
              $companyUser->pivot !== null &&
              $companyUser->pivot->is_primary
            ) {
              Notification::make()
                ->title(__("admin.companies.users.removing_primary"))
                ->warning()
                ->send();
            }

            return null;
          })
          ->after(function (RelationManager $livewire, User $record): void {
            $company = $livewire->getOwnerRecord();
            Assert::instanceOf($company, Company::class);

            // Reset selected_company_id if user had this company selected
            if ($record->selected_company_id === $company->id) {
              // Try to find another company the user belongs to
              $alternativeCompany = $record
                ->companies()
                ->where("companies.id", "!=", $company->id)
                ->first();

              $record->selected_company_id = $alternativeCompany?->id;
              $record->save();
            }
          }),
      ])
      ->emptyStateHeading(__("admin.companies.empty.no_users"))
      ->emptyStateDescription(__("admin.companies.help.add_users"))
      ->emptyStateIcon("heroicon-o-user-group");
  }
}
