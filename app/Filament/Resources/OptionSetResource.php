<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\OptionSetResource\Pages;
use App\Filament\Resources\OptionSetResource\RelationManagers\OptionsRelationManager;
use App\Helpers\Assert;
use App\Models\OptionSet;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

final class OptionSetResource extends Resource
{
  use Translatable;

  protected static ?string $model = OptionSet::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  protected static ?int $navigationSort = 15;

  public static function getModelLabel(): string
  {
    return __("admin.options.option_set");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.options.option_sets");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("admin.common.basic_info"))->schema([
        Forms\Components\TextInput::make("name")
          ->label(__("admin.common.name"))
          ->required()
          ->maxLength(255),
      ]),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(fn(Builder $query) => $query->with(["options", "calculationDefinitions"]))
      ->columns([
        Tables\Columns\TextColumn::make("name")
          ->label(__("admin.common.name"))
          ->searchable()
          ->sortable(),

        Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: false),
      ])
      ->filters([
        Tables\Filters\Filter::make("in_use")
          ->label(__("admin.options.status.active"))
          ->query(fn(Builder $query): Builder => $query->has("calculationDefinitions")),

        Tables\Filters\Filter::make("not_in_use")
          ->label(__("admin.options.status.inactive"))
          ->query(fn(Builder $query): Builder => $query->doesntHave("calculationDefinitions")),
      ])
      ->actions([
        Tables\Actions\EditAction::make(),
        Tables\Actions\DeleteAction::make()->before(function (
          Tables\Actions\DeleteAction $action,
          OptionSet $record,
        ) {
          if ($record->calculationDefinitions()->exists()) {
            $action->cancel();
            $action->failure();
            $action->failureNotificationTitle(__("admin.options.errors.cannot_delete"));
            $action->sendFailureNotification();

            throw new Exception(__("admin.options.errors.cannot_delete_in_use"));
          }
        }),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make()->before(function (
            Tables\Actions\DeleteBulkAction $action,
            Collection $records,
          ) {
            foreach ($records as $record) {
              Assert::instanceOf($record, OptionSet::class);
              if ($record->calculationDefinitions()->exists()) {
                $action->cancel();
                $action->failure();
                $action->failureNotificationTitle(__("admin.options.errors.cannot_delete"));
                $action->sendFailureNotification();

                throw new Exception(__("admin.options.errors.one_or_more_in_use"));
              }
            }
          }),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [OptionsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListOptionSets::route("/"),
      "create" => Pages\CreateOptionSet::route("/create"),
      "edit" => Pages\EditOptionSet::route("/{record}/edit"),
    ];
  }
}
