<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\OptionSetResource\Pages;
use App\Filament\Resources\OptionSetResource\RelationManagers\OptionsRelationManager;
use App\Helpers\Assert;
use App\Models\OptionSet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

final class OptionSetResource extends Resource
{
  use Translatable;

  protected static ?string $model = OptionSet::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  protected static ?int $navigationSort = 15;

  public static function getModelLabel(): string
  {
    return __("Vaihtoehtojoukko");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Vaihtoehtojoukot");
  }

  public static function getNavigationGroup(): string
  {
    return __("Laskenta");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("Perustiedot"))->schema([
        Forms\Components\TextInput::make("name")->label(__("Nimi"))->required()->maxLength(255),
      ]),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(fn(Builder $query) => $query->with(["options", "calculationDefinitions"]))
      ->columns([
        Tables\Columns\TextColumn::make("name")->label(__("Nimi"))->searchable()->sortable(),

        Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: false),
      ])
      ->filters([
        Tables\Filters\Filter::make("in_use")
          ->label(__("Käytössä"))
          ->query(fn(Builder $query): Builder => $query->has("calculationDefinitions")),

        Tables\Filters\Filter::make("not_in_use")
          ->label(__("Ei käytössä"))
          ->query(fn(Builder $query): Builder => $query->doesntHave("calculationDefinitions")),
      ])
      ->actions([
        Tables\Actions\EditAction::make(),
        Tables\Actions\DeleteAction::make()->before(function (
          Tables\Actions\DeleteAction $action,
          OptionSet $record,
        ) {
          if ($record->calculationDefinitions()->exists()) {
            $action->cancel();
            $action->failure();
            $action->failureNotificationTitle(__("Ei voida poistaa"));
            $action->sendFailureNotification();

            throw new \Exception(
              __("Vaihtoehtojoukkoa ei voi poistaa koska se on käytössä määrittelyissä"),
            );
          }
        }),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make()->before(function (
            Tables\Actions\DeleteBulkAction $action,
            \Illuminate\Support\Collection $records,
          ) {
            foreach ($records as $record) {
              Assert::instanceOf($record, OptionSet::class);
              if ($record->calculationDefinitions()->exists()) {
                $action->cancel();
                $action->failure();
                $action->failureNotificationTitle(__("Ei voida poistaa"));
                $action->sendFailureNotification();

                throw new \Exception(
                  __("Yksi tai useampi vaihtoehtojoukko on käytössä määrittelyissä"),
                );
              }
            }
          }),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [OptionsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListOptionSets::route("/"),
      "create" => Pages\CreateOptionSet::route("/create"),
      "edit" => Pages\EditOptionSet::route("/{record}/edit"),
    ];
  }
}
