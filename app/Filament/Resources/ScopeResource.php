<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\ScopeResource\Pages;
use App\Filament\Resources\ScopeResource\RelationManagers;
use App\Models\Scope;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

final class ScopeResource extends Resource
{
  use Translatable;

  protected static ?string $model = Scope::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\TextInput::make("number")
        ->required()
        ->numeric()
        ->label(__("admin.scopes.fields.number")),
      Forms\Components\Toggle::make("allow_grouping_hiding")
        ->label(__("admin.scopes.fields.allow_grouping_hiding"))
        ->default(false),
    ]);
  }

  public static function getModelLabel(): string
  {
    return __("admin.scopes.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.scopes.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.scopes.fields.grouping");
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("number")
          ->label(__("admin.scopes.fields.number"))
          ->numeric()
          ->sortable(),
        Tables\Columns\ToggleColumn::make("allow_grouping_hiding")
          ->label(__("admin.scopes.fields.hiding_allowed"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        //
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([Tables\Actions\DeleteBulkAction::make()]),
      ]);
  }

  public static function getRelations(): array
  {
    return [RelationManagers\CalculationVariantsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListScopes::route("/"),
      "create" => Pages\CreateScope::route("/create"),
      "edit" => Pages\EditScope::route("/{record}/edit"),
    ];
  }
}
