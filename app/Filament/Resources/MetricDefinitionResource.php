<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\MetricDefinitionResource\Pages;
use App\Filament\Resources\MetricDefinitionResource\Pages\ListMetricDefinitions;
use App\Filament\Resources\MetricDefinitionResource\RelationManagers\YearsRelationManager;
use App\Models\MetricDefinition;
use App\Models\Unit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class MetricDefinitionResource extends Resource
{
  use Translatable;

  protected static ?string $model = MetricDefinition::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("admin.metrics.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.metrics.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("admin.common.basic_info"))
        ->schema([
          Forms\Components\TextInput::make("name")
            ->label(__("admin.metrics.fields.name"))
            ->maxLength(255)
            ->required()
            ->helperText(__("admin.metrics.help.display_name"))
            ->columnSpanFull(),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("admin.metrics.fields.unit"))
        ->schema([
          Forms\Components\Select::make("unit_id")
            ->relationship("unit", "id", fn(Builder $query) => $query->with("translations"))
            ->getOptionLabelFromRecordUsing(
              fn(
                Unit $record,
                Page $livewire,
                MetricDefinitionResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("admin.metrics.fields.unit"))
            ->required()
            ->searchable()
            ->preload()
            ->helperText(__("admin.metrics.help.select_unit")),
        ])
        ->columns(1),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(
          Builder $query,
          ListMetricDefinitions $livewire,
          MetricDefinitionResource $resource,
        ) => $query->with([
          "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          "unit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "years",
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("id")
          ->label("ID")
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.name",
        )
          ->label(__("admin.metrics.fields.name"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "unit.translations.name",
        )
          ->label(__("admin.metrics.fields.unit"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "unit.translations.symbol",
        )
          ->label(__("admin.metrics.fields.unit_symbol"))
          ->sortable()
          ->toggleable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->label(__("admin.common.deleted"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TrashedFilter::make(),
        Tables\Filters\SelectFilter::make("unit_id")
          ->label(__("admin.metrics.fields.unit"))
          ->relationship("unit", "id", fn(Builder $query) => $query->with("translations"))
          ->getOptionLabelFromRecordUsing(
            fn(Unit $record) => $record->translate() !== null ? $record->translate()->name : "",
          )
          ->multiple()
          ->preload(),
        Tables\Filters\Filter::make("has_years")
          ->label(__("admin.metrics.year_definitions"))
          ->query(fn(Builder $query): Builder => $query->has("years"))
          ->toggle(),
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("id", "desc");
  }

  public static function getRelations(): array
  {
    return [YearsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListMetricDefinitions::route("/"),
      "create" => Pages\CreateMetricDefinition::route("/create"),
      "edit" => Pages\EditMetricDefinition::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Model>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()
      ->withoutGlobalScopes([SoftDeletingScope::class])
      ->with(["translations"]); // Always load translations
  }
}
