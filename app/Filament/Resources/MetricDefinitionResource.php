<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\MetricDefinitionResource\Pages;
use App\Filament\Resources\MetricDefinitionResource\Pages\ListMetricDefinitions;
use App\Filament\Resources\MetricDefinitionResource\RelationManagers\YearsRelationManager;
use App\Models\MetricDefinition;
use App\Models\Unit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class MetricDefinitionResource extends Resource
{
  use Translatable;

  protected static ?string $model = MetricDefinition::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("Mittarimäärittely");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Mittarimäärittelyt");
  }

  public static function getNavigationGroup(): string
  {
    return __("Laskenta");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("Perustiedot"))
        ->schema([
          Forms\Components\TextInput::make("name")
            ->label(__("Mittarin nimi"))
            ->maxLength(255)
            ->required()
            ->helperText(__("Mittarin nimi, joka näkyy käyttäjille"))
            ->columnSpanFull(),
        ])
        ->columns(1),

      Forms\Components\Section::make(__("Yksikkö"))
        ->schema([
          Forms\Components\Select::make("unit_id")
            ->relationship("unit", "id", fn(Builder $query) => $query->with("translations"))
            ->getOptionLabelFromRecordUsing(
              fn(
                Unit $record,
                Page $livewire,
                MetricDefinitionResource $resource,
              ) => $record->translate($resource::getActiveLocale())->name ?? "",
            )
            ->label(__("Mittarin yksikkö"))
            ->required()
            ->searchable()
            ->preload()
            ->helperText(__("Valitse yksikkö, jossa mittarin arvo ilmoitetaan")),
        ])
        ->columns(1),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(
          Builder $query,
          ListMetricDefinitions $livewire,
          MetricDefinitionResource $resource,
        ) => $query->with([
          "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
          "unit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "years",
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("id")
          ->label("ID")
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.name",
        )
          ->label(__("Mittarin nimi"))
          ->searchable()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "unit.translations.name",
        )
          ->label(__("Yksikkö"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "unit.translations.symbol",
        )
          ->label(__("Yksikön symboli"))
          ->sortable()
          ->toggleable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->label(__("Poistettu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([
        Tables\Filters\TrashedFilter::make(),
        Tables\Filters\SelectFilter::make("unit_id")
          ->label(__("Yksikkö"))
          ->relationship("unit", "id", fn(Builder $query) => $query->with("translations"))
          ->getOptionLabelFromRecordUsing(
            fn(Unit $record) => $record->translate() !== null ? $record->translate()->name : "",
          )
          ->multiple()
          ->preload(),
        Tables\Filters\Filter::make("has_years")
          ->label(__("Vuosimäärittelyt"))
          ->query(fn(Builder $query): Builder => $query->has("years"))
          ->toggle(),
      ])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("id", "desc");
  }

  public static function getRelations(): array
  {
    return [YearsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListMetricDefinitions::route("/"),
      "create" => Pages\CreateMetricDefinition::route("/create"),
      "edit" => Pages\EditMetricDefinition::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Model>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()
      ->withoutGlobalScopes([SoftDeletingScope::class])
      ->with(["translations"]); // Always load translations
  }
}
