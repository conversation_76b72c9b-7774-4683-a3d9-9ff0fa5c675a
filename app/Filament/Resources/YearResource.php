<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\YearResource\Pages;
use App\Filament\Resources\YearResource\RelationManagers\CalculationDefinitionsRelationManager;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class YearResource extends Resource
{
  use Translatable;

  protected static ?string $model = Year::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("Vuosi");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Vuodet");
  }

  public static function getNavigationGroup(): string
  {
    return __("Laskenta");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\TextInput::make("year")->required()->numeric()->label(__("Vuosi")),

      Forms\Components\DateTimePicker::make("published")
        ->label(__("Julkaistu"))
        ->nullable()
        ->seconds(false),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("year")
          ->label(__("Vuosi"))
          ->sortable()
          ->searchable(),

        Tables\Columns\IconColumn::make("published")
          ->label(__("Julkaistu"))
          ->boolean()
          ->sortable()
          ->getStateUsing(fn(Year $record) => $record->published !== null),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("Luotu"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("Päivitetty"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([
        Tables\Actions\Action::make("toggle_published")
          ->label(fn(Year $record) => $record->published !== null ? __("Piilota") : __("Julkaise"))
          ->icon(
            fn(Year $record) => $record->published !== null
              ? "heroicon-o-eye-slash"
              : "heroicon-o-eye",
          )
          ->action(function (Year $record) {
            $record->published = $record->published !== null ? null : now();
            $record->save();
          }),

        Tables\Actions\EditAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("year", "desc");
  }

  public static function getRelations(): array
  {
    return [CalculationDefinitionsRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListYears::route("/"),
      "create" => Pages\CreateYear::route("/create"),
      "edit" => Pages\EditYear::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Year>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
