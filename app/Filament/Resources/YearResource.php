<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\YearResource\Pages;
use App\Filament\Resources\YearResource\RelationManagers\CalculationDefinitionsRelationManager;
use App\Filament\Resources\YearResource\RelationManagers\CompaniesRelationManager;
use App\Models\Year;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class YearResource extends Resource
{
  use Translatable;

  protected static ?string $model = Year::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("admin.years.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.years.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Section::make(__("admin.common.basic_info"))->schema([
        Forms\Components\TextInput::make("year")
          ->required()
          ->numeric()
          ->label(__("admin.years.label")),

        Forms\Components\DateTimePicker::make("published")
          ->label(__("admin.years.status.published"))
          ->nullable()
          ->seconds(false),
      ]),

      Forms\Components\Section::make(__("admin.years.sections.badge_images"))
        ->description(__("admin.years.actions.download_badges"))
        ->schema([
          Forms\Components\FileUpload::make("scope1_2_mark_image")
            ->label(__("admin.years.labels.scope_1_2_badge"))
            ->image()
            ->imageEditor()
            ->maxSize(5120) // 5MB
            ->disk("public")
            ->directory("year-marks/scope-1-2")
            ->visibility("public")
            ->acceptedFileTypes(["image/jpeg", "image/png", "image/svg+xml", "image/webp"])
            ->helperText(__("admin.years.help.scope_1_2_badge_image"))
            ->columnSpan([
              "sm" => 2,
              "md" => 1,
            ]),

          Forms\Components\FileUpload::make("scope1_3_mark_image")
            ->label(__("admin.years.labels.scope_1_3_badge"))
            ->image()
            ->imageEditor()
            ->maxSize(5120) // 5MB
            ->disk("public")
            ->directory("year-marks/scope-1-3")
            ->visibility("public")
            ->acceptedFileTypes(["image/jpeg", "image/png", "image/svg+xml", "image/webp"])
            ->helperText(__("admin.years.help.scope_1_3_badge_image"))
            ->columnSpan([
              "sm" => 2,
              "md" => 1,
            ]),
        ])
        ->columns(2),

      Forms\Components\Section::make(__("admin.years.labels.documents"))
        ->description(__("admin.years.actions.download_criteria"))
        ->schema([
          Forms\Components\FileUpload::make("scope1_2_criteria_procedures_pdf")
            ->label(__("admin.years.labels.scope_1_2_criteria"))
            ->acceptedFileTypes(["application/pdf"])
            ->maxSize(20480) // 20MB
            ->disk("public")
            ->directory("year-documents/scope-1-2")
            ->visibility("public")
            ->downloadable()
            ->previewable()
            ->openable()
            ->helperText(__("admin.years.help.scope_1_2_criteria_document"))
            ->columnSpan([
              "sm" => 2,
              "md" => 1,
            ]),

          Forms\Components\FileUpload::make("scope1_3_criteria_procedures_pdf")
            ->label(__("admin.years.labels.scope_1_3_criteria"))
            ->acceptedFileTypes(["application/pdf"])
            ->maxSize(20480) // 20MB
            ->disk("public")
            ->directory("year-documents/scope-1-3")
            ->visibility("public")
            ->downloadable()
            ->previewable()
            ->openable()
            ->helperText(__("admin.years.help.scope_1_3_criteria_document"))
            ->columnSpan([
              "sm" => 2,
              "md" => 1,
            ]),
        ])
        ->columns(2),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("year")
          ->label(__("admin.years.label"))
          ->sortable()
          ->searchable(),

        Tables\Columns\IconColumn::make("published")
          ->label(__("admin.years.status.published"))
          ->boolean()
          ->sortable()
          ->getStateUsing(fn(Year $record) => $record->published !== null),

        Tables\Columns\ImageColumn::make("scope1_2_mark_image")
          ->label(__("admin.years.scopes.scope_1_2"))
          ->circular()
          ->size(40)
          ->tooltip(
            fn($state): string => $state
              ? __("admin.years.messages.scope_1_2_badge_uploaded")
              : __("admin.years.no_scope_1_2_badge"),
          )
          ->toggleable(),

        Tables\Columns\ImageColumn::make("scope1_3_mark_image")
          ->label(__("admin.years.scopes.scope_1_3"))
          ->circular()
          ->size(40)
          ->tooltip(
            fn($state): string => $state
              ? __("admin.years.messages.scope_1_3_badge_uploaded")
              : __("admin.years.no_scope_1_3_badge"),
          )
          ->toggleable(),

        Tables\Columns\IconColumn::make("scope1_2_criteria_procedures_pdf")
          ->label(__("admin.years.scope_1_2_pdf"))
          ->icon(fn($state): string => $state ? "heroicon-o-document-text" : "heroicon-o-x-circle")
          ->color(fn($state): string => $state ? "success" : "gray")
          ->tooltip(
            fn($state): string => $state
              ? __("admin.years.messages.scope_1_2_pdf_uploaded")
              : __("admin.years.no_scope_1_2_pdf"),
          )
          ->toggleable(),

        Tables\Columns\IconColumn::make("scope1_3_criteria_procedures_pdf")
          ->label(__("admin.years.scope_1_3_pdf"))
          ->icon(fn($state): string => $state ? "heroicon-o-document-text" : "heroicon-o-x-circle")
          ->color(fn($state): string => $state ? "success" : "gray")
          ->tooltip(
            fn($state): string => $state
              ? __("admin.years.messages.scope_1_3_pdf_uploaded")
              : __("admin.years.no_scope_1_3_pdf"),
          )
          ->toggleable(),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->label(__("admin.common.created"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),

        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->label(__("admin.common.updated"))
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([
        Tables\Actions\Action::make("toggle_published")
          ->label(
            fn(Year $record) => $record->published !== null
              ? __("admin.years.actions.hide")
              : __("admin.years.actions.publish"),
          )
          ->icon(
            fn(Year $record) => $record->published !== null
              ? "heroicon-o-eye-slash"
              : "heroicon-o-eye",
          )
          ->action(function (Year $record) {
            $record->published = $record->published !== null ? null : now();
            $record->save();
          }),

        Tables\Actions\EditAction::make(),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort("year", "desc");
  }

  public static function getRelations(): array
  {
    return [CalculationDefinitionsRelationManager::class, CompaniesRelationManager::class];
  }

  public static function getPages(): array
  {
    return [
      "index" => Pages\ListYears::route("/"),
      "create" => Pages\CreateYear::route("/create"),
      "edit" => Pages\EditYear::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Year>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()
      ->with(["translations"])
      ->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
