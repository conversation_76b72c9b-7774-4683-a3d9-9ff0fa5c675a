<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\UnitResource\Pages;
use App\Filament\Resources\UnitResource\Pages\ListUnits;
use App\Models\Unit;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class UnitResource extends Resource
{
  use Translatable;

  protected static ?string $model = Unit::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function form(Form $form): Form
  {
    return $form->schema([
      TextInput::make("name")->label(__("admin.units.fields.name")),
      TextInput::make("symbol")->label(__("admin.units.fields.symbol")),
    ]);
  }

  public static function getModelLabel(): string
  {
    return __("admin.units.label");
  }

  public static function getPluralModelLabel(): string
  {
    return __("admin.units.plural");
  }

  public static function getNavigationGroup(): string
  {
    return __("admin.navigation.calculations");
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(Builder $query, ListUnits $livewire, UnitResource $resource) => $query->with([
          "translations" => fn(Relation $q) => $q->where("locale", $resource::getActiveLocale()),
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.name",
        )
          ->label(__("admin.units.fields.name"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "translations.symbol",
        )
          ->label(__("admin.units.fields.symbol"))
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([Tables\Actions\EditAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListUnits::route("/"),
      "create" => Pages\CreateUnit::route("/create"),
      "edit" => Pages\EditUnit::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<Unit>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
