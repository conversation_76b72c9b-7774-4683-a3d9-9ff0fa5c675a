<?php

declare(strict_types=1);

namespace App\Filament\Resources\LanguageLineResource\Pages;

use App\Filament\Plugins\AstrotomicTranslatable\Actions\LocaleSwitcher;
use App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\ListRecords\Concerns\Translatable;
use App\Filament\Resources\LanguageLineResource;
use Filament\Resources\Pages\ListRecords;

final class ListLanguageLines extends ListRecords
{
  use Translatable;

  protected static string $resource = LanguageLineResource::class;

  protected function getHeaderActions(): array
  {
    return [LocaleSwitcher::make()];
  }
}
