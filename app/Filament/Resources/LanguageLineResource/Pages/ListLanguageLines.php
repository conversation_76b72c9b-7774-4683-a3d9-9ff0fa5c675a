<?php

declare(strict_types=1);

namespace App\Filament\Resources\LanguageLineResource\Pages;

use App\Filament\Resources\LanguageLineResource;
use App\Helpers\Assert;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\File;
use RuntimeException;
use Spatie\TranslationLoader\LanguageLine;

final class ListLanguageLines extends ListRecords
{
  protected static string $resource = LanguageLineResource::class;

  /**
   * Get the header actions for the page.
   *
   * @return array<Actions\Action>
   */
  protected function getHeaderActions(): array
  {
    return [
      Actions\Action::make("import_from_files")
        ->label(__("Tuo tiedostoista"))
        ->icon("heroicon-o-arrow-down-tray")
        ->action(function (): void {
          $this->importTranslationsFromFiles();
        }),
    ];
  }

  /**
   * Import translations from language files.
   *
   * @throws RuntimeException
   */
  protected function importTranslationsFromFiles(): void
  {
    $importCount = 0;
    $langPath = lang_path();

    // Define specific JSON files to import
    $jsonFilesToImport = [
      ".json", // E.g., fi.json, en.json
    ];

    // Get available locales
    $locales = array_keys(LanguageLineResource::getAvailableLocales());

    foreach ($locales as $locale) {
      // Import JSON files
      foreach ($jsonFilesToImport as $jsonExt) {
        $jsonPath = $langPath . "/" . $locale . $jsonExt;

        if (File::exists($jsonPath)) {
          $jsonContent = File::get($jsonPath);
          $jsonData = json_decode($jsonContent, true);

          if (is_array($jsonData)) {
            $translations = [];
            foreach ($jsonData as $key => $value) {
              if (is_string($key)) {
                $translations[$key] = $value;
              }
            }

            // Import JSON translations (group = *)
            $importCount += $this->importTranslationArray($translations, "*", $locale, true);
          }
        }
      }
    }

    $this->notify("success", __(":count käännöstä tuotu onnistuneesti", ["count" => $importCount]));
  }

  /**
   * Import a translation array into the database.
   *
   * @param  array<string, mixed>  $translations
   */
  protected function importTranslationArray(
    array $translations,
    string $group,
    string $locale,
    bool $isFlat = false,
    string $prefix = "",
  ): int {
    $count = 0;

    foreach ($translations as $key => $value) {
      $fullKey = $prefix !== "" ? "{$prefix}.{$key}" : $key;

      if (is_array($value) && !$isFlat) {
        Assert::stringKeyedArray($value);
        // Recursively import nested translations
        $count += $this->importTranslationArray($value, $group, $locale, false, $fullKey);
      } else {
        // Import single translation
        $languageLine = LanguageLine::query()->firstOrNew([
          "group" => $group,
          "key" => $fullKey,
        ]);

        $text = $languageLine->text ?? [];
        $text[$locale] = $value;

        $languageLine->text = $text;
        $languageLine->save();

        $count++;
      }
    }

    return $count;
  }

  /**
   * Show a notification.
   */
  protected function notify(string $status, string $message): void
  {
    Notification::make()->title($message)->status($status)->send();
  }
}
