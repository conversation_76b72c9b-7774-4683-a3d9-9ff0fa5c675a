<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns\Translatable;
use App\Filament\Resources\CompoundUnitResource\Pages;
use App\Filament\Resources\CompoundUnitResource\Pages\ListCompoundUnits;
use App\Models\CompoundUnit;
use App\Models\Unit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;

final class CompoundUnitResource extends Resource
{
  use Translatable;

  protected static ?string $model = CompoundUnit::class;

  protected static ?string $navigationIcon = "heroicon-o-rectangle-stack";

  public static function getModelLabel(): string
  {
    return __("Yksikköyhdistelmä");
  }

  public static function getPluralModelLabel(): string
  {
    return __("Yksikköyhdistelmät");
  }

  public static function getNavigationGroup(): string
  {
    return __("Laskenta");
  }

  public static function form(Form $form): Form
  {
    return $form->schema([
      Forms\Components\Select::make("numerator_unit_id")
        ->relationship("numeratorUnit", "id", fn(Builder $query) => $query->with("translations"))
        ->getOptionLabelFromRecordUsing(
          fn(Unit $record, Page $livewire, CompoundUnitResource $resource) => $record->translate(
            $resource::getActiveLocale(),
          )->name ?? "",
        )
        ->label(__("Osoittajayksikkö"))
        ->required(),
      Forms\Components\Select::make("denominator_unit_id")
        ->relationship("denominatorUnit", "id", fn(Builder $query) => $query->with("translations"))
        ->getOptionLabelFromRecordUsing(
          fn(Unit $record, Page $livewire, CompoundUnitResource $resource) => $record->translate(
            $resource::getActiveLocale(),
          )->name ?? "",
        )
        ->label(__("Nimittäjäyksikkö"))
        ->required(),
    ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->modifyQueryUsing(
        fn(
          Builder $query,
          ListCompoundUnits $livewire,
          CompoundUnitResource $resource,
        ) => $query->with([
          "numeratorUnit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
          "denominatorUnit.translations" => fn(Relation $q) => $q->where(
            "locale",
            $resource::getActiveLocale(),
          ),
        ]),
      )
      ->columns([
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "numeratorUnit.translations.name",
        )
          ->label(__("Osoittajayksikkö"))
          ->numeric()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make(
          "denominatorUnit.translations.name",
        )
          ->label(__("Nimittäjäyksikkö"))
          ->numeric()
          ->sortable(),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("created_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("updated_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
        \App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns\TextColumn::make("deleted_at")
          ->dateTime()
          ->sortable()
          ->toggleable(isToggledHiddenByDefault: true),
      ])
      ->filters([Tables\Filters\TrashedFilter::make()])
      ->actions([Tables\Actions\EditAction::make(), Tables\Actions\RestoreAction::make()])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ]);
  }

  public static function getRelations(): array
  {
    return [
        //
      ];
  }

  public static function getPages(): array
  {
    return [
      "index" => ListCompoundUnits::route("/"),
      "create" => Pages\CreateCompoundUnit::route("/create"),
      "edit" => Pages\EditCompoundUnit::route("/{record}/edit"),
    ];
  }

  /**
   * @return Builder<CompoundUnit>
   */
  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()->withoutGlobalScopes([SoftDeletingScope::class]);
  }
}
