<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\CreateRecord\Concerns;

use App\Helpers\Assert;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Illuminate\Database\Eloquent\MassAssignmentException;
use Illuminate\Database\Eloquent\Model;

trait Translatable
{
  public ?string $activeLocale = null;

  public function mount(): void
  {
    parent::mount();

    $activeLocale = static::getResource()::getActiveLocale();

    Assert::string($activeLocale);

    $this->activeLocale = $activeLocale;
  }

  public function updatedActiveLocale(): void
  {
    if ($this->activeLocale !== null) {
      static::getResource()::setActiveLocale($this->activeLocale);
    }
  }

  /**
   * @param  array<string, mixed>  $data
   *
   * @throws MassAssignmentException
   */
  protected function handleRecordCreation(array $data): Model
  {
    $locale = $this->activeLocale ?? app()->getLocale();
    $modelClass = static::getModel();

    $model = new $modelClass();

    /** The IDE is not as smart as the linter. */
    /** @phpstan-ignore-next-line instanceof.alwaysTrue */
    assert($model instanceof Model);

    if ($model instanceof TranslatableContract) {
      // Separate translatable and non-translatable attributes
      $translatableAttributes = [];

      if (property_exists($model, "translatedAttributes")) {
        $attributes = $model->translatedAttributes;
        if (is_array($attributes)) {
          $translatableAttributes = $attributes;
        }
      }

      $nonTranslatableData = [];
      $translatableData = [];

      foreach ($data as $key => $value) {
        if (in_array($key, $translatableAttributes, true)) {
          $translatableData[$key] = $value;
        } else {
          $nonTranslatableData[$key] = $value;
        }
      }

      // Create model with non-translatable data
      $model->fill($nonTranslatableData);
      $model->save();

      // Add translations for the active locale
      $model->setRelation("translation", null);
      $translation = $model->translateOrNew($locale);
      foreach ($translatableData as $key => $value) {
        $translation->setAttribute($key, $value ?? "");
      }

      $model->save();

      return $model;
    }

    return parent::handleRecordCreation($data);
  }
}
