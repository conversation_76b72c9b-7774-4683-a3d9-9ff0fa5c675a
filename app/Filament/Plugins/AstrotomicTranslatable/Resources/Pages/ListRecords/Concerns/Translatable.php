<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\ListRecords\Concerns;

use App\Helpers\Assert;

trait Translatable
{
  public ?string $activeLocale = null;

  public function mount(): void
  {
    parent::mount();

    $activeLocale = static::getResource()::getActiveLocale();

    Assert::string($activeLocale);

    $this->activeLocale = $activeLocale;
  }

  public function updatedActiveLocale(): void
  {
    if ($this->activeLocale !== null) {
      static::getResource()::setActiveLocale($this->activeLocale);
      $this->resetTable();
    }
  }
}
