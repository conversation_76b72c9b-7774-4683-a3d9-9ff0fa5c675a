<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\Pages\EditRecord\Concerns;

use App\Helpers\Assert;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Illuminate\Database\Eloquent\MassAssignmentException;
use Illuminate\Database\Eloquent\Model;

trait Translatable
{
  public ?string $activeLocale = null;

  /**
   * @param  int|string  $record
   */
  public function mount(mixed $record): void
  {
    parent::mount($record);

    $activeLocale = static::getResource()::getActiveLocale();

    Assert::string($activeLocale);

    $this->activeLocale = $activeLocale;
  }

  public function updatedActiveLocale(): void
  {
    if ($this->activeLocale !== null) {
      static::getResource()::setActiveLocale($this->activeLocale);

      $this->dispatch("localeChanged");
      // Refill the form with the new locale's data
      $this->fillForm();
    }
  }

  /**
   * @param  array<string, mixed>  $data
   *
   * @throws MassAssignmentException
   */
  protected function handleRecordUpdate(Model $model, array $data): Model
  {
    $locale = $this->activeLocale ?? app()->getLocale();

    if ($model instanceof TranslatableContract) {
      // Separate translatable and non-translatable attributes
      $translatableAttributes = [];

      if (property_exists($model, "translatedAttributes")) {
        $attributes = $model->translatedAttributes;
        if (is_array($attributes)) {
          $translatableAttributes = $attributes;
        }
      }

      $nonTranslatableData = [];
      $translatableData = [];

      foreach ($data as $key => $value) {
        if (in_array($key, $translatableAttributes, true)) {
          $translatableData[$key] = $value;
        } else {
          $nonTranslatableData[$key] = $value;
        }
      }

      // Update non-translatable attributes
      $model->fill($nonTranslatableData);
      $model->save();

      // Update translatable attributes for the active locale
      $translation = $model->translateOrNew($locale);
      foreach ($translatableData as $key => $value) {
        $translation->setAttribute($key, $value ?? "");
      }

      $model->save();

      return $model;
    }

    return parent::handleRecordUpdate($model, $data);
  }

  /**
   * @param  array<string, mixed>  $data
   * @return array<string, mixed>
   */
  protected function mutateFormDataBeforeFill(array $data): array
  {
    $locale = $this->activeLocale ?? app()->getLocale();
    $record = $this->getRecord();

    if ($record instanceof TranslatableContract) {
      $translation = $record->translate($locale, false);

      if ($translation !== null) {
        $translatableAttributes = [];

        if (property_exists($record, "translatedAttributes")) {
          $attributes = $record->translatedAttributes;
          if (is_array($attributes)) {
            $translatableAttributes = $attributes;
          }
        }

        foreach ($translatableAttributes as $attribute) {
          if (is_string($attribute) && $translation->offsetExists($attribute)) {
            $data[$attribute] = $translation->getAttribute($attribute);
          } else {
            $data[$attribute] = null;
          }
        }
      } else {
        // If no translation exists, set translatable fields to null
        $translatableAttributes = [];

        if (property_exists($record, "translatedAttributes")) {
          $attributes = $record->translatedAttributes;
          if (is_array($attributes)) {
            $translatableAttributes = $attributes;
          }
        }

        foreach ($translatableAttributes as $attribute) {
          $data[$attribute] = null;
        }
      }
    }

    Assert::stringKeyedArray($data);

    return parent::mutateFormDataBeforeFill($data);
  }
}
