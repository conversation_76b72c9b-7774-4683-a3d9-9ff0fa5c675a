<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns;

use App\Helpers\Assert;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Actions\EditAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Livewire\Attributes\On;

trait Translatable
{
  #[On("locale-changed")]
  public function handleLocaleChange(): void
  {
    $this->resetTable();
  }

  public function getActiveLocale(): string
  {
    $pageClass = $this->getPageClass();

    if (method_exists($pageClass, "getResource")) {
      $resourceClass = $pageClass::getResource();
      Assert::string($resourceClass);
      Assert::classString($resourceClass);
      if (method_exists($resourceClass, "getActiveLocale")) {
        $locale = $resourceClass::getActiveLocale();
        if (is_string($locale)) {
          return $locale;
        }
      }
    }

    Assert::never();
  }

  protected function configureEditActionForTranslatablePivot(EditAction $action): EditAction
  {
    $relationship = $this->getRelationship();
    if (!($relationship instanceof BelongsToMany)) {
      return $action;
    }

    $pivotClass = $relationship->getPivotClass();
    if (!is_subclass_of($pivotClass, TranslatableContract::class)) {
      return $action;
    }

    return $action
      ->mutateRecordDataUsing(function (array $data, Model $record) use (
        $pivotClass,
        $relationship,
      ): array {
        // Check if pivot data exists (when coming through BelongsToMany)
        $pivot = $record->getAttribute("pivot");
        if (!($pivot instanceof Pivot)) {
          return $data;
        }

        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Add all pivot attributes to the form data
        $pivotAttributes = $pivot->getAttributes();
        foreach ($pivotAttributes as $key => $value) {
          // Skip relationship keys - they shouldn't be in the form
          if (
            $key === $relationship->getForeignPivotKeyName() ||
            $key === $relationship->getRelatedPivotKeyName()
          ) {
            continue;
          }

          // For non-translatable attributes, add them directly
          if (!in_array($key, $translatableAttributes, true)) {
            $data[$key] = $value;
          }
        }

        // Handle translatable attributes if the pivot supports translations
        if (count($translatableAttributes) > 0 && $pivot instanceof TranslatableContract) {
          $locale = static::getActiveLocale();
          $translation = $pivot->translate($locale, false);

          if ($translation !== null) {
            foreach ($translatableAttributes as $attribute) {
              if (is_string($attribute)) {
                $data[$attribute] = $translation->getAttribute($attribute) ?? "";
              }
            }
          } else {
            // No translation exists - set empty string values
            foreach ($translatableAttributes as $attribute) {
              if (is_string($attribute)) {
                $data[$attribute] = "";
              }
            }
          }
        }

        return $data;
      })
      ->mutateFormDataUsing(function (array $data) use ($pivotClass): array {
        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Ensure all translatable attributes exist in the data
        foreach ($translatableAttributes as $attribute) {
          if (is_string($attribute)) {
            if (!isset($data[$attribute])) {
              $data[$attribute] = "";
            }
          }
        }

        return $data;
      })
      ->using(function (Model $record, array $data) use ($pivotClass, $relationship): Model {
        // Check if pivot data exists
        $pivot = $record->getAttribute("pivot");
        if (!($pivot instanceof Pivot)) {
          return $record;
        }

        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Separate the data into pivot and translation data
        $pivotData = [];
        $translationData = [];

        $foreignKey = $relationship->getForeignPivotKeyName();
        $relatedKey = $relationship->getRelatedPivotKeyName();

        foreach ($data as $key => $value) {
          // Skip the relationship keys
          if ($key === $foreignKey || $key === $relatedKey) {
            continue;
          }

          if (in_array($key, $translatableAttributes, true)) {
            // Store the actual value for translation
            $translationData[$key] = $value ?? "";
            // Set empty string in pivot table for translatable columns
            $pivotData[$key] = "";
          } else {
            $pivotData[$key] = $value;
          }
        }

        // Update pivot data (with null values for translatable columns)
        if (count($pivotData) > 0) {
          Assert::stringKeyedArray($pivotData);
          $pivot->update($pivotData);
        }

        // Update translatable attributes if applicable
        if (count($translatableAttributes) > 0 && $pivot instanceof TranslatableContract) {
          $locale = static::getActiveLocale();
          $translation = $pivot->translateOrNew($locale);

          // Ensure all translatable attributes have values (default to empty string)
          foreach ($translatableAttributes as $attribute) {
            if (is_string($attribute) && !isset($translationData[$attribute])) {
              $translationData[$attribute] = "";
            }
          }

          Assert::stringKeyedArray($translationData);
          $translation->fill($translationData);
          $translation->save();
        }

        return $record;
      });
  }

  protected function configureAttachActionForTranslatablePivot(AttachAction $action): AttachAction
  {
    $relationship = $this->getRelationship();
    if (!($relationship instanceof BelongsToMany)) {
      return $action;
    }

    $pivotClass = $relationship->getPivotClass();
    if (!is_subclass_of($pivotClass, TranslatableContract::class)) {
      return $action;
    }

    $pivotInstance = new $pivotClass();
    $translatableAttributes = [];
    if (
      property_exists($pivotInstance, "translatedAttributes") &&
      is_array($pivotInstance->translatedAttributes)
    ) {
      $translatableAttributes = $pivotInstance->translatedAttributes;
    }

    if (count($translatableAttributes) === 0) {
      return $action;
    }

    // The form fields defined in the AttachAction are automatically saved to the pivot table
    // We use the after() hook to handle the translation separately
    return $action->after(function (Model $record, array $data) use (
      $relationship,
      $pivotClass,
      $translatableAttributes,
    ): void {
      $foreignPivotKey = $relationship->getForeignPivotKeyName();
      $relatedPivotKey = $relationship->getRelatedPivotKeyName();
      $ownerRecord = $this->getOwnerRecord();

      // Find the just-created pivot record
      $pivot = $pivotClass
        ::where($relatedPivotKey, $record->getKey())
        ->where($foreignPivotKey, $ownerRecord->getKey())
        ->first();

      if (!($pivot instanceof Pivot) || !($pivot instanceof TranslatableContract)) {
        return;
      }

      // Extract and save translatable data to the translations table
      $translationData = [];
      foreach ($translatableAttributes as $attribute) {
        if (is_string($attribute)) {
          // Use the original form value or empty string
          $translationData[$attribute] = $data[$attribute] ?? "";
        }
      }

      // Save the translation for the active locale
      if (count($translationData) > 0) {
        $locale = static::getActiveLocale();
        $translation = $pivot->translateOrNew($locale);
        $translation->fill($translationData);
        $translation->save();
      }
    });
  }

  protected function getTranslatableColumnState(Model $record, string $column): mixed
  {
    $relationship = $this->getRelationship();

    // Handle BelongsToMany pivot translations
    if ($relationship instanceof BelongsToMany) {
      $pivot = $record->getAttribute("pivot");
      if ($pivot === null) {
        return $record->getAttribute($column) ?? "";
      }

      if (!($pivot instanceof Pivot)) {
        return $record->getAttribute($column) ?? "";
      }

      $pivotClass = $relationship->getPivotClass();
      if (!is_subclass_of($pivotClass, TranslatableContract::class)) {
        return $pivot->getAttribute($column);
      }

      $pivotInstance = new $pivotClass();
      $translatableAttributes = [];
      if (
        property_exists($pivotInstance, "translatedAttributes") &&
        is_array($pivotInstance->translatedAttributes)
      ) {
        $translatableAttributes = $pivotInstance->translatedAttributes;
      }

      // If this column is translatable, get it from translations
      if (
        in_array($column, $translatableAttributes, true) &&
        $pivot instanceof TranslatableContract
      ) {
        $locale = static::getActiveLocale();
        $translation = $pivot->translate($locale, false);

        return $translation !== null ? $translation->getAttribute($column) : "";
      }

      // Otherwise, get it directly from the pivot
      return $pivot->getAttribute($column) ?? "";
    }

    // Handle regular model translations
    if ($record instanceof TranslatableContract) {
      $translatableAttributes = [];
      if (
        property_exists($record, "translatedAttributes") &&
        is_array($record->translatedAttributes)
      ) {
        $translatableAttributes = $record->translatedAttributes;
      }

      if (in_array($column, $translatableAttributes, true)) {
        $locale = static::getActiveLocale();
        $translation = $record->translate($locale, false);

        return $translation !== null ? $translation->getAttribute($column) : "";
      }
    }

    return $record->getAttribute($column) ?? "";
  }
}
