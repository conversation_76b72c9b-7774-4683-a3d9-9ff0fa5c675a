<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\RelationManagers\Concerns;

use App\Helpers\Assert;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Actions\EditAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Livewire\Attributes\On;

trait Translatable
{
  #[On("locale-changed")]
  public function handleLocaleChange(): void
  {
    $this->resetTable();
  }

  /**
   * Override getTableRecords to properly load pivot translations
   * @return \Illuminate\Database\Eloquent\Collection<int, Model>|\Illuminate\Contracts\Pagination\Paginator<int, Model>|\Illuminate\Contracts\Pagination\CursorPaginator<int, Model>
   */
  public function getTableRecords(): \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\Paginator|\Illuminate\Contracts\Pagination\CursorPaginator
  {
    $records = parent::getTableRecords();

    // Get the relationship instance
    $relationship = $this->getRelationship();

    // If it's a BelongsToMany relationship with a translatable pivot
    if ($relationship instanceof BelongsToMany) {
      $pivotClass = $relationship->getPivotClass();

      // Check if pivot is translatable
      if (is_subclass_of($pivotClass, TranslatableContract::class)) {
        // Get the items - handle paginator, cursor paginator, and collection
        $items = [];
        if (
          $records instanceof \Illuminate\Contracts\Pagination\Paginator ||
          $records instanceof \Illuminate\Contracts\Pagination\CursorPaginator
        ) {
          $items = $records->items();
        } else {
          $items = $records->all();
        }

        // Collect all pivot IDs
        $pivotIds = collect($items)
          ->map(function ($record) {
            Assert::instanceOf($record, Model::class);
            $pivot = $record->getAttribute("pivot");
            Assert::instanceOf($pivot, Pivot::class);
            return $pivot->getAttribute("id");
          })
          ->filter()
          ->unique();

        if ($pivotIds->isNotEmpty()) {
          // Load all pivot translations in one query
          $pivotTranslations = $pivotClass
            ::with("translations")
            ->whereIn("id", $pivotIds)
            ->get()
            ->keyBy("id");

          // Replace the pivot objects with the ones that have translations loaded
          foreach ($items as $record) {
            Assert::instanceOf($record, Model::class);
            $pivot = $record->getAttribute("pivot");
            Assert::instanceOf($pivot, Pivot::class);

            $pivotId = $pivot->getAttribute("id");
            if ($pivotId !== null && isset($pivotTranslations[$pivotId])) {
              // Replace the pivot with the one that has translations loaded
              $record->setRelation("pivot", $pivotTranslations[$pivotId]);
            }
          }
        }
      }
    }

    return $records;
  }

  public function getActiveLocale(): string
  {
    $pageClass = $this->getPageClass();

    if (method_exists($pageClass, "getResource")) {
      $resourceClass = $pageClass::getResource();
      Assert::string($resourceClass);
      Assert::classString($resourceClass);
      if (method_exists($resourceClass, "getActiveLocale")) {
        $locale = $resourceClass::getActiveLocale();
        if (is_string($locale)) {
          return $locale;
        }
      }
    }

    Assert::never();
  }

  /**
   * Configure EditAction for translatable models or pivots
   */
  protected function configureEditActionForTranslatable(EditAction $action): EditAction
  {
    $relationship = $this->getRelationship();

    // Handle HasMany relationships with translatable models
    if ($relationship instanceof HasMany) {
      $relatedModel = $relationship->getRelated();
      if ($relatedModel instanceof TranslatableContract) {
        return $this->configureEditActionForTranslatableModel($action);
      }
      // HasMany relationship but model is not translatable - shouldn't happen
      Assert::never("HasMany relationship model must implement TranslatableContract");
    }

    // Handle BelongsToMany relationships with translatable pivots
    if ($relationship instanceof BelongsToMany) {
      $pivotClass = $relationship->getPivotClass();
      if (is_subclass_of($pivotClass, TranslatableContract::class)) {
        return $this->configureEditActionForTranslatablePivot($action);
      }
      // BelongsToMany relationship but pivot is not translatable - shouldn't happen
      Assert::never("BelongsToMany relationship pivot must implement TranslatableContract");
    }

    // Unsupported relationship type for translation - shouldn't happen
    Assert::never("Only HasMany and BelongsToMany relationships are supported for translation");
  }

  /**
   * Configure EditAction for HasMany relationships with translatable models
   */
  protected function configureEditActionForTranslatableModel(EditAction $action): EditAction
  {
    return $action
      ->mutateRecordDataUsing(function (array $data, Model $record): array {
        if (!($record instanceof TranslatableContract)) {
          return $data;
        }

        $locale = $this->getActiveLocale();

        // Get translatable attributes
        $translatableAttributes = [];
        if (
          property_exists($record, "translatedAttributes") &&
          is_array($record->translatedAttributes)
        ) {
          $translatableAttributes = $record->translatedAttributes;
        }

        // Load translation data for the current locale
        $translation = $record->translate($locale, false);
        if ($translation !== null) {
          foreach ($translatableAttributes as $attribute) {
            if (is_string($attribute)) {
              $data[$attribute] = $translation->getAttribute($attribute) ?? "";
            }
          }
        } else {
          // No translation exists - set empty values
          foreach ($translatableAttributes as $attribute) {
            if (is_string($attribute)) {
              $data[$attribute] = "";
            }
          }
        }

        return $data;
      })
      ->using(function (Model $record, array $data): Model {
        if (!($record instanceof TranslatableContract)) {
          Assert::stringKeyedArray($data);
          $record->update($data);
          return $record;
        }

        $locale = $this->getActiveLocale();

        // Get translatable attributes
        $translatableAttributes = [];
        if (
          property_exists($record, "translatedAttributes") &&
          is_array($record->translatedAttributes)
        ) {
          $translatableAttributes = $record->translatedAttributes;
        }

        // Separate translatable and non-translatable data
        $modelData = [];
        $translationData = [];

        foreach ($data as $key => $value) {
          if (in_array($key, $translatableAttributes, true)) {
            $translationData[$key] = $value ?? "";
          } else {
            $modelData[$key] = $value;
          }
        }

        // Update non-translatable attributes
        if (count($modelData) > 0) {
          Assert::stringKeyedArray($modelData);
          $record->fill($modelData);
          $record->save();
        }

        // Update or create translation for the current locale
        if (count($translationData) > 0) {
          // Check if translation exists
          $translation = $record->translateOrNew($locale);
          Assert::stringKeyedArray($translationData);
          $translation->fill($translationData);
          $translation->save();
        }

        return $record;
      });
  }

  /**
   * Configure EditAction for BelongsToMany relationships with translatable pivots
   */
  protected function configureEditActionForTranslatablePivot(EditAction $action): EditAction
  {
    $relationship = $this->getRelationship();
    Assert::instanceOf($relationship, BelongsToMany::class);
    $pivotClass = $relationship->getPivotClass();

    return $action
      ->mutateRecordDataUsing(function (array $data, Model $record) use (
        $pivotClass,
        $relationship,
      ): array {
        // Check if pivot data exists (when coming through BelongsToMany)
        $pivot = $record->getAttribute("pivot");
        if (!($pivot instanceof Pivot)) {
          return $data;
        }

        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Add all pivot attributes to the form data
        $pivotAttributes = $pivot->getAttributes();
        foreach ($pivotAttributes as $key => $value) {
          // Skip relationship keys - they shouldn't be in the form
          if (
            $key === $relationship->getForeignPivotKeyName() ||
            $key === $relationship->getRelatedPivotKeyName()
          ) {
            continue;
          }

          // For non-translatable attributes, add them directly
          if (!in_array($key, $translatableAttributes, true)) {
            $data[$key] = $value;
          }
        }

        // Handle translatable attributes if the pivot supports translations
        if (count($translatableAttributes) > 0 && $pivot instanceof TranslatableContract) {
          $locale = $this->getActiveLocale();
          $translation = $pivot->translate($locale, false);

          if ($translation !== null) {
            foreach ($translatableAttributes as $attribute) {
              if (is_string($attribute)) {
                $data[$attribute] = $translation->getAttribute($attribute) ?? "";
              }
            }
          } else {
            // No translation exists - set empty string values
            foreach ($translatableAttributes as $attribute) {
              if (is_string($attribute)) {
                $data[$attribute] = "";
              }
            }
          }
        }

        return $data;
      })
      ->mutateFormDataUsing(function (array $data) use ($pivotClass): array {
        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Ensure all translatable attributes exist in the data
        foreach ($translatableAttributes as $attribute) {
          if (is_string($attribute)) {
            if (!isset($data[$attribute])) {
              $data[$attribute] = "";
            }
          }
        }

        return $data;
      })
      ->using(function (Model $record, array $data) use ($pivotClass, $relationship): Model {
        // Check if pivot data exists
        $pivot = $record->getAttribute("pivot");
        if (!($pivot instanceof Pivot)) {
          return $record;
        }

        // Get translatable attributes from the pivot model
        $pivotInstance = new $pivotClass();
        $translatableAttributes = [];
        if (
          property_exists($pivotInstance, "translatedAttributes") &&
          is_array($pivotInstance->translatedAttributes)
        ) {
          $translatableAttributes = $pivotInstance->translatedAttributes;
        }

        // Separate the data into pivot and translation data
        $pivotData = [];
        $translationData = [];

        $foreignKey = $relationship->getForeignPivotKeyName();
        $relatedKey = $relationship->getRelatedPivotKeyName();

        foreach ($data as $key => $value) {
          // Skip the relationship keys
          if ($key === $foreignKey || $key === $relatedKey) {
            continue;
          }

          if (in_array($key, $translatableAttributes, true)) {
            // Store the actual value for translation
            $translationData[$key] = $value ?? "";
            // Set empty string in pivot table for translatable columns
            $pivotData[$key] = "";
          } else {
            $pivotData[$key] = $value;
          }
        }

        // Update pivot data (with null values for translatable columns)
        if (count($pivotData) > 0) {
          Assert::stringKeyedArray($pivotData);
          $pivot->update($pivotData);
        }

        // Update translatable attributes if applicable
        if (count($translatableAttributes) > 0 && $pivot instanceof TranslatableContract) {
          $locale = $this->getActiveLocale();
          $translation = $pivot->translateOrNew($locale);

          // Ensure all translatable attributes have values (default to empty string)
          foreach ($translatableAttributes as $attribute) {
            if (is_string($attribute) && !isset($translationData[$attribute])) {
              $translationData[$attribute] = "";
            }
          }

          Assert::stringKeyedArray($translationData);
          $translation->fill($translationData);
          $translation->save();
        }

        return $record;
      });
  }

  protected function configureAttachActionForTranslatablePivot(AttachAction $action): AttachAction
  {
    $relationship = $this->getRelationship();
    if (!($relationship instanceof BelongsToMany)) {
      return $action;
    }

    $pivotClass = $relationship->getPivotClass();
    if (!is_subclass_of($pivotClass, TranslatableContract::class)) {
      return $action;
    }

    $pivotInstance = new $pivotClass();
    $translatableAttributes = [];
    if (
      property_exists($pivotInstance, "translatedAttributes") &&
      is_array($pivotInstance->translatedAttributes)
    ) {
      $translatableAttributes = $pivotInstance->translatedAttributes;
    }

    if (count($translatableAttributes) === 0) {
      return $action;
    }

    // The form fields defined in the AttachAction are automatically saved to the pivot table
    // We use the after() hook to handle the translation separately
    return $action->after(function (Model $record, array $data) use (
      $relationship,
      $pivotClass,
      $translatableAttributes,
    ): void {
      $foreignPivotKey = $relationship->getForeignPivotKeyName();
      $relatedPivotKey = $relationship->getRelatedPivotKeyName();
      $ownerRecord = $this->getOwnerRecord();

      // Find the just-created pivot record
      $pivot = $pivotClass
        ::where($relatedPivotKey, $record->getKey())
        ->where($foreignPivotKey, $ownerRecord->getKey())
        ->first();

      if (!($pivot instanceof Pivot) || !($pivot instanceof TranslatableContract)) {
        return;
      }

      // Extract and save translatable data to the translations table
      $translationData = [];
      foreach ($translatableAttributes as $attribute) {
        if (is_string($attribute)) {
          // Use the original form value or empty string
          $translationData[$attribute] = $data[$attribute] ?? "";
        }
      }

      // Save the translation for the active locale
      if (count($translationData) > 0) {
        $locale = $this->getActiveLocale();
        $translation = $pivot->translateOrNew($locale);
        $translation->fill($translationData);
        $translation->save();
      }
    });
  }

  protected function getTranslatableColumnState(Model $record, string $column): mixed
  {
    $relationship = $this->getRelationship();

    // Handle BelongsToMany pivot translations
    if ($relationship instanceof BelongsToMany) {
      $pivot = $record->getAttribute("pivot");
      if ($pivot === null) {
        return $record->getAttribute($column) ?? "";
      }

      if (!($pivot instanceof Pivot)) {
        return $record->getAttribute($column) ?? "";
      }

      $pivotClass = $relationship->getPivotClass();
      if (!is_subclass_of($pivotClass, TranslatableContract::class)) {
        return $pivot->getAttribute($column);
      }

      $pivotInstance = new $pivotClass();
      $translatableAttributes = [];
      if (
        property_exists($pivotInstance, "translatedAttributes") &&
        is_array($pivotInstance->translatedAttributes)
      ) {
        $translatableAttributes = $pivotInstance->translatedAttributes;
      }

      // If this column is translatable, get it from translations
      if (
        in_array($column, $translatableAttributes, true) &&
        $pivot instanceof TranslatableContract
      ) {
        $locale = $this->getActiveLocale();
        $translation = $pivot->translate($locale, false);

        return $translation !== null ? $translation->getAttribute($column) : "";
      }

      // Otherwise, get it directly from the pivot
      return $pivot->getAttribute($column) ?? "";
    }

    // Handle regular model translations
    if ($record instanceof TranslatableContract) {
      $translatableAttributes = [];
      if (
        property_exists($record, "translatedAttributes") &&
        is_array($record->translatedAttributes)
      ) {
        $translatableAttributes = $record->translatedAttributes;
      }

      if (in_array($column, $translatableAttributes, true)) {
        $locale = $this->getActiveLocale();
        $translation = $record->translate($locale, false);

        return $translation !== null ? $translation->getAttribute($column) : "";
      }
    }

    return $record->getAttribute($column) ?? "";
  }
}
