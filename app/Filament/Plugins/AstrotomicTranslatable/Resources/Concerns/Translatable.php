<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Resources\Concerns;

use App\Helpers\Assert;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;
use RuntimeException;

trait Translatable
{
  /**
   * @return non-empty-list<string>
   *
   * @throws RuntimeException
   */
  public static function getTranslatableLocales(): array
  {
    $plugin = Filament::getCurrentPanel()?->getPlugin("app-astrotomic-translatable");

    if (
      $plugin instanceof
      \App\Filament\Plugins\AstrotomicTranslatable\FilamentAstrotomicTranslatablePlugin
    ) {
      return $plugin->getDefaultLocales();
    }

    $result = Config::array("translatable.locales");
    Assert::stringArray($result);
    Assert::nonEmptyList($result);

    return $result;
  }

  /**
   * @throws RuntimeException
   */
  public static function getActiveLocale(): string
  {
    $value = Session::get(
      "app-astrotomic-translatable.locale." . static::class,
      app()->getLocale(),
    );

    Assert::string($value);

    return $value;
  }

  /**
   * @throws RuntimeException
   */
  public static function setActiveLocale(string $locale): void
  {
    Session::put(["app-astrotomic-translatable.locale." . static::class => $locale]);
  }

  /**
   * @throws RuntimeException
   */
  public static function getDefaultLocale(): string
  {
    $locales = static::getTranslatableLocales();

    return $locales[0];
  }
}
