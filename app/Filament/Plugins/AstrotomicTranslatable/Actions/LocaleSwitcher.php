<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Actions;

use App\Helpers\Assert;
use Filament\Actions\SelectAction;
use Illuminate\Support\Facades\Config;
use RuntimeException;

final class LocaleSwitcher extends SelectAction
{
  protected function setUp(): void
  {
    parent::setUp();

    $this->label(__("admin.common.language"));

    $this->icon("heroicon-m-language");

    $this->options(function (): array {
      $locales = $this->getTranslatableLocales();
      $options = [];

      foreach ($locales as $locale) {
        $options[$locale] = mb_strtoupper($locale);
      }

      return $options;
    });
  }

  public static function getDefaultName(): string
  {
    return "activeLocale";
  }

  /**
   * @return non-empty-list<string>
   *
   * @throws RuntimeException
   */
  protected function getTranslatableLocales(): array
  {
    $livewire = $this->getLivewire();

    if (method_exists($livewire, "getResource")) {
      $resourceClass = $livewire->getResource();
      Assert::string($resourceClass);
      Assert::classString($resourceClass);

      if (method_exists($resourceClass, "getTranslatableLocales")) {
        $result = $resourceClass::getTranslatableLocales();
        Assert::array($result);
        Assert::stringArray($result);
        Assert::nonEmptyList($result);

        return $result;
      }
    }

    $result = Config::array("translatable.locales");
    Assert::stringArray($result);
    Assert::nonEmptyList($result);

    return $result;
  }
}
