<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable\Tables\Columns;

use App\Helpers\Assert;
use Closure;
use Filament\Tables\Columns\TextColumn as BaseTextColumn;
use Illuminate\Database\Eloquent\Builder;

final class TextColumn extends BaseTextColumn
{
  public function sortable(Closure|array|bool $condition = true, ?Closure $query = null): static
  {
    // If it's a translatable column and no custom query is provided
    if (
      $condition === true &&
      $query === null &&
      str_contains($this->getName(), ".translations.")
    ) {
      $query = $this->buildTranslatableSortQuery();
    }

    return parent::sortable($condition, $query);
  }

  protected function buildTranslatableSortQuery(): Closure
  {
    return function (Builder $query, string $direction) {
      $columnName = $this->getName();
      $parts = explode(".", $columnName);

      // Find where "translations" appears
      $translationIndex = array_search("translations", $parts, true);

      if ($translationIndex === false || $translationIndex === count($parts) - 1) {
        // Not a translatable column - use default sorting
        return $query->orderBy($columnName, $direction);
      }

      $field = $parts[count($parts) - 1];
      $currentModel = $query->getModel();

      // Get locale
      $table = $this->getTable();
      $livewire = $table->getLivewire();
      $locale = null;

      if (method_exists($livewire, "getActiveLocale")) {
        $locale = $livewire->getActiveLocale();
      } elseif (method_exists($livewire, "getResource")) {
        $livewireClass = get_class($livewire);
        $resourceClass = $livewireClass::getResource();

        Assert::string($resourceClass);
        Assert::classString($resourceClass);
        Assert::true(method_exists($resourceClass, "getActiveLocale"));

        $locale = $resourceClass::getActiveLocale();
      }

      Assert::notNull($locale);

      // Handle direct translations (translations.field)
      if ($translationIndex === 0) {
        Assert::true(count($parts) === 2);

        Assert::true(method_exists($currentModel, "translations"));

        $translationRelation = $currentModel->translations();
        Assert::instanceOf(
          $translationRelation,
          \Illuminate\Database\Eloquent\Relations\HasMany::class,
        );

        $translationModel = $translationRelation->getRelated();
        $translationTable = $translationModel->getTable();
        $translationForeignKey = $translationRelation->getForeignKeyName();

        return $query->orderBy(
          $currentModel
            ->newQuery()
            ->select("{$translationTable}.{$field}")
            ->from($translationTable)
            ->whereColumn(
              "{$translationTable}.{$translationForeignKey}",
              "{$query->getModel()->getTable()}.{$currentModel->getKeyName()}",
            )
            ->where("{$translationTable}.locale", $locale)
            ->limit(1),
          $direction,
        );
      }

      // Handle nested relations
      $relationPath = array_slice($parts, 0, $translationIndex);

      $previousModel = $currentModel;
      $joins = [];

      // Traverse the relation path
      foreach ($relationPath as $relationName) {
        Assert::true(method_exists($previousModel, $relationName));

        // @phpstan-ignore-next-line method.dynamicName
        $relation = $previousModel->{$relationName}();

        Assert::true(
          $relation instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo ||
            $relation instanceof \Illuminate\Database\Eloquent\Relations\HasOne ||
            $relation instanceof \Illuminate\Database\Eloquent\Relations\HasMany,
        );

        $relatedModel = $relation->getRelated();

        // Store join information
        $joins[] = [
          "relation" => $relation,
          "fromModel" => $previousModel,
          "fromTable" => $previousModel->getTable(),
          "toModel" => $relatedModel,
          "toTable" => $relatedModel->getTable(),
        ];

        $previousModel = $relatedModel;
      }

      // Now previousModel should have translations
      Assert::true(method_exists($previousModel, "translations"));

      $translationRelation = $previousModel->translations();
      Assert::instanceOf(
        $translationRelation,
        \Illuminate\Database\Eloquent\Relations\HasMany::class,
      );

      $translationModel = $translationRelation->getRelated();
      $translationTable = $translationModel->getTable();
      $translationForeignKey = $translationRelation->getForeignKeyName();
      $finalTable = $previousModel->getTable();

      // Build subquery starting from the deepest model
      $subquery = $previousModel
        ->newQuery()
        ->select("{$translationTable}.{$field}")
        ->join(
          $translationTable,
          "{$finalTable}.{$previousModel->getKeyName()}",
          "=",
          "{$translationTable}.{$translationForeignKey}",
        );

      // Add joins in reverse order
      for ($i = count($joins) - 1; $i > 0; $i--) {
        Assert::true(isset($joins[$i]));
        Assert::true(isset($joins[$i - 1]));

        $join = $joins[$i];
        $prevJoin = $joins[$i - 1];
        $relation = $join["relation"];

        if ($relation instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
          $subquery->join(
            $prevJoin["toTable"],
            "{$prevJoin["toTable"]}.{$relation->getOwnerKeyName()}",
            "=",
            "{$join["fromTable"]}.{$relation->getForeignKeyName()}",
          );
        } else {
          // HasOne or HasMany
          $subquery->join(
            $prevJoin["toTable"],
            "{$join["fromTable"]}.{$relation->getForeignKeyName()}",
            "=",
            "{$prevJoin["toTable"]}.{$relation->getLocalKeyName()}",
          );
        }
      }

      // Connect to main query table
      $firstJoin = $joins[0];
      $firstRelation = $firstJoin["relation"];
      $mainTable = $query->getModel()->getTable();

      if ($firstRelation instanceof \Illuminate\Database\Eloquent\Relations\BelongsTo) {
        $subquery->whereColumn(
          "{$firstJoin["toTable"]}.{$firstRelation->getOwnerKeyName()}",
          "{$mainTable}.{$firstRelation->getForeignKeyName()}",
        );
      } else {
        // HasOne or HasMany
        $subquery->whereColumn(
          "{$firstJoin["toTable"]}.{$firstRelation->getForeignKeyName()}",
          "{$mainTable}.{$firstRelation->getLocalKeyName()}",
        );
      }

      $subquery->where("{$translationTable}.locale", $locale)->limit(1);

      return $query->orderBy($subquery, $direction);
    };
  }
}
