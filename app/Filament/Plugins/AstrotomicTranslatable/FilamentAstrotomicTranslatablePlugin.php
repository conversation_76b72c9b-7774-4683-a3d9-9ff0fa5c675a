<?php

declare(strict_types=1);

namespace App\Filament\Plugins\AstrotomicTranslatable;

use App\Helpers\Assert;
use Filament\Contracts\Plugin;
use Filament\Panel;
use Illuminate\Support\Facades\Config;
use RuntimeException;

final class FilamentAstrotomicTranslatablePlugin implements Plugin
{
  /** @var list<string> */
  private array $defaultLocales = [];

  public static function make(): static
  {
    return new self();
  }

  public function getId(): string
  {
    return "app-astrotomic-translatable";
  }

  public function register(Panel $panel): void {}

  public function boot(Panel $panel): void {}

  /**
   * @param  non-empty-list<string>  $locales
   */
  public function defaultLocales(array $locales): static
  {
    $this->defaultLocales = $locales;

    return $this;
  }

  /**
   * @return non-empty-list<string>
   *
   * @throws RuntimeException
   */
  public function getDefaultLocales(): array
  {
    $result =
      count($this->defaultLocales) !== 0
        ? $this->defaultLocales
        : Config::array("translatable.locales");

    Assert::stringArray($result);
    Assert::nonEmptyList($result);

    return $result;
  }
}
