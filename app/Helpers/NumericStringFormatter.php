<?php

declare(strict_types=1);

namespace App\Helpers;

/**
 * Formats numeric strings with thousand and decimal separators
 * Only handles string manipulation, no mathematical operations
 */
final class NumericStringFormatter
{
  private string $thousandSeparator;
  private string $decimalSeparator;

  public function __construct(string $thousandSeparator = " ", string $decimalSeparator = ".")
  {
    $this->thousandSeparator = $thousandSeparator;
    $this->decimalSeparator = $decimalSeparator;
  }

  /**
   * Add thousand and decimal separators to a numeric string
   *
   * @param string $number A numeric string (e.g., "1234567.89" or "-0.123")
   * @return string Formatted string with separators (e.g., "1 234 567,89" or "-0,123")
   * @throws \InvalidArgumentException If input is not a valid numeric string
   */
  public function format(string $number): string
  {
    // Validate that input is a numeric string
    if (!$this->isValidNumericString($number)) {
      throw new \InvalidArgumentException("Input must be a valid numeric string, got: {$number}");
    }

    // Handle negative numbers
    $isNegative = str_starts_with($number, "-");
    if ($isNegative) {
      $number = substr($number, 1);
    }

    // Split into integer and decimal parts
    $parts = explode(".", $number);
    $integerPart = $parts[0];
    $decimalPart = $parts[1] ?? null;

    // Format integer part with thousand separators
    $formattedInteger = $this->addThousandSeparators($integerPart);

    // Reconstruct the formatted number
    $result = $formattedInteger;
    if ($decimalPart !== null) {
      $result .= $this->decimalSeparator . $decimalPart;
    }

    // Add negative sign back if needed
    if ($isNegative) {
      $result = "-" . $result;
    }

    return $result;
  }

  /**
   * Check if a string is a valid numeric string
   *
   * @param string $number The string to validate
   * @return bool True if valid numeric string
   */
  private function isValidNumericString(string $number): bool
  {
    // Pattern: optional minus, one or more digits, optional decimal point followed by digits
    return preg_match('/^-?\d+(\.\d+)?$/', $number) === 1;
  }

  /**
   * Add thousand separators to an integer string
   *
   * @param string $integerPart String containing only digits
   * @return string Formatted string with thousand separators
   */
  private function addThousandSeparators(string $integerPart): string
  {
    $length = strlen($integerPart);

    if ($length <= 3) {
      return $integerPart;
    }

    $result = "";
    $position = 0;

    // Process from left to right
    for ($i = 0; $i < $length; $i++) {
      // Calculate position from the right
      $positionFromRight = $length - $i;

      // Add separator before every 3rd digit from the right (except at the beginning)
      if ($i > 0 && $positionFromRight % 3 === 0) {
        $result .= $this->thousandSeparator;
      }

      $result .= $integerPart[$i];
    }

    return $result;
  }
}
