<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use RuntimeException;

abstract class RemoveRolesMigration extends PermissionMigration
{
  /**
   * @var array<string>
   */
  protected array $roles = [];

  protected string $guard = "web";

  abstract public function down(): void;

  /**
   * @throws RuntimeException
   */
  final public function up(): void
  {
    foreach ($this->roles as $role) {
      $this->migrator->deleteRole($role, $this->guard);
    }
  }
}
