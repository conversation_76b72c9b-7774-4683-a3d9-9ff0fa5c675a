<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use RuntimeException;

abstract class AddRolesMigration extends PermissionMigration
{
  /**
   * @var array<string>
   */
  protected array $roles = [];

  protected string $guard = "web";

  /**
   * @throws RuntimeException
   */
  final public function up(): void
  {
    foreach ($this->roles as $role) {
      $this->migrator->createRole($role, $this->guard);
    }
  }

  /**
   * @throws RuntimeException
   */
  final public function down(): void
  {
    foreach ($this->roles as $role) {
      $this->migrator->deleteRole($role, $this->guard);
    }
  }
}
