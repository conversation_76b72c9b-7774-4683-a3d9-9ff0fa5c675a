<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use PDOException;
use RuntimeException;

final class PermissionsMigrator
{
  private string $permissionsTable;

  private string $rolesTable;

  private string $roleHasPermissionsTable;

  private string $modelHasRolesTable;

  /**
   * @throws RuntimeException
   */
  public function __construct()
  {
    $this->permissionsTable = Config::string("permission.table_names.permissions");
    $this->rolesTable = Config::string("permission.table_names.roles");
    $this->roleHasPermissionsTable = Config::string("permission.table_names.role_has_permissions");
    $this->modelHasRolesTable = Config::string("permission.table_names.model_has_roles");
  }

  /**
   * @throws PDOException
   * @throws RuntimeException
   */
  public function createPermission(string $name, string $guard = "web"): int
  {
    if ($this->permissionExists($name, $guard)) {
      $id = $this->getPermissionId($name, $guard);

      return $id ?? -1;
    }

    DB::table($this->permissionsTable)->insert([
      "name" => $name,
      "guard_name" => $guard,
      "created_at" => now(),
      "updated_at" => now(),
    ]);

    $lastId = DB::getPdo()->lastInsertId();

    return $lastId !== false ? (int) $lastId : -1;
  }

  /**
   * @throws PDOException
   * @throws RuntimeException
   */
  public function createRole(string $name, string $guard = "web"): int
  {
    if ($this->roleExists($name, $guard)) {
      $id = $this->getRoleId($name, $guard);

      return $id ?? -1;
    }

    DB::table($this->rolesTable)->insert([
      "name" => $name,
      "guard_name" => $guard,
      "created_at" => now(),
      "updated_at" => now(),
    ]);

    $lastId = DB::getPdo()->lastInsertId();

    return $lastId !== false ? (int) $lastId : -1;
  }

  /**
   * @param  array<string>  $permissionNames
   *
   * @throws PDOException
   * @throws RuntimeException
   */
  public function assignPermissionsToRole(
    string $roleName,
    array $permissionNames,
    string $guard = "web",
  ): void {
    $roleId = $this->getRoleId($roleName, $guard);

    if ($roleId === null) {
      $roleId = $this->createRole($roleName, $guard);
    }

    foreach ($permissionNames as $permissionName) {
      $permissionId = $this->getPermissionId($permissionName, $guard);

      if ($permissionId === null) {
        $permissionId = $this->createPermission($permissionName, $guard);
      }

      if (!$this->roleHasPermission($roleId, $permissionId)) {
        DB::table($this->roleHasPermissionsTable)->insert([
          "permission_id" => $permissionId,
          "role_id" => $roleId,
        ]);
      }
    }
  }

  /**
   * @param  array<string>  $permissionNames
   *
   * @throws RuntimeException
   */
  public function revokePermissionsFromRole(
    string $roleName,
    array $permissionNames,
    string $guard = "web",
  ): void {
    $roleId = $this->getRoleId($roleName, $guard);

    if ($roleId === null) {
      return;
    }

    foreach ($permissionNames as $permissionName) {
      $permissionId = $this->getPermissionId($permissionName, $guard);

      if ($permissionId !== null) {
        DB::table($this->roleHasPermissionsTable)
          ->where("permission_id", $permissionId)
          ->where("role_id", $roleId)
          ->delete();
      }
    }
  }

  /**
   * @throws PDOException
   * @throws RuntimeException
   */
  public function assignRoleToUser(int $userId, string $roleName, string $guard = "web"): void
  {
    $roleId = $this->getRoleId($roleName, $guard);

    if ($roleId === null) {
      $roleId = $this->createRole($roleName, $guard);
    }

    if (!$this->userHasRole($userId, $roleId)) {
      $modelType = Config::string("auth.providers.users.model");

      DB::table($this->modelHasRolesTable)->insert([
        "role_id" => $roleId,
        "model_type" => $modelType,
        "model_id" => $userId,
      ]);
    }
  }

  /**
   * @throws RuntimeException
   */
  public function removeRoleFromUser(int $userId, string $roleName, string $guard = "web"): void
  {
    $roleId = $this->getRoleId($roleName, $guard);

    if ($roleId === null) {
      return;
    }

    $modelType = Config::string("auth.providers.users.model");

    DB::table($this->modelHasRolesTable)
      ->where("role_id", $roleId)
      ->where("model_id", $userId)
      ->where("model_type", $modelType)
      ->delete();
  }

  /**
   * @throws RuntimeException
   */
  public function deletePermission(string $name, string $guard = "web"): void
  {
    $permissionId = $this->getPermissionId($name, $guard);

    if ($permissionId !== null) {
      // First remove from role_has_permissions
      DB::table($this->roleHasPermissionsTable)->where("permission_id", $permissionId)->delete();

      // Then delete the permission
      DB::table($this->permissionsTable)->where("id", $permissionId)->delete();
    }
  }

  /**
   * @throws RuntimeException
   */
  public function deleteRole(string $name, string $guard = "web"): void
  {
    $roleId = $this->getRoleId($name, $guard);

    if ($roleId !== null) {
      // Remove from role_has_permissions
      DB::table($this->roleHasPermissionsTable)->where("role_id", $roleId)->delete();

      // Remove from model_has_roles
      DB::table($this->modelHasRolesTable)->where("role_id", $roleId)->delete();

      // Delete the role
      DB::table($this->rolesTable)->where("id", $roleId)->delete();
    }
  }

  /**
   * @throws RuntimeException
   */
  public function permissionExists(string $name, string $guard = "web"): bool
  {
    return DB::table($this->permissionsTable)
      ->where("name", $name)
      ->where("guard_name", $guard)
      ->exists();
  }

  /**
   * @throws RuntimeException
   */
  public function roleExists(string $name, string $guard = "web"): bool
  {
    return DB::table($this->rolesTable)
      ->where("name", $name)
      ->where("guard_name", $guard)
      ->exists();
  }

  /**
   * @throws RuntimeException
   */
  private function getPermissionId(string $name, string $guard = "web"): ?int
  {
    $id = DB::table($this->permissionsTable)
      ->where("name", $name)
      ->where("guard_name", $guard)
      ->value("id");

    if ($id === null) {
      return null;
    }

    return is_numeric($id) ? (int) $id : null;
  }

  /**
   * @throws RuntimeException
   */
  private function getRoleId(string $name, string $guard = "web"): ?int
  {
    $id = DB::table($this->rolesTable)
      ->where("name", $name)
      ->where("guard_name", $guard)
      ->value("id");

    if ($id === null) {
      return null;
    }

    return is_numeric($id) ? (int) $id : null;
  }

  /**
   * @throws RuntimeException
   */
  private function roleHasPermission(int $roleId, int $permissionId): bool
  {
    return DB::table($this->roleHasPermissionsTable)
      ->where("permission_id", $permissionId)
      ->where("role_id", $roleId)
      ->exists();
  }

  /**
   * @throws RuntimeException
   */
  private function userHasRole(int $userId, int $roleId): bool
  {
    $modelType = Config::string("auth.providers.users.model");

    return DB::table($this->modelHasRolesTable)
      ->where("role_id", $roleId)
      ->where("model_id", $userId)
      ->where("model_type", $modelType)
      ->exists();
  }
}
