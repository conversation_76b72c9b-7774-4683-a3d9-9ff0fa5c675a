<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use RuntimeException;

abstract class AddPermissionsMigration extends PermissionMigration
{
  /**
   * @var array<string>
   */
  protected array $permissions = [];

  protected string $guard = "web";

  /**
   * @throws RuntimeException
   */
  final public function up(): void
  {
    foreach ($this->permissions as $permission) {
      $this->migrator->createPermission($permission, $this->guard);
    }
  }

  /**
   * @throws RuntimeException
   */
  final public function down(): void
  {
    foreach ($this->permissions as $permission) {
      $this->migrator->deletePermission($permission, $this->guard);
    }
  }
}
