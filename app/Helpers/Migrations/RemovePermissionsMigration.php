<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use RuntimeException;

abstract class RemovePermissionsMigration extends PermissionMigration
{
  /**
   * @var array<string>
   */
  protected array $permissions = [];

  protected string $guard = "web";

  abstract public function down(): void;

  /**
   * @throws RuntimeException
   */
  final public function up(): void
  {
    foreach ($this->permissions as $permission) {
      $this->migrator->deletePermission($permission, $this->guard);
    }
  }
}
