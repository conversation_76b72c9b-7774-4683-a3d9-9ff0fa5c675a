<?php

declare(strict_types=1);

namespace App\Helpers\Migrations;

use RuntimeException;

abstract class RevokePermissionsMigration extends PermissionMigration
{
  /**
   * @var array<string, array<string>>
   */
  protected array $rolePermissions = [];

  protected string $guard = "web";

  /**
   * @throws RuntimeException
   */
  final public function up(): void
  {
    foreach ($this->rolePermissions as $role => $permissions) {
      $this->migrator->revokePermissionsFromRole($role, $permissions, $this->guard);
    }
  }

  /**
   * @throws RuntimeException
   */
  final public function down(): void
  {
    foreach ($this->rolePermissions as $role => $permissions) {
      $this->migrator->assignPermissionsToRole($role, $permissions, $this->guard);
    }
  }
}
