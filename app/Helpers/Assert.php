<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Exceptions\AssertionException;
use Stringable;

final class Assert
{
  /**
   * @throws AssertionException
   */
  public static function never(string $message = "This code should never be reached"): never
  {
    throw new AssertionException($message);
  }

  /**
   * @template T of object
   *
   * @phpstan-assert T $value
   *
   * @param  class-string<T>  $class
   *
   * @throws AssertionException
   */
  public static function instanceOf(mixed $value, string $class, ?string $message = null): void
  {
    if (!$value instanceof $class) {
      self::fail(
        sprintf(
          "Value must be an instance of %s, got %s",
          $class,
          is_object($value) ? get_class($value) : gettype($value),
        ),
        $message,
      );
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert array<string, T> $array
   *
   * @param  array<mixed, T>  $array
   *
   * @throws AssertionException
   */
  public static function stringKeyedArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $value) {
      if (!is_string($key)) {
        self::fail("Array must have string keys only", $message);
      }
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert array<T, string> $array
   *
   * @param  array<T, mixed>  $array
   *
   * @throws AssertionException
   */
  public static function stringArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $value) {
      if (!is_string($value)) {
        self::fail("Array must have string values only", $message);
      }
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert array<int, T> $array
   *
   * @param  array<mixed, T>  $array
   *
   * @throws AssertionException
   */
  public static function intKeyedArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $value) {
      if (!is_int($key)) {
        self::fail("Array must have int keys only", $message);
      }
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert array<T, int> $array
   *
   * @param  array<T, mixed>  $array
   *
   * @throws AssertionException
   */
  public static function intArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $value) {
      if (!is_int($value)) {
        self::fail("Array must have int values only", $message);
      }
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert array<T, array<mixed, mixed>> $array
   *
   * @param  array<T, mixed>  $array
   *
   * @throws AssertionException
   */
  public static function arrayArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $value) {
      if (!is_array($value)) {
        self::fail("Array must have array values only", $message);
      }
    }
  }

  /**
   * @template T
   * @template Y
   *
   * @phpstan-assert array<T, array<string, mixed>> $array
   *
   * @param  array<T, array<mixed, Y>>  $array
   *
   * @throws AssertionException
   */
  public static function stringArrayArray(array $array, ?string $message = null): void
  {
    foreach ($array as $key => $nested) {
      foreach ($nested as $nestedKey => $nestedValue) {
        if (!is_string($nestedKey)) {
          self::fail("Nested array must have string keys only", $message);
        }
      }
    }
  }

  /**
   * @template T
   * @template Y
   *
   * @phpstan-assert list<Y> $array
   *
   * @param  array<T, Y>  $array
   *
   * @throws AssertionException
   */
  public static function list(array $array, ?string $message = null): void
  {
    if (!array_is_list($array)) {
      self::fail("Array must be a list", $message);
    }
  }

  /**
   * @template T
   * @template Y
   *
   * @phpstan-assert non-empty-list<Y> $array
   *
   * @param  array<T, Y>  $array
   *
   * @throws AssertionException
   */
  public static function nonEmptyList(array $array, ?string $message = null): void
  {
    if (!array_is_list($array) || count($array) === 0) {
      self::fail("Array must be a list and not empty", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert T $value
   *
   * @param  T|null  $value
   *
   * @throws AssertionException
   *
   * @phpstan-ignore-next-line float.type
   */
  public static function notNull(mixed $value, ?string $message = null): void
  {
    if ($value === null) {
      self::fail("Value must not be null", $message);
    }
  }

  /**
   * @phpstan-assert array<mixed> $value
   *
   * @throws AssertionException
   */
  public static function array(mixed $value, ?string $message = null): void
  {
    if (!is_array($value)) {
      self::fail("Value must be an array", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert string $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function string(mixed $value, ?string $message = null): void
  {
    if (!is_string($value)) {
      self::fail("Value must be string", $message);
    }
  }

  /**
   * @phpstan-assert non-empty-string $value
   *
   * @throws AssertionException
   */
  public static function nonEmptyString(string $value, ?string $message = null): void
  {
    if ($value === "") {
      self::fail("String must be non-empty", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert true $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function true(mixed $value, ?string $message = null): void
  {
    if (!is_bool($value) || $value === false) {
      self::fail("Value must be true", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert bool $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function bool(mixed $value, ?string $message = null): void
  {
    if (!is_bool($value)) {
      self::fail("Value must be bool", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert numeric-string $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function numeric(mixed $value, ?string $message = null): void
  {
    if (!is_numeric($value)) {
      self::fail("Value must be numeric", $message);
    }
  }

  /**
   * @phpstan-assert class-string $string
   *
   * @throws AssertionException
   */
  public static function classString(string $string, ?string $message = null): void
  {
    if (!class_exists($string)) {
      self::fail("String must be a class-string", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert int $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function int(mixed $value, ?string $message = null): void
  {
    if (!is_int($value)) {
      self::fail("Value must be int", $message);
    }
  }

  /**
   * @template T
   *
   * @phpstan-assert scalar|null|\Stringable $value
   *
   * @param  T  $value
   *
   * @throws AssertionException
   */
  public static function stringable(mixed $value, ?string $message = null): void
  {
    if (!is_scalar($value) && !is_null($value) && !$value instanceof Stringable) {
      self::fail("Value must be stringable", $message);
    }
  }

  /**
   * @throws AssertionException
   */
  private static function fail(string $defaultMessage, ?string $customMessage = null): never
  {
    throw new AssertionException($customMessage ?? $defaultMessage);
  }
}
