<?php

declare(strict_types=1);

namespace App\Helpers;

use Dom\HTMLDocument;

final class RichTextProcessor
{
  /**
   * Process HTML content to ensure all images have alt attributes
   *
   * @param string $html The HTML content to process
   * @return string The processed HTML with alt attributes added
   */
  public static function ensureImageAlts(string $html): string
  {
    if ($html === "") {
      return "";
    }

    try {
      [$dom, $body] = self::createDocument($html);

      // Add empty alt to all images without alt attribute
      foreach ($dom->getElementsByTagName("img") as $img) {
        if (!$img->hasAttribute("alt")) {
          $img->setAttribute("alt", "");
        }
      }

      return $body->innerHTML;
    } catch (\Throwable $e) {
      report($e);
      return $html;
    }
  }

  /**
   * Create a DOM document with the HTML content loaded into the body
   *
   * @param string $html The HTML content to load
   * @return array{HTMLDocument, \Dom\Element} The document and body element
   * @throws \Exception If document creation fails
   */
  private static function createDocument(string $html): array
  {
    $dom = HTMLDocument::createEmpty();

    $body = $dom->body;
    if ($body === null) {
      $html_element = $dom->documentElement;
      if ($html_element === null) {
        $html_element = $dom->createElement("html");
        $dom->appendChild($html_element);
      }
      $body = $dom->createElement("body");
      $html_element->appendChild($body);
    }

    $body->innerHTML = $html;

    return [$dom, $body];
  }
}
