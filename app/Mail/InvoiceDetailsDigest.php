<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

final class InvoiceDetailsDigest extends Mailable
{
  use Queueable, SerializesModels;

  /**
   * @param  Collection<int, \App\Models\Company>  $companies
   */
  public function __construct(public readonly Collection $companies) {}

  public function envelope(): Envelope
  {
    return new Envelope(
      subject: __(
        "E-laskutustiedot päivitetty - :count yritys|E-laskutustiedot päivitetty - :count yritystä",
        ["count" => $this->companies->count()],
      ),
    );
  }

  public function content(): Content
  {
    return new Content(
      markdown: "emails.invoice-details-digest",
      with: [
        "companies" => $this->companies,
        "updateTime" => now(),
      ],
    );
  }
}
