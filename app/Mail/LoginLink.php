<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

final class LoginLink extends Mailable
{
  use Queueable, SerializesModels;

  /**
   * The magic link URL.
   */
  public string $magicLink;

  /**
   * Token expiration in minutes.
   */
  public int $expirationMinutes;

  /**
   * Create a new message instance.
   */
  public function __construct(string $magicLink, int $expirationMinutes)
  {
    $this->magicLink = $magicLink;
    $this->expirationMinutes = $expirationMinutes;
  }

  /**
   * Get the message envelope.
   */
  public function envelope(): Envelope
  {
    return new Envelope(subject: __("Kirjautumislinkkisi"));
  }

  /**
   * Get the message content definition.
   */
  public function content(): Content
  {
    return new Content(
      markdown: "emails.login-link",
      with: [
        "magicLink" => $this->magicLink,
        "expirationTime" => $this->formatExpirationTime(),
      ],
    );
  }

  /**
   * Format the expiration time for display.
   */
  private function formatExpirationTime(): string
  {
    if ($this->expirationMinutes < 60) {
      return trans_choice(":count minuutin", $this->expirationMinutes, [
        "count" => $this->expirationMinutes,
      ]);
    }

    $hours = intdiv($this->expirationMinutes, 60);
    $minutes = $this->expirationMinutes % 60;

    if ($minutes === 0) {
      return trans_choice(":count tunnin", $hours, ["count" => $hours]);
    }

    $hourPart = trans_choice(":count tunnin", $hours, ["count" => $hours]);
    $minutePart = trans_choice(":count minuutin", $minutes, ["count" => $minutes]);

    return __(":hours_text ja :minutes_text", [
      "hours_text" => $hourPart,
      "minutes_text" => $minutePart,
    ]);
  }
}
