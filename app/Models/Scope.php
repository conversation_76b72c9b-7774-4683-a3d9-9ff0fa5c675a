<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Scope extends Model
{
  /** @use HasFactory<\Database\Factories\ScopeFactory> */
  use HasFactory;

  /**
   * @var list<string>
   */
  protected $fillable = ["number", "allow_grouping_hiding"];

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class);
  }

  /**
   * @return HasMany<ScopeCalculationVariant, $this>
   */
  public function calculationVariants(): Has<PERSON><PERSON>
  {
    return $this->hasMany(ScopeCalculationVariant::class);
  }

  /**
   * @return array{'number': 'integer', 'allow_grouping_hiding': 'boolean'}
   */
  public function casts(): array
  {
    return [
      "number" => "integer",
      "allow_grouping_hiding" => "boolean",
    ];
  }
}
