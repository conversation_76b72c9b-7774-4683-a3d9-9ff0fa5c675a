<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[AllowDynamicProperties]
final class GroupingState extends Model
{
  /**
   * @var list<string>
   */
  protected $fillable = [
    "company_id",
    "year_id",
    "scope_id",
    "grouping_id",
    "state",
    "select_option_id",
    "custom_reason",
  ];

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<Year, $this>
   */
  public function year(): BelongsTo
  {
    return $this->belongsTo(Year::class);
  }

  /**
   * @return BelongsTo<Scope, $this>
   */
  public function scope(): BelongsTo
  {
    return $this->belongsTo(Scope::class);
  }

  /**
   * @return BelongsTo<Grouping, $this>
   */
  public function grouping(): BelongsTo
  {
    return $this->belongsTo(Grouping::class);
  }

  /**
   * @return BelongsTo<SelectOption, $this>
   */
  public function selectOption(): BelongsTo
  {
    return $this->belongsTo(SelectOption::class);
  }

  /**
   * @return array{'consent_for_data_examination': 'boolean', 'applying_for_mark': 'boolean', 'fiscal_start_month': 'integer', 'fiscal_start_day': 'integer', 'fiscal_end_month': 'integer', 'fiscal_end_day': 'integer'}
   */
  protected function casts(): array
  {
    return [
      "consent_for_data_examination" => "boolean",
      "applying_for_mark" => "boolean",
      "fiscal_start_month" => "integer",
      "fiscal_start_day" => "integer",
      "fiscal_end_month" => "integer",
      "fiscal_end_day" => "integer",
    ];
  }
}
