<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Auditable as AuditableContract;
use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class DataValue extends Model implements AuditableContract
{
  /** @use HasFactory<\Database\Factories\DataValueFactory> */
  use Auditable, HasFactory, SoftDeletes;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["data_definition_id", "company_id", "value", "year_id", "source"];

  public function getAuditableFields(): array
  {
    return ["value", "source"];
  }

  /**
   * @return BelongsTo<CalculationDefinition, $this>
   */
  public function calculationDefinition(): BelongsTo
  {
    return $this->belongsTo(CalculationDefinition::class, "data_definition_id");
  }

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<Year, $this>
   */
  public function year(): BelongsTo
  {
    return $this->belongsTo(Year::class);
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'value': 'string'}
   */
  protected function casts(): array
  {
    return [
      "value" => "string",
    ];
  }
}
