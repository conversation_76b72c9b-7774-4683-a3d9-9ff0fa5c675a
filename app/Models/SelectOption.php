<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<SelectOptionTranslation>
 */
final class SelectOption extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\SelectOptionFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<SelectOptionTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["label"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = SelectOptionTranslation::class;

  protected string $translationForeignKey = "select_option_id";

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["type", "sort_order"];
}
