<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

final class CompanyYear extends Pivot
{
  /**
   * Indicates if the IDs are auto-incrementing.
   *
   * @var bool
   */
  public $incrementing = true;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    "company_id",
    "year_id",
    "participates_in_climate_program",
    "participates_in_climate_community",
    "awarded_scope1_2_mark",
    "awarded_scope1_3_mark",
  ];

  /**
   * @return array{'participates_in_climate_program': 'boolean', 'participates_in_climate_community': 'boolean', 'awarded_scope1_2_mark': 'boolean', 'awarded_scope1_3_mark': 'boolean'}
   */
  public function casts(): array
  {
    return [
      "participates_in_climate_program" => "boolean",
      "participates_in_climate_community" => "boolean",
      "awarded_scope1_2_mark" => "boolean",
      "awarded_scope1_3_mark" => "boolean",
    ];
  }
}
