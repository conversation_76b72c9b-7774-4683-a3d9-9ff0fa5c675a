<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class InvoiceNotificationLog extends Model
{
  /** @use HasFactory<\Database\Factories\InvoiceNotificationLogFactory> */
  use HasFactory;

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return array{'pending_marked_at': 'datetime', 'last_emailed_at': 'datetime'}
   */
  protected function casts(): array
  {
    return [
      "pending_marked_at" => "datetime",
      "last_emailed_at" => "datetime",
    ];
  }
}
