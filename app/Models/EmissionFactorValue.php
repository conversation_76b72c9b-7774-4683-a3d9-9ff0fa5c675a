<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Auditable as AuditableContract;
use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class EmissionFactorValue extends Model implements AuditableContract
{
  /** @use HasFactory<\Database\Factories\EmissionFactorValueFactory> */
  use Auditable, HasFactory, SoftDeletes;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    "emission_factor_definition_id",
    "value",
    "company_id",
    "year_id",
    "calculation_definition_option_id",
    "source",
  ];

  public function getAuditableFields(): array
  {
    return ["value", "source"];
  }

  /**
   * Get the company that owns this emission factor value.
   *
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<CalculationDefinition, $this>
   */
  public function calculationDefinition(): BelongsTo
  {
    return $this->belongsTo(CalculationDefinition::class, "emission_factor_definition_id");
  }

  /**
   * @return BelongsTo<Year, $this>
   */
  public function year(): BelongsTo
  {
    return $this->belongsTo(Year::class);
  }

  /**
   * @return BelongsTo<CalculationDefinitionOption, $this>
   */
  public function selectedOption(): BelongsTo
  {
    return $this->belongsTo(CalculationDefinitionOption::class, "calculation_definition_option_id");
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'value': 'string'}
   */
  public function casts(): array
  {
    return [
      "value" => "string",
    ];
  }
}
