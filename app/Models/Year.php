<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

#[AllowDynamicProperties]
final class Year extends Model
{
  /** @use HasFactory<\Database\Factories\YearFactory> */
  use HasFactory, SoftDeletes;

  /**
   * @var list<string>
   */
  protected $fillable = ["year", "published"];

  /**
   * @return BelongsToMany<CalculationDefinition, $this, CalculationDefinitionYear>
   */
  public function calculationDefinitions(): BelongsToMany
  {
    return $this->belongsToMany(CalculationDefinition::class)
      ->using(CalculationDefinitionYear::class)
      ->as("pivot")
      ->withPivot("emission_factor_default_value", "emission_factor_default_source")
      ->withTimestamps();
  }

  /**
   * @return BelongsToMany<Company, $this, CompanyYear>
   */
  public function companies(): BelongsToMany
  {
    return $this->belongsToMany(Company::class)
      ->using(CompanyYear::class)
      ->withPivot(["participates_in_climate_program", "participates_in_climate_community"])
      ->withTimestamps();
  }

  /**
   * @return array{'published': 'datetime'}
   */
  public function casts(): array
  {
    return [
      "published" => "datetime",
    ];
  }
}
