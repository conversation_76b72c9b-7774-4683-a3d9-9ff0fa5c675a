<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<YearTranslation>
 */
#[AllowDynamicProperties]
final class Year extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\YearFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<YearTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = [
    "scope1_2_mark_image",
    "scope1_3_mark_image",
    "scope1_2_criteria_procedures_pdf",
    "scope1_3_criteria_procedures_pdf",
  ];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = YearTranslation::class;

  protected string $translationForeignKey = "year_id";

  /**
   * @var list<string>
   */
  protected $fillable = ["year", "published"];

  /**
   * @return BelongsToMany<CalculationDefinition, $this, CalculationDefinitionYear>
   */
  public function calculationDefinitions(): BelongsToMany
  {
    return $this->belongsToMany(CalculationDefinition::class)
      ->using(CalculationDefinitionYear::class)
      ->as("pivot")
      ->withPivot(["id", "emission_factor_default_value", "emission_factor_default_source"])
      ->withTimestamps();
  }

  /**
   * @return BelongsToMany<Company, $this, CompanyYear>
   */
  public function companies(): BelongsToMany
  {
    return $this->belongsToMany(Company::class)
      ->using(CompanyYear::class)
      ->withPivot([
        "id",
        "participates_in_climate_program",
        "participates_in_climate_community",
        "awarded_scope1_2_mark",
        "awarded_scope1_3_mark",
      ])
      ->withTimestamps();
  }

  /**
   * @return BelongsToMany<MetricDefinition, $this, MetricDefinitionYear>
   */
  public function metricDefinitions(): BelongsToMany
  {
    return $this->belongsToMany(MetricDefinition::class)
      ->using(MetricDefinitionYear::class)
      ->as("pivot")
      ->withTimestamps();
  }

  /**
   * @return array{'published': 'datetime'}
   */
  public function casts(): array
  {
    return [
      "published" => "datetime",
    ];
  }
}
