<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<UnitTranslation>
 */
final class Unit extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\UnitFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<UnitTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["name", "symbol"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = UnitTranslation::class;

  protected string $translationForeignKey = "unit_id";

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["company_id"];

  /**
   * Get the company that owns this unit, if any.
   *
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return HasMany<CompoundUnit, $this>
   */
  public function asNumerator(): HasMany
  {
    return $this->hasMany(CompoundUnit::class, "numerator_unit_id");
  }

  /**
   * @return HasMany<CompoundUnit, $this>
   */
  public function asDenominator(): HasMany
  {
    return $this->hasMany(CompoundUnit::class, "denominator_unit_id");
  }

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function dataDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class, "data_unit_id");
  }

  /**
   * @return HasMany<UnitConversion, $this>
   */
  public function sourceConversions(): HasMany
  {
    return $this->hasMany(UnitConversion::class, "from_unit_id");
  }

  /**
   * @return HasMany<UnitConversion, $this>
   */
  public function targetConversions(): HasMany
  {
    return $this->hasMany(UnitConversion::class, "to_unit_id");
  }
}
