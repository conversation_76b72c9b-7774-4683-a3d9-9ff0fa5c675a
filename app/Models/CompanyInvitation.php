<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class CompanyInvitation extends Model
{
  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    "company_id",
    "inviter_user_id",
    "invitee_email",
    "selector",
    "token",
    "status",
    "decided_at",
    "expires_at",
  ];

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<User, $this>
   */
  public function inviter(): BelongsTo
  {
    return $this->belongsTo(User::class, "inviter_user_id");
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'decided_at': 'datetime', 'expires_at': 'datetime'}
   */
  protected function casts(): array
  {
    return [
      "decided_at" => "datetime",
      "expires_at" => "datetime",
    ];
  }
}
