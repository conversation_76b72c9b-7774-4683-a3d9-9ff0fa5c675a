<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @implements TranslatableContract<CalculationDefinitionOptionTranslation>
 */
final class CalculationDefinitionOption extends Model implements TranslatableContract
{
  /** @use Translatable<CalculationDefinitionOptionTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["label"];

  protected string $localeKey = "locale";

  protected string $translationForeignKey = "calculation_definition_option_id";

  /**
   * @var class-string
   */
  protected string $translationModel = CalculationDefinitionOptionTranslation::class;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["calculation_definition_id", "value", "sort_order"];

  /**
   * @return BelongsTo<CalculationDefinition, $this>
   */
  public function calculationDefinition(): BelongsTo
  {
    return $this->belongsTo(CalculationDefinition::class);
  }

  /**
   * @return HasMany<CalculationDefinitionOptionYearValue, $this>
   */
  public function yearValues(): HasMany
  {
    return $this->hasMany(CalculationDefinitionOptionYearValue::class);
  }

  /**
   * @return BelongsTo<OptionSet, $this>
   */
  public function optionSet(): BelongsTo
  {
    return $this->belongsTo(OptionSet::class);
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'value': 'string'}
   */
  public function casts(): array
  {
    return [
      "value" => "string",
    ];
  }
}
