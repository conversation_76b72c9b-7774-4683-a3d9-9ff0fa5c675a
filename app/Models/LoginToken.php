<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class LoginToken extends Model
{
  /** @use HasFactory<\Database\Factories\LoginTokenFactory> */
  use HasFactory;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["user_id", "selector", "token", "expires_at"];

  /**
   * @return BelongsTo<User, $this>
   */
  public function user(): BelongsTo
  {
    return $this->belongsTo(User::class);
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'expires_at': 'datetime'}
   */
  protected function casts(): array
  {
    return [
      "expires_at" => "datetime",
    ];
  }
}
