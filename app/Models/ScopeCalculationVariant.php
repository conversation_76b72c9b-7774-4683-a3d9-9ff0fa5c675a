<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @implements TranslatableContract<ScopeCalculationVariantTranslation>
 */
final class ScopeCalculationVariant extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\ScopeCalculationVariantFactory> */
  use HasFactory;

  /** @use Translatable<ScopeCalculationVariantTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["label"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = ScopeCalculationVariantTranslation::class;

  protected string $translationForeignKey = "scope_calculation_variant_id";

  /**
   * @var list<string>
   */
  protected $fillable = ["scope_id", "predicate", "display_order", "include_in_total"];

  /**
   * @return BelongsTo<Scope, $this>
   */
  public function scope(): BelongsTo
  {
    return $this->belongsTo(Scope::class);
  }

  /**
   * @return array{
   *     scope_id: 'integer',
   *     display_order: 'integer',
   *     include_in_total: 'bool'
   * }
   */
  public function casts(): array
  {
    return [
      "scope_id" => "integer",
      "display_order" => "integer",
      "include_in_total" => "bool",
    ];
  }
}
