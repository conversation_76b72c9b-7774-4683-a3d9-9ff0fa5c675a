<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @implements TranslatableContract<CalculationDefinitionYearTranslation>
 */
final class CalculationDefinitionYear extends Pivot implements TranslatableContract
{
  /** @use Translatable<CalculationDefinitionYearTranslation> */
  use Translatable;

  public $incrementing = true;

  public $timestamps = true;

  /**
   * @var list<string>
   */
  public $translatedAttributes = [
    "data_help_text",
    "emission_factor_help_text",
    "result_help_text",
  ];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = CalculationDefinitionYearTranslation::class;

  protected string $translationForeignKey = "calculation_definition_year_id";

  /**
   * @return array{'emission_factor_default_value': 'string'}
   */
  public function casts(): array
  {
    return [
      "emission_factor_default_value" => "string",
    ];
  }
}
