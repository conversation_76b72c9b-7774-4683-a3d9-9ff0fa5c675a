<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class UnitConversion extends Model
{
  /** @use HasFactory<\Database\Factories\UnitConversionFactory> */
  use HasFactory;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["from_unit_id", "to_unit_id", "conversion_factor"];

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function fromUnit(): BelongsTo
  {
    return $this->belongsTo(Unit::class, "from_unit_id");
  }

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function toUnit(): BelongsTo
  {
    return $this->belongsTo(Unit::class, "to_unit_id");
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'conversion_factor': 'string'}
   */
  protected function casts(): array
  {
    return [
      "conversion_factor" => "string",
    ];
  }
}
