<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class CompoundUnit extends Model
{
  /** @use HasFactory<\Database\Factories\CompoundUnitFactory> */
  use HasFactory, SoftDeletes;

  /**
   * @var list<string>
   */
  protected $fillable = ["numerator_unit_id", "denominator_unit_id", "company_id"];

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function numeratorUnit(): BelongsTo
  {
    return $this->belongsTo(Unit::class, "numerator_unit_id");
  }

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function denominatorUnit(): BelongsTo
  {
    return $this->belongsTo(Unit::class, "denominator_unit_id");
  }

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class, "emission_factor_compound_unit_id");
  }
}
