<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<GroupingTranslation>
 */
final class Grouping extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\GroupingFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<GroupingTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["title"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = GroupingTranslation::class;

  protected string $translationForeignKey = "grouping_id";

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["sort_order"];

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class);
  }
}
