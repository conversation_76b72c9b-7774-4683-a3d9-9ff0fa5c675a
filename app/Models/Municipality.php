<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<MunicipalityTranslation>
 */
final class Municipality extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\MunicipalityFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<MunicipalityTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["name"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = MunicipalityTranslation::class;

  protected string $translationForeignKey = "municipality_id";

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["code", "local_id"];
}
