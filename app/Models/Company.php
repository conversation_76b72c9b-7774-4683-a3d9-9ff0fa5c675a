<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use App\Interfaces\Auditable as AuditableContract;
use App\Traits\Auditable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

// https://github.com/larastan/larastan/issues/2256#issuecomment-2783542939

#[AllowDynamicProperties]
final class Company extends Model implements AuditableContract
{
  /** @use HasFactory<\Database\Factories\CompanyFactory> */
  use Auditable, HasFactory, SoftDeletes;

  /**
   * @var list<string>
   */
  protected $fillable = [
    "name",
    "business_id",
    "fiscal_start_month",
    "fiscal_start_day",
    "fiscal_end_month",
    "fiscal_end_day",
    "consent_for_data_examination",
    "applying_for_scope1_2_mark",
    "applying_for_scope3_mark",
    "industry_classification_id",
    "municipality_id",
    "revenue_range_id",
    "employee_count_range_id",
    "e_invoice_address",
    "e_invoice_operator",
    "e_invoice_reference",
    "e_invoice_contact_name",
    "e_invoice_contact_email",
    "e_invoice_additional_info",
    "terms_of_service_accepted_at",
  ];

  public function getAuditableFields(): array
  {
    return [
      "name",
      "business_id",
      "fiscal_start_month",
      "fiscal_start_day",
      "fiscal_end_month",
      "fiscal_end_day",
      "consent_for_data_examination",
      "applying_for_scope1_2_mark",
      "applying_for_scope3_mark",
      "industry_classification_id",
      "municipality_id",
      "revenue_range_id",
      "employee_count_range_id",
      "e_invoice_address",
      "e_invoice_operator",
      "e_invoice_reference",
      "e_invoice_contact_name",
      "e_invoice_contact_email",
      "e_invoice_additional_info",
      "terms_of_service_accepted_at",
    ];
  }

  /**
   * @return BelongsToMany<User, $this, CompanyUser>
   */
  public function users(): BelongsToMany
  {
    return $this->belongsToMany(User::class)
      ->using(CompanyUser::class)
      ->as("pivot")
      ->withPivot("is_primary")
      ->withTimestamps();
  }

  /**
   * @return BelongsToMany<Year, $this, CompanyYear>
   */
  public function years(): BelongsToMany
  {
    return $this->belongsToMany(Year::class)
      ->using(CompanyYear::class)
      ->withPivot(["participates_in_climate_program", "participates_in_climate_community"])
      ->withTimestamps();
  }

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class);
  }

  /**
   * @return HasMany<DataValue, $this>
   */
  public function dataValues(): HasMany
  {
    return $this->hasMany(DataValue::class);
  }

  /**
   * @return HasMany<EmissionFactorValue, $this>
   */
  public function emissionFactorValues(): HasMany
  {
    return $this->hasMany(EmissionFactorValue::class);
  }

  /**
   * @return BelongsTo<Municipality, $this>
   */
  public function municipality(): BelongsTo
  {
    return $this->belongsTo(Municipality::class);
  }

  /**
   * @return BelongsTo<IndustryClassification, $this>
   */
  public function industryClassification(): BelongsTo
  {
    return $this->belongsTo(IndustryClassification::class);
  }

  /**
   * @return BelongsTo<SelectOption, $this>
   */
  public function revenueRange(): BelongsTo
  {
    return $this->belongsTo(SelectOption::class, "revenue_range_id");
  }

  /**
   * @return BelongsTo<SelectOption, $this>
   */
  public function employeeCountRange(): BelongsTo
  {
    return $this->belongsTo(SelectOption::class, "employee_count_range_id");
  }

  /**
   * @return array{'consent_for_data_examination': 'boolean', 'applying_for_scope1_2_mark': 'boolean', 'applying_for_scope3_mark': 'boolean', 'fiscal_start_month': 'integer', 'fiscal_start_day': 'integer', 'fiscal_end_month': 'integer', 'fiscal_end_day': 'integer', 'terms_of_service_accepted_at': 'datetime'}
   */
  public function casts(): array
  {
    return [
      "consent_for_data_examination" => "boolean",
      "applying_for_scope1_2_mark" => "boolean",
      "applying_for_scope3_mark" => "boolean",
      "fiscal_start_month" => "integer",
      "fiscal_start_day" => "integer",
      "fiscal_end_month" => "integer",
      "fiscal_end_day" => "integer",
      "terms_of_service_accepted_at" => "datetime",
    ];
  }
}
