<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class CalculationDefinitionOptionYearValue extends Model
{
  protected $fillable = ["calculation_definition_option_id", "year_id", "value"];

  /**
   * @return BelongsTo<CalculationDefinitionOption, $this>
   */
  public function option(): BelongsTo
  {
    return $this->belongsTo(CalculationDefinitionOption::class, "calculation_definition_option_id");
  }

  /**
   * @return BelongsTo<Year, $this>
   */
  public function year(): BelongsTo
  {
    return $this->belongsTo(Year::class);
  }

  /**
   * @return array{'value': 'string'}
   */
  public function casts(): array
  {
    return [
      "value" => "string",
    ];
  }
}
