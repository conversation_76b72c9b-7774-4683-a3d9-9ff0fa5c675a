<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<MetricDefinitionTranslation>
 */
#[AllowDynamicProperties]
final class MetricDefinition extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\MetricDefinitionFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<MetricDefinitionTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["name"];

  protected string $localeKey = "locale";

  protected string $translationForeignKey = "metric_definition_id";

  /**
   * @var class-string
   */
  protected string $translationModel = MetricDefinitionTranslation::class;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["unit_id"];

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function unit(): BelongsTo
  {
    return $this->belongsTo(Unit::class);
  }

  /**
   * @return BelongsToMany<Year, $this, MetricDefinitionYear>
   */
  public function years(): BelongsToMany
  {
    return $this->belongsToMany(Year::class, "metric_definition_year")
      ->using(MetricDefinitionYear::class)
      ->as("pivot")
      ->withPivot("id")
      ->withTimestamps();
  }

  /**
   * @return HasMany<MetricValue, $this>
   */
  public function metricValues(): HasMany
  {
    return $this->hasMany(MetricValue::class);
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array<string, string>
   */
  protected function casts(): array
  {
    return [];
  }
}
