<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class MetricValue extends Model
{
  /** @use HasFactory<\Database\Factories\MetricValueFactory> */
  use HasFactory;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["value", "metric_definition_id", "company_id", "year_id", "source"];

  /**
   * @return BelongsTo<MetricDefinition, $this>
   */
  public function metricDefinition(): BelongsTo
  {
    return $this->belongsTo(MetricDefinition::class);
  }

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<Year, $this>
   */
  public function year(): BelongsTo
  {
    return $this->belongsTo(Year::class);
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'value': 'string'}
   */
  protected function casts(): array
  {
    return [
      "value" => "string",
    ];
  }
}
