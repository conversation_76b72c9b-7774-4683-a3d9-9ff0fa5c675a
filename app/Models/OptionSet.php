<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class OptionSet extends Model
{
  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["name"];

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class);
  }

  /**
   * @return HasMany<CalculationDefinitionOption, $this>
   */
  public function options(): HasMany
  {
    return $this->hasMany(CalculationDefinitionOption::class);
  }
}
