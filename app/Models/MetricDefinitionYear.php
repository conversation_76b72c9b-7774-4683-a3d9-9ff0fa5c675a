<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @implements TranslatableContract<MetricDefinitionYearTranslation>
 */
final class MetricDefinitionYear extends Pivot implements TranslatableContract
{
  /** @use Translatable<MetricDefinitionYearTranslation> */
  use Translatable;

  public $incrementing = true;

  public $timestamps = true;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["help_text"];

  protected $table = "metric_definition_year";

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = MetricDefinitionYearTranslation::class;

  protected string $translationForeignKey = "metric_definition_year_id";
}
