<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use App\Enums\InputMethod;
use App\Interfaces\Auditable as AuditableContract;
use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Auditable;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<CalculationDefinitionTranslation>
 */
#[AllowDynamicProperties]
final class CalculationDefinition extends Model implements AuditableContract, TranslatableContract
{
  /** @use HasFactory<\Database\Factories\CalculationDefinitionFactory> */
  use Auditable, HasFactory, SoftDeletes;

  /** @use Translatable<CalculationDefinitionTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["data_name", "emission_factor_name", "result_name"];

  protected string $localeKey = "locale";

  protected string $translationForeignKey = "calculation_definition_id";

  /**
   * @var class-string
   */
  protected string $translationModel = CalculationDefinitionTranslation::class;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    "company_id",
    "scope_id",
    "category_id",
    "grouping_id",
    "custom_name",
    "input_method",
    "sort_order",
    "hide_from_data_page",
    "hide_from_emission_factor_page",
    "hide_from_results_page",
    "tag",
    "link_id",
    "option_set_id",
    // Data definition fields
    "data_unit_id",
    "data_formula",
    // Emission factor definition fields
    "emission_factor_compound_unit_id",
  ];

  public function getAuditableFields(): array
  {
    return ["custom_name"];
  }

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }

  /**
   * @return BelongsTo<Unit, $this>
   */
  public function dataUnit(): BelongsTo
  {
    return $this->belongsTo(Unit::class, "data_unit_id");
  }

  /**
   * @return BelongsTo<CompoundUnit, $this>
   */
  public function emissionFactorCompoundUnit(): BelongsTo
  {
    return $this->belongsTo(CompoundUnit::class, "emission_factor_compound_unit_id");
  }

  /**
   * @return BelongsTo<Scope, $this>
   */
  public function scope(): BelongsTo
  {
    return $this->belongsTo(Scope::class);
  }

  /**
   * @return BelongsTo<Category, $this>
   */
  public function category(): BelongsTo
  {
    return $this->belongsTo(Category::class);
  }

  /**
   * @return BelongsTo<Grouping, $this>
   */
  public function grouping(): BelongsTo
  {
    return $this->belongsTo(Grouping::class);
  }

  /**
   * @return HasMany<DataValue, $this>
   */
  public function dataValues(): HasMany
  {
    return $this->hasMany(DataValue::class, "data_definition_id");
  }

  /**
   * @return HasMany<EmissionFactorValue, $this>
   */
  public function emissionFactorValues(): HasMany
  {
    return $this->hasMany(EmissionFactorValue::class, "emission_factor_definition_id");
  }

  /**
   * @return BelongsToMany<Year, $this, CalculationDefinitionYear>
   */
  public function years(): BelongsToMany
  {
    return $this->belongsToMany(Year::class)
      ->using(CalculationDefinitionYear::class)
      ->as("pivot")
      ->withPivot("id", "emission_factor_default_value", "emission_factor_default_source")
      ->withTimestamps();
  }

  /**
   * @return BelongsTo<OptionSet, $this>
   */
  public function optionSet(): BelongsTo
  {
    return $this->belongsTo(OptionSet::class);
  }

  /**
   * @return array{'input_method': 'App\\Enums\\InputMethod', 'hide_from_data_page': 'boolean', 'hide_from_emission_factor_page': 'boolean', 'hide_from_results_page': 'boolean'}
   */
  public function casts(): array
  {
    return [
      "input_method" => InputMethod::class,
      "hide_from_data_page" => "boolean",
      "hide_from_emission_factor_page" => "boolean",
      "hide_from_results_page" => "boolean",
    ];
  }
}
