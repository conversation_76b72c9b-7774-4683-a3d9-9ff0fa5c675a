<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @implements TranslatableContract<IndustryClassificationTranslation>
 */
final class IndustryClassification extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\IndustryClassificationFactory> */
  use HasFactory, SoftDeletes;

  /** @use Translatable<IndustryClassificationTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["name"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = IndustryClassificationTranslation::class;

  protected string $translationForeignKey = "industry_classification_id";

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["code", "local_id"];
}
