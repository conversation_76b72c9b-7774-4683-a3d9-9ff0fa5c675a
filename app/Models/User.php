<?php

declare(strict_types=1);

namespace App\Models;

use AllowDynamicProperties;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

// https://github.com/larastan/larastan/issues/2256#issuecomment-2783542939

#[AllowDynamicProperties]
final class User extends Authenticatable implements FilamentUser
{
  /** @use HasFactory<\Database\Factories\UserFactory> */
  use HasFactory, HasRoles, Notifiable;

  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = ["name", "email", "password", "selected_company_id"];

  /**
   * The attributes that should be hidden for serialization.
   *
   * @var list<string>
   */
  protected $hidden = ["password", "remember_token"];

  /**
   * The companies that the user belongs to.
   *
   * @return BelongsToMany<Company, $this, CompanyUser>
   */
  public function companies(): BelongsToMany
  {
    return $this->belongsToMany(Company::class)
      ->using(CompanyUser::class)
      ->as("pivot")
      ->withPivot("is_primary")
      ->withTimestamps();
  }

  /**
   * The company that the user has selected.
   *
   * @return HasOne<Company, $this>
   */
  public function selectedCompany(): HasOne
  {
    return $this->hasOne(Company::class);
  }

  public function canAccessPanel(Panel $panel): bool
  {
    return match ($panel->getId()) {
      "admin" => $this->hasPermissionTo("view-admin-panel"),
      default => false,
    };
  }

  /**
   * Get the attributes that should be cast.
   *
   * @return array{'email_verified_at': 'datetime', 'password': 'hashed'}
   */
  protected function casts(): array
  {
    return [
      "email_verified_at" => "datetime",
      "password" => "hashed",
    ];
  }
}
