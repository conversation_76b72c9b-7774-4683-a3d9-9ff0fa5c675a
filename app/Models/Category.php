<?php

declare(strict_types=1);

namespace App\Models;

use App\Interfaces\Translatable as TranslatableContract;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @implements TranslatableContract<CategoryTranslation>
 */
final class Category extends Model implements TranslatableContract
{
  /** @use HasFactory<\Database\Factories\YearFactory> */
  use HasFactory;

  /** @use Translatable<CategoryTranslation> */
  use Translatable;

  /**
   * @var list<string>
   */
  public $translatedAttributes = ["title", "description"];

  protected string $localeKey = "locale";

  /**
   * @var class-string
   */
  protected string $translationModel = CategoryTranslation::class;

  protected string $translationForeignKey = "category_id";

  /**
   * @var list<string>
   */
  protected $fillable = ["sort_order", "hide_total"];

  /**
   * Get the attributes that should be cast.
   *
   * @return array{hide_total: 'boolean'}
   */
  public function casts(): array
  {
    return [
      "hide_total" => "boolean",
    ];
  }

  /**
   * @return HasMany<CalculationDefinition, $this>
   */
  public function calculationDefinitions(): HasMany
  {
    return $this->hasMany(CalculationDefinition::class);
  }

  /**
   * @return BelongsToMany<CalculationDefinition, $this>
   */
  public function templateDefinitions(): BelongsToMany
  {
    return $this->belongsToMany(CalculationDefinition::class)
      ->withPivot("sort_order")
      ->withTimestamps()
      ->orderByPivot("sort_order");
  }
}
