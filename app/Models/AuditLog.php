<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\AuditEvent;
use App\Enums\ScalarType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

final class AuditLog extends Model
{
  /**
   * The attributes that are mass assignable.
   *
   * @var list<string>
   */
  protected $fillable = [
    "event",
    "auditable_type",
    "auditable_id",
    "user_id",
    "company_id",
    "batch_uuid",
    "field_name",
    "field_type",
    "old_value",
    "new_value",
    "occurred_at",
  ];

  /**
   * @return array{'event': 'App\\Enums\\AuditEvent', 'occurred_at': 'datetime', 'field_type': 'App\\Enums\\ScalarType'}
   */
  public function casts(): array
  {
    return [
      "event" => AuditEvent::class,
      "occurred_at" => "datetime",
      "field_type" => ScalarType::class,
    ];
  }

  /**
   * @return MorphTo<Model, $this>
   */
  public function auditable(): MorphTo
  {
    return $this->morphTo();
  }

  /**
   * @return BelongsTo<User, $this>
   */
  public function user(): BelongsTo
  {
    return $this->belongsTo(User::class);
  }

  /**
   * @return BelongsTo<Company, $this>
   */
  public function company(): BelongsTo
  {
    return $this->belongsTo(Company::class);
  }
}
