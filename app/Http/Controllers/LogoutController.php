<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\AuthenticationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

final class LogoutController extends Controller
{
  public function __construct(private readonly AuthenticationService $authenticationService) {}

  /**
   * Log the user out of the application.
   *
   * @return RedirectResponse
   * @throws \RuntimeException
   */
  public function logout(): RedirectResponse
  {
    $this->authenticationService->logout();

    return redirect()->to(LaravelLocalization::localizeUrl("login"));
  }
}
