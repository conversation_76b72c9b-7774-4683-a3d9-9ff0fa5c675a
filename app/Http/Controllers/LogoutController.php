<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\AuthenticationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;

final class LogoutController extends Controller
{
  public function __construct(private readonly AuthenticationService $authenticationService) {}

  /**
   * Log the user out of the application.
   *
   * @param string $_locale The current locale
   * @return RedirectResponse
   * @throws \RuntimeException
   */
  public function logout(string $_locale): RedirectResponse
  {
    $this->authenticationService->logout();

    return redirect()->route("login", ["_locale" => $_locale]);
  }
}
