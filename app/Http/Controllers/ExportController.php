<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\Abilities;
use App\Exports\EmissionCalculationExport;
use App\Models\Year;
use App\Services\CompanyContextService;
use App\Services\YearService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use LogicException;
use Maatwebsite\Excel\Facades\Excel;
use RuntimeException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

final class ExportController extends Controller
{
  public function __construct(
    private CompanyContextService $companyService,
    private YearService $yearService,
  ) {}

  /**
   * @throws LogicException
   * @throws RuntimeException
   */
  public function exportEmissionCalculations(
    string $_locale,
    ?int $yearId = null,
  ): BinaryFileResponse|\Illuminate\Http\RedirectResponse {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return redirect()
        ->route("results")
        ->with("error", __("Yritystä ei ole valittu. Valitse yritys ennen vientiä."));
    }

    // Check authorization
    if (Gate::denies(Abilities::VIEW_COMPANY_DATA, $company)) {
      return redirect()
        ->route("results")
        ->with("error", __("Sinulla ei ole oikeutta viedä yrityksen tietoja."));
    }

    // Get year value for filename if yearId is provided
    $yearValue = null;
    if ($yearId !== null) {
      $yearModel = $this->yearService->findById($yearId);
      $yearValue = $yearModel?->year;
    }

    $filename =
      Str::slug(
        __("paastolaskelmat") .
          "_" .
          ($yearValue !== null ? __("vuosi") . "_" . $yearValue : __("kaikki_vuodet")) .
          "_" .
          $company->name .
          "_" .
          $company->business_id .
          "_" .
          date("Y-m-d"),
      ) . ".xlsx";

    return Excel::download(new EmissionCalculationExport($company->id, $yearId), $filename);
  }
}
