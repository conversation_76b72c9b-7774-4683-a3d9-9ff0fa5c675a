<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\Abilities;
use App\Exports\EmissionCalculationExport;
use App\Models\Year;
use App\Services\CompanyContextService;
use App\Services\YearService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use LogicException;
use Maatwebsite\Excel\Facades\Excel;
use RuntimeException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

final class ExportController extends Controller
{
  public function __construct(
    private CompanyContextService $companyService,
    private YearService $yearService,
  ) {}

  /**
   * @throws LogicException
   * @throws RuntimeException
   */
  public function exportEmissionCalculations(
    string $_locale,
    ?int $yearId = null,
  ): BinaryFileResponse|\Illuminate\Http\RedirectResponse {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return redirect()->route("results")->with("error", __("exports.errors.no_company_selected"));
    }

    // Check authorization
    if (Gate::denies(Abilities::VIEW_COMPANY_DATA, $company)) {
      return redirect()->route("results")->with("error", __("exports.errors.no_export_permission"));
    }

    // Get year value for filename if yearId is provided
    $yearValue = null;
    if ($yearId !== null) {
      $yearModel = $this->yearService->findById($yearId);
      $yearValue = $yearModel?->year;
    }

    $filename =
      Str::slug(
        __("exports.sheets.emission_calculations") .
          "_" .
          ($yearValue !== null
            ? __("exports.filters.year") . "_" . $yearValue
            : __("exports.filters.all_years")) .
          "_" .
          $company->name .
          "_" .
          $company->business_id .
          "_" .
          date("Y-m-d"),
      ) . ".xlsx";

    return Excel::download(new EmissionCalculationExport($company->id, $yearId), $filename);
  }
}
