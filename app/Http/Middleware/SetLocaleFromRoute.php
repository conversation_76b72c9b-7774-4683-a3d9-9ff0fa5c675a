<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Helpers\Assert;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

final class SetLocaleFromRoute
{
  /**
   * Handle an incoming request.
   *
   * @param  Closure(Request): (Response)  $next
   *
   * @throws RuntimeException
   */
  public function handle(Request $request, Closure $next): Response
  {
    // Get the first segment from the URL (assuming locale is the first segment)
    $locale = $request->segment(1);

    // Get supported locales from config
    $supportedLocales = Config::array("translatable.locales");
    Assert::stringArray($supportedLocales);
    Assert::nonEmptyList($supportedLocales);

    // Check if the segment is a valid locale
    if (is_string($locale) && in_array($locale, $supportedLocales, true)) {
      App::setLocale($locale);
      URL::defaults(["_locale" => $locale]);
    } else {
      // Set a default locale if none is found or invalid
      $defaultLocale = Config::string("app.locale");
      App::setLocale($defaultLocale);
      URL::defaults(["_locale" => $defaultLocale]);
    }

    return $next($request);
  }
}
