<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class SetPermissionsPolicy
{
  /**
   * Handle an incoming request.
   *
   * @param  Closure(Request): (Response)  $next
   */
  public function handle(Request $request, Closure $next): Response
  {
    $response = $next($request);

    $response->headers->set(
      key: "Permissions-Policy",
      values: "camera=(), microphone=(), geolocation=(), payment=(), usb=(), midi=(), screen-wake-lock=(), xr-spatial-tracking=()",
    );

    return $response;
  }
}
