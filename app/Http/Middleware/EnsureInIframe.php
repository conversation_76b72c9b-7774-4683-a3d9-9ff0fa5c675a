<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Helpers\Assert;
use App\Settings\UrlSettings;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use Symfony\Component\HttpFoundation\Response;

final class EnsureInIframe
{
  /**
   * Handle an incoming request.
   *
   * @param  Closure(Request): (Response)  $next
   * @throws \RuntimeException
   */
  public function handle(Request $request, Closure $next): Response
  {
    if ($request->hasHeader("Sec-Fetch-Dest") && $request->header("Sec-Fetch-Dest") !== "iframe") {
      $urlSettings = app(UrlSettings::class);
      $redirectUrl = $urlSettings->embedded_app_url[LaravelLocalization::getCurrentLocale()] ?? "";
      Assert::nonEmptyString($redirectUrl);
      return response()->redirectTo($redirectUrl);
    }

    return $next($request);
  }
}
