<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Symfony\Component\HttpFoundation\Response;

final class EnsureInIframe
{
  /**
   * Handle an incoming request.
   *
   * @param  Closure(Request): (Response)  $next
   */
  public function handle(Request $request, Closure $next): Response
  {
    if ($request->hasHeader("Sec-Fetch-Dest") && $request->header("Sec-Fetch-Dest") !== "iframe") {
      return response()->redirectTo(Config::string("misc.embedded_app_url"));
    }

    return $next($request);
  }
}
