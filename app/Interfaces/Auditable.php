<?php

declare(strict_types=1);

namespace App\Interfaces;

use App\Enums\AuditEvent;
use RuntimeException;

interface Auditable
{
  /**
   * Get the list of fields that should be audited
   *
   * @return array<int, string>
   */
  public function getAuditableFields(): array;

  /**
   * Log an audit event
   *
   * @throws RuntimeException
   */
  public function logAuditEvent(AuditEvent $event): void;
}
