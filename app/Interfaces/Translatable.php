<?php

declare(strict_types=1);

namespace App\Interfaces;

use Astrotomic\Translatable\Contracts\Translatable as AstrotomicTranslatable;
use Illuminate\Database\Eloquent\Model;

/**
 * @template T of Model
 */
interface Translatable extends AstrotomicTranslatable
{
  /**
   * @return T | null
   */
  public function translate(?string $locale = null, bool $withFallback = false): ?Model;

  /**
   * @return T
   */
  public function translateOrNew(?string $locale = null): Model;
}
