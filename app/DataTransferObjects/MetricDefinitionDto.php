<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\MetricDefinition;
use InvalidArgumentException;
use Livewire\Wireable;

final class MetricDefinitionDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly string $name,
    public readonly int $unitId,
    public readonly string $unitSymbol,
    public readonly string $helpText,
  ) {}

  /**
   * @param  string  $translatedName  Pre-fetched translated name
   * @param  string  $unitSymbol  Pre-fetched unit symbol
   * @param  string  $helpText  Pre-fetched help text from year pivot
   */
  public static function fromModel(
    MetricDefinition $definition,
    string $translatedName,
    string $unitSymbol,
    string $helpText = "",
  ): self {
    return new self(
      id: $definition->id,
      name: $translatedName,
      unitId: $definition->unit_id,
      unitSymbol: $unitSymbol,
      helpText: $helpText,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException(
        "Invalid payload for MetricDefinitionDto, array expected.",
      );
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [id] in MetricDefinitionDto payload.",
      );
    }

    if (!array_key_exists("name", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [name] in MetricDefinitionDto payload.",
      );
    }

    if (!array_key_exists("unitId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [unitId] in MetricDefinitionDto payload.",
      );
    }

    if (!array_key_exists("unitSymbol", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [unitSymbol] in MetricDefinitionDto payload.",
      );
    }

    if (!array_key_exists("helpText", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [helpText] in MetricDefinitionDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("MetricDefinitionDto id must be numeric.");
    }

    if (!is_string($value["name"])) {
      throw new InvalidArgumentException("MetricDefinitionDto name must be a string.");
    }

    if (!is_int($value["unitId"]) && !is_numeric($value["unitId"])) {
      throw new InvalidArgumentException("MetricDefinitionDto unitId must be numeric.");
    }

    if (!is_string($value["unitSymbol"])) {
      throw new InvalidArgumentException("MetricDefinitionDto unitSymbol must be a string.");
    }

    if (!is_string($value["helpText"])) {
      throw new InvalidArgumentException("MetricDefinitionDto helpText must be a string.");
    }

    return new self(
      id: (int) $value["id"],
      name: $value["name"],
      unitId: (int) $value["unitId"],
      unitSymbol: $value["unitSymbol"],
      helpText: $value["helpText"],
    );
  }

  /**
   * @return array{id: int, name: string, unitId: int, unitSymbol: string, helpText: string}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "name" => $this->name,
      "unitId" => $this->unitId,
      "unitSymbol" => $this->unitSymbol,
      "helpText" => $this->helpText,
    ];
  }
}
