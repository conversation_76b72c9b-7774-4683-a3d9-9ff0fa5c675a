<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

final class ScopeVariantTotal
{
  /**
   * @param  array<int, string>  $totals  Year => Total amount mapping
   */
  public function __construct(
    public readonly int $scopeId,
    public readonly int $scopeNumber,
    public readonly ?int $variantId,
    public readonly string $label,
    public readonly array $totals,
    public readonly bool $includeInTotal,
  ) {}
}
