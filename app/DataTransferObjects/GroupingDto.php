<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\Grouping;
use InvalidArgumentException;
use Livewire\Wireable;

final class GroupingDto implements Wireable
{
  public function __construct(public readonly int $id, public readonly string $title) {}

  /**
   * @param  string  $translatedTitle  Pre-fetched translated title
   */
  public static function fromModel(Grouping $grouping, string $translatedTitle): self
  {
    return new self(id: $grouping->id, title: $translatedTitle);
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for GroupingDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in GroupingDto payload.");
    }

    if (!array_key_exists("title", $value)) {
      throw new InvalidArgumentException("Missing required key [title] in GroupingDto payload.");
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("GroupingDto id must be numeric.");
    }

    if (!is_string($value["title"])) {
      throw new InvalidArgumentException("GroupingDto title must be a string.");
    }

    return new self(id: (int) $value["id"], title: $value["title"]);
  }

  /**
   * @return array{id: int, title: string}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "title" => $this->title,
    ];
  }
}
