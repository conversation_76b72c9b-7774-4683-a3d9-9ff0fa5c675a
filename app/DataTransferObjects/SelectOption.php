<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use InvalidArgumentException;
use Livewire\Wireable;

final readonly class SelectOption implements Wireable
{
  public function __construct(public int|string $value, public string $label) {}

  /**
   * @throws InvalidArgumentException
   */
  public static function fromLivewire(mixed $value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for SelectOption, array expected.");
    }

    if (!isset($value["value"], $value["label"])) {
      throw new InvalidArgumentException("Missing keys [value, label] in SelectOption payload.");
    }

    $rawValue = $value["value"];
    $rawLabel = $value["label"];

    if (!is_int($rawValue) && !is_string($rawValue)) {
      throw new InvalidArgumentException("SelectOption value must be int or string.");
    }

    if (!is_string($rawLabel)) {
      throw new InvalidArgumentException("SelectOption label must be a string.");
    }

    return new self($rawValue, $rawLabel);
  }

  /**
   * @return array{value:int|string, label:string}
   */
  public function toLivewire(): array
  {
    return [
      "value" => $this->value,
      "label" => $this->label,
    ];
  }
}
