<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\CompoundUnit;
use InvalidArgumentException;
use Livewire\Wireable;

final class CompoundUnitDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly int $numeratorUnitId,
    public readonly int $denominatorUnitId,
    public readonly string $formatted,
  ) {}

  /**
   * @param  string  $numeratorSymbol  Pre-fetched numerator unit symbol
   * @param  string  $denominatorSymbol  Pre-fetched denominator unit symbol
   */
  public static function fromModel(
    CompoundUnit $unit,
    string $numeratorSymbol,
    string $denominatorSymbol,
  ): self {
    $formatted = "";
    if ($numeratorSymbol !== "" && $denominatorSymbol !== "") {
      $formatted = $numeratorSymbol . "/" . $denominatorSymbol;
    }

    return new self(
      id: $unit->id,
      numeratorUnitId: $unit->numerator_unit_id,
      denominatorUnitId: $unit->denominator_unit_id,
      formatted: $formatted,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for CompoundUnitDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in CompoundUnitDto payload.");
    }

    if (!array_key_exists("numeratorUnitId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [numeratorUnitId] in CompoundUnitDto payload.",
      );
    }

    if (!array_key_exists("denominatorUnitId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [denominatorUnitId] in CompoundUnitDto payload.",
      );
    }

    if (!array_key_exists("formatted", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [formatted] in CompoundUnitDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("CompoundUnitDto id must be numeric.");
    }

    if (!is_int($value["numeratorUnitId"]) && !is_numeric($value["numeratorUnitId"])) {
      throw new InvalidArgumentException("CompoundUnitDto numeratorUnitId must be numeric.");
    }

    if (!is_int($value["denominatorUnitId"]) && !is_numeric($value["denominatorUnitId"])) {
      throw new InvalidArgumentException("CompoundUnitDto denominatorUnitId must be numeric.");
    }

    if (!is_string($value["formatted"])) {
      throw new InvalidArgumentException("CompoundUnitDto formatted must be a string.");
    }

    return new self(
      id: (int) $value["id"],
      numeratorUnitId: (int) $value["numeratorUnitId"],
      denominatorUnitId: (int) $value["denominatorUnitId"],
      formatted: $value["formatted"],
    );
  }

  /**
   * @return array{id: int, numeratorUnitId: int, denominatorUnitId: int, formatted: string}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "numeratorUnitId" => $this->numeratorUnitId,
      "denominatorUnitId" => $this->denominatorUnitId,
      "formatted" => $this->formatted,
    ];
  }
}
