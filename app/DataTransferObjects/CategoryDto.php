<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\Category;
use InvalidArgumentException;
use Livewire\Wireable;

final class CategoryDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly string $title,
    public readonly string $description,
  ) {}

  /**
   * @param  string  $translatedTitle  Pre-fetched translated title
   * @param  string  $translatedDescription  Pre-fetched translated description
   */
  public static function fromModel(
    Category $category,
    string $translatedTitle,
    string $translatedDescription,
  ): self {
    return new self(
      id: $category->id,
      title: $translatedTitle,
      description: $translatedDescription,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for CategoryDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in CategoryDto payload.");
    }

    if (!array_key_exists("title", $value)) {
      throw new InvalidArgumentException("Missing required key [title] in CategoryDto payload.");
    }

    if (!array_key_exists("description", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [description] in CategoryDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("CategoryDto id must be numeric.");
    }

    if (!is_string($value["title"])) {
      throw new InvalidArgumentException("CategoryDto title must be a string.");
    }

    if (!is_string($value["description"])) {
      throw new InvalidArgumentException("CategoryDto description must be a string.");
    }

    return new self(
      id: (int) $value["id"],
      title: $value["title"],
      description: $value["description"],
    );
  }

  /**
   * @return array{id: int, title: string, description: string}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "title" => $this->title,
      "description" => $this->description,
    ];
  }
}
