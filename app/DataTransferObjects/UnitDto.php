<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\Unit;
use InvalidArgumentException;
use Livewire\Wireable;

final class UnitDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly string $name,
    public readonly string $symbol,
  ) {}

  /**
   * @param  string  $translatedName  Pre-fetched translated name
   * @param  string  $translatedSymbol  Pre-fetched translated symbol
   */
  public static function fromModel(
    Unit $unit,
    string $translatedName,
    string $translatedSymbol,
  ): self {
    return new self(id: $unit->id, name: $translatedName, symbol: $translatedSymbol);
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for UnitDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in UnitDto payload.");
    }

    if (!array_key_exists("name", $value)) {
      throw new InvalidArgumentException("Missing required key [name] in UnitDto payload.");
    }

    if (!array_key_exists("symbol", $value)) {
      throw new InvalidArgumentException("Missing required key [symbol] in UnitDto payload.");
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("UnitDto id must be numeric.");
    }

    if (!is_string($value["name"])) {
      throw new InvalidArgumentException("UnitDto name must be a string.");
    }

    if (!is_string($value["symbol"])) {
      throw new InvalidArgumentException("UnitDto symbol must be a string.");
    }

    return new self(id: (int) $value["id"], name: $value["name"], symbol: $value["symbol"]);
  }

  /**
   * @return array{id: int, name: string, symbol: string}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "name" => $this->name,
      "symbol" => $this->symbol,
    ];
  }
}
