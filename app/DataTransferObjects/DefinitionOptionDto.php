<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\CalculationDefinitionOption;
use Brick\Math\BigDecimal;
use InvalidArgumentException;
use Livewire\Wireable;

final class DefinitionOptionDto implements Wireable
{
  /**
   * @param  array<int, string>  $yearValues  Map of year to value
   */
  public function __construct(
    public readonly int $id,
    public readonly string $label,
    public readonly int $sortOrder,
    public readonly array $yearValues,
  ) {}

  public static function fromModel(CalculationDefinitionOption $option): self
  {
    $yearValues = [];

    // Build year values from the relationship
    foreach ($option->yearValues as $yearValue) {
      if ($yearValue->value !== null && $yearValue->year !== null) {
        try {
          // Convert to BigDecimal and strip trailing zeros
          $bigDecimal = BigDecimal::of($yearValue->value);
          $yearValues[$yearValue->year->year] = (string) $bigDecimal->stripTrailingZeros();
        } catch (\Brick\Math\Exception\MathException $e) {
          // This probably shouldn't happen so report it.
          report($e);
          // If there's any math exception, just use the value as-is
          $yearValues[$yearValue->year->year] = $yearValue->value;
        }
      }
    }

    return new self(
      id: $option->id,
      label: $option->translate()->label ?? "",
      sortOrder: $option->sort_order,
      yearValues: $yearValues,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException(
        "Invalid payload for DefinitionOptionDto, array expected.",
      );
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [id] in DefinitionOptionDto payload.",
      );
    }

    if (!array_key_exists("label", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [label] in DefinitionOptionDto payload.",
      );
    }

    if (!array_key_exists("sortOrder", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [sortOrder] in DefinitionOptionDto payload.",
      );
    }

    if (!array_key_exists("yearValues", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [yearValues] in DefinitionOptionDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("DefinitionOptionDto id must be numeric.");
    }

    if (!is_string($value["label"])) {
      throw new InvalidArgumentException("DefinitionOptionDto label must be a string.");
    }

    if (!is_int($value["sortOrder"]) && !is_numeric($value["sortOrder"])) {
      throw new InvalidArgumentException("DefinitionOptionDto sortOrder must be numeric.");
    }

    if (!is_array($value["yearValues"])) {
      throw new InvalidArgumentException("DefinitionOptionDto yearValues must be an array.");
    }

    $yearValues = [];
    foreach ($value["yearValues"] as $year => $val) {
      if (!is_numeric($year)) {
        throw new InvalidArgumentException(
          "DefinitionOptionDto yearValues keys must be numeric years.",
        );
      }
      if (!is_string($val) && !is_numeric($val)) {
        throw new InvalidArgumentException(
          "DefinitionOptionDto yearValues values must be numeric strings.",
        );
      }
      $yearValues[(int) $year] = (string) $val;
    }

    return new self(
      id: (int) $value["id"],
      label: $value["label"],
      sortOrder: (int) $value["sortOrder"],
      yearValues: $yearValues,
    );
  }

  /**
   * @return array{id: int, label: string, sortOrder: int, yearValues: array<int, string>}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "label" => $this->label,
      "sortOrder" => $this->sortOrder,
      "yearValues" => $this->yearValues,
    ];
  }
}
