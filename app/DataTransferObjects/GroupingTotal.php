<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use Livewire\Wireable;

/**
 * @phpstan-type LivewireArray array{name: string, scope: int, total: string}
 */
final class GroupingTotal implements Wireable
{
  public function __construct(
    public readonly string $name,
    public readonly int $scope,
    public readonly string $total,
  ) {}

  public static function fromLivewire(mixed $value): self
  {
    if (
      is_array($value) &&
      array_key_exists("name", $value) &&
      array_key_exists("scope", $value) &&
      array_key_exists("total", $value) &&
      is_string($value["name"]) &&
      is_int($value["scope"]) &&
      is_string($value["total"])
    ) {
      $name = $value["name"];
      $scopeId = $value["scope"];
      $total = $value["total"];

      return new self($name, $scopeId, $total);
    }

    return new self("", 0, "0");
  }

  /**
   * @return LivewireArray
   */
  public function toLivewire(): array
  {
    return [
      "name" => $this->name,
      "scope" => $this->scope,
      "total" => $this->total,
    ];
  }
}
