<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\Scope;
use InvalidArgumentException;
use Livewire\Wireable;

final class ScopeDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly int $number,
    public readonly bool $allowGroupingHiding,
  ) {}

  public static function fromModel(Scope $scope): self
  {
    return new self(
      id: $scope->id,
      number: $scope->number,
      allowGroupingHiding: $scope->allow_grouping_hiding,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for ScopeDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in ScopeDto payload.");
    }

    if (!array_key_exists("number", $value)) {
      throw new InvalidArgumentException("Missing required key [number] in ScopeDto payload.");
    }

    if (!array_key_exists("allowGroupingHiding", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [allowGroupingHiding] in ScopeDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("ScopeDto id must be numeric.");
    }

    if (!is_int($value["number"]) && !is_numeric($value["number"])) {
      throw new InvalidArgumentException("ScopeDto number must be numeric.");
    }

    if (!is_bool($value["allowGroupingHiding"])) {
      throw new InvalidArgumentException("ScopeDto number must be numeric.");
    }

    return new self(
      id: (int) $value["id"],
      number: (int) $value["number"],
      allowGroupingHiding: $value["allowGroupingHiding"],
    );
  }

  /**
   * @return array{id: int, number: int, allowGroupingHiding: bool}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "number" => $this->number,
      "allowGroupingHiding" => $this->allowGroupingHiding,
    ];
  }
}
