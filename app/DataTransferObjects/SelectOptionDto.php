<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Models\SelectOption as SelectOptionModel;
use InvalidArgumentException;
use Livewire\Wireable;

final class SelectOptionDto implements Wireable
{
  public function __construct(
    public readonly int $id,
    public readonly int $value,
    public readonly string $label,
    public readonly string $type,
    public readonly int $order,
  ) {}

  /**
   * @param  string  $translatedLabel  Pre-fetched translated label
   * @param  string  $typeValue  Pre-fetched type enum value
   */
  public static function fromModel(
    SelectOptionModel $option,
    string $translatedLabel,
    string $typeValue,
    int $order = 0,
  ): self {
    return new self(
      id: $option->id,
      value: $option->id,
      label: $translatedLabel,
      type: $typeValue,
      order: $order,
    );
  }

  /**
   * Convert existing SelectOption DTO to SelectOptionDto
   *
   * @throws InvalidArgumentException
   */
  public static function fromSelectOption(SelectOption $option): self
  {
    if (!is_int($option->value) && !is_numeric($option->value)) {
      throw new InvalidArgumentException(
        "SelectOption value must be numeric for conversion to SelectOptionDto.",
      );
    }

    return new self(
      id: (int) $option->value,
      value: (int) $option->value,
      label: $option->label,
      type: "", // Type not available in the service DTO
      order: 0, // Order not available in the service DTO
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException("Invalid payload for SelectOptionDto, array expected.");
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException("Missing required key [id] in SelectOptionDto payload.");
    }

    if (!array_key_exists("value", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [value] in SelectOptionDto payload.",
      );
    }

    if (!array_key_exists("label", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [label] in SelectOptionDto payload.",
      );
    }

    if (!array_key_exists("type", $value)) {
      throw new InvalidArgumentException("Missing required key [type] in SelectOptionDto payload.");
    }

    if (!array_key_exists("order", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [order] in SelectOptionDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("SelectOptionDto id must be numeric.");
    }

    if (!is_int($value["value"]) && !is_numeric($value["value"])) {
      throw new InvalidArgumentException("SelectOptionDto value must be numeric.");
    }

    if (!is_string($value["label"])) {
      throw new InvalidArgumentException("SelectOptionDto label must be a string.");
    }

    if (!is_string($value["type"])) {
      throw new InvalidArgumentException("SelectOptionDto type must be a string.");
    }

    if (!is_int($value["order"]) && !is_numeric($value["order"])) {
      throw new InvalidArgumentException("SelectOptionDto order must be numeric.");
    }

    return new self(
      id: (int) $value["id"],
      value: (int) $value["value"],
      label: $value["label"],
      type: $value["type"],
      order: (int) $value["order"],
    );
  }

  /**
   * @return array{id: int, value: int, label: string, type: string, order: int}
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "value" => $this->value,
      "label" => $this->label,
      "type" => $this->type,
      "order" => $this->order,
    ];
  }
}
