<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

final readonly class InvitationAcceptanceResult
{
  public function __construct(public bool $success, public bool $userExisted) {}

  public static function failure(): self
  {
    return new self(success: false, userExisted: false);
  }

  public static function successForExistingUser(): self
  {
    return new self(success: true, userExisted: true);
  }

  public static function successForNewUser(): self
  {
    return new self(success: true, userExisted: false);
  }
}
