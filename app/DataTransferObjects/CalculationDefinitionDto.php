<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Enums\InputMethod;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Livewire\Wireable;
use TypeError;
use ValueError;

final class CalculationDefinitionDto implements Wireable
{
  /**
   * @param  array<int, string>  $helpTexts  Data help texts for data page
   * @param  array<int, string>  $emissionFactorHelpTexts  Emission factor help texts for emission factors page
   * @param  list<DefinitionOptionDto>  $options
   * @param  array<int, string|null>  $emissionFactorDefaults
   * @param  array<int, string>  $emissionFactorDefaultSources
   */
  public function __construct(
    public readonly int $id,
    public readonly ?string $customName,
    public readonly ?string $dataName,
    public readonly ?int $dataUnitId,
    public readonly ?string $dataUnitSymbol,
    public readonly int $scopeId,
    public readonly int $categoryId,
    public readonly int $groupingId,
    public readonly ?int $companyId,
    public readonly ?int $emissionFactorCompoundUnitId,
    public readonly array $helpTexts,
    public readonly string $emissionFactorName,
    public readonly string $emissionFactorCompoundUnitFormatted,
    public readonly InputMethod $inputMethod,
    public readonly array $options,
    public readonly array $emissionFactorDefaults,
    public readonly array $emissionFactorDefaultSources,
    public readonly array $emissionFactorHelpTexts,
    public readonly ?string $linkId,
  ) {}

  /**
   * @param  Collection<int, \App\Models\Year&object{pivot: \App\Models\CalculationDefinitionYear}>  $yearsWithPivot  Pre-loaded years collection with pivot data and translations
   */
  public static function fromModel(
    CalculationDefinition $definition,
    Collection $yearsWithPivot,
  ): self {
    $helpTexts = [];
    $emissionFactorHelpTexts = [];
    $emissionFactorDefaults = [];
    $emissionFactorDefaultSources = [];

    foreach ($yearsWithPivot as $yearModel) {
      // Pivot translations should already be loaded
      $translation = $yearModel->pivot->translate();

      // Both help texts for different contexts
      $helpTexts[$yearModel->year] = $translation->data_help_text ?? "";
      $emissionFactorHelpTexts[$yearModel->year] = $translation->emission_factor_help_text ?? "";

      // Add emission factor default value
      if ($yearModel->pivot->emission_factor_default_value !== null) {
        $emissionFactorDefaults[$yearModel->year] =
          $yearModel->pivot->emission_factor_default_value;
      }

      $emissionFactorDefaultSources[$yearModel->year] =
        $yearModel->pivot->emission_factor_default_source;
    }

    $options = [];

    // Get input method
    $inputMethod = $definition->input_method;

    if ($inputMethod === InputMethod::SELECT) {
      Assert::notNull($definition->optionSet);
      foreach ($definition->optionSet->options as $option) {
        $options[] = DefinitionOptionDto::fromModel($option);
      }
    }

    $compoundUnitFormatted = "";
    if ($definition->emissionFactorCompoundUnit !== null) {
      $numeratorSymbol =
        $definition->emissionFactorCompoundUnit->numeratorUnit?->translate()->symbol ?? "";
      $denominatorSymbol =
        $definition->emissionFactorCompoundUnit->denominatorUnit?->translate()->symbol ?? "";
      if ($numeratorSymbol !== "" && $denominatorSymbol !== "") {
        $compoundUnitFormatted = $numeratorSymbol . "/" . $denominatorSymbol;
      }
    }

    return new self(
      id: $definition->id,
      customName: $definition->custom_name,
      dataName: $definition->translate()->data_name ?? "",
      dataUnitId: $definition->data_unit_id,
      dataUnitSymbol: $definition->dataUnit?->translate()->symbol ?? "",
      scopeId: $definition->scope_id,
      categoryId: $definition->category_id,
      groupingId: $definition->grouping_id,
      companyId: $definition->company_id,
      emissionFactorCompoundUnitId: $definition->emission_factor_compound_unit_id,
      helpTexts: $helpTexts,
      emissionFactorName: $definition->translate()->emission_factor_name ?? "",
      emissionFactorCompoundUnitFormatted: $compoundUnitFormatted,
      inputMethod: $inputMethod,
      options: $options,
      emissionFactorDefaults: $emissionFactorDefaults,
      emissionFactorDefaultSources: $emissionFactorDefaultSources,
      emissionFactorHelpTexts: $emissionFactorHelpTexts,
      linkId: $definition->link_id,
    );
  }

  /**
   * @param  mixed  $value
   *
   * @throws InvalidArgumentException
   * @throws TypeError
   * @throws ValueError
   */
  public static function fromLivewire($value): static
  {
    if (!is_array($value)) {
      throw new InvalidArgumentException(
        "Invalid payload for CalculationDefinitionDto, array expected.",
      );
    }

    if (!array_key_exists("id", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [id] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("scopeId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [scopeId] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("categoryId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [categoryId] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("groupingId", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [groupingId] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("dataName", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [dataName] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("inputMethod", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [inputMethod] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("emissionFactorName", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [emissionFactorName] in CalculationDefinitionDto payload.",
      );
    }

    if (!array_key_exists("emissionFactorCompoundUnitFormatted", $value)) {
      throw new InvalidArgumentException(
        "Missing required key [emissionFactorCompoundUnitFormatted] in CalculationDefinitionDto payload.",
      );
    }

    if (!is_int($value["id"]) && !is_numeric($value["id"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto id must be numeric.");
    }

    if (!is_int($value["scopeId"]) && !is_numeric($value["scopeId"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto scopeId must be numeric.");
    }

    if (!is_int($value["categoryId"]) && !is_numeric($value["categoryId"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto categoryId must be numeric.");
    }

    if (!is_int($value["groupingId"]) && !is_numeric($value["groupingId"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto groupingId must be numeric.");
    }

    if (!is_string($value["dataName"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto dataName must be a string.");
    }

    if (!is_string($value["inputMethod"])) {
      throw new InvalidArgumentException("CalculationDefinitionDto inputMethod must be a string.");
    }

    if (!is_string($value["emissionFactorName"])) {
      throw new InvalidArgumentException(
        "CalculationDefinitionDto emissionFactorName must be a string.",
      );
    }

    if (!is_string($value["emissionFactorCompoundUnitFormatted"])) {
      throw new InvalidArgumentException(
        "CalculationDefinitionDto emissionFactorCompoundUnitFormatted must be a string.",
      );
    }

    $helpTexts = [];
    if (isset($value["helpTexts"]) && is_array($value["helpTexts"])) {
      foreach ($value["helpTexts"] as $key => $text) {
        if (is_numeric($key) && is_string($text)) {
          $helpTexts[(int) $key] = $text;
        }
      }
    }

    $emissionFactorHelpTexts = [];
    if (isset($value["emissionFactorHelpTexts"]) && is_array($value["emissionFactorHelpTexts"])) {
      foreach ($value["emissionFactorHelpTexts"] as $key => $text) {
        if (is_numeric($key) && is_string($text)) {
          $emissionFactorHelpTexts[(int) $key] = $text;
        }
      }
    }

    $emissionFactorDefaults = [];
    if (isset($value["emissionFactorDefaults"]) && is_array($value["emissionFactorDefaults"])) {
      foreach ($value["emissionFactorDefaults"] as $year => $defaultValue) {
        if (is_numeric($year) && (is_string($defaultValue) || $defaultValue === null)) {
          $emissionFactorDefaults[(int) $year] = $defaultValue;
        }
      }
    }

    $emissionFactorDefaultSources = [];
    if (
      isset($value["emissionFactorDefaultSources"]) &&
      is_array($value["emissionFactorDefaultSources"])
    ) {
      foreach ($value["emissionFactorDefaultSources"] as $year => $defaultSource) {
        if (is_numeric($year) && is_string($defaultSource)) {
          $emissionFactorDefaultSources[(int) $year] = $defaultSource;
        }
      }
    }

    $options = [];
    if (isset($value["options"]) && is_array($value["options"])) {
      foreach ($value["options"] as $option) {
        $options[] = DefinitionOptionDto::fromLivewire($option);
      }
    }

    return new self(
      id: (int) $value["id"],
      customName: isset($value["customName"]) && is_string($value["customName"])
        ? $value["customName"]
        : null,
      dataName: $value["dataName"],
      dataUnitId: isset($value["dataUnitId"]) && is_numeric($value["dataUnitId"])
        ? (int) $value["dataUnitId"]
        : null,
      dataUnitSymbol: isset($value["dataUnitSymbol"]) && is_string($value["dataUnitSymbol"])
        ? $value["dataUnitSymbol"]
        : null,
      scopeId: (int) $value["scopeId"],
      categoryId: (int) $value["categoryId"],
      groupingId: (int) $value["groupingId"],
      companyId: isset($value["companyId"]) && is_numeric($value["companyId"])
        ? (int) $value["companyId"]
        : null,
      emissionFactorCompoundUnitId: isset($value["emissionFactorCompoundUnitId"]) &&
      is_numeric($value["emissionFactorCompoundUnitId"])
        ? (int) $value["emissionFactorCompoundUnitId"]
        : null,
      helpTexts: $helpTexts,
      emissionFactorName: $value["emissionFactorName"],
      emissionFactorCompoundUnitFormatted: $value["emissionFactorCompoundUnitFormatted"],
      inputMethod: InputMethod::from($value["inputMethod"]),
      options: $options,
      emissionFactorDefaults: $emissionFactorDefaults,
      emissionFactorDefaultSources: $emissionFactorDefaultSources,
      emissionFactorHelpTexts: $emissionFactorHelpTexts,
      linkId: isset($value["linkId"]) && is_string($value["linkId"]) ? $value["linkId"] : null,
    );
  }

  /**
   * @return array{
   *     id: int,
   *     customName: string|null,
   *     dataName: string|null,
   *     dataUnitId: int|null,
   *     dataUnitSymbol: string|null,
   *     scopeId: int,
   *     categoryId: int,
   *     groupingId: int,
   *     companyId: int|null,
   *     emissionFactorCompoundUnitId: int|null,
   *     helpTexts: array<int, string>,
   *     emissionFactorName: string|null,
   *     emissionFactorCompoundUnitFormatted: string|null,
   *     inputMethod: string,
   *     options: list<array{id: int, label: string, sortOrder: int, yearValues: array<int, string>}>,
   *     emissionFactorDefaults: array<int, string|null>,
   *     emissionFactorDefaultSources: array<int, string|null>,
   *     emissionFactorHelpTexts: array<int, string>,
   *     linkId: string|null
   * }
   */
  public function toLivewire(): array
  {
    return [
      "id" => $this->id,
      "customName" => $this->customName,
      "dataName" => $this->dataName,
      "dataUnitId" => $this->dataUnitId,
      "dataUnitSymbol" => $this->dataUnitSymbol,
      "scopeId" => $this->scopeId,
      "categoryId" => $this->categoryId,
      "groupingId" => $this->groupingId,
      "companyId" => $this->companyId,
      "emissionFactorCompoundUnitId" => $this->emissionFactorCompoundUnitId,
      "helpTexts" => $this->helpTexts,
      "emissionFactorName" => $this->emissionFactorName,
      "emissionFactorCompoundUnitFormatted" => $this->emissionFactorCompoundUnitFormatted,
      "inputMethod" => $this->inputMethod->value,
      "options" => array_map(fn(DefinitionOptionDto $dto) => $dto->toLivewire(), $this->options),
      "emissionFactorDefaults" => $this->emissionFactorDefaults,
      "emissionFactorDefaultSources" => $this->emissionFactorDefaultSources,
      "emissionFactorHelpTexts" => $this->emissionFactorHelpTexts,
      "linkId" => $this->linkId,
    ];
  }
}
