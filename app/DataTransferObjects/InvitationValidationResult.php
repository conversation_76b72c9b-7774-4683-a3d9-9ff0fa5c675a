<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

final readonly class InvitationValidationResult
{
  public function __construct(
    public bool $valid,
    public ?string $companyName = null,
    public ?string $inviteeEmail = null,
  ) {}

  public static function invalid(): self
  {
    return new self(valid: false);
  }

  public static function valid(string $companyName, string $inviteeEmail): self
  {
    return new self(valid: true, companyName: $companyName, inviteeEmail: $inviteeEmail);
  }
}
