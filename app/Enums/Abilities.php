<?php

declare(strict_types=1);

namespace App\Enums;

enum Abilities: string
{
  case VIEW_COMPANY = "view-company";
  case VIEW_ANY_COMPANY = "view-any-company";
  case EDIT_COMPANY = "edit-company";
  case MANAGE_COMPANY_USERS = "manage-company-users";
  case CREATE_COMPANY = "create-company";
  case DELETE_COMPANY_USER = "delete-company-user";
  case VIEW_COMPANY_DATA = "view-company-data";
  case EDIT_COMPANY_DATA = "edit-company-data";
  case VIEW_COMPANY_AUDIT_LOGS = "view-company-audit-logs";
}
