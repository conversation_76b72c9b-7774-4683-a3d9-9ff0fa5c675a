<?php

declare(strict_types=1);

namespace App\Enums;

enum AuditEvent: string
{
    case CREATED = "created";
    case UPDATED = "updated";
    case DELETED = "deleted";
    case SOFT_DELETED = "soft_deleted";
    case FORCE_DELETED = "force_deleted";
    case RESTORED = "restored";
}

enum ActivityEvent: string
{
    case LOGIN_ATTEMPT = "login_attempt";
    case LOGIN_SUCCESS = "login_success";
    case LOGIN_FAILURE = "login_failure";
    case MAGIC_LINK_SENT = "magic_link_sent";
    case MAGIC_LINK_CLICKED = "magic_link_clicked";
    case LOGOUT = "logout";
}
