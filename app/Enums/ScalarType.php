<?php

declare(strict_types=1);

namespace App\Enums;

use Stringable;

enum ScalarType: string
{
  case STRING = "string";
  case INTEGER = "integer";
  case BOOLEAN = "boolean";

  /**
   * @param  string|int|bool|Stringable  $value
   */
  public static function fromValue(string|int|bool|Stringable $value): self
  {
    return match (true) {
      is_bool($value) => self::BOOLEAN,
      is_int($value) => self::INTEGER,
      is_string($value) => self::STRING,
      $value instanceof Stringable => self::STRING,
    };
  }
}
