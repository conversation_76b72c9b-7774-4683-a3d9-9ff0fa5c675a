<?php

declare(strict_types=1);

namespace App\Enums;

enum SelectOptionType: string
{
  case REVENUE_RANGE = "revenue_range";
  case EMPLOYEE_COUNT_RANGE = "employee_count_range";
  case GROUPING_HIDING_REASON = "grouping_hiding_reason";

  /**
   * Get a human-readable label for the type
   */
  public function label(): string
  {
    return match ($this) {
      self::REVENUE_RANGE => __("enums.select_option_type.revenue"),
      self::EMPLOYEE_COUNT_RANGE => __("enums.select_option_type.employee_count"),
      self::GROUPING_HIDING_REASON => __("enums.select_option_type.grouping_hiding_reason"),
    };
  }
}
