<?php

declare(strict_types=1);

namespace App\Enums;

enum SelectOptionType: string
{
  case REVENUE_RANGE = "revenue_range";
  case EMPLOYEE_COUNT_RANGE = "employee_count_range";
  case GROUPING_HIDING_REASON = "grouping_hiding_reason";

  /**
   * Get a human-readable label for the type
   */
  public function label(): string
  {
    return match ($this) {
      self::REVENUE_RANGE => __("Revenue Range"),
      self::EMPLOYEE_COUNT_RANGE => __("Employee Count Range"),
      self::GROUPING_HIDING_REASON => __("Grouping Hiding Reason"),
    };
  }
}
