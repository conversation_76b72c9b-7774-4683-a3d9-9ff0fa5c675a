<?php

declare(strict_types=1);

namespace App\Exports\Sheets;

use App\Models\CalculationDefinition;
use App\Models\Year;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

final class EmissionFactorsSheet implements
  FromCollection,
  ShouldAutoSize,
  WithEvents,
  WithHeadings,
  WithStyles,
  WithTitle
{
  /** @var Collection<int, CalculationDefinition> */
  private Collection $calculationDefinitions;

  /** @var array<int, Year> */
  private array $years;

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to value */
  private array $emissionFactorValues;

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to source */
  private array $emissionFactorSources = [];

  /** @var array<int, array<string, mixed>> */
  private array $rowData = [];

  /** @var array<int, int> Maps definition ID to row number */
  private array $rowMapping = [];

  /** @var array<int, string> Maps column letter to year ID */
  private array $yearColumnMap = [];

  /** @var SupportCollection<int, array<string, mixed>> Cached collection data */
  private ?SupportCollection $collectionCache = null;

  /** @var bool Whether to use multi-year format */
  private bool $isMultiYear;

  /**
   * @param  Collection<int, CalculationDefinition>  $calculationDefinitions
   * @param  array<int, Year>  $years
   * @param  array<int, array<int, string>>  $emissionFactorValues
   * @param  array<int, array<int, string>>  $emissionFactorSources
   */
  public function __construct(
    Collection $calculationDefinitions,
    array $years,
    array $emissionFactorValues,
    array $emissionFactorSources = [],
  ) {
    $this->calculationDefinitions = $calculationDefinitions;
    $this->years = $years;
    $this->emissionFactorValues = $emissionFactorValues;
    $this->emissionFactorSources = $emissionFactorSources;

    // Determine if we're in multi-year mode (more than one year)
    $this->isMultiYear = count($this->years) > 1;

    // Build year column mapping
    if ($this->isMultiYear) {
      // Multi-year: years start from column B
      $columnIndex = 1; // B column (A=0, B=1, etc.)
      foreach ($this->years as $yearId => $year) {
        $this->yearColumnMap[$yearId] = $this->getColumnLetter($columnIndex);
        $columnIndex++;
      }
    } else {
      // Single year: value is in column B
      foreach ($this->years as $yearId => $year) {
        $this->yearColumnMap[$yearId] = "B";
        break; // Only one year
      }
    }
  }

  public function title(): string
  {
    return __("exports.emissions.title");
  }

  /**
   * @return array<int, string>
   */
  public function headings(): array
  {
    if ($this->isMultiYear) {
      // Multi-year format: Name | 2022 | 2023 | 2024 | ... | Unit | ID
      $headings = [__("exports.common.name")];

      foreach ($this->years as $year) {
        $headings[] = (string) $year->year;
      }

      $headings[] = __("exports.common.unit");
      $headings[] = "ID"; // Hidden column
    } else {
      // Single year format: Name | Value | Unit | ID
      $headings = [
        __("exports.common.name"),
        __("exports.emissions.fields.value"),
        __("exports.common.unit"),
        "ID", // Hidden column
      ];
    }

    return $headings;
  }

  /**
   * Build the actual collection and mapping in one pass
   *
   * @return SupportCollection<int, array<string, mixed>>
   */
  public function collection(): SupportCollection
  {
    // Return cached collection if already built
    if ($this->collectionCache !== null) {
      return $this->collectionCache;
    }

    $export = new SupportCollection();
    $rowIndex = 2; // Start after headers

    // Clear any existing mapping
    $this->rowMapping = [];
    $this->rowData = [];

    $currentScopeGroup = null;
    $currentCategory = null;

    foreach ($this->calculationDefinitions as $item) {
      // Skip if hidden from emission factor page
      if ($item->hide_from_emission_factor_page) {
        continue;
      }

      $scope = $item->scope;
      $grouping = $item->grouping;
      $category = $item->category;

      if ($scope === null || $grouping === null || $category === null) {
        continue;
      }

      // Create a unique identifier for scope+grouping combination
      $scopeGroupId = $scope->id . "-" . $grouping->id;

      // Add scope-group header row if scope-grouping changes
      if ($currentScopeGroup !== $scopeGroupId) {
        // Add a spacer row if this isn't the first scope-group
        if ($currentScopeGroup !== null) {
          if ($this->isMultiYear) {
            $row = ["name" => ""];
            foreach ($this->years as $year) {
              $row["year_" . $year->id] = null;
            }
            $row["unit"] = null;
            $row["id"] = null;
          } else {
            $row = ["name" => "", "value" => null, "unit" => null, "id" => null];
          }
          $export->push($row);

          $this->rowData[$rowIndex] = ["is_data_row" => false, "is_spacer" => true];
          $rowIndex++;
        }

        if ($this->isMultiYear) {
          $row = [
            "name" => __("exports.scopes.scope_format", [
              "number" => (string) $scope->number,
              "title" => $grouping->translate()->title ?? "",
            ]),
          ];
          foreach ($this->years as $year) {
            $row["year_" . $year->id] = null;
          }
          $row["unit"] = null;
          $row["id"] = null;
        } else {
          $row = [
            "name" => __("exports.scopes.scope_format", [
              "number" => (string) $scope->number,
              "title" => $grouping->translate()->title ?? "",
            ]),
            "value" => null,
            "unit" => null,
            "id" => null,
          ];
        }
        $export->push($row);

        $this->rowData[$rowIndex] = ["is_data_row" => false, "is_scope" => true];
        $rowIndex++;

        $currentScopeGroup = $scopeGroupId;
        $currentCategory = null;
      }

      // Add category header row if category changes
      if ($currentCategory !== $category->id) {
        if ($this->isMultiYear) {
          $row = ["name" => $category->translate()->title ?? ""];
          foreach ($this->years as $year) {
            $row["year_" . $year->id] = null;
          }
          $row["unit"] = null;
          $row["id"] = null;
        } else {
          $row = [
            "name" => $category->translate()->title ?? "",
            "value" => null,
            "unit" => null,
            "id" => null,
          ];
        }
        $export->push($row);

        $this->rowData[$rowIndex] = ["is_data_row" => false, "is_category" => true];
        $rowIndex++;

        $currentCategory = $category->id;
      }

      $emissionFactorName = "";
      if ($item->company_id !== null && $item->custom_name !== null && $item->custom_name !== "") {
        $emissionFactorName = $item->custom_name;
      } else {
        $emissionFactorName = $item->translate()->emission_factor_name ?? "";
      }

      $emissionFactorUnitText = "";
      if ($item->emissionFactorCompoundUnit !== null) {
        $numerator = $item->emissionFactorCompoundUnit->numeratorUnit?->translate()->symbol ?? "";
        $denominator =
          $item->emissionFactorCompoundUnit->denominatorUnit?->translate()->symbol ?? "";
        if ($numerator !== "" && $denominator !== "") {
          $emissionFactorUnitText = "{$numerator}/{$denominator}";
        }
      }

      if ($this->isMultiYear) {
        // Multi-year format
        $row = ["name" => $emissionFactorName];

        // Add year values
        foreach ($this->years as $yearId => $year) {
          $emissionFactorValue = "";
          if (isset($this->emissionFactorValues[$item->id][$yearId])) {
            $emissionFactorValue = $this->emissionFactorValues[$item->id][$yearId];
          }
          $row["year_" . $yearId] = $emissionFactorValue;
        }

        $row["unit"] = $emissionFactorUnitText;
        $row["id"] = $item->id;
      } else {
        // Single year format
        $row = ["name" => $emissionFactorName];

        // Get the single year's value
        $emissionFactorValue = "";
        $yearId = array_key_first($this->years);
        if ($yearId !== null && isset($this->emissionFactorValues[$item->id][$yearId])) {
          $emissionFactorValue = $this->emissionFactorValues[$item->id][$yearId];
        }

        $row["value"] = $emissionFactorValue;
        $row["unit"] = $emissionFactorUnitText;
        $row["id"] = $item->id;
      }

      $export->push($row);

      // Map definition ID to actual row number
      $this->rowMapping[$item->id] = $rowIndex;

      $this->rowData[$rowIndex] = [
        "is_data_row" => true,
        "company_specific" => $item->company_id !== null,
        "definition_id" => $item->id,
      ];
      $rowIndex++;
    }

    // Cache the collection to ensure consistency
    $this->collectionCache = $export;

    return $export;
  }

  /**
   * @return array<int|string, array<string, mixed>>
   */
  public function styles(Worksheet $sheet): array
  {
    // Ensure collection is built so we have row data
    if ($this->collectionCache === null) {
      $this->collection();
    }

    $highestRow = $sheet->getHighestRow();
    $highestColumn = $sheet->getHighestColumn();

    // Hide ID column (last column)
    if ($this->isMultiYear) {
      $idColumn = $this->getColumnLetter(count($this->years) + 2); // After Name, Years, Unit
    } else {
      $idColumn = "D"; // After Name, Value, Unit
    }
    $sheet->getColumnDimension($idColumn)->setVisible(false);

    $styles = [
      // All cells with borders
      "A1:" . $highestColumn . $highestRow => [
        "borders" => [
          "allBorders" => [
            "borderStyle" => Border::BORDER_THIN,
            "color" => ["rgb" => "D8D8D8"],
          ],
        ],
      ],
      // Header row
      1 => [
        "font" => ["bold" => true, "color" => ["rgb" => "FFFFFF"]],
        "fill" => [
          "fillType" => Fill::FILL_SOLID,
          "startColor" => ["rgb" => "002663"],
        ],
        "alignment" => [
          "horizontal" => Alignment::HORIZONTAL_CENTER,
          "vertical" => Alignment::VERTICAL_CENTER,
        ],
      ],
    ];

    // Apply row-specific styles
    foreach ($this->rowData as $row => $data) {
      if (($data["is_spacer"] ?? false) === true) {
        // Spacer rows - no borders, white background
        $styles[$row] = [
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "FFFFFF"],
          ],
        ];
        $styles["A{$row}:" . $highestColumn . "{$row}"] = [
          "borders" => [
            "allBorders" => [
              "borderStyle" => Border::BORDER_NONE,
            ],
          ],
        ];
      } elseif (($data["is_scope"] ?? false) === true) {
        // Scope headers - dark blue background
        $styles[$row] = [
          "font" => ["bold" => true, "color" => ["rgb" => "FFFFFF"]],
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "1D397F"],
          ],
        ];
        // Merge cells from A to the unit column (before ID)
        if ($this->isMultiYear) {
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
        } else {
          $unitColumn = "C";
        }
        $sheet->mergeCells("A{$row}:{$unitColumn}{$row}");
      } elseif (($data["is_category"] ?? false) === true) {
        // Category headers - medium blue background
        $styles[$row] = [
          "font" => ["bold" => true, "italic" => true, "color" => ["rgb" => "FFFFFF"]],
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "002854"],
          ],
        ];
        // Merge cells from A to the unit column (before ID)
        if ($this->isMultiYear) {
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
        } else {
          $unitColumn = "C";
        }
        $sheet->mergeCells("A{$row}:{$unitColumn}{$row}");
      } elseif (($data["is_data_row"] ?? false) === true) {
        // Data rows
        if (($data["company_specific"] ?? false) === true) {
          // Company-specific rows - pink background
          $styles[$row] = [
            "fill" => [
              "fillType" => Fill::FILL_SOLID,
              "startColor" => ["rgb" => "FBEDEF"],
            ],
          ];
        }
      }
    }

    return $styles;
  }

  /**
   * @return array<string, callable>
   */
  public function registerEvents(): array
  {
    return [
      AfterSheet::class => function (AfterSheet $event): void {
        // Ensure collection is built so we have row data
        if ($this->collectionCache === null) {
          $this->collection();
        }

        $sheet = $event->sheet->getDelegate();

        // Set column widths
        $sheet->getColumnDimension("A")->setWidth(35);

        if ($this->isMultiYear) {
          // Set width for year columns
          foreach ($this->yearColumnMap as $column) {
            $sheet->getColumnDimension($column)->setWidth(15);
          }
          // Set width for unit column
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
          $sheet->getColumnDimension($unitColumn)->setWidth(20);
        } else {
          // Value column (B) width
          $sheet->getColumnDimension("B")->setWidth(15);
          $sheet->getColumnDimension("C")->setWidth(20);
        }

        // Add source information as comments
        foreach ($this->rowData as $row => $data) {
          if (($data["is_data_row"] ?? false) !== true) {
            continue;
          }

          $definitionId = $data["definition_id"] ?? null;

          if ($definitionId !== null && isset($this->emissionFactorSources[$definitionId])) {
            if ($this->isMultiYear) {
              // Add source information for each year if available
              foreach ($this->emissionFactorSources[$definitionId] as $yearId => $source) {
                if ($source !== "" && isset($this->yearColumnMap[$yearId])) {
                  $column = $this->yearColumnMap[$yearId];
                  $comment = $sheet->getComment("{$column}{$row}");
                  $richText = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
                  $richText->createText(
                    __("exports.emissions.labels.source", ["source" => $source]),
                  );
                  $comment->setText($richText);
                }
              }
            } else {
              // Single year - add comment to value column (B)
              $yearId = array_key_first($this->years);
              if ($yearId !== null && isset($this->emissionFactorSources[$definitionId][$yearId])) {
                $source = $this->emissionFactorSources[$definitionId][$yearId];
                if ($source !== "") {
                  $comment = $sheet->getComment("B{$row}");
                  $richText = new \PhpOffice\PhpSpreadsheet\RichText\RichText();
                  $richText->createText(
                    __("exports.emissions.labels.source", ["source" => $source]),
                  );
                  $comment->setText($richText);
                }
              }
            }
          }
        }
      },
    ];
  }

  /**
   * Get the row mapping for external use
   *
   * @return array<int, int>
   */
  public function getRowMapping(): array
  {
    // Ensure collection has been built so mapping is populated
    if ($this->collectionCache === null) {
      $this->collection();
    }

    return $this->rowMapping;
  }

  /**
   * Get the year column mapping for external use
   *
   * @return array<int, string>
   */
  public function getYearColumnMap(): array
  {
    return $this->yearColumnMap;
  }

  /**
   * Convert column index to Excel column letter
   */
  private function getColumnLetter(int $index): string
  {
    $letter = "";
    while ($index >= 0) {
      $letter = chr(65 + ($index % 26)) . $letter;
      $index = (int) ($index / 26) - 1;
      if ($index < 0) {
        break;
      }
    }

    return $letter;
  }
}
