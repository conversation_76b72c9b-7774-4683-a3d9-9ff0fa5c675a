<?php

declare(strict_types=1);

namespace App\Exports\Sheets;

use App\Models\CalculationDefinition;
use App\Models\Unit;
use App\Models\Year;
use App\Services\EmissionFormulaService;
use Brick\Math\BigDecimal;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use InvalidArgumentException;
use LogicException;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use ReflectionException;
use RuntimeException;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\ExpressionLanguage\Node\BinaryNode;
use Symfony\Component\ExpressionLanguage\Node\ConstantNode;
use Symfony\Component\ExpressionLanguage\Node\FunctionNode;
use Symfony\Component\ExpressionLanguage\Node\NameNode;
use Symfony\Component\ExpressionLanguage\Node\Node;
use Symfony\Component\ExpressionLanguage\Node\UnaryNode;

final class ResultsSheet implements
  FromCollection,
  ShouldAutoSize,
  WithEvents,
  WithHeadings,
  WithStyles,
  WithTitle
{
  /** @var Collection<int, CalculationDefinition> */
  private Collection $calculationDefinitions;

  /** @var array<int, Year> */
  private array $years;

  private ?Unit $resultUnit;

  /** @var array<int, array<string, mixed>> */
  private array $rowData = [];

  /** @var array<int, int> Data sheet row mapping */
  private array $dataRowMapping = [];

  /** @var array<int, int> Emission factors sheet row mapping */
  private array $emissionRowMapping = [];

  /** @var array<int, string> Maps year ID to column letter */
  private array $yearColumnMap = [];

  /** @var bool Whether to use multi-year format */
  private bool $isMultiYear;

  /** Sheet titles for formula references */
  private string $dataSheetTitle;

  private string $emissionSheetTitle;

  /**
   * @param  Collection<int, CalculationDefinition>  $calculationDefinitions
   * @param  array<int, Year>  $years
   * @param  array<int, int>  $dataRowMapping
   * @param  array<int, int>  $emissionRowMapping
   * @param  string  $dataSheetTitle  The title of the data sheet
   * @param  string  $emissionSheetTitle  The title of the emission factors sheet
   */
  public function __construct(
    Collection $calculationDefinitions,
    array $years,
    ?Unit $resultUnit,
    array $dataRowMapping = [],
    array $emissionRowMapping = [],
    string $dataSheetTitle = "Kulutustiedot",
    string $emissionSheetTitle = "Päästökertoimet",
  ) {
    $this->calculationDefinitions = $calculationDefinitions;
    $this->years = $years;
    $this->resultUnit = $resultUnit;
    $this->dataRowMapping = $dataRowMapping;
    $this->emissionRowMapping = $emissionRowMapping;
    $this->dataSheetTitle = $dataSheetTitle;
    $this->emissionSheetTitle = $emissionSheetTitle;

    // Determine if we're in multi-year mode (more than one year)
    $this->isMultiYear = count($this->years) > 1;

    // Build year column mapping
    if ($this->isMultiYear) {
      // Multi-year: years start from column B
      $columnIndex = 1; // B column (A=0, B=1, etc.)
      foreach ($this->years as $yearId => $year) {
        $this->yearColumnMap[$yearId] = $this->getColumnLetter($columnIndex);
        $columnIndex++;
      }
    } else {
      // Single year: result is in column B
      foreach ($this->years as $yearId => $year) {
        $this->yearColumnMap[$yearId] = "B";
        break; // Only one year
      }
    }
  }

  public function title(): string
  {
    return __("exports.results.title");
  }

  /**
   * @return array<int, string>
   */
  public function headings(): array
  {
    if ($this->isMultiYear) {
      // Multi-year format: Name | 2022 | 2023 | 2024 | ... | Unit
      $headings = [__("exports.common.name")];

      foreach ($this->years as $year) {
        $headings[] = (string) $year->year;
      }

      $headings[] = __("exports.common.unit");
    } else {
      // Single year format: Name | Result | Unit
      $headings = [
        __("exports.common.name"),
        __("exports.results.fields.result"),
        __("exports.common.unit"),
      ];
    }

    return $headings;
  }

  /**
   * @return SupportCollection<int, array<string, mixed>>
   */
  public function collection(): SupportCollection
  {
    $export = new SupportCollection();
    $rowIndex = 2;

    $currentScopeGroup = null;
    $currentCategory = null;

    foreach ($this->calculationDefinitions as $item) {
      // Skip if hidden from results page
      if ($item->hide_from_results_page) {
        continue;
      }

      $scope = $item->scope;
      $grouping = $item->grouping;
      $category = $item->category;

      if ($scope === null || $grouping === null || $category === null) {
        continue;
      }

      // Create a unique identifier for scope+grouping combination
      $scopeGroupId = $scope->id . "-" . $grouping->id;

      // Add scope-group header row if scope-grouping changes
      if ($currentScopeGroup !== $scopeGroupId) {
        // Add a spacer row if this isn't the first scope-group
        if ($currentScopeGroup !== null) {
          if ($this->isMultiYear) {
            $row = ["name" => ""];
            foreach ($this->years as $year) {
              $row["year_" . $year->id] = null;
            }
            $row["unit"] = null;
          } else {
            $row = ["name" => "", "result" => null, "unit" => null];
          }
          $export->push($row);

          $this->rowData[$rowIndex] = ["is_data_row" => false, "is_spacer" => true];
          $rowIndex++;
        }

        if ($this->isMultiYear) {
          $row = [
            "name" => __("exports.scopes.scope_format", [
              "number" => (string) $scope->number,
              "title" => $grouping->translate()->title ?? "",
            ]),
          ];
          foreach ($this->years as $year) {
            $row["year_" . $year->id] = null;
          }
          $row["unit"] = null;
        } else {
          $row = [
            "name" => __("exports.scopes.scope_format", [
              "number" => (string) $scope->number,
              "title" => $grouping->translate()->title ?? "",
            ]),
            "result" => null,
            "unit" => null,
          ];
        }
        $export->push($row);

        $this->rowData[$rowIndex] = ["is_data_row" => false, "is_scope" => true];
        $rowIndex++;

        $currentScopeGroup = $scopeGroupId;
        $currentCategory = null;
      }

      // Add category header row if category changes
      if ($currentCategory !== $category->id) {
        if ($this->isMultiYear) {
          $row = ["name" => $category->translate()->title ?? ""];
          foreach ($this->years as $year) {
            $row["year_" . $year->id] = null;
          }
          $row["unit"] = null;
        } else {
          $row = [
            "name" => $category->translate()->title ?? "",
            "result" => null,
            "unit" => null,
          ];
        }
        $export->push($row);

        $this->rowData[$rowIndex] = ["is_data_row" => false, "is_category" => true];
        $rowIndex++;

        $currentCategory = $category->id;
      }

      $resultName = "";
      if ($item->company_id !== null && $item->custom_name !== null && $item->custom_name !== "") {
        $resultName = $item->custom_name;
      } else {
        $resultName = $item->translate()->result_name ?? "";
      }

      // Determine result unit
      $resultUnitSymbol = "";
      $resultUnitId = null;
      if ($this->resultUnit !== null) {
        $resultUnitSymbol = $this->resultUnit->translate()->symbol ?? "";
        $resultUnitId = $this->resultUnit->id;
      }

      // Get the row references for this definition
      $dataRow = $this->dataRowMapping[$item->id] ?? null;
      $emissionRow = $this->emissionRowMapping[$item->id] ?? null;

      if ($this->isMultiYear) {
        // Multi-year format
        $row = ["name" => $resultName];

        // Add placeholder for year results (will be replaced with formulas)
        foreach ($this->years as $year) {
          $row["year_" . $year->id] = "";
        }

        $row["unit"] = $resultUnitSymbol;
      } else {
        // Single year format
        $row = [
          "name" => $resultName,
          "result" => "", // Will be replaced with formula
          "unit" => $resultUnitSymbol,
        ];
      }

      $export->push($row);

      $numeratorUnitId = $item->emissionFactorCompoundUnit?->numerator_unit_id;

      $this->rowData[$rowIndex] = [
        "is_data_row" => true,
        "definition_id" => $item->id,
        "numerator_unit_id" => $numeratorUnitId,
        "result_unit_id" => $resultUnitId,
        "company_specific" => $item->company_id !== null,
        "has_formula" => $item->data_formula !== null && $item->data_formula !== "",
        "formula" => $item->data_formula,
        "data_row" => $dataRow,
        "emission_row" => $emissionRow,
      ];
      $rowIndex++;
    }

    return $export;
  }

  /**
   * @return array<int|string, array<string, mixed>>
   */
  public function styles(Worksheet $sheet): array
  {
    $highestRow = $sheet->getHighestRow();
    $highestColumn = $sheet->getHighestColumn();

    $styles = [
      // All cells with borders
      "A1:" . $highestColumn . $highestRow => [
        "borders" => [
          "allBorders" => [
            "borderStyle" => Border::BORDER_THIN,
            "color" => ["rgb" => "D8D8D8"],
          ],
        ],
      ],
      // Header row
      1 => [
        "font" => ["bold" => true, "color" => ["rgb" => "FFFFFF"]],
        "fill" => [
          "fillType" => Fill::FILL_SOLID,
          "startColor" => ["rgb" => "002663"],
        ],
        "alignment" => [
          "horizontal" => Alignment::HORIZONTAL_CENTER,
          "vertical" => Alignment::VERTICAL_CENTER,
        ],
      ],
    ];

    // Apply row-specific styles
    foreach ($this->rowData as $row => $data) {
      if (($data["is_spacer"] ?? false) === true) {
        // Spacer rows - no borders, white background
        $styles[$row] = [
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "FFFFFF"],
          ],
        ];
        $styles["A{$row}:" . $highestColumn . "{$row}"] = [
          "borders" => [
            "allBorders" => [
              "borderStyle" => Border::BORDER_NONE,
            ],
          ],
        ];
      } elseif (($data["is_scope"] ?? false) === true) {
        // Scope headers - dark blue background
        $styles[$row] = [
          "font" => ["bold" => true, "color" => ["rgb" => "FFFFFF"]],
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "1D397F"],
          ],
        ];
        // Merge cells from A to the unit column
        if ($this->isMultiYear) {
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
        } else {
          $unitColumn = "C";
        }
        $sheet->mergeCells("A{$row}:{$unitColumn}{$row}");
      } elseif (($data["is_category"] ?? false) === true) {
        // Category headers - medium blue background
        $styles[$row] = [
          "font" => ["bold" => true, "italic" => true, "color" => ["rgb" => "FFFFFF"]],
          "fill" => [
            "fillType" => Fill::FILL_SOLID,
            "startColor" => ["rgb" => "002854"],
          ],
        ];
        // Merge cells from A to the unit column
        if ($this->isMultiYear) {
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
        } else {
          $unitColumn = "C";
        }
        $sheet->mergeCells("A{$row}:{$unitColumn}{$row}");
      } elseif (($data["is_data_row"] ?? false) === true) {
        // Data rows
        if (($data["company_specific"] ?? false) === true) {
          // Company-specific rows - pink background
          $styles[$row] = [
            "fill" => [
              "fillType" => Fill::FILL_SOLID,
              "startColor" => ["rgb" => "FBEDEF"],
            ],
          ];
        }

        if ($this->isMultiYear) {
          // Highlight result columns (all year columns)
          foreach ($this->yearColumnMap as $column) {
            $styles["{$column}{$row}"] = [
              "font" => ["bold" => true],
              "fill" => [
                "fillType" => Fill::FILL_SOLID,
                "startColor" => ["rgb" => "E8F1F9"], // Light blue for all results
              ],
            ];
          }
        } else {
          // Single year - highlight result column B
          $styles["B{$row}"] = [
            "font" => ["bold" => true],
            "fill" => [
              "fillType" => Fill::FILL_SOLID,
              "startColor" => ["rgb" => "E8F1F9"], // Light blue for result
            ],
          ];
        }
      }
    }

    return $styles;
  }

  /**
   * @return array<string, callable>
   */
  public function registerEvents(): array
  {
    return [
      AfterSheet::class => function (AfterSheet $event): void {
        $sheet = $event->sheet->getDelegate();

        // Set column widths
        $sheet->getColumnDimension("A")->setWidth(35);

        if ($this->isMultiYear) {
          // Set width for year columns
          foreach ($this->yearColumnMap as $column) {
            $sheet->getColumnDimension($column)->setWidth(20);
          }
          // Set width for unit column
          $unitColumn = $this->getColumnLetter(count($this->years) + 1);
          $sheet->getColumnDimension($unitColumn)->setWidth(12);
        } else {
          // Result column (B) width
          $sheet->getColumnDimension("B")->setWidth(20);
          $sheet->getColumnDimension("C")->setWidth(12);
        }

        // Add formulas for each data row
        foreach ($this->rowData as $row => $data) {
          // Skip non-data rows
          if (($data["is_data_row"] ?? false) !== true) {
            continue;
          }

          $definitionId = is_numeric($data["definition_id"] ?? null)
            ? (int) $data["definition_id"]
            : 0;

          // Get row mappings from our stored data with proper type checking
          $dataRowRaw = $data["data_row"] ?? null;
          $emissionRowRaw = $data["emission_row"] ?? null;

          $dataRow = is_int($dataRowRaw) ? $dataRowRaw : null;
          $emissionRow = is_int($emissionRowRaw) ? $emissionRowRaw : null;

          if ($this->isMultiYear) {
            // Set formulas for each year column
            foreach ($this->years as $yearId => $year) {
              if (!isset($this->yearColumnMap[$yearId])) {
                continue;
              }

              $column = $this->yearColumnMap[$yearId];
              $cellRef = $column . (string) $row;

              if (
                ($data["has_formula"] ?? false) === true &&
                isset($data["formula"]) &&
                is_string($data["formula"])
              ) {
                // Convert the custom formula to Excel formula for this specific year
                try {
                  $excelFormula = $this->convertFormulaToExcel($data["formula"], $yearId);

                  if ($excelFormula !== null) {
                    // Apply unit conversion to formula results
                    $fromUnitIdRaw = $data["numerator_unit_id"] ?? null;
                    $toUnitIdRaw = $data["result_unit_id"] ?? null;

                    $fromUnitId = is_numeric($fromUnitIdRaw) ? (int) $fromUnitIdRaw : null;
                    $toUnitId = is_numeric($toUnitIdRaw) ? (int) $toUnitIdRaw : null;

                    // Get conversion factor from natural unit to result unit
                    $conversionFactor = $this->getUnitConversionFactor($fromUnitId, $toUnitId);

                    if ($conversionFactor === null) {
                      // Missing conversion - show error
                      $sheet->setCellValue(
                        $cellRef,
                        __("exports.results.errors.missing_conversion"),
                      );
                      $this->setErrorStyle($sheet, $cellRef);
                    } elseif (!$conversionFactor->isEqualTo(1)) {
                      // Apply conversion factor to the entire formula result
                      $convertedFormula = $this->applyConversionToFormula(
                        $excelFormula,
                        $conversionFactor,
                      );
                      $sheet->setCellValue($cellRef, $convertedFormula);
                    } else {
                      // No conversion needed, use formula as-is
                      $sheet->setCellValue($cellRef, $excelFormula);
                    }
                  } else {
                    // Fallback if formula conversion fails - use direct references
                    $this->setStandardFormula(
                      $sheet,
                      $cellRef,
                      $dataRow,
                      $emissionRow,
                      $data,
                      $yearId,
                    );
                  }
                } catch (\Symfony\Component\ExpressionLanguage\SyntaxError $e) {
                  // Fallback if formula has syntax errors
                  $this->setStandardFormula(
                    $sheet,
                    $cellRef,
                    $dataRow,
                    $emissionRow,
                    $data,
                    $yearId,
                  );
                } catch (Exception $e) {
                  // Fallback if formula conversion fails unexpectedly
                  report($e);
                  $this->setStandardFormula(
                    $sheet,
                    $cellRef,
                    $dataRow,
                    $emissionRow,
                    $data,
                    $yearId,
                  );
                }
              } else {
                // Standard calculation: data * emission_factor * conversion_factor
                $this->setStandardFormula($sheet, $cellRef, $dataRow, $emissionRow, $data, $yearId);
              }
            }
          } else {
            // Set formula in column B
            $yearId = array_key_first($this->years);
            $cellRef = "B" . (string) $row;

            if (
              $yearId !== null &&
              ($data["has_formula"] ?? false) === true &&
              isset($data["formula"]) &&
              is_string($data["formula"])
            ) {
              // Convert the custom formula to Excel formula
              try {
                $excelFormula = $this->convertFormulaToExcel($data["formula"], $yearId);

                if ($excelFormula !== null) {
                  // Apply unit conversion to formula results
                  $fromUnitIdRaw = $data["numerator_unit_id"] ?? null;
                  $toUnitIdRaw = $data["result_unit_id"] ?? null;

                  $fromUnitId = is_numeric($fromUnitIdRaw) ? (int) $fromUnitIdRaw : null;
                  $toUnitId = is_numeric($toUnitIdRaw) ? (int) $toUnitIdRaw : null;

                  // Get conversion factor from natural unit to result unit
                  $conversionFactor = $this->getUnitConversionFactor($fromUnitId, $toUnitId);

                  if ($conversionFactor === null) {
                    // Missing conversion - show error
                    $sheet->setCellValue($cellRef, __("exports.results.errors.missing_conversion"));
                    $this->setErrorStyle($sheet, $cellRef);
                  } elseif (!$conversionFactor->isEqualTo(1)) {
                    // Apply conversion factor to the entire formula result
                    $convertedFormula = $this->applyConversionToFormula(
                      $excelFormula,
                      $conversionFactor,
                    );
                    $sheet->setCellValue($cellRef, $convertedFormula);
                  } else {
                    // No conversion needed, use formula as-is
                    $sheet->setCellValue($cellRef, $excelFormula);
                  }
                } else {
                  // Fallback if formula conversion fails - use direct references
                  $this->setStandardFormula(
                    $sheet,
                    $cellRef,
                    $dataRow,
                    $emissionRow,
                    $data,
                    $yearId,
                  );
                }
              } catch (\Symfony\Component\ExpressionLanguage\SyntaxError $e) {
                // Fallback if formula has syntax errors
                $this->setStandardFormula($sheet, $cellRef, $dataRow, $emissionRow, $data, $yearId);
              } catch (Exception $e) {
                // Fallback if formula conversion fails unexpectedly
                report($e);
                $this->setStandardFormula($sheet, $cellRef, $dataRow, $emissionRow, $data, $yearId);
              }
            } else {
              // Standard calculation: data * emission_factor * conversion_factor
              if ($yearId !== null) {
                $this->setStandardFormula($sheet, $cellRef, $dataRow, $emissionRow, $data, $yearId);
              } else {
                $sheet->setCellValue($cellRef, "0");
              }
            }
          }
        }
      },
    ];
  }

  /**
   * Convert column index to Excel column letter
   */
  private function getColumnLetter(int $index): string
  {
    $letter = "";
    while ($index >= 0) {
      $letter = chr(65 + ($index % 26)) . $letter;
      $index = (int) ($index / 26) - 1;
      if ($index < 0) {
        break;
      }
    }

    return $letter;
  }

  /**
   * Set error style for a cell
   */
  private function setErrorStyle(Worksheet $sheet, string $cellRef): void
  {
    // Highlight the cell in red
    $sheet
      ->getStyle($cellRef)
      ->getFill()
      ->setFillType(Fill::FILL_SOLID)
      ->getStartColor()
      ->setRGB("FAE5E8"); // pink-100

    // Set font color to red
    $sheet->getStyle($cellRef)->getFont()->getColor()->setRGB("DC1B1B"); // red-600
  }

  /**
   * Set standard formula for non-custom calculations
   *
   * @param  array<string, mixed>  $data  Row data containing unit IDs and other metadata
   */
  private function setStandardFormula(
    Worksheet $sheet,
    string $cellRef,
    ?int $dataRow,
    ?int $emissionRow,
    array $data,
    int $yearId,
  ): void {
    if ($dataRow !== null && $emissionRow !== null && isset($this->yearColumnMap[$yearId])) {
      // Convert from the natural result unit to the configured result unit
      $fromUnitIdRaw = $data["numerator_unit_id"] ?? null;
      $toUnitIdRaw = $data["result_unit_id"] ?? null;

      $fromUnitId = is_numeric($fromUnitIdRaw) ? (int) $fromUnitIdRaw : null;
      $toUnitId = is_numeric($toUnitIdRaw) ? (int) $toUnitIdRaw : null;

      // Get conversion factor from natural unit to result unit
      $conversionFactor = $this->getUnitConversionFactor($fromUnitId, $toUnitId);

      // Get the column letter for this year
      $yearColumn = $this->yearColumnMap[$yearId];

      // Build references to Data and Emission Factors sheets
      $dataRef = "'" . $this->dataSheetTitle . "'!" . $yearColumn . $dataRow;
      $emissionRef = "'" . $this->emissionSheetTitle . "'!" . $yearColumn . $emissionRow;

      if ($conversionFactor === null) {
        // Missing conversion - show error
        $sheet->setCellValue($cellRef, __("exports.results.errors.missing_conversion"));
        $this->setErrorStyle($sheet, $cellRef);
      } elseif (!$conversionFactor->isEqualTo(1)) {
        // Build the base formula
        $baseFormula = "=" . $dataRef . "*" . $emissionRef;
        // Apply conversion using the helper method for better accuracy
        $convertedFormula = $this->applyConversionToFormula($baseFormula, $conversionFactor);
        $sheet->setCellValue($cellRef, $convertedFormula);
      } else {
        $sheet->setCellValue($cellRef, "=" . $dataRef . "*" . $emissionRef);
      }
    } else {
      $sheet->setCellValue($cellRef, "0");
    }
  }

  /**
   * Convert custom formula AST to Excel formula for a specific year
   */
  private function convertFormulaToExcel(string $formula, int $yearId): ?string
  {
    try {
      // Convert the formula to Excel syntax using AST
      $excelFormula = $this->convertAstToExcel($formula, $yearId);

      // Ensure it starts with =
      if (!str_starts_with($excelFormula, "=")) {
        $excelFormula = "=" . $excelFormula;
      }

      return $excelFormula;
    } catch (\Symfony\Component\ExpressionLanguage\SyntaxError $e) {
      // Expected error for invalid formulas
      return null;
    } catch (RuntimeException | LogicException $e) {
      // Expected errors from AST conversion
      return null;
    } catch (Exception $e) {
      // Unexpected error - report it
      report($e);

      return null;
    }
  }

  /**
   * Convert AST to Excel formula for a specific year
   *
   * @throws ReflectionException
   * @throws RuntimeException
   * @throws LogicException
   */
  private function convertAstToExcel(string $formula, int $yearId): string
  {
    // We need to create an ExpressionLanguage with all the functions that might be used
    $formulaService = app(EmissionFormulaService::class);

    // Create expression language with all functions including data/kerroin
    $expressionLanguage = $formulaService->createExpressionLanguageWithDataFunctions([
      "data_values" => [],
      "emission_factors" => [],
    ]);

    // Parse the formula to get AST
    // Include both "year" and "definitions" as valid variables
    $parsed = $expressionLanguage->parse($formula, ["year", "definitions", "_"]);
    $ast = $parsed->getNodes();

    // Convert AST to Excel formula
    return $this->nodeToExcel($ast, $yearId);
  }

  /**
   * Convert an AST node to Excel formula syntax for a specific year
   */
  private function nodeToExcel(Node $node, int $yearId): string
  {
    // Handle ConstantNode
    // @phpstan-ignore-next-line
    if ($node instanceof ConstantNode) {
      $value = $node->attributes["value"] ?? 0;
      if (is_string($value)) {
        // Keep strings quoted for Excel
        return '"' . str_replace('"', '""', $value) . '"';
      }
      if (is_scalar($value)) {
        return (string) $value;
      }

      return "0";
    }

    // Handle NameNode
    // @phpstan-ignore-next-line
    if ($node instanceof NameNode) {
      $name = $node->attributes["name"] ?? "";

      // Handle variable references
      if ($name === "year") {
        $year = $this->years[$yearId] ?? null;
        if ($year !== null) {
          return (string) $year->year;
        }

        return "YEAR(TODAY())";
      }

      // Handle underscore variable (used in map/filter operations)
      if ($name === "_") {
        // This will be replaced contextually in map/filter operations
        return "@"; // Excel's implicit intersection operator
      }

      // Handle definitions reference
      if ($name === "definitions") {
        // Return metadata that indicates we want all definitions
        return "@DEFINITIONS:ALL";
      }

      return "0";
    }

    // Handle BinaryNode
    // @phpstan-ignore-next-line
    if ($node instanceof BinaryNode) {
      $operator = $node->attributes["operator"] ?? "+";
      $leftNode = $node->nodes["left"] ?? null;
      $rightNode = $node->nodes["right"] ?? null;

      if (!$leftNode instanceof Node || !$rightNode instanceof Node) {
        return "0";
      }

      $left = $this->nodeToExcel($leftNode, $yearId);
      $right = $this->nodeToExcel($rightNode, $yearId);

      // Map operators to Excel
      $operatorStr = is_scalar($operator) ? (string) $operator : "+";
      $excelOp = match ($operatorStr) {
        "**" => "^",
        "and" => "*",
        "or" => "+",
        "==" => "=",
        "!=" => "<>",
        "<=" => "<=",
        ">=" => ">=",
        "<" => "<",
        ">" => ">",
        default => $operatorStr,
      };

      // Special handling for comparison operators
      if (in_array($excelOp, ["=", "<>", "<=", ">=", "<", ">"], true)) {
        return "({$left}{$excelOp}{$right})";
      }

      return "({$left}{$excelOp}{$right})";
    }

    // Handle UnaryNode
    // @phpstan-ignore-next-line
    if ($node instanceof UnaryNode) {
      $operator = $node->attributes["operator"] ?? "";
      $operandNode = $node->nodes["node"] ?? null;

      if (!$operandNode instanceof Node) {
        return "0";
      }

      $operand = $this->nodeToExcel($operandNode, $yearId);

      if ($operator === "-") {
        return "(-{$operand})";
      }
      if ($operator === "not" || $operator === "!") {
        return "NOT({$operand})";
      }

      return $operand;
    }

    // Handle FunctionNode - This is where all the complex functions are handled
    // @phpstan-ignore-next-line
    if ($node instanceof FunctionNode) {
      $name = $node->attributes["name"] ?? "";
      $arguments = $node->nodes["arguments"] ?? null;

      // Convert arguments first
      $args = [];
      $rawArgNodes = []; // Keep raw nodes for special processing
      if ($arguments instanceof Node) {
        foreach ($arguments->nodes as $argNode) {
          if ($argNode instanceof Node) {
            $rawArgNodes[] = $argNode;
            $args[] = $this->nodeToExcel($argNode, $yearId);
          }
        }
      }

      $nameStr = is_string($name) ? mb_strtolower($name) : "";

      // Handle all functions
      switch ($nameStr) {
        case "data":
          if (isset($rawArgNodes[0])) {
            // @phpstan-ignore instanceof.internalClass
            if ($rawArgNodes[0] instanceof ConstantNode) {
              $value = $rawArgNodes[0]->attributes["value"] ?? 0;
              $definitionId = is_numeric($value) ? (int) $value : 0;

              if (isset($this->dataRowMapping[$definitionId], $this->yearColumnMap[$yearId])) {
                $targetRow = $this->dataRowMapping[$definitionId];
                $yearColumn = $this->yearColumnMap[$yearId];

                return "'" . $this->dataSheetTitle . "'!" . $yearColumn . $targetRow;
              }
            }
          }
          // If it's a dynamic reference (from get function), pass it through
          if (isset($args[0]) && mb_strpos($args[0], "INDEX") !== false) {
            // This is already a cell reference from get() function
            $yearColumn = $this->yearColumnMap[$yearId] ?? "B";

            return "INDEX('" .
              $this->dataSheetTitle .
              "'!" .
              $yearColumn .
              ":" .
              $yearColumn .
              "," .
              $args[0] .
              ")";
          }

          return "0";

        case "kerroin":
          if (isset($rawArgNodes[0])) {
            // @phpstan-ignore instanceof.internalClass
            if ($rawArgNodes[0] instanceof ConstantNode) {
              $value = $rawArgNodes[0]->attributes["value"] ?? 0;
              $definitionId = is_numeric($value) ? (int) $value : 0;

              if (isset($this->emissionRowMapping[$definitionId], $this->yearColumnMap[$yearId])) {
                $targetRow = $this->emissionRowMapping[$definitionId];
                $yearColumn = $this->yearColumnMap[$yearId];

                return "'" . $this->emissionSheetTitle . "'!" . $yearColumn . $targetRow;
              }
            }
          }
          // If it's a dynamic reference (from get function), pass it through
          if (isset($args[0]) && mb_strpos($args[0], "INDEX") !== false) {
            // This is already a cell reference from get() function
            $yearColumn = $this->yearColumnMap[$yearId] ?? "B";

            return "INDEX('" .
              $this->emissionSheetTitle .
              "'!" .
              $yearColumn .
              ":" .
              $yearColumn .
              "," .
              $args[0] .
              ")";
          }

          return "0";

        case "add":
          if (count($args) > 0) {
            // Filter out any "0" values to avoid unnecessary additions
            $nonZeroArgs = [];
            foreach ($args as $arg) {
              if ($arg !== "0" && $arg !== "(0)") {
                $nonZeroArgs[] = $arg;
              }
            }

            if (count($nonZeroArgs) > 0) {
              return "(" . implode("+", $nonZeroArgs) . ")";
            }
          }

          return "0";

        case "sum":
          if (count($args) > 0) {
            // For sum(), if it's a single SUMPRODUCT argument, don't wrap it
            if (count($args) === 1 && mb_strpos($args[0], "SUMPRODUCT") === 0) {
              return $args[0];
            }

            // Filter out any "0" values
            $nonZeroArgs = [];
            foreach ($args as $arg) {
              if ($arg !== "0" && $arg !== "(0)") {
                $nonZeroArgs[] = $arg;
              }
            }

            if (count($nonZeroArgs) > 0) {
              // If we have a single argument that's already a sum, just return it
              if (count($nonZeroArgs) === 1) {
                // Fixed: Direct access since we know the array has exactly one element
                return $nonZeroArgs[0];
              }

              return "(" . implode("+", $nonZeroArgs) . ")";
            }
          }

          return "0";

        case "multiply":
          if (count($args) > 0) {
            return "(" . implode("*", $args) . ")";
          }

          return "1";

        case "subtract":
          if (isset($args[0], $args[1])) {
            return "(" . $args[0] . "-" . $args[1] . ")";
          }

          return "0";

        case "divide":
          if (isset($args[0], $args[1])) {
            return "IF(" . $args[1] . "=0,0," . $args[0] . "/" . $args[1] . ")";
          }

          return "0";

        case "where":
          // where(range, property, operator, value)
          // This needs to be evaluated in context - pass metadata for later processing
          if (count($rawArgNodes) >= 3) {
            $inputData = $args[0] ?? "";

            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              $property = is_string($propValue) ? $propValue : "";
            }

            $operator = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[2]) && $rawArgNodes[2] instanceof ConstantNode) {
              $opValue = $rawArgNodes[2]->attributes["value"] ?? "";
              $operator = is_string($opValue) ? $opValue : "";
            }

            $value = null;
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[3]) && $rawArgNodes[3] instanceof ConstantNode) {
              $value = $rawArgNodes[3]->attributes["value"] ?? null;
            }

            // Check if we're filtering definitions
            if ($inputData === "@DEFINITIONS:ALL") {
              // Return metadata about the filter
              // Fixed: Handle mixed type properly
              if ($value === null) {
                return "@WHERE:" . $property . ":" . $operator . ":null";
              }
              if (is_scalar($value)) {
                return "@WHERE:" . $property . ":" . $operator . ":" . (string) $value;
              }

              return "@WHERE:" . $property . ":" . $operator . ":null";
            }

            // For other inputs, pass through with filter info
            // Fixed: Handle mixed type properly
            if ($value === null) {
              return "@WHERE:" . $property . ":" . $operator . ":null";
            }
            if (is_scalar($value)) {
              return "@WHERE:" . $property . ":" . $operator . ":" . (string) $value;
            }

            return "@WHERE:" . $property . ":" . $operator . ":null";
          }

          return isset($args[0]) ? $args[0] : "0";

        case "groupby":
          // groupBy(range, property)
          // Process the where clause if it exists and group the results
          if (count($rawArgNodes) >= 2) {
            $inputData = isset($args[0]) ? $args[0] : "";

            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              $property = is_string($propValue) ? $propValue : "";
            }

            // Check if input is from WHERE clause
            if (mb_strpos($inputData, "@WHERE:") === 0) {
              // Parse the WHERE metadata
              $parts = explode(":", mb_substr($inputData, 7));
              // Fixed: explode() always returns at least one element, so $parts[0] always exists
              $whereProperty = $parts[0];
              $whereOperator = isset($parts[1]) ? $parts[1] : "";
              $whereValue = isset($parts[2]) ? $parts[2] : "null";

              // Apply the filter and group
              if (
                $whereProperty === "link_id" &&
                $whereOperator === "!=" &&
                $property === "link_id"
              ) {
                // Return metadata for the grouped definitions
                return "@GROUPED:link_id:not_null";
              }
            }

            return "@GROUPED:" . $property . ":all";
          }

          return "0";

        case "values":
          // values(grouped_data) - just passes through the metadata
          if (isset($args[0])) {
            return $args[0];
          }

          return "0";

        case "map":
          // map(array, formula_string)
          // This is where we actually build the Excel formula
          if (count($rawArgNodes) >= 2) {
            $arrayMetadata = isset($args[0]) ? $args[0] : "";

            $mapFormula = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $formulaValue = $rawArgNodes[1]->attributes["value"] ?? "";
              if (is_scalar($formulaValue)) {
                $mapFormula = (string) $formulaValue;
              }
            }

            // Check if we're mapping over grouped link_ids
            if ($arrayMetadata === "@GROUPED:link_id:not_null") {
              // Build Excel formulas for each group
              $sumParts = [];

              // Get all definitions with non-null link_id and group them
              $linkedDefs = [];
              foreach ($this->calculationDefinitions as $def) {
                if ($def->link_id !== null) {
                  $linkedDefs[] = $def;
                }
              }

              // If no linked definitions found, return 0
              if (count($linkedDefs) === 0) {
                return "0";
              }

              // Group by link_id manually
              $grouped = [];
              foreach ($linkedDefs as $def) {
                $linkId = $def->link_id;
                if (!isset($grouped[$linkId])) {
                  $grouped[$linkId] = [];
                }
                $grouped[$linkId][] = $def;
              }

              // Parse the map formula into AST
              if ($mapFormula !== "") {
                try {
                  $formulaService = app(EmissionFormulaService::class);
                  $expressionLanguage = $formulaService->createExpressionLanguageWithDataFunctions([
                    "data_values" => [],
                    "emission_factors" => [],
                  ]);

                  // Parse the inner formula
                  $innerParsed = $expressionLanguage->parse($mapFormula, ["_"]);
                  $innerAst = $innerParsed->getNodes();

                  // Process each group
                  foreach ($grouped as $linkId => $group) {
                    // Convert the inner AST to Excel for this specific group
                    $groupFormula = $this->nodeToExcelWithContext($innerAst, $yearId, $group);

                    if ($groupFormula !== "0" && $groupFormula !== "(0)") {
                      $sumParts[] = $groupFormula;
                    }
                  }
                } catch (\Symfony\Component\ExpressionLanguage\SyntaxError $e) {
                  // Syntax error in formula - return 0
                  return "0";
                } catch (Exception $e) {
                  // Log unexpected errors and return 0
                  report($e);

                  return "0";
                }
              }

              // Return the sum of all parts
              if (count($sumParts) > 0) {
                return "(" . implode("+", $sumParts) . ")";
              }

              return "0";
            }

            // For other array metadata, return 0
            return "0";
          }

          return "0";

        case "firstwhere":
          // firstWhere(array, property, value)
          if (count($rawArgNodes) >= 3) {
            $range = isset($args[0]) ? $args[0] : "";

            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              if (is_scalar($propValue)) {
                $property = (string) $propValue;
              }
            }

            $searchValue = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[2]) && $rawArgNodes[2] instanceof ConstantNode) {
              $searchVal = $rawArgNodes[2]->attributes["value"] ?? "";
              if (is_scalar($searchVal)) {
                $searchValue = (string) $searchVal;
              }
            }

            // Return row reference that matches the criteria
            return $this->buildFirstWhereFormula($range, $property, $searchValue);
          }

          return "0";

        case "get":
          // get(object/row, property)
          if (count($rawArgNodes) >= 2) {
            $rowRef = isset($args[0]) ? $args[0] : "";

            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              if (is_scalar($propValue)) {
                $property = (string) $propValue;
              }
            }

            // Get specific property from row
            return $this->buildGetFormula($rowRef, $property);
          }

          return "0";

        case "pow":
          if (isset($args[0], $args[1])) {
            return "POWER(" . $args[0] . "," . $args[1] . ")";
          }

          return "0";

        case "round":
          if (isset($args[0])) {
            $precision = $args[1] ?? "0";

            return "ROUND(" . $args[0] . "," . $precision . ")";
          }

          return "0";

        case "floor":
          if (isset($args[0])) {
            return "FLOOR(" . $args[0] . ",1)";
          }

          return "0";

        case "ceil":
          if (isset($args[0])) {
            return "CEILING(" . $args[0] . ",1)";
          }

          return "0";

        case "abs":
          if (isset($args[0])) {
            return "ABS(" . $args[0] . ")";
          }

          return "0";

        case "min":
          if (count($args) > 0) {
            return "MIN(" . implode(",", $args) . ")";
          }

          return "0";

        case "max":
          if (count($args) > 0) {
            return "MAX(" . implode(",", $args) . ")";
          }

          return "0";

        case "sqrt":
          if (isset($args[0])) {
            return "SQRT(" . $args[0] . ")";
          }

          return "0";

        case "between":
          if (isset($args[0], $args[1], $args[2])) {
            return "AND(" . $args[0] . ">=" . $args[1] . "," . $args[0] . "<=" . $args[2] . ")";
          }

          return "FALSE";

        case "count":
          if (count($args) > 0) {
            return "COUNT(" . implode(",", $args) . ")";
          }

          return "0";

        default:
          // Unknown function - try uppercase version
          $excelFunction = is_string($name) ? mb_strtoupper($name) : "UNKNOWN";
          if (count($args) > 0) {
            return $excelFunction . "(" . implode(",", $args) . ")";
          }

          return $excelFunction . "()";
      }
    }

    // Default case
    return "0";
  }

  /**
   * Build FIRSTWHERE formula for Excel
   */
  private function buildFirstWhereFormula(
    string $range,
    string $property,
    string $searchValue,
  ): string {
    // Find first row where property equals searchValue

    if ($property === "tag") {
      // Look up definition by tag
      $definition = $this->calculationDefinitions->firstWhere("tag", $searchValue);

      if ($definition !== null) {
        // Return the row number or reference
        return (string) $definition->id;
      }
    }

    // Generic MATCH formula
    // This would need the actual column reference for the property
    return 'MATCH("' . $searchValue . '",TagColumn,0)';
  }

  /**
   * Build GET formula for Excel
   */
  private function buildGetFormula(string $rowRef, string $property): string
  {
    if ($property === "id") {
      // If we have a definition ID from firstWhere, just return it
      if (is_numeric($rowRef)) {
        return $rowRef;
      }

      // Otherwise, try to extract ID from row reference
      return $rowRef;
    }

    // For other properties, would need column mapping
    return $rowRef;
  }

  /**
   * Convert an AST node to Excel formula syntax with a specific group context
   * This is used for map() function to evaluate the formula for each group
   *
   * @param  array<CalculationDefinition>  $group  The current group of definitions
   */
  private function nodeToExcelWithContext(Node $node, int $yearId, array $group): string
  {
    // Handle ConstantNode
    // @phpstan-ignore-next-line
    if ($node instanceof ConstantNode) {
      $value = $node->attributes["value"] ?? 0;
      if (is_string($value)) {
        return '"' . str_replace('"', '""', $value) . '"';
      }
      if (is_scalar($value)) {
        return (string) $value;
      }

      return "0";
    }

    // Handle NameNode
    // @phpstan-ignore-next-line
    if ($node instanceof NameNode) {
      $name = $node->attributes["name"] ?? "";

      if ($name === "year") {
        $year = $this->years[$yearId] ?? null;
        if ($year !== null) {
          return (string) $year->year;
        }

        return "YEAR(TODAY())";
      }

      // Handle underscore variable - this represents the current group
      if ($name === "_") {
        // Return a placeholder that will be replaced by firstWhere
        return "@CURRENT_GROUP";
      }

      return "0";
    }

    // Handle BinaryNode
    // @phpstan-ignore-next-line
    if ($node instanceof BinaryNode) {
      $operator = $node->attributes["operator"] ?? "+";
      $leftNode = $node->nodes["left"] ?? null;
      $rightNode = $node->nodes["right"] ?? null;

      if (!$leftNode instanceof Node || !$rightNode instanceof Node) {
        return "0";
      }

      $left = $this->nodeToExcelWithContext($leftNode, $yearId, $group);
      $right = $this->nodeToExcelWithContext($rightNode, $yearId, $group);

      $operatorStr = is_scalar($operator) ? (string) $operator : "+";
      $excelOp = match ($operatorStr) {
        "**" => "^",
        "and" => "*",
        "or" => "+",
        "==" => "=",
        "!=" => "<>",
        "<=" => "<=",
        ">=" => ">=",
        "<" => "<",
        ">" => ">",
        default => $operatorStr,
      };

      if (in_array($excelOp, ["=", "<>", "<=", ">=", "<", ">"], true)) {
        return "({$left}{$excelOp}{$right})";
      }

      return "({$left}{$excelOp}{$right})";
    }

    // Handle UnaryNode
    // @phpstan-ignore-next-line
    if ($node instanceof UnaryNode) {
      $operator = $node->attributes["operator"] ?? "";
      $operandNode = $node->nodes["node"] ?? null;

      if (!$operandNode instanceof Node) {
        return "0";
      }

      $operand = $this->nodeToExcelWithContext($operandNode, $yearId, $group);

      if ($operator === "-") {
        return "(-{$operand})";
      }
      if ($operator === "not" || $operator === "!") {
        return "NOT({$operand})";
      }

      return $operand;
    }

    // Handle FunctionNode
    // @phpstan-ignore-next-line
    if ($node instanceof FunctionNode) {
      $name = $node->attributes["name"] ?? "";
      $arguments = $node->nodes["arguments"] ?? null;

      $args = [];
      $rawArgNodes = [];
      if ($arguments instanceof Node) {
        foreach ($arguments->nodes as $argNode) {
          if ($argNode instanceof Node) {
            $rawArgNodes[] = $argNode;
            $args[] = $this->nodeToExcelWithContext($argNode, $yearId, $group);
          }
        }
      }

      $nameStr = is_string($name) ? mb_strtolower($name) : "";

      switch ($nameStr) {
        case "firstwhere":
          // firstWhere(_, property, value) - find in current group
          if (count($rawArgNodes) >= 3 && isset($args[0]) && $args[0] === "@CURRENT_GROUP") {
            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              if (is_scalar($propValue)) {
                $property = (string) $propValue;
              }
            }

            $searchValue = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[2]) && $rawArgNodes[2] instanceof ConstantNode) {
              $searchVal = $rawArgNodes[2]->attributes["value"] ?? "";
              if (is_scalar($searchVal)) {
                $searchValue = (string) $searchVal;
              }
            }

            // Find the definition in the group with matching property
            foreach ($group as $def) {
              if ($property === "tag" && $def->tag === $searchValue) {
                return (string) $def->id;
              }
            }
          }

          return "0";

        case "get":
          // get(object, property)
          if (count($rawArgNodes) >= 2) {
            $defId = isset($args[0]) ? $args[0] : "";

            $property = "";
            // @phpstan-ignore instanceof.internalClass
            if (isset($rawArgNodes[1]) && $rawArgNodes[1] instanceof ConstantNode) {
              $propValue = $rawArgNodes[1]->attributes["value"] ?? "";
              if (is_scalar($propValue)) {
                $property = (string) $propValue;
              }
            }

            // If we're getting the id property, just return the definition ID
            if ($property === "id" && is_numeric($defId)) {
              return $defId;
            }
          }

          return "0";

        case "data":
          if (isset($rawArgNodes[0])) {
            // Check if it's a constant or a computed value
            $defId = isset($args[0]) ? $args[0] : "";
            if (is_numeric($defId)) {
              $definitionId = (int) $defId;
              if (isset($this->dataRowMapping[$definitionId], $this->yearColumnMap[$yearId])) {
                $targetRow = $this->dataRowMapping[$definitionId];
                $yearColumn = $this->yearColumnMap[$yearId];

                return "'" . $this->dataSheetTitle . "'!" . $yearColumn . $targetRow;
              }
            }
          }

          return "0";

        case "kerroin":
          if (isset($rawArgNodes[0])) {
            // Check if it's a constant or a computed value
            $defId = isset($args[0]) ? $args[0] : "";
            if (is_numeric($defId)) {
              $definitionId = (int) $defId;
              if (isset($this->emissionRowMapping[$definitionId], $this->yearColumnMap[$yearId])) {
                $targetRow = $this->emissionRowMapping[$definitionId];
                $yearColumn = $this->yearColumnMap[$yearId];

                return "'" . $this->emissionSheetTitle . "'!" . $yearColumn . $targetRow;
              }
            }
          }

          return "0";

        case "add":
          if (count($args) > 0) {
            $nonZeroArgs = [];
            foreach ($args as $arg) {
              if ($arg !== "0" && $arg !== "(0)") {
                $nonZeroArgs[] = $arg;
              }
            }
            if (count($nonZeroArgs) > 0) {
              return "(" . implode("+", $nonZeroArgs) . ")";
            }
          }

          return "0";

        case "multiply":
          if (count($args) > 0) {
            return "(" . implode("*", $args) . ")";
          }

          return "1";

        default:
          // For other functions, use the same logic as nodeToExcel
          // You could delegate to the main nodeToExcel for standard functions
          $excelFunction = is_string($name) ? mb_strtoupper($name) : "UNKNOWN";
          if (count($args) > 0) {
            return $excelFunction . "(" . implode(",", $args) . ")";
          }

          return $excelFunction . "()";
      }
    }

    return "0";
  }

  /**
   * Get conversion factor between units
   * Returns null if conversion fails or units are incompatible
   * Returns BigDecimal for precise arithmetic
   */
  private function getUnitConversionFactor(?int $fromUnitId, ?int $toUnitId): ?BigDecimal
  {
    if ($fromUnitId === null || $toUnitId === null || $fromUnitId === $toUnitId) {
      return BigDecimal::one();
    }

    try {
      // Use the UnitService to convert a value of 1
      $unitService = app(\App\Services\UnitService::class);
      $convertedValue = $unitService->convertValue("1", $fromUnitId, $toUnitId);

      if ($convertedValue !== null) {
        return BigDecimal::of($convertedValue);
      }
    } catch (\Brick\Math\Exception\MathException $e) {
      // Expected error for invalid conversions
      return null;
    } catch (Exception $e) {
      // Unexpected error - report it
      report($e);

      return null;
    }

    return null;
  }

  /**
   * Apply conversion factor to Excel formula
   * Uses division for better floating point accuracy when factor < 1
   */
  private function applyConversionToFormula(string $formula, BigDecimal $conversionFactor): string
  {
    // Remove equals sign if present
    $formulaWithoutEquals = mb_ltrim($formula, "=");

    // If conversion factor is less than 1, use division by reciprocal for better accuracy
    if ($conversionFactor->isLessThan(BigDecimal::one())) {
      try {
        $reciprocal = BigDecimal::one()->dividedBy($conversionFactor);
        $reciprocalStr = (string) $reciprocal;

        return "=(" . $formulaWithoutEquals . ")/" . $reciprocalStr;
      } catch (\Brick\Math\Exception\MathException $e) {
        // Division failed (e.g., division by zero), fall back to multiplication
        // This shouldn't happen with valid conversion factors, but we handle it gracefully
        $factorStr = (string) $conversionFactor;

        return "=(" . $formulaWithoutEquals . ")*" . $factorStr;
      } catch (InvalidArgumentException $e) {
        // This should never happen since we're not passing scale or rounding mode
        // If it does, it's a bug, so report it
        report($e);

        // Fall back to multiplication
        $factorStr = (string) $conversionFactor;

        return "=(" . $formulaWithoutEquals . ")*" . $factorStr;
      }
    }

    // For factors >= 1, use multiplication
    $factorStr = (string) $conversionFactor;

    return "=(" . $formulaWithoutEquals . ")*" . $factorStr;
  }
}
