<?php

declare(strict_types=1);

namespace App\Exports;

use App\Enums\InputMethod;
use App\Exports\Sheets\DataSheet;
use App\Exports\Sheets\EmissionFactorsSheet;
use App\Exports\Sheets\ResultsSheet;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionOptionYearValue;
use App\Models\CalculationDefinitionYear;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use App\Models\Unit;
use App\Models\Year;
use App\Services\EmissionCalculationService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use RuntimeException;

final class EmissionCalculationExport implements WithMultipleSheets
{
  private int $companyId;

  private ?int $yearId;

  private EmissionCalculationService $calculationService;

  private ?Unit $resultUnit = null;

  /** @var Collection<int, CalculationDefinition> */
  private Collection $calculationDefinitions;

  /** @var array<int, Year> */
  private array $years = [];

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to value */
  private array $dataValues = [];

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to source */
  private array $dataSources = [];

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to value */
  private array $emissionFactorValues = [];

  /** @var array<int, array<int, string>> Maps [definition_id][year_id] to source */
  private array $emissionFactorSources = [];

  /** @var array<int, array<int, array{value: ?string, source: ?string}>> Maps [definition_id][year_id] to default values */
  private array $defaultEmissionFactors = [];

  /**
   * @throws RuntimeException
   */
  public function __construct(int $companyId, ?int $yearId)
  {
    $this->companyId = $companyId;
    $this->yearId = $yearId;
    $this->calculationService = App::make(EmissionCalculationService::class);

    $this->loadData();
  }

  /**
   * @return array<int, object>
   */
  public function sheets(): array
  {
    // Create Data sheet with row mapping
    $dataSheet = new DataSheet(
      $this->calculationDefinitions,
      $this->years,
      $this->dataValues,
      $this->dataSources,
    );

    // Create Emission Factors sheet with row mapping
    $emissionFactorsSheet = new EmissionFactorsSheet(
      $this->calculationDefinitions,
      $this->years,
      $this->emissionFactorValues,
      $this->emissionFactorSources,
    );

    // Get row mappings from the sheets
    $dataRowMapping = $dataSheet->getRowMapping();
    $emissionRowMapping = $emissionFactorsSheet->getRowMapping();

    // Create Results sheet with the mappings
    $resultsSheet = new ResultsSheet(
      $this->calculationDefinitions,
      $this->years,
      $this->resultUnit,
      $dataRowMapping,
      $emissionRowMapping,
      $dataSheet->title(),
      $emissionFactorsSheet->title(),
    );

    return [$dataSheet, $emissionFactorsSheet, $resultsSheet];
  }

  /**
   * Load all required data
   *
   * @throws RuntimeException if no common unit can be found
   */
  private function loadData(): void
  {
    // Get all calculation definitions with proper ordering
    $this->calculationDefinitions = CalculationDefinition::with([
      "scope",
      "grouping.translations",
      "category.translations",
      "dataUnit.translations",
      "emissionFactorCompoundUnit.numeratorUnit.translations",
      "emissionFactorCompoundUnit.denominatorUnit.translations",
      "translations",
    ])
      ->whereHas("scope")
      ->whereHas("category")
      ->whereHas("grouping")
      ->where(function ($query) {
        $query->where("company_id", $this->companyId)->orWhereNull("company_id");
      })
      ->get()
      ->sortBy(function (CalculationDefinition $item) {
        return sprintf(
          "%010d-%010d-%010d-%010d-%010d-%010d-%01d-%010d",
          $item->scope->number ?? 0,
          $item->scope->id ?? 0, // Keep same scopes together
          $item->grouping->sort_order ?? 0,
          $item->grouping->id ?? 0, // Keep same groupings together
          $item->category->sort_order ?? 0,
          $item->category->id ?? 0, // Keep same categories together
          $item->company_id === null ? 0 : 1,
          $item->sort_order ?? 0,
        );
      });

    // Determine which years to load
    if ($this->yearId !== null) {
      // Load only specified year (if it's published)
      $year = Year::where("id", $this->yearId)->whereNotNull("published")->first();
      if ($year !== null) {
        $this->years[$year->id] = $year;
      }
    } else {
      // Load all published years
      Year::whereNotNull("published")
        ->orderBy("year", "asc")
        ->get()
        ->each(function (Year $year): void {
          $this->years[$year->id] = $year;
        });
    }

    // Check if we have any years to export
    if (count($this->years) === 0) {
      if ($this->yearId !== null) {
        throw new RuntimeException("Selected year is not published or does not exist");
      }
      throw new RuntimeException("No published years available for export");
    }

    // Load data and emission factor values for all relevant years
    foreach ($this->years as $year) {
      $this->loadDefaultEmissionFactors($year->id);
      $this->loadDataAndEmissionFactorValues($year->id);
    }

    $this->resultUnit = $this->calculationService->determineTargetUnit(
      $this->calculationDefinitions,
    );
  }

  /**
   * Load default emission factor values from calculation_definition_year table
   */
  private function loadDefaultEmissionFactors(int $yearId): void
  {
    $definitionIds = $this->calculationDefinitions->pluck("id")->filter()->toArray();

    // Load all default emission factors for the definitions and year
    $defaultFactors = CalculationDefinitionYear::whereIn(
      "calculation_definition_id",
      $definitionIds,
    )
      ->where("year_id", $yearId)
      ->get();

    foreach ($defaultFactors as $defaultFactor) {
      if (!isset($this->defaultEmissionFactors[$defaultFactor->calculation_definition_id])) {
        $this->defaultEmissionFactors[$defaultFactor->calculation_definition_id] = [];
      }

      $this->defaultEmissionFactors[$defaultFactor->calculation_definition_id][$yearId] = [
        "value" => $defaultFactor->emission_factor_default_value,
        "source" => $defaultFactor->emission_factor_default_source,
      ];
    }
  }

  /**
   * Load data values and emission factor values with their sources
   */
  private function loadDataAndEmissionFactorValues(int $yearId): void
  {
    $definitionIds = $this->calculationDefinitions->pluck("id")->filter()->toArray();

    // Load data values with sources
    $dataValueCollection = DataValue::whereIn("data_definition_id", $definitionIds)
      ->where("company_id", $this->companyId)
      ->where("year_id", $yearId)
      ->get();

    foreach ($dataValueCollection as $dataValue) {
      if (!isset($this->dataValues[$dataValue->data_definition_id])) {
        $this->dataValues[$dataValue->data_definition_id] = [];
      }
      if (!isset($this->dataSources[$dataValue->data_definition_id])) {
        $this->dataSources[$dataValue->data_definition_id] = [];
      }

      if ($dataValue->value !== null) {
        $this->dataValues[$dataValue->data_definition_id][$yearId] = $dataValue->value;
      }
      // Store source information if available
      if ($dataValue->source !== null && $dataValue->source !== "") {
        $this->dataSources[$dataValue->data_definition_id][$yearId] = $dataValue->source;
      }
    }

    // Load emission factor values (including SELECT-type options)
    $emissionFactorValueCollection = EmissionFactorValue::whereIn(
      "emission_factor_definition_id",
      $definitionIds,
    )
      ->where("company_id", $this->companyId)
      ->where("year_id", $yearId)
      ->get();

    // Collect all option IDs that might need year values (for SELECT-type inputs)
    $optionIds = [];
    foreach ($this->calculationDefinitions as $definition) {
      if ($definition->input_method === InputMethod::SELECT) {
        $selectedEmissionFactors = $emissionFactorValueCollection
          ->where("emission_factor_definition_id", $definition->id)
          ->whereNotNull("calculation_definition_option_id");

        foreach ($selectedEmissionFactors as $ef) {
          if ($ef->calculation_definition_option_id !== null) {
            $optionIds[] = $ef->calculation_definition_option_id;
          }
        }
      }
    }

    // Load all option year values in one query
    $optionYearValues = [];
    if (count($optionIds) !== 0) {
      $optionYearValues = CalculationDefinitionOptionYearValue::whereIn(
        "calculation_definition_option_id",
        $optionIds,
      )
        ->where("year_id", $yearId)
        ->get()
        ->keyBy("calculation_definition_option_id");
    }

    // Process emission factor values
    foreach ($this->calculationDefinitions as $definition) {
      $definitionId = $definition->id;
      $factorValue = null;
      $factorSource = null;

      if (!isset($this->emissionFactorValues[$definitionId])) {
        $this->emissionFactorValues[$definitionId] = [];
      }
      if (!isset($this->emissionFactorSources[$definitionId])) {
        $this->emissionFactorSources[$definitionId] = [];
      }

      // Check if this is a select-type input
      if ($definition->input_method === InputMethod::SELECT) {
        // For select type, find the emission factor with an option ID (which indicates the selected option)
        $selectedEmissionFactor = $emissionFactorValueCollection
          ->where("emission_factor_definition_id", $definitionId)
          ->whereNotNull("calculation_definition_option_id")
          ->first();

        if (
          $selectedEmissionFactor !== null &&
          $selectedEmissionFactor->calculation_definition_option_id !== null
        ) {
          // Look up the actual emission factor value for this option
          $optionYearValue =
            $optionYearValues[$selectedEmissionFactor->calculation_definition_option_id] ?? null;

          if ($optionYearValue !== null && $optionYearValue->value !== null) {
            $factorValue = $optionYearValue->value;
          }

          // Store source from emission factor value
          if ($selectedEmissionFactor->source !== null && $selectedEmissionFactor->source !== "") {
            $factorSource = $selectedEmissionFactor->source;
          }
        }
      } else {
        // For manual type, get the emission factor without an option ID
        $emissionFactorValue = $emissionFactorValueCollection
          ->where("emission_factor_definition_id", $definitionId)
          ->whereNull("calculation_definition_option_id")
          ->first();

        if ($emissionFactorValue !== null) {
          if ($emissionFactorValue->value !== null) {
            $factorValue = $emissionFactorValue->value;
          }
          // Store source information
          if ($emissionFactorValue->source !== null && $emissionFactorValue->source !== "") {
            $factorSource = $emissionFactorValue->source;
          }
        }
      }

      // Fallback: If no specific emission factor value found, use default
      if ($factorValue === null && isset($this->defaultEmissionFactors[$definitionId][$yearId])) {
        $defaultData = $this->defaultEmissionFactors[$definitionId][$yearId];

        // Use default value if available
        if ($defaultData["value"] !== null) {
          $factorValue = $defaultData["value"];

          // Only use default source when we're using the default value
          if (
            $factorSource === null &&
            $defaultData["source"] !== null &&
            $defaultData["source"] !== ""
          ) {
            $factorSource = $defaultData["source"];
          }
        }
      }

      // Store the emission factor value if found (either specific or default)
      if ($factorValue !== null) {
        $this->emissionFactorValues[$definitionId][$yearId] = $factorValue;
      }

      // Store the source if found (either specific or default)
      if ($factorSource !== null) {
        $this->emissionFactorSources[$definitionId][$yearId] = $factorSource;
      }
    }
  }
}
