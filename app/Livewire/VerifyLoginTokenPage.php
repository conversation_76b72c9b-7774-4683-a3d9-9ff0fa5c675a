<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\AuthenticationService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\View\View;
use InvalidArgumentException;
use Livewire\Component;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class VerifyLoginTokenPage extends Component
{
  /**
   * Determines if the login was successful.
   */
  public bool $success = false;

  /**
   * The message to display to the user.
   */
  public string $message = "";

  /**
   * The redirect URL from configuration.
   */
  public string $redirectUrl;

  /**
   * Mount the component and verify the token.
   *
   * @throws RuntimeException
   */
  public function mount(string $token, AuthenticationService $authenticationService): void
  {
    try {
      $result = $authenticationService->login($token);

      $this->success = $result;
      $this->message = $result
        ? __("Kirjautuminen onnistui!")
        : __("Virheellinen tai vanhentunut kirjautumislinkki.");
    } catch (TooManyRequestsHttpException) {
      $this->success = false;
      $this->message = __("Liian monta kirjautumisyritystä. Yritä myöhemmin uudelleen.");
    } catch (InvalidArgumentException) {
      $this->success = false;
      $this->message = __("Virheellinen kirjautumislinkki. Tarkista linkki tai pyydä uusi.");
    }

    // Get the redirect URL from configuration
    $baseRedirectUrl = Config::string("misc.auth_redirect_url");

    $localizeAuthRedirect = Config::boolean("misc.localize_auth_redirect");

    if ($localizeAuthRedirect) {
      // Get current locale
      $locale = App::getLocale();

      // Remove trailing slash from base URL if present
      $baseRedirectUrl = mb_rtrim($baseRedirectUrl, "/");

      // Append locale to redirect URL
      $this->redirectUrl = $baseRedirectUrl . "/" . $locale;
    } else {
      $this->redirectUrl = $baseRedirectUrl;
    }
  }

  /**
   * Render the component.
   */
  public function render(): View
  {
    return view("livewire.verify-login-token-page");
  }
}
