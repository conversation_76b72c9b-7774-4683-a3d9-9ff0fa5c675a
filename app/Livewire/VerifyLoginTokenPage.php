<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\AuthenticationService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\View\View;
use InvalidArgumentException;
use Livewire\Attributes\Locked;
use Livewire\Component;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class VerifyLoginTokenPage extends Component
{
  /**
   * Determines if the login was successful.
   */
  #[Locked]
  public bool $success = false;

  /**
   * The message to display to the user.
   */
  #[Locked]
  public string $message = "";

  /**
   * The redirect URL from configuration.
   */
  #[Locked]
  public string $redirectUrl = "";

  /**
   * The token selector.
   */
  #[Locked]
  public string $selector = "";

  /**
   * The token validator.
   */
  #[Locked]
  public string $validator = "";

  /**
   * Mount the component.
   *
   * @throws RuntimeException
   */
  public function mount(string $selector, string $validator): void
  {
    // Set the route parameters
    $this->selector = $selector;
    $this->validator = $validator;

    // Get the redirect URL from configuration
    $baseRedirectUrl = Config::string("misc.auth_redirect_url");
    $localizeAuthRedirect = Config::boolean("misc.localize_auth_redirect");

    if ($localizeAuthRedirect) {
      // Get current locale
      $locale = App::getLocale();

      // Remove trailing slash from base URL if present
      $baseRedirectUrl = mb_rtrim($baseRedirectUrl, "/");

      // Append locale to redirect URL
      $this->redirectUrl = $baseRedirectUrl . "/" . $locale;
    } else {
      $this->redirectUrl = $baseRedirectUrl;
    }
  }

  /**
   * Perform the actual login (called via wire:init).
   *
   * @throws RuntimeException
   */
  public function performLogin(): void
  {
    try {
      $authenticationService = App::make(AuthenticationService::class);
      $result = $authenticationService->login($this->selector, $this->validator);

      $this->success = $result;
      $this->message = $result
        ? __("auth.messages.login_success")
        : __("auth.errors.invalid_or_expired_link");
    } catch (TooManyRequestsHttpException) {
      $this->success = false;
      $this->message = __("auth.errors.too_many_attempts");
    } catch (InvalidArgumentException) {
      $this->success = false;
      $this->message = __("auth.errors.invalid_login_link");
    }

    // Dispatch the event with the results
    $this->dispatch("login-completed", [
      "success" => $this->success,
      "message" => $this->message,
      "redirectUrl" => $this->redirectUrl,
    ]);
  }

  /**
   * Render the component.
   */
  public function render(): View
  {
    return view("livewire.verify-login-token-page");
  }
}
