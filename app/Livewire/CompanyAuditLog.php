<?php

declare(strict_types=1);

namespace App\Livewire;

use App\DataTransferObjects\AuditLogEntry;
use App\Enums\Abilities;
use App\Enums\ScalarType;
use App\Helpers\Assert;
use App\Models\AuditLog;
use App\Models\CalculationDefinition;
use App\Models\Company;
use App\Models\DataValue;
use App\Models\EmissionFactorValue;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use InvalidArgumentException;
use Livewire\Attributes\Locked;
use Livewire\Component;
use Livewire\WithPagination;
use RuntimeException;

final class CompanyAuditLog extends Component
{
  use WithPagination;

  #[Locked]
  public ?int $companyId = null;

  #[Locked]
  public int $perPage = 10;

  public function mount(?int $companyId = null): void
  {
    $this->companyId = $companyId;
  }

  /**
   * @throws InvalidArgumentException
   * @throws RuntimeException
   */
  public function render(): View
  {
    return view("livewire.company-audit-log", [
      "auditLogs" => $this->getAuditLogs(),
    ]);
  }

  /**
   * @throws RuntimeException
   */
  private function getCompany(): ?Company
  {
    if ($this->companyId === null) {
      return null;
    }

    return Company::find($this->companyId);
  }

  /**
   * Get paginated audit logs
   *
   * @return null|LengthAwarePaginator<int, AuditLogEntry>
   *
   * @throws RuntimeException
   * @throws InvalidArgumentException
   */
  private function getAuditLogs(): ?LengthAwarePaginator
  {
    $company = $this->getCompany();
    if ($company === null) {
      return null;
    }

    $user = Auth::user();
    Assert::notNull($user);

    if (!Gate::allows(Abilities::VIEW_COMPANY_AUDIT_LOGS, $company)) {
      return null;
    }

    // Get all user IDs for this company
    $companyUserIds = $company->users()->pluck("users.id")->toArray();

    if (count($companyUserIds) === 0) {
      return null;
    }

    // Get paginated results
    $auditLogsPaginator = AuditLog::query()
      ->where("company_id", $this->companyId)
      ->with([
        "auditable" => function (Relation $morphTo) {
          Assert::true($morphTo instanceof MorphTo);
          $morphTo->morphWith([
            DataValue::class => ["calculationDefinition.translations", "year"],
            EmissionFactorValue::class => ["calculationDefinition.translations", "year"],
            Company::class => [],
          ]);
        },
      ])
      ->orderBy("occurred_at", "desc")
      ->orderBy("id", "desc")
      ->paginate($this->perPage);

    // Get user emails for mapping
    $companyUserEmails = User::select(["id", "email"])
      ->whereIn("id", $companyUserIds)
      ->pluck("email", "id")
      ->all();
    Assert::intKeyedArray($companyUserEmails);
    Assert::stringArray($companyUserEmails);

    // Transform items
    $transformedItems = $auditLogsPaginator
      ->getCollection()
      ->map(function ($auditLog) use ($companyUserEmails) {
        return new AuditLogEntry(
          userEmail: $companyUserEmails[$auditLog->user_id] ?? __("audit.unknown"),
          modelType: $this->getModelTypeLabel($auditLog->auditable_type),
          modelDescription: $auditLog->auditable !== null
            ? $this->getAuditTargetDescription($auditLog->auditable)
            : __("audit.unknown"),
          fieldName: $this->getFieldLabel($auditLog->auditable_type, $auditLog->field_name),
          oldValue: $this->formatFieldValue($auditLog->old_value, $auditLog->field_type),
          newValue: $this->formatFieldValue($auditLog->new_value, $auditLog->field_type),
          changedAt: $auditLog->occurred_at->format("H.i - d.m.Y"),
        );
      });

    // Return new paginator with transformed items
    $paginator = new LengthAwarePaginator(
      $transformedItems,
      $auditLogsPaginator->total(),
      $auditLogsPaginator->perPage(),
      $auditLogsPaginator->currentPage(),
      ["path" => request()->url()],
    );

    return $paginator;
  }

  /**
   * @throws RuntimeException
   */
  private function getModelTypeLabel(string $modelClass): string
  {
    return match ($modelClass) {
      Company::class => __("audit.fields.company"),
      DataValue::class => __("audit.types.data"),
      EmissionFactorValue::class => __("audit.types.emission_factor"),
      CalculationDefinition::class => __("audit.fields.row"),
      default => throw new RuntimeException("No label found for model class: {$modelClass}"),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getAuditTargetDescription(Model $auditable): string
  {
    return match (get_class($auditable)) {
      Company::class => __("common.dash"),

      DataValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->data_name ??
          ($auditable->calculationDefinition->custom_name ?? (string) __("audit.unknown")),
        $auditable->year->year ?? (string) __("audit.unknown"),
      ),

      EmissionFactorValue::class => sprintf(
        "%s (%s)",
        $auditable->calculationDefinition?->translate()->emission_factor_name ??
          ($auditable->calculationDefinition->custom_name ?? (string) __("audit.unknown")),
        $auditable->year->year ?? (string) __("audit.unknown"),
      ),

      CalculationDefinition::class => sprintf(
        "%s",
        $auditable->custom_name ??
          ($auditable->translate()->data_name ??
            ($auditable->translate()->emission_factor_name ??
              ($auditable->translate()->result_name ?? (string) __("audit.unknown")))),
      ),

      default => throw new RuntimeException(
        "No target description found for model class: " . get_class($auditable),
      ),
    };
  }

  /**
   * @throws RuntimeException
   */
  private function getFieldLabel(string $modelClass, string $field): string
  {
    return match ($modelClass) {
      Company::class => match ($field) {
        "name" => __("audit.fields.company_name"),
        "business_id" => __("audit.fields.business_id"),
        "fiscal_start_month" => __("audit.fields.fiscal_start_month"),
        "fiscal_start_day" => __("audit.fields.fiscal_start_day"),
        "fiscal_end_month" => __("audit.fields.fiscal_end_month"),
        "fiscal_end_day" => __("audit.fields.fiscal_end_day"),
        "consent_for_data_examination" => __("audit.fields.data_viewing_permission"),
        "applying_for_mark" => __("audit.fields.applying_badge"), // Legacy field
        "applying_for_scope1_2_mark" => __("audit.fields.applying_scope_1_2"),
        "applying_for_scope3_mark" => __(
          "audit.fields.applying_scope_3",
        ), // Legacy field before rename
        "applying_for_scope1_3_mark" => __("audit.fields.applying_scope_1_3"),
        "industry_classification_id" => __("audit.fields.industry"),
        "municipality_id" => __("audit.fields.municipality"),
        "revenue_range_id" => __("audit.fields.revenue"),
        "employee_count_range_id" => __("audit.fields.employee_count"),
        "e_invoice_address" => __("audit.fields.einvoice_address"),
        "e_invoice_operator" => __("audit.fields.einvoice_operator"),
        "e_invoice_reference" => __("audit.fields.reference"),
        "e_invoice_contact_name" => __("audit.fields.contact_name"),
        "e_invoice_contact_email" => __("audit.fields.contact_email"),
        "e_invoice_additional_info" => __("audit.fields.additional_info"),
        "terms_of_service_accepted_at" => __("audit.fields.terms_accepted"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in Company model",
        ),
      },
      DataValue::class => match ($field) {
        "value" => __("audit.fields.value"),
        "source" => __("audit.fields.source"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in DataValue model",
        ),
      },
      EmissionFactorValue::class => match ($field) {
        "value" => __("audit.fields.value"),
        "source" => __("audit.fields.source"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in EmissionFactorValue model",
        ),
      },
      CalculationDefinition::class => match ($field) {
        "custom_name" => __("audit.fields.name"),
        default => throw new RuntimeException(
          "No label found for field '{$field}' in CalculationDefinition model",
        ),
      },
      default => throw new RuntimeException("No labels found for model class: {$modelClass}"),
    };
  }

  /**
   * Format field value for display
   */
  private function formatFieldValue(?string $value, ScalarType $fieldType): string
  {
    if ($value === null) {
      return __("common.dash");
    }

    // Use type information for proper formatting
    if ($fieldType === ScalarType::BOOLEAN) {
      return $value === "1" ? __("common.yes") : __("common.no");
    }

    return $value;
  }
}
