<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Enums\Abilities;
use App\Enums\SelectOptionType;
use App\Helpers\Assert;
use App\Models\Company;
use App\Models\User;
use App\Services\AuthenticationService;
use App\Services\IndustryClassificationService;
use App\Services\MunicipalityService;
use App\Services\SelectOptionService;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Locked;
use Livewire\Component;
use RuntimeException;
use Throwable;

final class CompanyPage extends Component
{
  public string $name = "";

  public string $business_id = "";

  public int $fiscal_start_month = 1;

  public int $fiscal_start_day = 1;

  public int $fiscal_end_month = 12;

  public int $fiscal_end_day = 31;

  public bool $consent_for_data_examination = false;

  public bool $applying_for_scope1_2_mark = false;

  public bool $applying_for_scope1_3_mark = false;

  public ?int $industry_classification_id = null;

  public ?int $municipality_id = null;

  public ?int $revenue_range_id = null;

  public ?int $employee_count_range_id = null;

  public string $e_invoice_address = "";

  public string $e_invoice_operator = "";

  public string $e_invoice_reference = "";

  public string $e_invoice_contact_name = "";

  public string $e_invoice_contact_email = "";

  public string $e_invoice_additional_info = "";

  public string $newUserEmail = "";

  public bool $showDeleteModal = false;

  #[Locked]
  public ?int $userToDeleteId = null;

  public string $userToDeleteEmail = "";

  public int $selectedCompanyId = 0;

  public bool $terms_of_service_accepted = false;

  /**
   * Mount the component
   */
  public function mount(): void
  {
    try {
      // Check if we're on the create route
      if (request()->routeIs("company.create")) {
        Gate::authorize(Abilities::CREATE_COMPANY);
        $this->selectedCompanyId = -1;
        $this->resetFormFields();

        return;
      }

      $this->loadCompany();

      // Set the selected company ID for the dropdown
      $currentUser = Auth::user();
      Assert::notNull($currentUser);

      if ($currentUser->selected_company_id !== null) {
        $this->selectedCompanyId = $currentUser->selected_company_id;
      } else {
        // If no company selected, select the first one
        $firstCompany = $currentUser->companies()->first();
        if ($firstCompany !== null) {
          $this->selectedCompanyId = $firstCompany->id;
          $currentUser->update(["selected_company_id" => $firstCompany->id]);
        } else {
          // No companies exist - default to creation view if user has permission
          if (Gate::allows(Abilities::CREATE_COMPANY)) {
            $this->selectedCompanyId = -1;
          } else {
            $this->selectedCompanyId = 0;
          }
        }
      }
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Yrityksen lataaminen epäonnistui"));
      $this->selectedCompanyId = 0;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Sinulla ei ole oikeutta tarkastella tätä yritystä"));
      $this->selectedCompanyId = 0;
    }
  }

  /**
   * Handle company selection change
   *
   * @throws RuntimeException
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function updatedSelectedCompanyId(): void
  {
    if ($this->selectedCompanyId === 0) {
      return;
    }

    if ($this->selectedCompanyId === -1) {
      // Handle "Create New Company" selection
      try {
        Gate::authorize(Abilities::CREATE_COMPANY);

        // Redirect to the create new company route
        $this->redirect(route("company.create"));
      } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
        $this->addErrorWithFlash("global", __("Sinulla ei ole oikeutta luoda uutta yritystä"));
        // Reset to previous value
        $currentUser = Auth::user();
        Assert::notNull($currentUser);
        $this->selectedCompanyId = $currentUser->selected_company_id ?? 0;
      }

      return;
    }

    try {
      // Verify the user has permission to view the selected company
      $selectedCompany = Company::find($this->selectedCompanyId);

      if ($selectedCompany === null) {
        $this->addErrorWithFlash("global", __("Yritystä ei löydy"));
        $this->selectedCompanyId = 0;

        return;
      }

      Gate::authorize(Abilities::VIEW_COMPANY, $selectedCompany);

      // Update user's selected company in database
      $currentUser = Auth::user();
      Assert::notNull($currentUser);
      $currentUser->update(["selected_company_id" => $this->selectedCompanyId]);

      // Reload the company data
      $this->loadCompany();
      $this->resetErrorBag();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Sinulla ei ole oikeutta tarkastella tätä yritystä"));
      // Reset to previous company if unauthorized
      $currentUser = Auth::user();
      Assert::notNull($currentUser);
      $this->selectedCompanyId = $currentUser->selected_company_id ?? 0;
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Yrityksen vaihtaminen epäonnistui"));
    }
  }

  /**
   * Auto-save when a field changes
   */
  public function updated(string $field): void
  {
    try {
      $company = $this->getCompany();

      if ($company === null) {
        Gate::authorize(Abilities::CREATE_COMPANY);
      } else {
        Gate::authorize(Abilities::EDIT_COMPANY, $company);
      }

      // Handle XOR logic for scope marks
      if ($field === "applying_for_scope1_2_mark" && $this->applying_for_scope1_2_mark) {
        $this->applying_for_scope1_3_mark = false;
      } elseif ($field === "applying_for_scope1_3_mark" && $this->applying_for_scope1_3_mark) {
        $this->applying_for_scope1_2_mark = false;
      }

      $allowedFields = [
        "name",
        "business_id",
        "fiscal_start_month",
        "fiscal_start_day",
        "fiscal_end_month",
        "fiscal_end_day",
        "consent_for_data_examination",
        "applying_for_scope1_2_mark",
        "applying_for_scope1_3_mark",
        "industry_classification_id",
        "municipality_id",
        "revenue_range_id",
        "employee_count_range_id",
        "e_invoice_address",
        "e_invoice_operator",
        "e_invoice_reference",
        "e_invoice_contact_name",
        "e_invoice_contact_email",
        "e_invoice_additional_info",
        "terms_of_service_accepted",
      ];

      if (in_array($field, $allowedFields, true)) {
        $this->saveCompanyField($field);

        // If we just turned off one scope mark by turning on the other, save that change too
        if ($field === "applying_for_scope1_2_mark" && $this->applying_for_scope1_2_mark) {
          $this->saveCompanyField("applying_for_scope1_3_mark");
        } elseif ($field === "applying_for_scope1_3_mark" && $this->applying_for_scope1_3_mark) {
          $this->saveCompanyField("applying_for_scope1_2_mark");
        }
      }
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Sinulla ei ole oikeuksia muokata yritystietoja"));
    } catch (\Illuminate\Validation\ValidationException $e) {
      $this->handleValidationException($e);
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Kentän päivittäminen epäonnistui"));
    } catch (Throwable $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Kentän päivittäminen epäonnistui"));
    }
  }

  /**
   * Open the delete user modal
   */
  public function openDeleteModal(int $userId): void
  {
    try {
      $company = $this->getCompany();
      if ($company === null) {
        $this->addErrorWithFlash("global", __("Yritystä ei löydy"));

        return;
      }

      $user = User::find($userId);
      if ($user === null) {
        $this->addErrorWithFlash("global", __("Käyttäjää ei löydy"));

        return;
      }

      Gate::authorize(Abilities::DELETE_COMPANY_USER, [$company, $user]);

      $this->userToDeleteId = $userId;
      $this->userToDeleteEmail = $user->email;
      $this->showDeleteModal = true;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Et voi poistaa tätä käyttäjää"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Modaalin avaaminen epäonnistui"));
    }
  }

  /**
   * Close the delete user modal
   */
  public function closeDeleteModal(): void
  {
    $this->showDeleteModal = false;
    $this->userToDeleteId = null;
    $this->userToDeleteEmail = "";
  }

  /**
   * Confirm and remove the user
   */
  public function confirmRemoveUser(): void
  {
    if ($this->userToDeleteId === null) {
      $this->closeDeleteModal();

      return;
    }

    $this->removeUser($this->userToDeleteId);
    $this->closeDeleteModal();
  }

  /**
   * Invite a new user to the company
   */
  public function inviteUser(): void
  {
    try {
      $company = $this->getCompany();
      if ($company === null) {
        $this->addErrorWithFlash("global", __("Ole hyvä ja luo ensin yritys"));

        return;
      }

      Gate::authorize(Abilities::MANAGE_COMPANY_USERS, $company);

      try {
        Validator::make(
          ["email" => $this->newUserEmail],
          ["email" => "required|email|max:255"],
        )->validate();
      } catch (\Illuminate\Validation\ValidationException $e) {
        $this->handleValidationException($e);

        return;
      }

      $email = $this->newUserEmail;

      // Check if user already exists in the company
      $existingUser = User::where("email", $email)->first();
      if (
        $existingUser !== null &&
        $existingUser->companies()->where("companies.id", $company->id)->exists()
      ) {
        $this->addErrorWithFlash("newUserEmail", __("Käyttäjä on jo tämän yrityksen jäsen"));

        return;
      }

      $authenticationService = App::make(AuthenticationService::class);
      $authenticationService->sendMagicLink($email, $company);

      $this->flash(__("Kutsu lähetetty onnistuneesti"));
      $this->newUserEmail = "";
      $this->resetErrors("newUserEmail");
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Vain pääkäyttäjä voi kutsua uusia käyttäjiä"));
    } catch (\Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException $e) {
      $this->addErrorWithFlash(
        "global",
        __("Liian monta kutsupyyntöä. Yritä myöhemmin uudelleen."),
      );
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Käyttäjän kutsuminen epäonnistui"));
    } catch (Throwable $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Käyttäjän kutsuminen epäonnistui"));
    }
  }

  /**
   * Render the component
   *
   * @throws RuntimeException
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function render(): View
  {
    $company = $this->getCompany();

    $companyUsers = $company?->users()->lazy()->collect()->all() ?? [];

    $currentUser = Auth::user();

    Assert::notNull($currentUser);

    $currentCompanyUser = null;
    $currentUserIsPrimaryUser = false;

    if ($company !== null) {
      $companyUser = $currentUser->companies()->where("companies.id", $company->id)->first();
      if ($companyUser !== null) {
        $currentCompanyUser = $companyUser->pivot;
        $currentUserIsPrimaryUser = $currentCompanyUser->is_primary ?? false;
      }
    }

    // Get available companies for the dropdown
    $availableCompanies = collect();

    // Check if user has permission to view any company (admin)
    if (Gate::allows(Abilities::VIEW_ANY_COMPANY)) {
      $availableCompanies = Company::orderBy("name")->get();
    } else {
      // Get only the companies the user belongs to
      $availableCompanies = $currentUser->companies()->orderBy("name")->get();
    }

    // Company selector visibility logic
    $canCreateCompany = $currentUser->can(Abilities::CREATE_COMPANY);
    $canViewAnyCompany = $currentUser->can(Abilities::VIEW_ANY_COMPANY);
    $showSelector = $availableCompanies->count() > 1 || $canViewAnyCompany || $canCreateCompany;

    $industryClassificationService = App::make(IndustryClassificationService::class);
    $industryClassifications = $industryClassificationService->getSelectOptions();

    $municipalityService = App::make(MunicipalityService::class);
    $municipalities = $municipalityService->getSelectOptions();

    $selectOptionService = App::make(SelectOptionService::class);
    $revenueRanges = $selectOptionService->getSelectOptions(SelectOptionType::REVENUE_RANGE);
    $employeeCountRanges = $selectOptionService->getSelectOptions(
      SelectOptionType::EMPLOYEE_COUNT_RANGE,
    );

    return view("livewire.company-page", [
      "company" => $company,
      "companyUsers" => $companyUsers,
      "currentUserId" => Auth::id(),
      "currentUserIsPrimaryUser" => $currentUserIsPrimaryUser,
      "availableCompanies" => $availableCompanies,
      "canCreateCompany" => $canCreateCompany,
      "canViewAnyCompany" => $canViewAnyCompany,
      "showSelector" => $showSelector,
      "industryClassifications" => $industryClassifications,
      "municipalities" => $municipalities,
      "revenueRanges" => $revenueRanges,
      "employeeCountRanges" => $employeeCountRanges,
    ]);
  }

  /**
   * Remove a user from the companyw
   */
  private function removeUser(int $userId): void
  {
    try {
      $company = $this->getCompany();
      if ($company === null) {
        $this->addErrorWithFlash("global", __("Yritystä ei löydy"));

        return;
      }

      $user = User::find($userId);
      if ($user === null) {
        $this->addErrorWithFlash("global", __("Käyttäjää ei löydy"));

        return;
      }

      Gate::authorize(Abilities::DELETE_COMPANY_USER, [$company, $user]);

      DB::beginTransaction();
      try {
        $user->companies()->detach($company->id);
        DB::commit();
      } catch (Throwable $e) {
        DB::rollBack();
        throw $e;
      }
      $this->flash(__("Käyttäjä poistettu yrityksestä onnistuneesti"));
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("Et voi poistaa tätä käyttäjää"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Käyttäjän poistaminen epäonnistui"));
    } catch (Throwable $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("Käyttäjän poistaminen epäonnistui"));
    }
  }

  private function safeReport(Throwable $e): void
  {
    try {
      report($e);
    } catch (Throwable $reportException) {
      Log::emergency("Failed to report exception", [
        "exception" => get_class($e),
        "message" => $e->getMessage(),
        "reportException" => get_class($reportException),
        "reportExceptionMessage" => $reportException->getMessage(),
      ]);
    }
  }

  private function resetErrors(?string $field = null): void
  {
    if ($field !== null) {
      $this->resetErrorBag($field);
    } else {
      $this->resetErrorBag();
    }
  }

  private function flash(string $message, string $type = "success"): void
  {
    $this->dispatch("notify", [
      "message" => $message,
      "type" => $type,
    ]);
  }

  private function addErrorWithFlash(string $field, string $message): void
  {
    $this->addError($field, $message);
    $this->flash($message, "error");
  }

  private function handleValidationException(\Illuminate\Validation\ValidationException $e): void
  {
    foreach ($e->validator->errors()->messages() as $field => $messages) {
      foreach ($messages as $message) {
        $this->addErrorWithFlash($field, $message);
      }
    }
  }

  /**
   * Save a specific company field
   *
   * @param  string  $field  The field to save
   *
   * @throws RuntimeException
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  private function saveCompanyField(string $field): void
  {
    $company = $this->getCompany();

    if ($company === null) {
      // Only allow creating a company if in create mode
      if ($this->selectedCompanyId !== -1) {
        $this->addErrorWithFlash("global", __("Yritystä ei löydy"));

        return;
      }

      // In create mode, just validate the field without creating a company yet
      $this->validateField($field);

      return;
    }

    $validationRule = $this->getValidationRuleForField($field, $company);

    if ($validationRule === null) {
      $this->addErrorWithFlash("global", __("Kenttää ei ole olemassa"));

      return;
    }

    $data = $this->getSafePropertyValue($field);

    try {
      Validator::make([$field => $data], [$field => $validationRule])->validate();
    } catch (\Illuminate\Validation\ValidationException $e) {
      $this->handleValidationException($e);

      return;
    }

    // Validate fiscal dates if updating fiscal fields
    if (
      in_array(
        $field,
        ["fiscal_start_month", "fiscal_start_day", "fiscal_end_month", "fiscal_end_day"],
        true,
      )
    ) {
      if ($field === "fiscal_start_month") {
        $startMonth = is_numeric($data) ? (int) $data : $this->fiscal_start_month;
      } else {
        $startMonth = $this->fiscal_start_month;
      }

      if ($field === "fiscal_start_day") {
        $startDay = is_numeric($data) ? (int) $data : $this->fiscal_start_day;
      } else {
        $startDay = $this->fiscal_start_day;
      }

      if ($field === "fiscal_end_month") {
        $endMonth = is_numeric($data) ? (int) $data : $this->fiscal_end_month;
      } else {
        $endMonth = $this->fiscal_end_month;
      }

      if ($field === "fiscal_end_day") {
        $endDay = is_numeric($data) ? (int) $data : $this->fiscal_end_day;
      } else {
        $endDay = $this->fiscal_end_day;
      }

      if (!$this->isValidDate($startMonth, $startDay)) {
        $this->addErrorWithFlash(
          "fiscal_start_month",
          __("Tilikauden aloituspäivämäärä on virheellinen"),
        );
        $this->addErrorWithFlash(
          "fiscal_start_day",
          __("Tilikauden aloituspäivämäärä on virheellinen"),
        );

        return;
      }

      if (!$this->isValidDate($endMonth, $endDay)) {
        $this->addErrorWithFlash(
          "fiscal_end_month",
          __("Tilikauden päättymispäivämäärä on virheellinen"),
        );
        $this->addErrorWithFlash(
          "fiscal_end_day",
          __("Tilikauden päättymispäivämäärä on virheellinen"),
        );

        return;
      }
    }

    $allowedFields = [
      "name",
      "business_id",
      "fiscal_start_month",
      "fiscal_start_day",
      "fiscal_end_month",
      "fiscal_end_day",
      "consent_for_data_examination",
      "applying_for_scope1_2_mark",
      "applying_for_scope1_3_mark",
      "industry_classification_id",
      "municipality_id",
      "revenue_range_id",
      "employee_count_range_id",
      "e_invoice_address",
      "e_invoice_operator",
      "e_invoice_reference",
      "e_invoice_contact_name",
      "e_invoice_contact_email",
      "e_invoice_additional_info",
    ];

    if (in_array($field, $allowedFields, true)) {
      $company->update([$field => $data]);
      $this->flash(__("Kenttä päivitetty"));
      $this->resetErrors($field);
    } else {
      $this->addErrorWithFlash("global", __("Kenttä ei ole sallittu"));
    }
  }

  /**
   * Validate a single field in create mode
   *
   * @throws RuntimeException
   */
  private function validateField(string $field): void
  {
    // For create mode, we need a temporary company to get validation rules
    $tempCompany = new Company();

    $validationRule = $this->getValidationRuleForField($field, $tempCompany);

    if ($validationRule === null) {
      return;
    }

    $data = $this->getSafePropertyValue($field);

    try {
      Validator::make([$field => $data], [$field => $validationRule])->validate();
      $this->resetErrors($field);
    } catch (\Illuminate\Validation\ValidationException $e) {
      $this->handleValidationException($e);
    }

    // Check if we have enough data to create a company
    if (
      $this->name !== "" &&
      $this->business_id !== "" &&
      $this->terms_of_service_accepted === true
    ) {
      $this->createCompany();
    }
  }

  /**
   * Get validation rule for a specific field
   *
   * @return list<string | Closure>|null
   */
  private function getValidationRuleForField(string $field, Company $company): ?array
  {
    $rules = [
      "name" => ["required", "string", "max:255"],
      "business_id" => [
        "required",
        "string",
        "size:9",
        function ($attribute, string $value, callable $fail) {
          if (!$this->validateBusinessId($value)) {
            $fail(__("Y-tunnus on virheellinen"));
          }
        },
        (string) Rule::unique("companies", "business_id")->ignore($company->id),
      ],
      "fiscal_start_month" => ["numeric", "integer", "between:1,12"],
      "fiscal_start_day" => ["numeric", "integer", "between:1,31"],
      "fiscal_end_month" => ["numeric", "integer", "between:1,12"],
      "fiscal_end_day" => ["numeric", "integer", "between:1,31"],
      "consent_for_data_examination" => ["boolean"],
      "applying_for_scope1_2_mark" => [
        "boolean",
        function ($attribute, $value, callable $fail) {
          if ($value && $this->applying_for_scope1_3_mark) {
            $fail(__("Voit hakea vain joko Scope 1-2 tai Scope 1-3 merkkiä, et molempia"));
          }
        },
      ],
      "applying_for_scope1_3_mark" => [
        "boolean",
        function ($attribute, $value, callable $fail) {
          if ($value && $this->applying_for_scope1_2_mark) {
            $fail(__("Voit hakea vain joko Scope 1-2 tai Scope 1-3 merkkiä, et molempia"));
          }
        },
      ],
      "industry_classification_id" => ["nullable", "numeric", "integer"],
      "municipality_id" => ["nullable", "numeric", "integer"],
      "revenue_range_id" => ["nullable", "numeric", "integer"],
      "employee_count_range_id" => ["nullable", "numeric", "integer"],
      "e_invoice_address" => ["nullable", "string", "max:255"],
      "e_invoice_operator" => ["nullable", "string", "max:255"],
      "e_invoice_reference" => ["nullable", "string", "max:255"],
      "e_invoice_contact_name" => ["nullable", "string", "max:255"],
      "e_invoice_contact_email" => ["nullable", "email", "max:255"],
      "e_invoice_additional_info" => ["nullable", "string"],
      "terms_of_service_accepted" => ["boolean"],
    ];

    return $rules[$field] ?? null;
  }

  /**
   * Create a new company
   *
   * @throws RuntimeException
   */
  private function createCompany(): void
  {
    // Only allow creating a company in create mode
    if ($this->selectedCompanyId !== -1) {
      $this->addErrorWithFlash("global", __("Et voi luoda uutta yritystä tässä näkymässä"));

      return;
    }

    $validationRules = [
      "name" => ["required", "string", "max:255"],
      "business_id" => [
        "required",
        "string",
        "size:9",
        function ($attribute, string $value, callable $fail) {
          if (!$this->validateBusinessId($value)) {
            $fail(__("Y-tunnus on virheellinen"));
          }
        },
        "unique:companies,business_id",
      ],
      "fiscal_start_month" => ["numeric", "integer", "between:1,12"],
      "fiscal_start_day" => ["numeric", "integer", "between:1,31"],
      "fiscal_end_month" => ["numeric", "integer", "between:1,12"],
      "fiscal_end_day" => ["numeric", "integer", "between:1,31"],
      "consent_for_data_examination" => ["boolean"],
      "applying_for_scope1_2_mark" => [
        "boolean",
        function ($attribute, $value, callable $fail) {
          if ($value && $this->applying_for_scope1_3_mark) {
            $fail(__("Voit hakea vain joko Scope 1-2 tai Scope 1-3 merkkiä, et molempia"));
          }
        },
      ],
      "applying_for_scope1_3_mark" => [
        "boolean",
        function ($attribute, $value, callable $fail) {
          if ($value && $this->applying_for_scope1_2_mark) {
            $fail(__("Voit hakea vain joko Scope 1-2 tai Scope 1-3 merkkiä, et molempia"));
          }
        },
      ],
      "industry_classification_id" => ["nullable", "numeric", "integer"],
      "municipality_id" => ["nullable", "numeric", "integer"],
      "revenue_range_id" => ["nullable", "numeric", "integer"],
      "employee_count_range_id" => ["nullable", "numeric", "integer"],
      "e_invoice_address" => ["nullable", "string", "max:255"],
      "e_invoice_operator" => ["nullable", "string", "max:255"],
      "e_invoice_reference" => ["nullable", "string", "max:255"],
      "e_invoice_contact_name" => ["nullable", "string", "max:255"],
      "e_invoice_contact_email" => ["nullable", "email", "max:255"],
      "e_invoice_additional_info" => ["nullable", "string"],
    ];

    $validator = Validator::make(
      [
        "name" => $this->name,
        "business_id" => $this->business_id,
        "fiscal_start_month" => $this->fiscal_start_month,
        "fiscal_start_day" => $this->fiscal_start_day,
        "fiscal_end_month" => $this->fiscal_end_month,
        "fiscal_end_day" => $this->fiscal_end_day,
        "consent_for_data_examination" => $this->consent_for_data_examination,
        "applying_for_scope1_2_mark" => $this->applying_for_scope1_2_mark,
        "applying_for_scope1_3_mark" => $this->applying_for_scope1_3_mark,
        "industry_classification_id" => $this->industry_classification_id,
        "municipality_id" => $this->municipality_id,
        "revenue_range_id" => $this->revenue_range_id,
        "employee_count_range_id" => $this->employee_count_range_id,
        "e_invoice_address" => $this->e_invoice_address,
        "e_invoice_operator" => $this->e_invoice_operator,
        "e_invoice_reference" => $this->e_invoice_reference,
        "e_invoice_contact_name" => $this->e_invoice_contact_name,
        "e_invoice_contact_email" => $this->e_invoice_contact_email,
      ],
      $validationRules,
    );

    try {
      $validatedData = $validator->validate();
    } catch (\Illuminate\Validation\ValidationException $e) {
      $this->handleValidationException($e);

      return;
    }

    $startMonth = is_numeric($validatedData["fiscal_start_month"] ?? null)
      ? (int) $validatedData["fiscal_start_month"]
      : 1;
    $startDay = is_numeric($validatedData["fiscal_start_day"] ?? null)
      ? (int) $validatedData["fiscal_start_day"]
      : 1;
    $endMonth = is_numeric($validatedData["fiscal_end_month"] ?? null)
      ? (int) $validatedData["fiscal_end_month"]
      : 12;
    $endDay = is_numeric($validatedData["fiscal_end_day"] ?? null)
      ? (int) $validatedData["fiscal_end_day"]
      : 31;

    if (!$this->isValidDate($startMonth, $startDay)) {
      $this->addErrorWithFlash(
        "fiscal_start_month",
        __("Tilikauden aloituspäivämäärä on virheellinen"),
      );
      $this->addErrorWithFlash(
        "fiscal_start_day",
        __("Tilikauden aloituspäivämäärä on virheellinen"),
      );

      return;
    }

    if (!$this->isValidDate($endMonth, $endDay)) {
      $this->addErrorWithFlash(
        "fiscal_end_month",
        __("Tilikauden päättymispäivämäärä on virheellinen"),
      );
      $this->addErrorWithFlash(
        "fiscal_end_day",
        __("Tilikauden päättymispäivämäärä on virheellinen"),
      );

      return;
    }

    Assert::stringKeyedArray($validatedData);

    // Add the terms of service acceptance timestamp
    $validatedData["terms_of_service_accepted_at"] = now();

    DB::beginTransaction();
    try {
      $company = Company::create($validatedData);

      $user = Auth::user();
      Assert::notNull($user);

      $user->companies()->attach($company->id, [
        "is_primary" => true,
      ]);

      DB::commit();

      $this->flash(__("Yritys luotu onnistuneesti"));

      // Update the user's selected company
      $user->update(["selected_company_id" => $company->id]);

      // Redirect to the main company page (it will load the newly selected company)
      $this->redirect(route("company"));
    } catch (Throwable $e) {
      DB::rollBack();
      throw $e;
    }
  }

  /**
   * Helper method to safely get a property value
   */
  private function getSafePropertyValue(string $propertyName): string|int|bool|null
  {
    switch ($propertyName) {
      case "name":
        return $this->name;
      case "business_id":
        return $this->business_id;
      case "fiscal_start_month":
        return $this->fiscal_start_month;
      case "fiscal_start_day":
        return $this->fiscal_start_day;
      case "fiscal_end_month":
        return $this->fiscal_end_month;
      case "fiscal_end_day":
        return $this->fiscal_end_day;
      case "consent_for_data_examination":
        return $this->consent_for_data_examination;
      case "applying_for_scope1_2_mark":
        return $this->applying_for_scope1_2_mark;
      case "applying_for_scope1_3_mark":
        return $this->applying_for_scope1_3_mark;
      case "industry_classification_id":
        return $this->industry_classification_id;
      case "municipality_id":
        return $this->municipality_id;
      case "revenue_range_id":
        return $this->revenue_range_id;
      case "employee_count_range_id":
        return $this->employee_count_range_id;
      case "e_invoice_address":
        return $this->e_invoice_address;
      case "e_invoice_operator":
        return $this->e_invoice_operator;
      case "e_invoice_reference":
        return $this->e_invoice_reference;
      case "e_invoice_contact_name":
        return $this->e_invoice_contact_name;
      case "e_invoice_contact_email":
        return $this->e_invoice_contact_email;
      case "e_invoice_additional_info":
        return $this->e_invoice_additional_info;
      case "terms_of_service_accepted":
        return $this->terms_of_service_accepted;
      default:
        return null;
    }
  }

  /**
   * Load the current company
   *
   * @throws RuntimeException
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  private function loadCompany(): void
  {
    $company = $this->getCompany();

    if ($company !== null) {
      $this->name = $company->name;
      $this->business_id = $company->business_id;
      $this->fiscal_start_month = $company->fiscal_start_month;
      $this->fiscal_start_day = $company->fiscal_start_day;
      $this->fiscal_end_month = $company->fiscal_end_month;
      $this->fiscal_end_day = $company->fiscal_end_day;
      $this->consent_for_data_examination = $company->consent_for_data_examination;
      $this->applying_for_scope1_2_mark = $company->applying_for_scope1_2_mark;
      $this->applying_for_scope1_3_mark = $company->applying_for_scope1_3_mark;
      $this->industry_classification_id = $company->industry_classification_id;
      $this->municipality_id = $company->municipality_id;
      $this->revenue_range_id = $company->revenue_range_id;
      $this->employee_count_range_id = $company->employee_count_range_id;
      $this->e_invoice_address = $company->e_invoice_address ?? "";
      $this->e_invoice_operator = $company->e_invoice_operator ?? "";
      $this->e_invoice_reference = $company->e_invoice_reference ?? "";
      $this->e_invoice_contact_name = $company->e_invoice_contact_name ?? "";
      $this->e_invoice_contact_email = $company->e_invoice_contact_email ?? "";
      $this->e_invoice_additional_info = $company->e_invoice_additional_info ?? "";
    }
  }

  /**
   * Get the company for the current page
   *
   * @throws RuntimeException
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  private function getCompany(): ?Company
  {
    $currentUser = Auth::user();
    Assert::notNull($currentUser);

    // If we're in create mode (selectedCompanyId is -1), return null
    if ($this->selectedCompanyId === -1) {
      return null;
    }

    // Get the user's selected company
    if ($currentUser->selected_company_id !== null) {
      $company = Company::find($currentUser->selected_company_id);

      if ($company === null) {
        return null;
      }

      Gate::authorize(Abilities::VIEW_COMPANY, $company);

      return $company;
    }

    // No selected company, return user's first company
    return $currentUser->companies()->first();
  }

  /**
   * Reset form fields to default values
   */
  private function resetFormFields(): void
  {
    $this->name = "";
    $this->business_id = "";
    $this->fiscal_start_month = 1;
    $this->fiscal_start_day = 1;
    $this->fiscal_end_month = 12;
    $this->fiscal_end_day = 31;
    $this->consent_for_data_examination = false;
    $this->applying_for_scope1_2_mark = false;
    $this->applying_for_scope1_3_mark = false;
    $this->industry_classification_id = null;
    $this->municipality_id = null;
    $this->revenue_range_id = null;
    $this->employee_count_range_id = null;
    $this->newUserEmail = "";
    $this->e_invoice_address = "";
    $this->e_invoice_operator = "";
    $this->e_invoice_reference = "";
    $this->e_invoice_contact_name = "";
    $this->e_invoice_contact_email = "";
    $this->e_invoice_additional_info = "";
    $this->terms_of_service_accepted = false;
  }

  /**
   * Validate if a month/day combination is a valid date
   */
  private function isValidDate(int $month, int $day): bool
  {
    return checkdate($month, $day, 2025);
  }

  /**
   * Validate Finnish business ID
   */
  private function validateBusinessId(string $businessId): bool
  {
    // Some old company id's have only 6 digits. They should be prefixed with 0.
    if (preg_match("/^[0-9]{6}\\-[0-9]{1}/", $businessId) === 1) {
      $businessId = "0" . $businessId;
    }

    // Ensure that the company ID is entered in correct format.
    if (preg_match("/^[0-9]{7}\\-[0-9]{1}/", $businessId) !== 1) {
      return false;
    }

    $parts = explode("-", $businessId);
    Assert::true(array_key_exists(1, $parts));
    [$id, $checksum] = $parts;
    $checksum = (int) $checksum;

    $totalCount = 0;
    $multipliers = [7, 9, 10, 5, 8, 4, 2];
    foreach ($multipliers as $key => $multiplier) {
      if (!isset($id[$key]) || !is_numeric($id[$key])) {
        return false;
      }
      $totalCount = $totalCount + $multiplier * (int) $id[$key];
    }

    $remainder = $totalCount % 11;

    // Remainder 1 is not valid.
    if ($remainder === 1) {
      return false;
    }

    // Remainder 0 leads into checksum 0.
    if ($remainder === 0) {
      return $checksum === $remainder;
    }

    // If remainder is not 0, the checksum should be remainder deducted from 11.
    return $checksum === 11 - $remainder;
  }
}
