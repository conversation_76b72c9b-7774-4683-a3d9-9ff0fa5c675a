<?php

declare(strict_types=1);

namespace App\Livewire;

use App\DataTransferObjects\CalculationDefinitionDto;
use App\DataTransferObjects\CategoryDto;
use App\DataTransferObjects\CompoundUnitDto;
use App\DataTransferObjects\GroupingDto;
use App\DataTransferObjects\ScopeDto;
use App\DataTransferObjects\SelectOption;
use App\DataTransferObjects\SelectOptionDto;
use App\Enums\Abilities;
use App\Enums\SelectOptionType;
use App\Helpers\Assert;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionYear;
use App\Models\Category;
use App\Models\CompoundUnit;
use App\Models\Grouping;
use App\Services\CalculationDefinitionService;
use App\Services\CompanyContextService;
use App\Services\EmissionFactorValueService;
use App\Services\GroupingStateService;
use App\Services\SelectOptionService;
use App\Services\YearService;
use Brick\Math\BigDecimal;
use Brick\Math\Exception\DivisionByZeroException;
use Brick\Math\Exception\MathException;
use Brick\Math\Exception\NumberFormatException;
use Brick\Math\Exception\RoundingNecessaryException;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Component;
use RuntimeException;
use Throwable;

final class EmissionFactorsPage extends Component
{
  /**
   * @var array<int, array<int, string|null>>
   */
  public array $values = [];

  /**
   * @var array<int, array<int, string|null>>
   */
  public array $sources = [];

  /**
   * @var array<int, array<int, string>>
   */
  #[Locked]
  public array $defaultSources = [];

  /**
   * @var array<int, array<int, bool>>
   */
  public array $isDefault = [];

  /**
   * @var array<int, string>
   */
  public array $errors = [];

  public ?int $currentYear = null;

  /**
   * @var list<int>
   */
  #[Locked]
  public array $allYears = [];

  /**
   * @var list<int>
   */
  #[Locked]
  public array $tableYears = [];

  /**
   * @var array<int, array<int, array<int, list<CalculationDefinitionDto>>>>
   */
  #[Locked]
  public array $scopedGroupedCategorizedDefinitions = [];

  /**
   * @var list<ScopeDto>
   */
  #[Locked]
  public array $availableScopes = [];

  /**
   * @var array<int, list<CategoryDto>>
   */
  #[Locked]
  public array $scopeCategories = [];

  /**
   * @var array<int, list<GroupingDto>>
   */
  #[Locked]
  public array $scopeGroupings = [];

  /**
   * @var array<int, array<int, string>>
   */
  #[Locked]
  public array $helpTexts = [];

  /**
   * Editing fields
   */
  /**
   * @var array<int, string|null>
   */
  public array $definitionNames = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionCompoundUnitIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionScopeIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionCategoryIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionGroupingIds = [];

  /**
   * @var array<int, string|null>
   */
  public array $definitionDefaultValues = [];

  /**
   * Source modal properties
   */
  public ?int $sourceDefinitionId = null;

  public ?int $sourceYear = null;

  public ?string $sourceText = null;

  public ?string $sourceDefaultText = null;

  public ?string $sourceDefinitionName = null;

  public bool $isSourceModalOpen = false;

  /**
   * Grouping visibility properties
   */
  /**
   * Track which groupings are hidden
   *
   * @var array<string, bool>
   */
  public array $hiddenGroupings = [];

  /**
   * Store select option IDs for groupings
   *
   * @var array<string, int|null>
   */
  public array $selectOptionIds = [];

  /**
   * Store custom reasons for groupings
   *
   * @var array<string, string>
   */
  public array $customReasons = [];

  /**
   * Available hiding reason options
   *
   * @var list<SelectOptionDto>
   */
  #[Locked]
  public array $hidingReasonOptions = [];

  /**
   * @var list<CategoryDto>
   */
  #[Locked]
  public array $categories = [];

  /**
   * @var list<GroupingDto>
   */
  #[Locked]
  public array $groupings = [];

  /**
   * @var list<CompoundUnitDto>
   */
  #[Locked]
  public array $compoundUnits = [];

  /**
   * Service properties
   */
  private YearService $yearService;

  private EmissionFactorValueService $valueService;

  private CalculationDefinitionService $definitionService;

  private CompanyContextService $companyService;

  private GroupingStateService $groupingStateService;

  private SelectOptionService $selectOptionService;

  /**
   * Dependency injection via boot method
   */
  public function boot(
    YearService $yearService,
    EmissionFactorValueService $valueService,
    CalculationDefinitionService $definitionService,
    CompanyContextService $companyService,
    GroupingStateService $groupingStateService,
    SelectOptionService $selectOptionService,
  ): void {
    $this->yearService = $yearService;
    $this->valueService = $valueService;
    $this->definitionService = $definitionService;
    $this->companyService = $companyService;
    $this->groupingStateService = $groupingStateService;
    $this->selectOptionService = $selectOptionService;
  }

  /**
   * Check if a grouping is hidden
   */
  public function isGroupingHidden(int $scopeId, int $groupingId): bool
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);

    return isset($this->hiddenGroupings[$key]) && $this->hiddenGroupings[$key];
  }

  /**
   * Get the hiding reason for a grouping
   */
  public function getHidingReason(int $scopeId, int $groupingId): string
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);
    $selectOptionId = $this->selectOptionIds[$key] ?? null;

    // If selectOptionId is not null, it's a predefined reason
    if ($selectOptionId !== null) {
      // Find the label for this select option
      foreach ($this->hidingReasonOptions as $option) {
        if ($option->value === $selectOptionId) {
          return $option->label;
        }
      }
    }

    // Return custom reason if selectOptionId is null
    return $this->customReasons[$key] ?? "";
  }

  /**
   * Mount the component
   *
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   */
  public function mount(?int $year = null): void
  {
    try {
      // Initialize arrays
      $this->availableScopes = [];

      // Load all available years
      $this->loadYears();

      if ($year !== null && in_array($year, $this->allYears, true)) {
        $this->currentYear = $year;
      } else {
        $this->currentYear = $this->yearService->getCurrentYear();
      }

      // Get company from context service
      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));

        return;
      }

      // Check authorization
      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      // Filter to only include current year and previous year for the table
      $previousYear = $this->currentYear - 1;
      $this->tableYears = array_values(
        array_filter($this->allYears, function ($year) use ($previousYear) {
          return $year === $this->currentYear || $year === $previousYear;
        }),
      );

      // Sort years (to ensure previous year comes first if it exists)
      sort($this->tableYears);

      // Load hiding reason options
      $this->loadHidingReasonOptions();

      // Load grouping states for the current company and year
      $this->loadGroupingStates();

      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_view_permission"));
    } catch (RuntimeException | InvalidArgumentException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.loading_failed"));
    }
  }

  /**
   * Save an individual field value for the current year
   *
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   */
  public function saveField(int $definitionId, ?string $value): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.action_not_available"));

        return;
      }

      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      // Clear previous error for this field
      unset($this->errors[$definitionId]);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->errors[$definitionId] = __("emissions.errors.definition_not_found");

        return;
      }

      // Get year-specific default value from pivot
      $yearModel = $definition->years->where("year", $this->currentYear)->first();
      $defaultValue = null;
      if ($yearModel !== null) {
        $pivot = $yearModel->getAttribute("pivot");
        Assert::instanceOf($pivot, CalculationDefinitionYear::class);
        if ($pivot->emission_factor_default_value !== null) {
          $defaultValue = $pivot->emission_factor_default_value;
        }
      }

      // Check if value matches default and no override exists
      if ($value !== null && $value !== "" && $defaultValue !== null && $defaultValue === $value) {
        $companyId = $company->id;
        $existingOverride = false;

        $existingValue = $this->valueService->findValue(
          $definitionId,
          $companyId,
          $this->currentYear,
        );

        $existingOverride = $existingValue !== null;

        if (!$existingOverride) {
          $this->isDefault[$definitionId][$this->currentYear] = true;
          $this->values[$definitionId][$this->currentYear] = $value;

          $this->dispatch("notify", [
            "message" => __("emissions.labels.using_default"),
            "type" => "info",
          ]);

          return;
        }
      }

      // Save the value
      $result = $this->valueService->saveValue($definitionId, $value, $this->currentYear);

      if ($result["success"]) {
        // Update local data
        if ($value === null || $value === "") {
          $this->values[$definitionId][$this->currentYear] = null;
          $this->isDefault[$definitionId][$this->currentYear] = $defaultValue !== null;
        } else {
          $this->values[$definitionId][$this->currentYear] = $value;
          $this->isDefault[$definitionId][$this->currentYear] = false;
        }

        $this->dispatch("notify", [
          "message" => $result["message"],
          "type" => "success",
        ]);
      } else {
        $this->errors[$definitionId] = $result["message"];
      }
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_edit_permission"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.save_failed"));
    }
  }

  /**
   * Open the source modal for a definition and year
   *
   * @throws RuntimeException
   */
  public function openSourceModal(int $definitionId, int $year): void
  {
    try {
      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      $this->sourceDefinitionId = $definitionId;
      $this->sourceYear = $year;
      $this->sourceText = $this->sources[$definitionId][$year] ?? null;

      // Get the default source for placeholder
      $this->sourceDefaultText = $this->defaultSources[$definitionId][$year] ?? null;

      $this->sourceDefinitionName = $this->definitionNames[$definitionId] ?? "";

      $this->isSourceModalOpen = true;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_view_permission"));
    }
  }

  /**
   * Close the source modal
   */
  public function closeSourceModal(): void
  {
    $this->isSourceModalOpen = false;
    $this->sourceDefinitionId = null;
    $this->sourceYear = null;
    $this->sourceText = null;
    $this->sourceDefaultText = null;
    $this->sourceDefinitionName = null;
  }

  /**
   * Save the source for a definition and year
   *
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   * @throws RuntimeException
   */
  public function saveSource(): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.action_not_available"));
        $this->closeSourceModal();

        return;
      }

      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));
        $this->closeSourceModal();

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      if ($this->sourceDefinitionId === null || $this->sourceYear === null) {
        $this->closeSourceModal();

        return;
      }

      $result = $this->valueService->saveSource(
        $this->sourceDefinitionId,
        $this->sourceText ?? "",
        $this->sourceYear,
      );

      if ($result["success"]) {
        // Update local data
        $this->sources[$this->sourceDefinitionId][$this->sourceYear] = $this->sourceText;

        $this->dispatch("notify", [
          "message" => __("emissions.messages.source_updated"),
          "type" => "success",
        ]);
      } else {
        $this->dispatch("notify", [
          "message" => $result["message"],
          "type" => "error",
        ]);
      }

      $this->closeSourceModal();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_edit_permission"));
      $this->closeSourceModal();
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.source_save_failed"));
      $this->closeSourceModal();
    }
  }

  /**
   * Update definition name
   */
  public function updateDefinitionName(int $definitionId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.action_not_available"));

        return;
      }

      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.definition_not_found"),
          "type" => "error",
        ]);

        return;
      }

      // Verify the definition belongs to the current company
      if ($definition->company_id !== $company->id) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.can_only_edit_own"),
          "type" => "error",
        ]);

        return;
      }

      $customName = $this->definitionNames[$definitionId] ?? "";
      $emissionFactorCompoundUnitId = $this->getCompoundUnitIdFromForm($definitionId);
      $scopeId = $this->getScopeIdFromForm($definitionId);
      $categoryId = $this->getCategoryIdFromForm($definitionId);
      $groupingId = $this->getGroupingIdFromForm($definitionId);

      if ($scopeId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.scope_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($categoryId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.category_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($groupingId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.grouping_missing"),
          "type" => "error",
        ]);

        return;
      }

      $this->definitionService->updateDefinition(
        $definitionId,
        $customName,
        $scopeId,
        $categoryId,
        $groupingId,
        $definition->data_unit_id ?? null, // Preserve data unit
        $emissionFactorCompoundUnitId,
      );

      $this->dispatch("notify", [
        "message" => __("emissions.messages.name_updated"),
        "type" => "success",
      ]);

      // Reload all data to ensure consistency
      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_edit_permission"));
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      $this->addErrorWithFlash(
        "global",
        __("emissions.errors.definition_not_found_or_unauthorized"),
      );
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.name_update_failed"));
    }
  }

  /**
   * Update definition compound unit
   */
  public function updateDefinitionUnit(int $definitionId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.action_not_available"));

        return;
      }

      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("emissions.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.definition_not_found"),
          "type" => "error",
        ]);

        return;
      }

      // Verify the definition belongs to the current company
      if ($definition->company_id !== $company->id) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.can_only_edit_own"),
          "type" => "error",
        ]);

        return;
      }

      $customName = $this->definitionNames[$definitionId] ?? "";
      $emissionFactorCompoundUnitId = $this->getCompoundUnitIdFromForm($definitionId);
      $scopeId = $this->getScopeIdFromForm($definitionId);
      $categoryId = $this->getCategoryIdFromForm($definitionId);
      $groupingId = $this->getGroupingIdFromForm($definitionId);

      if ($scopeId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.scope_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($categoryId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.category_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($groupingId === null) {
        $this->dispatch("notify", [
          "message" => __("emissions.errors.grouping_missing"),
          "type" => "error",
        ]);

        return;
      }

      $this->definitionService->updateDefinition(
        $definitionId,
        $customName,
        $scopeId,
        $categoryId,
        $groupingId,
        $definition->data_unit_id ?? null, // Preserve data unit
        $emissionFactorCompoundUnitId,
      );

      $this->dispatch("notify", [
        "message" => __("emissions.messages.unit_updated"),
        "type" => "success",
      ]);

      // Reload all data to ensure consistency
      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("emissions.errors.no_edit_permission"));
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      $this->addErrorWithFlash(
        "global",
        __("emissions.errors.definition_not_found_or_unauthorized"),
      );
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.unit_update_failed"));
    }
  }

  /**
   * Get help text for a definition and year
   */
  public function getHelpText(int $definitionId, int $year): ?string
  {
    return $this->helpTexts[$definitionId][$year] ?? null;
  }

  /**
   * Render the component
   *
   * @throws RuntimeException
   */
  #[Layout("components.layouts.app")]
  public function render(): View
  {
    $company = $this->companyService->getCurrentCompany();

    return view("livewire.emission-factors-page", [
      "company" => $company,
      "availableScopes" => $this->availableScopes,
      "scopedGroupedCategorizedDefinitions" => $this->scopedGroupedCategorizedDefinitions,
      "scopeCategories" => $this->scopeCategories,
      "scopeGroupings" => $this->scopeGroupings,
      "allYears" => $this->allYears,
      "tableYears" => $this->tableYears,
      "currentYear" => $this->currentYear,
      "compoundUnits" => $this->compoundUnits,
      "categories" => $this->categories,
      "groupings" => $this->groupings,
    ]);
  }

  /**
   * Get years with pivot for a definition
   *
   * @return Collection<int, \App\Models\Year&object{pivot: CalculationDefinitionYear}>
   */
  private function getYearsWithPivot(CalculationDefinition $definition): Collection
  {
    /** @phpstan-ignore-next-line return.type */
    return $definition->years;
  }

  /**
   * Get a grouping key for flat array storage
   */
  private function getGroupingKey(int $scopeId, int $groupingId): string
  {
    return $scopeId . "." . $groupingId;
  }

  /**
   * Reload all data to prevent lazy loading issues
   *
   * @throws RuntimeException
   */
  private function reloadAllData(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    try {
      $this->loadDefinitions();
      $this->loadValues();
      $this->initializeDefinitionFields();
      $this->loadCompoundUnits();
      $this->loadCategoriesData();
      $this->loadGroupingsData();
    } catch (DivisionByZeroException | NumberFormatException | RoundingNecessaryException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("emissions.errors.number_processing"));
    }
  }

  /**
   * Initialize editable fields for all definitions
   */
  private function initializeDefinitionFields(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;
      if (!isset($this->scopedGroupedCategorizedDefinitions[$scopeId])) {
        continue;
      }

      foreach ($this->scopedGroupedCategorizedDefinitions[$scopeId] as $groupingId => $categories) {
        foreach ($categories as $categoryId => $definitions) {
          foreach ($definitions as $definitionDto) {
            $this->definitionNames[$definitionDto->id] = $definitionDto->customName;
            $this->definitionCompoundUnitIds[$definitionDto->id] =
              $definitionDto->emissionFactorCompoundUnitId;
            $this->definitionScopeIds[$definitionDto->id] = $definitionDto->scopeId;
            $this->definitionCategoryIds[$definitionDto->id] = $definitionDto->categoryId;
            $this->definitionGroupingIds[$definitionDto->id] = $definitionDto->groupingId;

            $defaultFromDto = $definitionDto->emissionFactorDefaults[$this->currentYear] ?? null;
            if ($defaultFromDto !== null && $defaultFromDto !== "") {
              try {
                $this->definitionDefaultValues[$definitionDto->id] = (string) BigDecimal::of(
                  $defaultFromDto,
                )->stripTrailingZeros();
              } catch (MathException $e) {
                report($e);
                $this->definitionDefaultValues[$definitionDto->id] = $defaultFromDto;
              }
            } else {
              $this->definitionDefaultValues[$definitionDto->id] = null;
            }
            // Load default sources from DTO
            if (isset($definitionDto->emissionFactorDefaultSources[$this->currentYear])) {
              $this->defaultSources[$definitionDto->id][$this->currentYear] =
                $definitionDto->emissionFactorDefaultSources[$this->currentYear];
            }

            // Use emission factor help texts for this page
            $this->helpTexts[$definitionDto->id] = $definitionDto->emissionFactorHelpTexts;
          }
        }
      }
    }
  }

  /**
   * Load all definitions sorted by scope, grouping, and category
   *
   * @throws RuntimeException
   */
  private function loadDefinitions(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    // Get all available scopes and convert to DTOs
    $scopes = $this->definitionService->getAvailableScopes($this->currentYear);
    $this->availableScopes = [];
    foreach ($scopes as $scope) {
      $this->availableScopes[] = ScopeDto::fromModel($scope);
    }

    // Initialize the nested arrays
    $this->scopedGroupedCategorizedDefinitions = [];
    $this->scopeCategories = [];
    $this->scopeGroupings = [];

    // Collect ALL definitions and pivot IDs first
    $allDefinitions = collect();
    $allPivotIds = collect();

    // First pass: collect all definitions and pivot IDs
    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;

      // Get only emission factor definitions for this scope
      $definitions = $this->definitionService->getDefinitions($this->currentYear, $scopeId);

      // Filter to only visible definitions on emission factors page
      $filteredDefinitions = $definitions->filter(function ($definition) {
        return !$definition->hide_from_emission_factor_page;
      });

      $allDefinitions = $allDefinitions->merge($filteredDefinitions);

      // Collect pivot IDs from each definition's years
      foreach ($filteredDefinitions as $definition) {
        $yearsWithPivot = $this->getYearsWithPivot($definition);
        $pivotIds = $yearsWithPivot->map(function ($year) {
          return $year->pivot->id;
        });
        $allPivotIds = $allPivotIds->merge($pivotIds);
      }
    }

    // Load ALL pivot translations in ONE query
    $pivotTranslations = new Collection();
    if ($allPivotIds->isNotEmpty()) {
      $pivotTranslations = \App\Models\CalculationDefinitionYearTranslation::whereIn(
        "calculation_definition_year_id",
        $allPivotIds->unique()->values(),
      )
        ->where("locale", app()->getLocale())
        ->get()
        ->keyBy("calculation_definition_year_id");
    }

    // Second pass: build DTOs with pre-loaded translations
    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;

      // Initialize arrays for this scope
      $this->scopedGroupedCategorizedDefinitions[$scopeId] = [];
      $this->scopeCategories[$scopeId] = [];
      $this->scopeGroupings[$scopeId] = [];

      // Get only emission factor definitions for this scope
      $definitions = $this->definitionService->getDefinitions($this->currentYear, $scopeId);

      // Filter to only visible definitions on emission factors page
      $filteredDefinitions = $definitions->filter(function ($definition) {
        return !$definition->hide_from_emission_factor_page;
      });

      // Group definitions by grouping and then by category
      $groupedCategorizedDefinitions = $filteredDefinitions
        ->groupBy("grouping_id")
        ->map(function ($groupDefinitions) {
          return $groupDefinitions->groupBy("category_id");
        });

      // Store grouping-categorized definitions as DTOs
      foreach ($groupedCategorizedDefinitions as $groupingId => $categorizedDefs) {
        $groupingIdInt = (int) $groupingId;
        $this->scopedGroupedCategorizedDefinitions[$scopeId][$groupingIdInt] = [];

        foreach ($categorizedDefs as $categoryId => $defs) {
          $categoryIdInt = (int) $categoryId;
          $defsArray = [];
          foreach ($defs as $definition) {
            $yearsWithPivot = $this->getYearsWithPivot($definition);

            // Set translations on pivots before creating DTO
            $yearsWithPivot->each(function ($year) use ($pivotTranslations) {
              $year->pivot->setRelation(
                "translation",
                $pivotTranslations[$year->pivot->id] ?? null,
              );
            });

            $defsArray[] = CalculationDefinitionDto::fromModel($definition, $yearsWithPivot);
          }
          $this->scopedGroupedCategorizedDefinitions[$scopeId][$groupingIdInt][
            $categoryIdInt
          ] = $defsArray;
        }
      }

      // Get all categories for this scope as DTOs
      $categories = $this->definitionService->getCategoriesForScope($scopeId);
      foreach ($categories as $category) {
        $this->scopeCategories[$scopeId][] = CategoryDto::fromModel(
          $category,
          $category->translate()->title ?? "",
          $category->translate()->description ?? "",
        );
      }

      // Get all groupings for this scope as DTOs
      $groupings = $this->definitionService->getGroupingsForScope($scopeId);
      foreach ($groupings as $grouping) {
        $this->scopeGroupings[$scopeId][] = GroupingDto::fromModel(
          $grouping,
          $grouping->translate()->title ?? "",
        );
      }
    }
  }

  /**
   * Convert form compound unit ID to integer
   */
  private function getCompoundUnitIdFromForm(int $definitionId): int
  {
    if (isset($this->definitionCompoundUnitIds[$definitionId])) {
      $formValue = $this->definitionCompoundUnitIds[$definitionId];

      return (int) $formValue;
    }

    // Default to first compound unit if value is missing or invalid
    return $this->getDefaultCompoundUnitId();
  }

  /**
   * Get scope from form value
   */
  private function getScopeIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionScopeIds[$definitionId])) {
      $formValue = $this->definitionScopeIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get category from form value
   */
  private function getCategoryIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionCategoryIds[$definitionId])) {
      $formValue = $this->definitionCategoryIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get grouping from form value
   */
  private function getGroupingIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionGroupingIds[$definitionId])) {
      $formValue = $this->definitionGroupingIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get a default compound unit ID to use
   */
  private function getDefaultCompoundUnitId(): int
  {
    if (count($this->compoundUnits) > 0) {
      return $this->compoundUnits[0]->id;
    }

    $compoundUnit = CompoundUnit::first();

    if ($compoundUnit === null) {
      // Assuming there's at least one compound unit in the database
      return 1;
    }

    return $compoundUnit->id;
  }

  /**
   * Load compound units
   */
  private function loadCompoundUnits(): void
  {
    $units = CompoundUnit::with([
      "numeratorUnit.translations",
      "denominatorUnit.translations",
    ])->get();

    $this->compoundUnits = [];
    foreach ($units as $unit) {
      $this->compoundUnits[] = CompoundUnitDto::fromModel(
        $unit,
        $unit->numeratorUnit?->translate()->symbol ?? "",
        $unit->denominatorUnit?->translate()->symbol ?? "",
      );
    }
  }

  /**
   * Load categories data
   */
  private function loadCategoriesData(): void
  {
    $categories = Category::with("translation")->orderByTranslation("title")->get();
    $this->categories = [];
    foreach ($categories as $category) {
      $this->categories[] = CategoryDto::fromModel(
        $category,
        $category->translate()->title ?? "",
        $category->translate()->description ?? "",
      );
    }
  }

  /**
   * Load groupings data
   */
  private function loadGroupingsData(): void
  {
    $groupings = Grouping::with("translation")->orderByTranslation("title")->get();
    $this->groupings = [];
    foreach ($groupings as $grouping) {
      $this->groupings[] = GroupingDto::fromModel($grouping, $grouping->translate()->title ?? "");
    }
  }

  /**
   * Load available years with data
   *
   * @throws RuntimeException
   */
  private function loadYears(): void
  {
    // Get all years from the YearService
    $years = $this->yearService->getAllYears();

    // Ensure it's a list (consecutive integer keys)
    $this->allYears = $years;
  }

  /**
   * Load hiding reason options from SelectOption table
   *
   * @throws InvalidArgumentException
   */
  private function loadHidingReasonOptions(): void
  {
    // Service returns SelectOption DTOs, convert them to wireable SelectOptionDto
    $options = $this->selectOptionService->getSelectOptions(
      SelectOptionType::GROUPING_HIDING_REASON,
    );

    $this->hidingReasonOptions = [];
    foreach ($options as $option) {
      $this->hidingReasonOptions[] = SelectOptionDto::fromSelectOption($option);
    }
  }

  /**
   * Load grouping states from database
   */
  private function loadGroupingStates(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    try {
      $company = $this->companyService->getCurrentCompany();
      if ($company === null) {
        return;
      }

      $states = $this->groupingStateService->loadGroupingStates($company, $this->currentYear);
      $this->hiddenGroupings = $states["hiddenGroupings"];
      $this->selectOptionIds = $states["selectOptionIds"];
      $this->customReasons = $states["customReasons"];
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      // fall back to empty arrays on error
      $this->hiddenGroupings = [];
      $this->selectOptionIds = [];
      $this->customReasons = [];
    }
  }

  /**
   * Load all emission factor values for all definitions and years
   *
   * @throws DivisionByZeroException
   * @throws NumberFormatException
   * @throws RoundingNecessaryException
   * @throws RuntimeException
   */
  private function loadValues(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    $allDefinitions = new Collection();

    // Collect all definition IDs from DTOs
    $definitionIds = [];
    foreach ($this->scopedGroupedCategorizedDefinitions as $scopeDefs) {
      foreach ($scopeDefs as $groupingDefs) {
        foreach ($groupingDefs as $categoryDefs) {
          foreach ($categoryDefs as $dto) {
            $definitionIds[] = $dto->id;
          }
        }
      }
    }

    if (count($definitionIds) === 0) {
      return;
    }

    // Load models by IDs
    $allDefinitions = CalculationDefinition::whereIn("id", $definitionIds)->with("years")->get();

    // Ensure years have proper pivot relationships
    $allDefinitions->each(function ($definition) {
      $definition->years->each(function ($year) {
        Assert::instanceOf($year->getAttribute("pivot"), CalculationDefinitionYear::class);
      });
    });

    $valueData = $this->valueService->loadValuesForYears($allDefinitions, $this->tableYears);

    $this->values = $valueData["values"];
    $this->isDefault = $valueData["isDefault"];
    $this->sources = $valueData["sources"];
  }

  /**
   * Safe error reporting
   */
  private function safeReport(Throwable $e): void
  {
    try {
      report($e);
    } catch (Throwable $reportException) {
      Log::emergency("Failed to report exception", [
        "exception" => get_class($e),
        "message" => $e->getMessage(),
        "reportException" => get_class($reportException),
        "reportExceptionMessage" => $reportException->getMessage(),
      ]);
    }
  }

  /**
   * Add error with flash notification
   */
  private function addErrorWithFlash(string $field, string $message): void
  {
    $this->addError($field, $message);
    $this->dispatch("notify", [
      "message" => $message,
      "type" => "error",
    ]);
  }
}
