<?php

declare(strict_types=1);

namespace App\Livewire;

use App\DataTransferObjects\GroupingTotal;
use App\DataTransferObjects\MetricDefinitionDto;
use App\DataTransferObjects\ScopeVariantTotal;
use App\DataTransferObjects\SelectOption;
use App\DataTransferObjects\SelectOptionDto;
use App\Enums\Abilities;
use App\Enums\SelectOptionType;
use App\Helpers\Assert;
use App\Helpers\NumericStringFormatter;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionYear;
use App\Models\MetricDefinition;
use App\Models\MetricDefinitionYear;
use App\Models\Scope;
use App\Models\Unit;
use App\Services\CompanyContextService;
use App\Services\EmissionCalculationService;
use App\Services\ExpressionEvaluatorService;
use App\Services\GroupingStateService;
use App\Services\MetricDefinitionService;
use App\Services\MetricValueService;
use App\Services\SelectOptionService;
use App\Services\YearService;
use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use InvalidArgumentException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Component;
use LogicException;
use RuntimeException;
use Symfony\Component\ExpressionLanguage\SyntaxError;
use Throwable;

final class ResultsPage extends Component
{
  public ?int $currentYear = null;

  /**
   * Grouping visibility properties
   */
  /**
   * Track which groupings are hidden
   *
   * @var array<string, bool>
   */
  public array $hiddenGroupings = [];

  /**
   * Store select option IDs for groupings
   *
   * @var array<string, int|null>
   */
  public array $selectOptionIds = [];

  /**
   * Store custom reasons for groupings
   *
   * @var array<string, string>
   */
  public array $customReasons = [];

  /** @var list<int> */
  #[Locked]
  private array $allYears = [];

  /** @var list<int> */
  #[Locked]
  private array $tableYears = [];

  /**
   * @var array<int, array<int, string>>
   */
  #[Locked]
  private array $helpTexts = [];

  /**
   * @var array<int, array<int, \App\DataTransferObjects\Result>>
   */
  #[Locked]
  private array $allResults = [];

  /**
   * @var array<int, \App\DataTransferObjects\TotalEmissions>
   */
  #[Locked]
  private array $allTotals = [];

  /**
   * @var array<int, array<int, string>>
   */
  #[Locked]
  private array $scopeTotals = [];

  /**
   * @var array<int, GroupingTotal>
   */
  #[Locked]
  private array $groupingTotals = [];

  /**
   * @var array<int, CalculationDefinition>
   */
  #[Locked]
  private array $displayDefinitions = [];

  /**
   * @var list<ScopeVariantTotal>
   */
  #[Locked]
  private array $scopeVariantTotals = [];

  /**
   * Metric-related properties
   */
  /**
   * @var list<MetricDefinitionDto>
   */
  #[Locked]
  private array $metricDefinitions = [];

  /**
   * @var array<int, array<int, string|null>>
   */
  #[Locked]
  private array $metricValues = [];

  /**
   * @var array<int, string>
   */
  #[Locked]
  private array $metricHelpTexts = [];

  /**
   * Available hiding reason options (for display only)
   *
   * @var list<SelectOptionDto>
   */
  #[Locked]
  private array $hidingReasonOptions = [];

  /**
   * Track which scopes allow hiding groupings
   *
   * @var array<int, bool>
   */
  #[Locked]
  private array $scopeAllowsHiding = [];

  /**
   * Track which categories have hidden totals
   *
   * @var array<int, bool>
   */
  #[Locked]
  private array $categoryHideTotals = [];

  /**
   * The emission result unit
   */
  #[Locked]
  private ?Unit $emissionUnit = null;

  /**
   * Mark award status and file paths
   */
  #[Locked]
  private bool $hasScope12Mark = false;

  #[Locked]
  private bool $hasScope13Mark = false;

  #[Locked]
  private ?string $scope12MarkImage = null;

  #[Locked]
  private ?string $scope13MarkImage = null;

  #[Locked]
  private ?string $scope12CriteriaPdf = null;

  #[Locked]
  private ?string $scope13CriteriaPdf = null;

  private YearService $yearService;

  private EmissionCalculationService $calculationService;

  private CompanyContextService $companyService;

  private MetricDefinitionService $metricDefinitionService;

  private MetricValueService $metricValueService;

  private GroupingStateService $groupingStateService;

  private SelectOptionService $selectOptionService;

  private ExpressionEvaluatorService $expressionEvaluator;

  private NumericStringFormatter $numberFormatter;

  public function boot(
    YearService $yearService,
    EmissionCalculationService $calculationService,
    CompanyContextService $companyService,
    MetricDefinitionService $metricDefinitionService,
    MetricValueService $metricValueService,
    GroupingStateService $groupingStateService,
    SelectOptionService $selectOptionService,
    ExpressionEvaluatorService $expressionEvaluator,
  ): void {
    $this->yearService = $yearService;
    $this->calculationService = $calculationService;
    $this->companyService = $companyService;
    $this->metricDefinitionService = $metricDefinitionService;
    $this->metricValueService = $metricValueService;
    $this->groupingStateService = $groupingStateService;
    $this->selectOptionService = $selectOptionService;
    $this->expressionEvaluator = $expressionEvaluator;
    $this->numberFormatter = new NumericStringFormatter(" ", ",");
  }

  /**
   * @throws MathException
   * @throws LogicException
   */
  public function mount(?int $year = null): void
  {
    try {
      // Initialize collections
      $this->metricDefinitions = [];
      $this->displayDefinitions = [];

      $this->allYears = $this->yearService->getAllYears();

      if ($year !== null && in_array($year, $this->allYears, true)) {
        $this->currentYear = $year;
      } else {
        $this->currentYear = $this->yearService->getCurrentYear();
      }

      // Get company from context service
      $company = $this->companyService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("results.errors.no_company_selected"));

        return;
      }

      // Check authorization
      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      // Create filtered list of years (current year + 3 past years)
      $filtered = [];
      foreach ($this->allYears as $y) {
        if ($y <= $this->currentYear && $y >= $this->currentYear - 3) {
          $filtered[] = $y;
        }
      }
      $this->tableYears = $filtered;
      sort($this->tableYears);

      // Load hiding reason options (for display only)
      $this->loadHidingReasonOptions();

      // Load grouping states for the current company and year
      $this->loadGroupingStates();

      // Load scope hiding permissions
      $this->loadScopeHidingPermissions();

      // Load category settings
      $this->loadCategorySettings();

      $this->loadDefinitions();

      // Get the results
      $this->calculateResults();

      // Calculate scope totals for chart
      $this->calculateScopeTotals();

      // Calculate grouping totals for donut chart
      $this->calculateGroupingTotals();

      // Calculate scope variant totals
      $this->calculateScopeVariantTotals();

      // Load help texts
      $this->loadHelpTexts();

      // Load metric data
      $this->loadMetricDefinitions();
      $this->loadMetricValues();

      // Load mark data
      $this->loadMarkData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("results.errors.no_view_permission"));
    } catch (RuntimeException | InvalidArgumentException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("results.errors.loading_failed"));
    }
  }

  /**
   * Get the emission unit symbol
   */
  public function getEmissionUnitSymbol(): string
  {
    return $this->emissionUnit?->translate()->symbol ?? "";
  }

  /**
   * Check if a grouping is hidden
   */
  public function isGroupingHidden(int $scopeId, int $groupingId): bool
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);

    return isset($this->hiddenGroupings[$key]) && $this->hiddenGroupings[$key];
  }

  /**
   * Check if a scope allows hiding groupings
   */
  public function scopeAllowsHiding(int $scopeId): bool
  {
    return $this->scopeAllowsHiding[$scopeId] ?? false;
  }

  /**
   * Get the hiding reason for a grouping
   */
  public function getHidingReason(int $scopeId, int $groupingId): string
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);
    $selectOptionId = $this->selectOptionIds[$key] ?? null;

    // If selectOptionId is not null, it's a predefined reason
    if ($selectOptionId !== null) {
      // Find the label for this select option
      foreach ($this->hidingReasonOptions as $option) {
        if ($option->value === $selectOptionId) {
          return $option->label;
        }
      }

      // Fallback if option not found
      return __("results.fields.reason_from_list");
    }

    // Otherwise return the custom reason
    return $this->customReasons[$key] ?? "";
  }

  /**
   * Check if a category should hide its total
   */
  public function shouldHideCategoryTotal(int $categoryId): bool
  {
    return $this->categoryHideTotals[$categoryId] ?? false;
  }

  /**
   * @throws RuntimeException
   * @throws MathException
   */
  #[Layout("components.layouts.app")]
  public function render(): View
  {
    $company = $this->companyService->getCurrentCompany();
    $definitions = $this->getResultDefinitions();
    $scopedGroupedDefinitions = $this->organizeByScopeGroupingAndCategory($definitions);

    // Get the year ID for the current year
    $yearModel =
      $this->currentYear !== null ? $this->yearService->getYearByValue($this->currentYear) : null;
    $yearId = $yearModel?->id;

    // Calculate total for current year
    $currentYearTotal =
      $this->currentYear !== null ? $this->calculateScopeTotal($this->currentYear) : "0";
    $formattedCurrentYearTotal = $this->formatValue($currentYearTotal);

    return view("livewire.results-page", [
      "company" => $company,
      "definitions" => $definitions,
      "scopedGroupedDefinitions" => $scopedGroupedDefinitions,
      "allYears" => $this->allYears,
      "tableYears" => $this->tableYears,
      "currentYear" => $this->currentYear,
      "helpTexts" => $this->helpTexts,
      "yearId" => $yearId,
      "allResults" => $this->allResults,
      "allTotals" => $this->allTotals,
      "scopeTotals" => $this->scopeTotals,
      "groupingTotals" => $this->groupingTotals,
      "metricDefinitions" => $this->metricDefinitions,
      "metricValues" => $this->metricValues,
      "metricHelpTexts" => $this->metricHelpTexts,
      "scopeAllowsHiding" => $this->scopeAllowsHiding,
      "scopeVariantTotals" => $this->scopeVariantTotals,
      "currentYearTotal" => $currentYearTotal,
      "formattedCurrentYearTotal" => $formattedCurrentYearTotal,
      "categoryHideTotals" => $this->categoryHideTotals,
      "emissionUnit" => $this->emissionUnit,
      "hasScope12Mark" => $this->hasScope12Mark,
      "hasScope13Mark" => $this->hasScope13Mark,
      "scope12MarkImage" => $this->scope12MarkImage,
      "scope13MarkImage" => $this->scope13MarkImage,
      "scope12CriteriaPdf" => $this->scope12CriteriaPdf,
      "scope13CriteriaPdf" => $this->scope13CriteriaPdf,
    ]);
  }

  /**
   * Format a numeric value for display
   */
  public function formatValue(string $value): string
  {
    try {
      $decimal = BigDecimal::of($value);

      if ($decimal->isZero()) {
        return "—";
      }

      // Determine scale based on value magnitude
      if ($decimal->isLessThan(BigDecimal::one())) {
        $scaled = $decimal->toScale(3, \Brick\Math\RoundingMode::HALF_UP)->stripTrailingZeros();
      } elseif ($decimal->isLessThan(BigDecimal::of(100))) {
        $scaled = $decimal->toScale(2, \Brick\Math\RoundingMode::HALF_UP)->stripTrailingZeros();
      } else {
        $scaled = $decimal->toScale(0, \Brick\Math\RoundingMode::HALF_UP);
      }

      return $this->numberFormatter->format((string) $scaled);
    } catch (MathException | InvalidArgumentException $e) {
      // Report since the output of BigDecimal shouldn't lead to this.
      report($e);

      return $value;
    }
  }

  /**
   * Get a grouping key for flat array storage
   */
  private function getGroupingKey(int $scopeId, int $groupingId): string
  {
    return $scopeId . "." . $groupingId;
  }

  /**
   * Load mark award status and file paths for the current year
   *
   * @throws RuntimeException
   */
  private function loadMarkData(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null || $this->currentYear === null) {
      return;
    }

    // Get the Year model for the current year
    $yearModel = $this->yearService->getYearByValue($this->currentYear);

    if ($yearModel === null) {
      return;
    }

    // Check if company has been awarded marks for this year
    $companyYear = \App\Models\CompanyYear::where("company_id", $company->id)
      ->where("year_id", $yearModel->id)
      ->first();

    if ($companyYear === null) {
      return;
    }

    $this->hasScope12Mark = $companyYear->awarded_scope1_2_mark ?? false;
    $this->hasScope13Mark = $companyYear->awarded_scope1_3_mark ?? false;

    // Load mark images and PDFs from year translations if awarded
    if ($this->hasScope12Mark || $this->hasScope13Mark) {
      $publicDisk = Storage::disk("public");

      // @phpstan-ignore-next-line function.alreadyNarrowedType - for IDE type hinting
      assert($publicDisk instanceof FilesystemAdapter);

      $yearTranslation = $yearModel->translate();

      if ($yearTranslation === null) {
        return;
      }

      if ($this->hasScope12Mark) {
        $this->scope12MarkImage =
          $yearTranslation->scope1_2_mark_image !== null
            ? $publicDisk->url($yearTranslation->scope1_2_mark_image)
            : null;
        $this->scope12CriteriaPdf =
          $yearTranslation->scope1_2_criteria_procedures_pdf !== null
            ? $publicDisk->url($yearTranslation->scope1_2_criteria_procedures_pdf)
            : null;
      }

      if ($this->hasScope13Mark) {
        $this->scope13MarkImage =
          $yearTranslation->scope1_3_mark_image !== null
            ? $publicDisk->url($yearTranslation->scope1_3_mark_image)
            : null;
        $this->scope13CriteriaPdf =
          $yearTranslation->scope1_3_criteria_procedures_pdf !== null
            ? $publicDisk->url($yearTranslation->scope1_3_criteria_procedures_pdf)
            : null;
      }
    }
  }

  /**
   * Load metric definitions for the current year
   *
   * @throws RuntimeException
   */
  private function loadMetricDefinitions(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $metrics = $this->metricDefinitionService->getDefinitionsForYear($this->currentYear);
    $yearModel = $this->yearService->getYearByValue($this->currentYear);

    if ($yearModel === null) {
      $this->metricDefinitions = [];

      return;
    }

    // Collect all metric definition year pivot IDs
    $pivotIds = [];
    $metricPivots = [];

    foreach ($metrics as $definition) {
      $yearPivot = $definition
        ->years()
        ->where("year_id", $yearModel->id)
        ->withPivot(["id"])
        ->first();

      if ($yearPivot !== null && $yearPivot->pivot !== null) {
        $pivotIds[] = $yearPivot->pivot->id;
        $metricPivots[$definition->id] = $yearPivot->pivot->id;
      }
    }

    // Load all MetricDefinitionYear models with translations in one query
    $metricDefYears = new Collection();
    if (count($pivotIds) !== 0) {
      $metricDefYears = MetricDefinitionYear::whereIn("id", $pivotIds)
        ->with("translations")
        ->get()
        ->keyBy("id");
    }

    // Now build DTOs without additional queries
    $this->metricDefinitions = [];
    foreach ($metrics as $definition) {
      $helpText = "";

      if (
        isset($metricPivots[$definition->id]) &&
        $metricDefYears->has($metricPivots[$definition->id])
      ) {
        $metricDefYear = $metricDefYears->get($metricPivots[$definition->id]);
        Assert::notNull($metricDefYear);

        $translation = $metricDefYear->translate();
        $helpText = $translation->help_text ?? "";
      }

      $dto = MetricDefinitionDto::fromModel(
        $definition,
        $definition->translate()->name ?? "",
        $definition->unit?->translate()->symbol ?? "",
        $helpText,
      );

      $this->metricDefinitions[] = $dto;
      $this->metricHelpTexts[$dto->id] = $dto->helpText;
    }
  }

  /**
   * Load metric values for all definitions and years
   *
   * @throws RuntimeException
   */
  private function loadMetricValues(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null || count($this->metricDefinitions) === 0) {
      return;
    }

    // Convert DTOs back to a collection of IDs for the service
    $metricIds = array_map(fn($dto) => $dto->id, $this->metricDefinitions);
    $metricsCollection = MetricDefinition::whereIn("id", $metricIds)->get();

    $valueData = $this->metricValueService->loadValuesForYears(
      $metricsCollection,
      $this->tableYears,
    );

    $this->metricValues = $valueData["values"];
  }

  /**
   * Load all definitions and create filtered display collection
   *
   * @throws RuntimeException
   */
  private function loadDefinitions(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      $this->displayDefinitions = [];

      return;
    }

    // Load ALL definitions for calculations with necessary relationships
    $allDefinitionsCollection = CalculationDefinition::with([
      "scope",
      "category.translations",
      "grouping.translations",
      "emissionFactorCompoundUnit",
      "years",
      "translations",
    ])
      ->where(function ($query) use ($company) {
        $query->where("company_id", $company->id)->orWhereNull("company_id");
      })
      ->orderBy("scope_id")
      ->orderBy("grouping_id")
      ->orderBy("category_id")
      ->orderByRaw("company_id IS NULL DESC")
      ->orderBy("sort_order")
      ->get();

    // Pre-load ALL pivot translations for all definitions in ONE query
    $allPivotIds = collect();
    foreach ($allDefinitionsCollection as $definition) {
      $pivotIds = $definition->years->map(function ($year) {
        $pivot = $year->getAttribute("pivot");
        Assert::instanceOf($pivot, CalculationDefinitionYear::class);

        return $pivot->id;
      });
      $allPivotIds = $allPivotIds->merge($pivotIds);
    }

    if ($allPivotIds->isNotEmpty()) {
      $pivotTranslations = \App\Models\CalculationDefinitionYearTranslation::whereIn(
        "calculation_definition_year_id",
        $allPivotIds->unique()->values(),
      )
        ->where("locale", app()->getLocale())
        ->get()
        ->keyBy("calculation_definition_year_id");

      // Set translations on all pivots
      foreach ($allDefinitionsCollection as $definition) {
        $definition->years->each(function ($year) use ($pivotTranslations) {
          $pivot = $year->getAttribute("pivot");
          Assert::instanceOf($pivot, CalculationDefinitionYear::class);
          $pivot->setRelation("translation", $pivotTranslations[$pivot->id] ?? null);
        });
      }
    }

    // Filter for definitions visible on the results page AND have the current year
    $this->displayDefinitions = $allDefinitionsCollection
      ->filter(function ($definition) {
        // Check if definition should be hidden
        if ($definition->hide_from_results_page) {
          return false;
        }

        // Check if definition has the current year
        $definitionYears = $definition->years->pluck("year")->toArray();

        return in_array($this->currentYear, $definitionYears, true);
      })
      ->all();
  }

  /**
   * Calculate emission results for all years
   * Note: Uses ALL definitions for calculations (including referenced ones)
   *
   * @throws MathException
   * @throws RuntimeException
   * @throws LogicException
   */
  private function calculateResults(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    // Get all definitions and filter out hidden groupings
    $definitions = $this->getVisibleResultDefinitions();

    // Calculate results and get the determined unit
    $calculationResult = $this->calculationService->calculateResultsForYears(
      $this->allYears,
      $definitions,
    );

    // Store the results and unit
    $this->allResults = $calculationResult["results"];
    $this->emissionUnit = $calculationResult["unit"];

    // Calculate totals using the determined unit
    $this->allTotals = $this->calculationService->getTotalEmissionsForYears(
      $this->allResults,
      $this->emissionUnit,
    );
  }

  /**
   * Calculate totals by scope for each year using BigDecimal for precision
   * Excludes hidden groupings from totals
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws MathException
   * @throws RuntimeException
   */
  private function calculateScopeTotals(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    $this->scopeTotals = [];
    $definitions = $this->getVisibleResultDefinitions();

    // Group definitions by scope
    $definitionsByScope = [];
    foreach ($definitions as $definition) {
      if ($definition->scope_id !== null) {
        $definitionsByScope[$definition->scope_id][] = $definition->id;
      }
    }

    // Calculate totals for each scope and year
    foreach ($definitionsByScope as $scopeId => $definitionIds) {
      $this->scopeTotals[$scopeId] = [];

      foreach ($this->allYears as $year) {
        $total = BigDecimal::zero();

        foreach ($definitionIds as $defId) {
          $result = $this->allResults[$year][$defId] ?? null;
          if ($result instanceof \App\DataTransferObjects\Success) {
            $total = $total->plus(BigDecimal::of($result->value));
          }
        }

        $this->scopeTotals[$scopeId][$year] = (string) $total;
      }
    }
  }

  /**
   * Calculate totals by grouping for the current year
   * Excludes hidden groupings
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws MathException
   * @throws RuntimeException
   */
  private function calculateGroupingTotals(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    $this->groupingTotals = [];
    $definitions = $this->getVisibleResultDefinitions();

    // Group definitions by grouping, tracking scope
    $definitionsByGrouping = [];
    $groupingInfo = [];

    foreach ($definitions as $definition) {
      if (
        $definition->grouping_id !== null &&
        $definition->scope_id !== null &&
        $definition->grouping !== null
      ) {
        $groupingId = $definition->grouping_id;

        if (!isset($definitionsByGrouping[$groupingId])) {
          $definitionsByGrouping[$groupingId] = [];

          $groupingTranslation = $definition->grouping->translate();
          $groupingTitle =
            $groupingTranslation !== null && $groupingTranslation->title !== null
              ? $groupingTranslation->title
              : "Grouping {$groupingId}";
          $scope = $definition->scope->number ?? null;
          Assert::notNull($scope);
          $groupingInfo[$groupingId] = [
            "name" => $groupingTitle,
            "scope" => $scope,
          ];
        }

        $definitionsByGrouping[$groupingId][] = $definition->id;
      }
    }

    // Calculate totals for each grouping for the current year only
    foreach ($definitionsByGrouping as $groupingId => $definitionIds) {
      $total = BigDecimal::zero();

      foreach ($definitionIds as $defId) {
        $result = $this->allResults[$this->currentYear][$defId] ?? null;
        if ($result instanceof \App\DataTransferObjects\Success) {
          $total = $total->plus(BigDecimal::of($result->value));
        }
      }

      if (isset($groupingInfo[$groupingId])) {
        $this->groupingTotals[$groupingId] = new GroupingTotal(
          name: $groupingInfo[$groupingId]["name"],
          scope: $groupingInfo[$groupingId]["scope"],
          total: (string) $total
            ->toScale(2, \Brick\Math\RoundingMode::HALF_UP)
            ->stripTrailingZeros(),
        );
      }
    }
  }

  /**
   * Calculate scope totals for each variant
   *
   * @throws MathException
   * @throws RuntimeException
   */
  private function calculateScopeVariantTotals(): void
  {
    $company = $this->companyService->getCurrentCompany();
    if ($company === null) {
      $this->scopeVariantTotals = [];

      return;
    }

    $this->scopeVariantTotals = [];
    $definitions = $this->getVisibleResultDefinitions();

    // Get all scopes with their variants
    $scopes = Scope::with([
      "calculationVariants" => function (Relation $query) {
        $query->with("translation")->orderBy("display_order");
      },
    ])
      ->orderBy("number")
      ->get();

    foreach ($scopes as $scope) {
      $variants = $scope->calculationVariants;

      foreach ($variants as $variant) {
        // Filter definitions for this scope and variant
        $filteredDefinitions = $definitions->filter(function ($definition) use ($scope, $variant) {
          if ($definition->scope_id !== $scope->id) {
            return false;
          }

          // If variant has a predicate, evaluate it
          if ($variant->predicate !== null) {
            try {
              return $this->expressionEvaluator->evaluateDefinition(
                $variant->predicate,
                $definition,
              );
            } catch (SyntaxError $e) {
              // Invalid expression syntax - skip this definition
              $this->safeReport($e);

              return false;
            }
          }

          // Default variant includes all definitions for the scope
          return true;
        });

        // Calculate totals for each year
        $totals = [];
        foreach ($this->tableYears as $year) {
          $total = BigDecimal::zero();

          foreach ($filteredDefinitions as $definition) {
            $result = $this->allResults[$year][$definition->id] ?? null;
            if ($result instanceof \App\DataTransferObjects\Success) {
              $total = $total->plus(BigDecimal::of($result->value));
            }
          }

          $totals[$year] = (string) $total;
        }

        $this->scopeVariantTotals[] = new ScopeVariantTotal(
          scopeId: $scope->id,
          scopeNumber: $scope->number,
          variantId: $variant->id,
          label: $variant->translate()->label ?? "",
          totals: $totals,
          includeInTotal: $variant->include_in_total,
        );
      }
    }
  }

  /**
   * Calculate the total emissions across all scope variants marked for inclusion
   * Only sums variants where include_in_total is true
   *
   * @throws MathException
   */
  private function calculateScopeTotal(int $year): string
  {
    $total = BigDecimal::zero();

    // Sum only the variants that are marked for inclusion in total
    foreach ($this->scopeVariantTotals as $variant) {
      if ($variant->includeInTotal) {
        $value = $variant->totals[$year] ?? "0";
        if ($value !== "0" && $value !== "0.0" && $value !== "0.00") {
          $total = $total->plus(BigDecimal::of($value));
        }
      }
    }

    return (string) $total;
  }

  /**
   * Load help texts from pivot data
   *
   * @throws RuntimeException
   */
  private function loadHelpTexts(): void
  {
    $company = $this->companyService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    $definitions = $this->getResultDefinitions();

    // Collect all pivot IDs first
    $allPivotIds = collect();
    $pivotsByDefinition = [];

    foreach ($definitions as $definition) {
      $years = $definition->years;
      $pivotsByDefinition[$definition->id] = $years;

      $pivotIds = $years->map(function ($year) {
        $pivot = $year->getAttribute("pivot");
        Assert::instanceOf($pivot, CalculationDefinitionYear::class);

        return $pivot->id;
      });
      $allPivotIds = $allPivotIds->merge($pivotIds);
    }

    // Load ALL pivot translations in ONE query
    $pivotTranslations = new Collection();
    if ($allPivotIds->isNotEmpty()) {
      $pivotTranslations = \App\Models\CalculationDefinitionYearTranslation::whereIn(
        "calculation_definition_year_id",
        $allPivotIds->unique()->values(),
      )
        ->where("locale", app()->getLocale())
        ->get()
        ->keyBy("calculation_definition_year_id");
    }

    // Now build help texts without additional queries
    foreach ($definitions as $definition) {
      $this->helpTexts[$definition->id] = [];

      if (isset($pivotsByDefinition[$definition->id])) {
        foreach ($pivotsByDefinition[$definition->id] as $yearModel) {
          $pivot = $yearModel->getAttribute("pivot");
          Assert::instanceOf($pivot, CalculationDefinitionYear::class);
          if (isset($pivotTranslations[$pivot->id])) {
            $translation = $pivotTranslations[$pivot->id];
            $helpTextValue = "";
            $resultHelpText = $translation->result_help_text;
            $helpTextValue = $resultHelpText;

            $this->helpTexts[$definition->id][$yearModel->year] = $helpTextValue;
          } else {
            $this->helpTexts[$definition->id][$yearModel->year] = "";
          }
        }
      }
    }
  }

  /**
   * Get all result definitions (including hidden ones)
   *
   * @return Collection<int, CalculationDefinition>
   */
  private function getResultDefinitions(): Collection
  {
    // Return display definitions for UI
    return new Collection($this->displayDefinitions);
  }

  /**
   * Get only visible result definitions (excluding hidden groupings)
   *
   * @return Collection<int, CalculationDefinition>
   *
   * @throws RuntimeException
   */
  private function getVisibleResultDefinitions(): Collection
  {
    $allDefinitions = $this->getResultDefinitions();

    // Filter out definitions from hidden groupings
    return $allDefinitions->filter(function ($definition) {
      if ($definition->scope_id === null || $definition->grouping_id === null) {
        return true; // Include if scope or grouping is not set
      }

      return !$this->isGroupingHidden($definition->scope_id, $definition->grouping_id);
    });
  }

  /**
   * Organize filtered definitions by scope, grouping, and category
   *
   * @param  Collection<int, CalculationDefinition>  $definitions  Already filtered definitions
   * @return array<int, array<int, array<int, Collection<int, CalculationDefinition>>>>
   */
  private function organizeByScopeGroupingAndCategory(Collection $definitions): array
  {
    $scopedGroupedDefinitions = [];

    foreach ($definitions as $definition) {
      if (
        $definition->scope_id === null ||
        $definition->category_id === null ||
        $definition->grouping_id === null
      ) {
        continue;
      }

      $scopeId = $definition->scope_id;
      $groupingId = $definition->grouping_id;
      $categoryId = $definition->category_id;

      if (!isset($scopedGroupedDefinitions[$scopeId])) {
        $scopedGroupedDefinitions[$scopeId] = [];
      }

      if (!isset($scopedGroupedDefinitions[$scopeId][$groupingId])) {
        $scopedGroupedDefinitions[$scopeId][$groupingId] = [];
      }

      if (!isset($scopedGroupedDefinitions[$scopeId][$groupingId][$categoryId])) {
        $scopedGroupedDefinitions[$scopeId][$groupingId][$categoryId] = new Collection();
      }

      if (isset($scopedGroupedDefinitions[$scopeId][$groupingId][$categoryId])) {
        $scopedGroupedDefinitions[$scopeId][$groupingId][$categoryId]->push($definition);
      }
    }

    return $scopedGroupedDefinitions;
  }

  /**
   * Load hiding reason options from SelectOption table (for display only)
   *
   * @throws InvalidArgumentException
   */
  private function loadHidingReasonOptions(): void
  {
    // Service returns SelectOption DTOs, convert them to wireable SelectOptionDto
    $options = $this->selectOptionService->getSelectOptions(
      SelectOptionType::GROUPING_HIDING_REASON,
    );

    $this->hidingReasonOptions = [];
    foreach ($options as $option) {
      $this->hidingReasonOptions[] = SelectOptionDto::fromSelectOption($option);
    }
  }

  /**
   * Load grouping states from database
   */
  private function loadGroupingStates(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    try {
      $company = $this->companyService->getCurrentCompany();
      if ($company === null) {
        return;
      }

      $states = $this->groupingStateService->loadGroupingStates($company, $this->currentYear);
      $this->hiddenGroupings = $states["hiddenGroupings"];
      $this->selectOptionIds = $states["selectOptionIds"];
      $this->customReasons = $states["customReasons"];
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      // fall back to empty arrays on error
      $this->hiddenGroupings = [];
      $this->selectOptionIds = [];
      $this->customReasons = [];
    }
  }

  /**
   * Load which scopes allow hiding groupings
   */
  private function loadScopeHidingPermissions(): void
  {
    $scopes = Scope::all();
    $this->scopeAllowsHiding = [];

    foreach ($scopes as $scope) {
      $this->scopeAllowsHiding[$scope->id] = $scope->allow_grouping_hiding ?? false;
    }
  }

  /**
   * Load category total visibility settings
   */
  private function loadCategorySettings(): void
  {
    // Load all categories with their hide_total setting
    $categories = \App\Models\Category::all();

    $this->categoryHideTotals = [];
    foreach ($categories as $category) {
      $this->categoryHideTotals[$category->id] = $category->hide_total ?? false;
    }
  }

  /**
   * Safe error reporting
   */
  private function safeReport(Throwable $e): void
  {
    try {
      report($e);
    } catch (Throwable $reportException) {
      Log::emergency("Failed to report exception", [
        "exception" => get_class($e),
        "message" => $e->getMessage(),
        "reportException" => get_class($reportException),
        "reportExceptionMessage" => $reportException->getMessage(),
      ]);
    }
  }

  /**
   * Add error with flash notification
   */
  private function addErrorWithFlash(string $field, string $message): void
  {
    $this->addError($field, $message);
    $this->dispatch("notify", [
      "message" => $message,
      "type" => "error",
    ]);
  }
}
