<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\CompanyInvitationService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\View\View;
use InvalidArgumentException;
use Livewire\Attributes\Locked;
use Livewire\Component;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class AcceptInvitationPage extends Component
{
  /**
   * Determines if the invitation is valid.
   */
  #[Locked]
  public bool $isValid = false;

  /**
   * The company name for the invitation.
   */
  #[Locked]
  public string $companyName = "";

  /**
   * The invitee email.
   */
  #[Locked]
  public string $inviteeEmail = "";

  /**
   * Whether an action has been taken.
   */
  #[Locked]
  public bool $actionTaken = false;

  /**
   * Determines if the invitation acceptance was successful.
   */
  #[Locked]
  public bool $success = false;

  /**
   * The message to display to the user.
   */
  #[Locked]
  public string $message = "";

  /**
   * The redirect URL from configuration.
   */
  #[Locked]
  public string $redirectUrl = "";

  /**
   * Whether the user already exists.
   */
  #[Locked]
  public bool $userExists = false;

  /**
   * The token selector.
   */
  #[Locked]
  public string $selector = "";

  /**
   * The token validator.
   */
  #[Locked]
  public string $validator = "";

  /**
   * Mount the component and validate the invitation.
   *
   * @throws RuntimeException
   */
  public function mount(string $selector, string $validator): void
  {
    $this->selector = $selector;
    $this->validator = $validator;

    // Only validate invitation on GET request
    $invitationService = App::make(CompanyInvitationService::class);
    $validationResult = $invitationService->validateInvitation($selector, $validator);

    $this->isValid = $validationResult->valid;
    $this->companyName = $validationResult->companyName ?? "";
    $this->inviteeEmail = $validationResult->inviteeEmail ?? "";

    if (!$this->isValid) {
      $this->message = __("auth.invitation.errors.invalid_or_expired");
    }

    // Get the redirect URL from configuration
    $baseRedirectUrl = Config::string("misc.auth_redirect_url");

    $localizeAuthRedirect = Config::boolean("misc.localize_auth_redirect");

    if ($localizeAuthRedirect) {
      // Get current locale
      $locale = App::getLocale();

      // Remove trailing slash from base URL if present
      $baseRedirectUrl = mb_rtrim($baseRedirectUrl, "/");

      // Append locale to redirect URL
      $this->redirectUrl = $baseRedirectUrl . "/" . $locale;
    } else {
      $this->redirectUrl = $baseRedirectUrl;
    }
  }

  /**
   * Accept the invitation.
   *
   * @throws RuntimeException
   */
  public function acceptInvitation(): void
  {
    if ($this->actionTaken || !$this->isValid) {
      return;
    }

    $this->actionTaken = true;

    try {
      $invitationService = App::make(CompanyInvitationService::class);
      $result = $invitationService->acceptInvitation($this->selector, $this->validator);

      $this->success = $result->success;
      $this->userExists = $result->userExisted;

      if ($result->success) {
        if ($result->userExisted) {
          $this->message = __("auth.invitation.messages.added_to_company");
        } else {
          $this->message = __("auth.invitation.messages.accepted_create_account");
        }

        // Dispatch event for BroadcastChannel listener
        $this->dispatch("invitation-accepted", [
          "redirectUrl" => $this->redirectUrl,
        ]);
      } else {
        $this->message = __("auth.invitation.errors.invalid_or_expired");
      }
    } catch (TooManyRequestsHttpException) {
      $this->success = false;
      $this->message = __("auth.invitation.errors.too_many_attempts");
    } catch (InvalidArgumentException) {
      $this->success = false;
      $this->message = __("auth.invitation.errors.invalid_check_link");
    }
  }

  /**
   * Decline the invitation.
   *
   * @throws RuntimeException
   */
  public function declineInvitation(): void
  {
    if ($this->actionTaken || !$this->isValid) {
      return;
    }

    $this->actionTaken = true;

    try {
      $invitationService = App::make(CompanyInvitationService::class);
      $result = $invitationService->declineInvitation($this->selector, $this->validator);

      $this->success = false; // Declining is not a "success" in the UI sense

      if ($result) {
        $this->message = __("auth.invitation.messages.invitation_declined");
      } else {
        $this->message = __("auth.invitation.errors.decline_failed");
      }
    } catch (TooManyRequestsHttpException) {
      $this->message = __("auth.invitation.errors.too_many_attempts");
    } catch (InvalidArgumentException) {
      $this->message = __("auth.invitation.errors.invalid_invitation");
    }
  }

  /**
   * Render the component.
   */
  public function render(): View
  {
    return view("livewire.accept-invitation-page");
  }
}
