<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Services\AuthenticationService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

final class AuthPage extends Component
{
  /**
   * The user's email.
   */
  public string $email = "";

  /**
   * Success message to display after sending a magic link.
   */
  #[Locked]
  public string $successMessage = "";

  /**
   * Error message to display when an error occurs.
   */
  #[Locked]
  public string $errorMessage = "";

  /**
   * Validation rules.
   *
   * @var array<string, string>
   */
  protected $rules = [
    "email" => "required|email|max:255",
  ];

  #[On("auth-error")]
  public function setAuthError(string $message): void
  {
    $this->errorMessage = $message;
  }

  /**
   * Authenticate a user or register them if they don't exist
   *
   * @throws RuntimeException
   */
  public function authenticate(): void
  {
    $this->reset(["successMessage", "errorMessage"]);
    $this->validate();

    try {
      $this->authenticationService()->sendMagicLink($this->email);
      $this->reset(["email"]);
      $this->successMessage = __("auth.messages.check_email");
    } catch (TooManyRequestsHttpException $e) {
      $this->errorMessage = $e->getMessage();
    }
  }

  /**
   * Render the component.
   *
   * @throws RuntimeException
   */
  public function render(): View
  {
    $sessionError = Session::get("error");

    // If there's a session error and no component error, use the session error
    if ($this->errorMessage === "" && is_string($sessionError)) {
      $this->errorMessage = $sessionError;
    }

    return view("livewire.auth-page");
  }

  /**
   * Reset the form to its initial state.
   */
  public function resetForm(): void
  {
    $this->reset(["email", "successMessage", "errorMessage"]);
  }

  /**
   * Get the magic link service instance.
   *
   * @throws RuntimeException
   */
  protected function authenticationService(): AuthenticationService
  {
    return App::make(AuthenticationService::class);
  }
}
