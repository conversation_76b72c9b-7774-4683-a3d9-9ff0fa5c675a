<?php

declare(strict_types=1);

namespace App\Livewire;

use App\DataTransferObjects\GroupingTotal;
use App\Helpers\Assert;
use DivisionByZeroError;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Livewire\Attributes\Locked;

final class CarbonFootprintDonut<PERSON>hart extends ChartWidget
{
  #[Locked]
  public int $year = 0;

  /** @var array<int, GroupingTotal> */
  #[Locked]
  public array $groupingData = [];

  /**
   * The unit symbol to display in tooltips
   */
  #[Locked]
  public string $unitSymbol = "";

  protected static ?string $maxHeight = "400px";

  /**
   * @return array{datasets: array<array{data: array<string>, backgroundColor: array<string>, hoverBackgroundColor: array<string>, borderWidth: int}>, labels: array<string>}
   */
  protected function getData(): array
  {
    $labels = [];
    $data = [];
    $backgroundColors = [];
    $hoverBackgroundColors = [];

    if (count($this->groupingData) === 0) {
      return [
        "datasets" => [
          [
            "data" => [],
            "backgroundColor" => [],
            "hoverBackgroundColor" => [],
            "borderWidth" => 0,
          ],
        ],
        "labels" => [],
      ];
    }

    $groupingsByScope = [];
    foreach ($this->groupingData as $groupingId => $groupingInfo) {
      $scope = $groupingInfo->scope;
      if (!isset($groupingsByScope[$scope])) {
        $groupingsByScope[$scope] = [];
      }
      $groupingsByScope[$scope][] = $groupingId;
    }

    foreach ($groupingsByScope as $scope => $groupingIds) {
      $baseColor = $this->getBaseColorForScope($scope);

      try {
        $variations = $this->generateColorVariations($baseColor, 5, 60);
      } catch (DivisionByZeroError $e) {
        // Fallback to base color if color generation fails
        $variations = [$baseColor];
      }

      // Sort groupings by total (descending)
      $sortedGroupingIds = $groupingIds;
      usort($sortedGroupingIds, function ($a, $b) {
        /** @phpstan-ignore-next-line float.assign */
        $totalA = (float) ($this->groupingData[$a]->total ?? 0);
        /** @phpstan-ignore-next-line float.assign */
        $totalB = (float) ($this->groupingData[$b]->total ?? 0);

        return $totalB <=> $totalA; // Descending order
      });

      foreach ($sortedGroupingIds as $sortIndex => $groupingId) {
        if (!array_key_exists($groupingId, $this->groupingData)) {
          continue;
        }
        $groupingInfo = $this->groupingData[$groupingId];

        if ((float) $groupingInfo->total === 0.0) {
          continue;
        }

        $labels[] = "Scope {$groupingInfo->scope}: {$groupingInfo->name}";
        $data[] = $groupingInfo->total;

        // Largest group gets the exact base color
        if ($sortIndex === 0) {
          $backgroundColors[] = $baseColor;
        } else {
          // Distribute other variations
          $colorIndex = ($sortIndex - 1) % count($variations);
          $color = $variations[$colorIndex] ?? null;
          Assert::notNull($color);
          $backgroundColors[] = $color;
        }

        // Use darkened version of the color for hover (25% darker)
        $currentColor = $baseColor;
        if ($sortIndex !== 0) {
          $colorIndex = ($sortIndex - 1) % count($variations);
          $variationColor = $variations[$colorIndex] ?? null;
          Assert::notNull($variationColor);
          $currentColor = $variationColor;
        }

        try {
          $hoverBackgroundColors[] = $this->darkenColor($currentColor);
        } catch (DivisionByZeroError $e) {
          report($e);
          // Fallback to original color
          $hoverBackgroundColors[] = $currentColor;
        }
      }
    }

    return [
      "datasets" => [
        [
          "data" => $data,
          "backgroundColor" => $backgroundColors,
          "hoverBackgroundColor" => $hoverBackgroundColors,
          "borderWidth" => 0,
        ],
      ],
      "labels" => $labels,
    ];
  }

  protected function getOptions(): RawJs
  {
    $blue = "#002663"; // blue-900
    $unitSymbol = base64_encode($this->unitSymbol ?? ""); // Would throw error with json_encode

    return RawJs::make(
      <<<JS
      {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
              tooltip: {
                  callbacks: {
                      label: function(context) {
                          const label = context.label || '';
                          const value = context.parsed || 0;
                          const unitSymbol = atob('{$unitSymbol}');
                          if (context.dataset && context.dataset.data && Array.isArray(context.dataset.data)) {
                              const total = context.dataset.data.reduce((a, b) => parseFloat(a) + parseFloat(b), 0);
                              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                              const unitPart = unitSymbol ? ' ' + unitSymbol : '';
                              return label + ': ' + value.toFixed(2) + unitPart + ' (' + percentage + '%)';
                          }
                          const unitPart = unitSymbol ? ' ' + unitSymbol : '';
                          return label + ': ' + value.toFixed(2) + unitPart;
                      }
                  },
                  backgroundColor: 'white',
                  borderColor: '{$blue}',
                  borderWidth: 1,
                  cornerRadius: 3,
                  titleColor: '{$blue}',
                  bodyColor: '{$blue}',
                  position: 'nearest',
              },
              legend: {
                  position: 'left',
                  align: 'start',
                  labels: {
                      boxWidth: 16,
                      boxHeight: 16,
                      padding: 12,
                      font: {
                          size: 16,
                          family: 'Work Sans, sans-serif',
                      },
                      color: '{$blue}',
                      generateLabels: function(chart) {
                          const data = chart.data;
                          if (data.labels && data.labels.length && data.datasets && data.datasets.length) {
                              const dataset = data.datasets[0];
                              if (dataset.data && Array.isArray(dataset.data) && dataset.data.length > 0) {
                                  const total = dataset.data.reduce((a, b) => parseFloat(a) + parseFloat(b), 0);

                                  return data.labels.map((label, i) => {
                                      const value = parseFloat(dataset.data[i]) || 0;
                                      const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';

                                      return {
                                          text: label + ' (' + percentage + '%)',
                                          fillStyle: dataset.backgroundColor ? dataset.backgroundColor[i] : '#6B7280',
                                          strokeStyle: dataset.backgroundColor ? dataset.backgroundColor[i] : '#6B7280',
                                          hidden: !chart.getDataVisibility(i),
                                          index: i
                                      };
                                  });
                              }
                          }
                          return [];
                      }
                  }
              },
              title: {
                  display: false
              }
          },
          layout: {
              padding: {
                  top: 24,
                  bottom: 24,
              }
          },
          scales: {
              x: {
                  display: false
              },
              y: {
                  display: false
              }
          },
          devicePixelRatio: 2
      }
      JS
      ,
    );
  }

  protected function getType(): string
  {
    return "doughnut";
  }

  /**
   * Convert RGB to HSL
   *
   * @param  int  $r  Red (0-255)
   * @param  int  $g  Green (0-255)
   * @param  int  $b  Blue (0-255)
   * @return array{h: float, s: float, l: float} HSL values (h: 0-360, s: 0-1, l: 0-1)
   *
   * @throws DivisionByZeroError
   */
  private function rgbToHsl(int $r, int $g, int $b): array
  {
    /** @phpstan-ignore-next-line float.assign */
    $r /= 255;
    /** @phpstan-ignore-next-line float.assign */
    $g /= 255;
    /** @phpstan-ignore-next-line float.assign */
    $b /= 255;

    /** @phpstan-ignore-next-line float.assign */
    $max = max($r, $g, $b);
    /** @phpstan-ignore-next-line float.assign */
    $min = min($r, $g, $b);
    /** @phpstan-ignore-next-line float.assign */
    $l = ($max + $min) / 2;

    $h = 0;
    $s = 0;

    if ($max === $min) {
      $h = $s = 0; // achromatic
    } else {
      /** @phpstan-ignore-next-line float.assign */
      $d = $max - $min;
      /** @phpstan-ignore-next-line float.assign */
      $s = $l > 0.5 ? $d / (2 - $max - $min) : $d / ($max + $min);

      switch ($max) {
        case $r:
          /** @phpstan-ignore-next-line float.assign */
          $h = (($g - $b) / $d + ($g < $b ? 6 : 0)) / 6;
          break;
        case $g:
          /** @phpstan-ignore-next-line float.assign */
          $h = (($b - $r) / $d + 2) / 6;
          break;
        case $b:
          /** @phpstan-ignore-next-line float.assign */
          $h = (($r - $g) / $d + 4) / 6;
          break;
      }
    }

    return ["h" => $h * 360, "s" => $s, "l" => $l];
  }

  /**
   * Convert HSL to RGB
   *
   * @param  float  $h  Hue (0-360)
   * @param  float  $s  Saturation (0-1)
   * @param  float  $l  Lightness (0-1)
   * @return array{r: int, g: int, b: int} RGB values (0-255)
   *
   * @phpstan-ignore-next-line float.type
   */
  private function hslToRgb(float $h, float $s, float $l): array
  {
    /** @phpstan-ignore-next-line float.assign */
    $h /= 360;

    if ($s === 0.0) {
      /** @phpstan-ignore-next-line float.assign */
      $r = $g = $b = $l; // achromatic
    } else {
      $hue2rgb = function (float $p, float $q, float $t): float {
        if ($t < 0) {
          /** @phpstan-ignore-next-line float.assign */
          $t += 1;
        }
        if ($t > 1) {
          /** @phpstan-ignore-next-line float.assign */
          $t -= 1;
        }
        if ($t < 1 / 6) {
          return $p + ($q - $p) * 6 * $t;
        }
        if ($t < 1 / 2) {
          return $q;
        }
        if ($t < 2 / 3) {
          return $p + ($q - $p) * (2 / 3 - $t) * 6;
        }

        return $p;
      };

      /** @phpstan-ignore-next-line float.assign */
      $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
      /** @phpstan-ignore-next-line float.assign */
      $p = 2 * $l - $q;
      /** @phpstan-ignore-next-line float.assign */
      $r = $hue2rgb($p, $q, $h + 1 / 3);
      /** @phpstan-ignore-next-line float.assign */
      $g = $hue2rgb($p, $q, $h);
      /** @phpstan-ignore-next-line float.assign */
      $b = $hue2rgb($p, $q, $h - 1 / 3);
    }

    return [
      "r" => (int) round($r * 255),
      "g" => (int) round($g * 255),
      "b" => (int) round($b * 255),
    ];
  }

  /**
   * Generate color variations based on a base color using hue shifts
   * Variations will never include the exact base color
   *
   * @param  non-falsy-string  $baseColor  Hex color
   * @param  int  $count  Number of variations to generate
   * @param  float  $hueShiftRange  Maximum hue shift in degrees
   * @return non-empty-list<non-falsy-string>
   *
   * @throws DivisionByZeroError
   *
   * @phpstan-ignore-next-line float.type
   */
  private function generateColorVariations(
    string $baseColor,
    int $count = 5,
    float $hueShiftRange = 60,
  ): array {
    $variations = [];

    // Convert hex to RGB
    $hex = mb_ltrim($baseColor, "#");
    $r = (int) hexdec(mb_substr($hex, 0, 2));
    $g = (int) hexdec(mb_substr($hex, 2, 2));
    $b = (int) hexdec(mb_substr($hex, 4, 2));

    // Convert to HSL
    $hsl = $this->rgbToHsl($r, $g, $b);

    // Generate variations, ensuring none match the base color
    for ($i = 0; $i < $count; $i++) {
      // Create variations that exclude the center (base color)
      // First half: negative shifts
      if ($i < $count / 2) {
        /** @phpstan-ignore-next-line float.assign */
        $progress = ($i + 1) / (floor($count / 2) + 1);
        /** @phpstan-ignore-next-line float.assign */
        $hueShift = -$hueShiftRange * $progress;
      } else {
        // Second half: positive shifts
        /** @phpstan-ignore-next-line float.assign */
        $adjustedIndex = $i - floor($count / 2);
        /** @phpstan-ignore-next-line float.assign */
        $progress = ($adjustedIndex + 1) / (ceil($count / 2) + 1);
        /** @phpstan-ignore-next-line float.assign */
        $hueShift = $hueShiftRange * $progress;
      }

      /** @phpstan-ignore-next-line float.assign */
      $newHue = $hsl["h"] + $hueShift;
      // Wrap around if needed
      if ($newHue < 0) {
        /** @phpstan-ignore-next-line float.assign */
        $newHue += 360;
      }
      if ($newHue > 360) {
        /** @phpstan-ignore-next-line float.assign */
        $newHue -= 360;
      }

      // Convert back to RGB
      $rgb = $this->hslToRgb($newHue, $hsl["s"], $hsl["l"]);

      $variations[] = sprintf("#%02x%02x%02x", $rgb["r"], $rgb["g"], $rgb["b"]);
    }

    if (count($variations) === 0) {
      return [$baseColor];
    }

    return $variations;
  }

  /**
   * Darken a color by reducing its lightness
   *
   * @param  string  $hexColor  Hex color
   * @param  float  $amount  Amount to darken (0-1)
   * @return string Hex color
   *
   * @throws DivisionByZeroError
   *
   * @phpstan-ignore-next-line float.type
   */
  private function darkenColor(string $hexColor, float $amount = 0.25): string
  {
    // Convert hex to RGB
    $hex = mb_ltrim($hexColor, "#");
    $r = (int) hexdec(mb_substr($hex, 0, 2));
    $g = (int) hexdec(mb_substr($hex, 2, 2));
    $b = (int) hexdec(mb_substr($hex, 4, 2));

    // Convert to HSL
    $hsl = $this->rgbToHsl($r, $g, $b);

    // Reduce lightness
    /** @phpstan-ignore-next-line float.assign */
    $hsl["l"] = max(0.1, $hsl["l"] - $amount); // Keep minimum lightness of 0.1

    // Convert back to RGB
    $rgb = $this->hslToRgb($hsl["h"], $hsl["s"], $hsl["l"]);

    return sprintf("#%02x%02x%02x", $rgb["r"], $rgb["g"], $rgb["b"]);
  }

  /**
   * Get the base color for a given scope
   *
   * @return non-falsy-string Hex color
   */
  private function getBaseColorForScope(int $scope): string
  {
    $scopeColors = [
      1 => "#A5C9E7", // sky-300
      2 => "#FD9180", // red-300
      3 => "#BB95EF", // indigo-300
    ];

    return $scopeColors[$scope] ?? "#9CA3AF"; // neutral-400 as fallback
  }
}
