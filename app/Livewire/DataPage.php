<?php

declare(strict_types=1);

namespace App\Livewire;

use App\DataTransferObjects\CalculationDefinitionDto;
use App\DataTransferObjects\CategoryDto;
use App\DataTransferObjects\GroupingDto;
use App\DataTransferObjects\MetricDefinitionDto;
use App\DataTransferObjects\ScopeDto;
use App\DataTransferObjects\SelectOption;
use App\DataTransferObjects\SelectOptionDto;
use App\DataTransferObjects\UnitDto;
use App\Enums\Abilities;
use App\Enums\SelectOptionType;
use App\Models\CalculationDefinition;
use App\Models\CalculationDefinitionYear;
use App\Models\Category;
use App\Models\Grouping;
use App\Models\MetricDefinition;
use App\Models\MetricDefinitionYear;
use App\Models\Scope;
use App\Models\Unit;
use App\Services\CalculationDefinitionService;
use App\Services\CompanyContextService;
use App\Services\DataValueService;
use App\Services\GroupingStateService;
use App\Services\MetricDefinitionService;
use App\Services\MetricValueService;
use App\Services\SelectOptionService;
use App\Services\YearService;
use Error;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use InvalidArgumentException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Component;
use RuntimeException;
use Throwable;

final class DataPage extends Component
{
  /**
   * @var array<int, array<int, string|null>>
   */
  public array $values = [];

  /**
   * @var array<int, array<int, string|null>>
   */
  public array $sources = [];

  /**
   * @var array<int, string>
   */
  public array $errors = [];

  /**
   * Metric-related properties
   */
  /**
   * @var list<MetricDefinitionDto>
   */
  #[Locked]
  public array $metricDefinitions = [];

  /**
   * @var array<int, array<int, string|null>>
   */
  public array $metricValues = [];

  /**
   * @var array<int, array<int, string|null>>
   */
  public array $metricSources = [];

  /**
   * @var array<int, string>
   */
  public array $metricErrors = [];

  /**
   * @var array<int, string>
   */
  #[Locked]
  public array $metricHelpTexts = [];

  public ?int $currentYear = null;

  /**
   * @var list<int>
   */
  #[Locked]
  public array $allYears = [];

  /**
   * @var list<int>
   */
  #[Locked]
  public array $tableYears = [];

  /**
   * @var array<int, array<int, array<int, list<CalculationDefinitionDto>>>>
   */
  #[Locked]
  public array $scopedGroupedCategorizedDefinitions = [];

  /**
   * @var list<ScopeDto>
   */
  #[Locked]
  public array $availableScopes = [];

  /**
   * @var array<int, list<CategoryDto>>
   */
  #[Locked]
  public array $scopeCategories = [];

  /**
   * @var array<int, list<GroupingDto>>
   */
  #[Locked]
  public array $scopeGroupings = [];

  /**
   * @var array<int, array<int, string>>
   */
  #[Locked]
  public array $helpTexts = [];

  /**
   * Track link IDs for definitions
   *
   * @var array<int, string|null>
   */
  #[Locked]
  public array $definitionLinkIds = [];

  /**
   * Editing fields
   */
  /**
   * @var array<int, string|null>
   */
  public array $definitionNames = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionUnitIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionScopeIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionCategoryIds = [];

  /**
   * @var array<int, int|numeric-string|null>
   */
  public array $definitionGroupingIds = [];

  /**
   * Source modal properties
   */
  public ?int $sourceDefinitionId = null;

  public ?int $sourceYear = null;

  public ?string $sourceText = null;

  public ?string $sourceDefinitionName = null;

  public bool $isSourceModalOpen = false;

  /**
   * Delete modal properties
   */
  public bool $showDeleteModal = false;

  #[Locked]
  public ?int $definitionToDeleteId = null;

  public string $definitionToDeleteName = "";

  public bool $isMetricSource = false;

  /**
   * Track linked definitions for deletion
   *
   * @var list<int>
   */
  #[Locked]
  public array $linkedDefinitionIds = [];

  /**
   * Track names of linked definitions for display
   *
   * @var list<string>
   */
  #[Locked]
  public array $linkedDefinitionNames = [];

  /**
   * Grouping visibility properties
   */
  /**
   * Track which groupings are hidden
   *
   * @var array<string, bool>
   */
  public array $hiddenGroupings = [];

  /**
   * Store select option IDs for groupings
   *
   * @var array<string, int|null>
   */
  public array $selectOptionIds = [];

  /**
   * Store custom reasons for groupings
   *
   * @var array<string, string>
   */
  public array $customReasons = [];

  /**
   * Available hiding reason options
   *
   * @var list<SelectOptionDto>
   */
  #[Locked]
  public array $hidingReasonOptions = [];

  /**
   * @var list<UnitDto>
   */
  #[Locked]
  public array $units = [];

  /**
   * @var list<CategoryDto>
   */
  #[Locked]
  public array $categories = [];

  /**
   * @var list<GroupingDto>
   */
  #[Locked]
  public array $groupings = [];

  /**
   * Service properties
   */
  private YearService $yearService;

  private DataValueService $valueService;

  private CalculationDefinitionService $definitionService;

  private CompanyContextService $companyContextService;

  private MetricDefinitionService $metricDefinitionService;

  private MetricValueService $metricValueService;

  private GroupingStateService $groupingStateService;

  private SelectOptionService $selectOptionService;

  /**
   * Dependency injection via boot method
   */
  public function boot(
    YearService $yearService,
    DataValueService $valueService,
    CalculationDefinitionService $definitionService,
    CompanyContextService $companyContextService,
    MetricDefinitionService $metricDefinitionService,
    MetricValueService $metricValueService,
    GroupingStateService $groupingStateService,
    SelectOptionService $selectOptionService,
  ): void {
    $this->yearService = $yearService;
    $this->valueService = $valueService;
    $this->definitionService = $definitionService;
    $this->companyContextService = $companyContextService;
    $this->metricDefinitionService = $metricDefinitionService;
    $this->metricValueService = $metricValueService;
    $this->groupingStateService = $groupingStateService;
    $this->selectOptionService = $selectOptionService;
  }

  /**
   * Check if a grouping is using a custom reason
   */
  public function isUsingCustomReason(int $scopeId, int $groupingId): bool
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);
    $selectOptionId = $this->selectOptionIds[$key] ?? null;

    // Null selectOptionId means custom reason is being used
    return $selectOptionId === null && $this->isGroupingHidden($scopeId, $groupingId);
  }

  /**
   * Get the current selected option ID for display in the dropdown
   */
  public function getCurrentSelectedOptionId(int $scopeId, int $groupingId): ?int
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);

    // If using custom reason, return null
    if ($this->isUsingCustomReason($scopeId, $groupingId)) {
      return null;
    }

    $selectedId = $this->selectOptionIds[$key] ?? null;
    if ($selectedId === null && !$this->isGroupingHidden($scopeId, $groupingId)) {
      return $this->getDefaultHidingReasonId();
    }

    return $selectedId;
  }

  /**
   * Get the default hiding reason option ID (public for blade access)
   */
  public function getDefaultHidingReasonId(): ?int
  {
    if (count($this->hidingReasonOptions) > 0) {
      return $this->hidingReasonOptions[0]->value;
    }

    return null;
  }

  /**
   * Mount the component
   */
  public function mount(?int $year = null): void
  {
    try {
      // Initialize arrays
      $this->availableScopes = [];
      $this->metricDefinitions = [];

      // Load all available years
      $this->loadYears();

      if ($year !== null && in_array($year, $this->allYears, true)) {
        $this->currentYear = $year;
      } else {
        $this->currentYear = $this->yearService->getCurrentYear();
      }

      // Get company from context service
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      // Check authorization
      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      // Filter to only include current year and previous year for the table
      $previousYear = $this->currentYear - 1;
      $this->tableYears = array_values(
        array_filter($this->allYears, function ($year) use ($previousYear) {
          return $year === $this->currentYear || $year === $previousYear;
        }),
      );

      // Sort years (to ensure previous year comes first if it exists)
      sort($this->tableYears);

      // Load hiding reason options
      $this->loadHidingReasonOptions();

      // Load grouping states for the current company and year
      $this->loadGroupingStates();

      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_view_permission"));
    } catch (RuntimeException | InvalidArgumentException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.loading_failed"));
    }
  }

  /**
   * Toggle grouping visibility
   */
  public function toggleGrouping(int $scopeId, int $groupingId): void
  {
    try {
      $currentYear = $this->currentYear;

      if ($currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $key = $this->getGroupingKey($scopeId, $groupingId);
      $wasHidden = $this->isGroupingHidden($scopeId, $groupingId);

      if ($wasHidden) {
        // Show the grouping - clear all data
        $this->clearGroupingData($scopeId, $groupingId);
      } else {
        // Hide the grouping - set default values
        $this->hiddenGroupings[$key] = true;
        $this->customReasons[$key] = "";

        // Set default reason to first available option
        $defaultOptionId = $this->getDefaultHidingReasonId();
        if ($defaultOptionId !== null) {
          $this->selectOptionIds[$key] = $defaultOptionId;
        }
      }

      // Save state to database
      $selectOptionId = !$wasHidden ? $this->selectOptionIds[$key] ?? null : null;
      $customReason = null;

      $this->groupingStateService->saveGroupingState(
        $company,
        $currentYear,
        $scopeId,
        $groupingId,
        !$wasHidden,
        $selectOptionId,
        $customReason,
      );

      // Reload only grouping states
      $this->loadGroupingStates();
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.grouping_toggle_failed"));
    }
  }

  /**
   * Handle reason selection change
   */
  public function updateReasonSelection(
    int $scopeId,
    int $groupingId,
    string|int|null $selectedValue = null,
  ): void {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $key = $this->getGroupingKey($scopeId, $groupingId);

      // Convert "null" string to actual null for custom reason
      if ($selectedValue === "null" || $selectedValue === "") {
        $selectedValue = null;
      } elseif ($selectedValue !== null) {
        $selectedValue = (int) $selectedValue;
      }

      // Use the passed value or fall back to the property
      $selectedOptionId = $selectedValue;

      if ($selectedOptionId === null) {
        // Switch to custom reason mode
        $this->customReasons[$key] = ""; // Empty string default
        $this->selectOptionIds[$key] = null;

        // Save immediately with empty custom reason
        if ($this->isGroupingHidden($scopeId, $groupingId)) {
          $this->groupingStateService->updateReason(
            $company,
            $this->currentYear,
            $scopeId,
            $groupingId,
            null,
            "", // Empty custom reason
          );
        }
      } else {
        // Using predefined reason
        $this->selectOptionIds[$key] = $selectedOptionId;
        $this->customReasons[$key] = "";

        // Save immediately for predefined reasons
        if ($this->isGroupingHidden($scopeId, $groupingId)) {
          $this->groupingStateService->updateReason(
            $company,
            $this->currentYear,
            $scopeId,
            $groupingId,
            $selectedOptionId,
            null,
          );
        }
      }
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.reason_update_failed"));
    }
  }

  /**
   * Save the custom reason for a grouping
   */
  public function saveCustomReason(int $scopeId, int $groupingId, ?string $value = null): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $key = $this->getGroupingKey($scopeId, $groupingId);

      // Use passed value directly (can be empty string)
      $customReason = $value ?? "";

      // Validate the custom reason
      $validator = Validator::make(
        ["custom_reason" => $customReason],
        ["custom_reason" => "nullable|string|max:65535"],
      );

      if ($validator->fails()) {
        $this->addErrorWithFlash("global", __("data.validation.reason_too_long", ["n" => 65535]));

        return;
      }

      // Update both properties
      $this->customReasons[$key] = $customReason;

      // Update reason in database if grouping is hidden
      if ($this->isGroupingHidden($scopeId, $groupingId)) {
        $this->groupingStateService->updateReason(
          $company,
          $this->currentYear,
          $scopeId,
          $groupingId,
          null, // null select_option_id for custom reason
          $customReason,
        );

        // Show success notification
        $this->dispatch("notify", [
          "message" => __("data.messages.reason_updated"),
          "type" => "success",
        ]);
      }
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.hiding_reason_save_failed"));
    }
  }

  /**
   * Switch back from custom reason to predefined reasons
   */
  public function switchToPredefinedReasons(int $scopeId, int $groupingId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $key = $this->getGroupingKey($scopeId, $groupingId);

      // Clear custom reason mode
      $this->customReasons[$key] = "";

      // Set default predefined reason
      $defaultOptionId = $this->getDefaultHidingReasonId();
      if ($defaultOptionId !== null) {
        $this->selectOptionIds[$key] = $defaultOptionId;

        // Save the default reason if grouping is hidden
        if ($this->isGroupingHidden($scopeId, $groupingId)) {
          $this->groupingStateService->updateReason(
            $company,
            $this->currentYear,
            $scopeId,
            $groupingId,
            $defaultOptionId,
            null,
          );
        }
      }
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.switch_to_reasons_failed"));
    }
  }

  /**
   * Check if a grouping is hidden
   */
  public function isGroupingHidden(int $scopeId, int $groupingId): bool
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);

    return isset($this->hiddenGroupings[$key]) && $this->hiddenGroupings[$key];
  }

  /**
   * Get the hiding reason for a grouping
   */
  public function getHidingReason(int $scopeId, int $groupingId): string
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);
    $selectOptionId = $this->selectOptionIds[$key] ?? null;

    // If selectOptionId is not null, it's a predefined reason
    if ($selectOptionId !== null) {
      // Find the label for this select option
      foreach ($this->hidingReasonOptions as $option) {
        if ($option->value === $selectOptionId) {
          return $option->label;
        }
      }
    }

    // Return custom reason if selectOptionId is null
    return $this->customReasons[$key] ?? "";
  }

  /**
   * Save a metric value for the current year
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   */
  public function saveMetricField(int $definitionId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $value = $this->metricValues[$definitionId][$this->currentYear] ?? null;

      // Clear previous error for this field
      unset($this->metricErrors[$definitionId]);

      $result = $this->metricValueService->saveValue($definitionId, $value, $this->currentYear);

      if ($result["success"]) {
        // Update local data
        $this->metricValues[$definitionId][$this->currentYear] = $value;

        $this->dispatch("notify", [
          "message" => $result["message"],
          "type" => "success",
        ]);
      } else {
        $this->metricErrors[$definitionId] = $result["message"];
      }
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.value_save_failed"));
    }
  }

  /**
   * Open the source modal for a metric definition and year
   *
   * @throws RuntimeException
   */
  public function openMetricSourceModal(int $definitionId, int $year): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      $this->sourceDefinitionId = $definitionId;
      $this->sourceYear = $year;
      $this->sourceText = $this->metricSources[$definitionId][$year] ?? null;
      $this->isMetricSource = true;

      // Find the metric definition DTO
      $definitionDto = null;
      foreach ($this->metricDefinitions as $metric) {
        if ($metric->id === $definitionId) {
          $definitionDto = $metric;
          break;
        }
      }

      $this->sourceDefinitionName = $definitionDto->name ?? "";

      $this->isSourceModalOpen = true;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_view_permission"));
    }
  }

  /**
   * Save an individual field value for the current year
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   */
  public function saveField(int $definitionId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $value = $this->values[$definitionId][$this->currentYear] ?? null;

      // Clear previous error for this field
      unset($this->errors[$definitionId]);

      $result = $this->valueService->saveValue($definitionId, $value, $this->currentYear);

      if ($result["success"]) {
        // Update local data
        $this->values[$definitionId][$this->currentYear] = $value;

        $this->dispatch("notify", [
          "message" => $result["message"],
          "type" => "success",
        ]);
      } else {
        $this->errors[$definitionId] = $result["message"];
      }
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.value_save_failed"));
    }
  }

  /**
   * Open the source modal for a definition and year
   *
   * @throws RuntimeException
   */
  public function openSourceModal(int $definitionId, int $year): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::VIEW_COMPANY_DATA, $company);

      $this->sourceDefinitionId = $definitionId;
      $this->sourceYear = $year;
      $this->sourceText = $this->sources[$definitionId][$year] ?? null;
      $this->isMetricSource = false;

      $this->sourceDefinitionName = $this->definitionNames[$definitionId] ?? "";

      $this->isSourceModalOpen = true;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_view_permission"));
    }
  }

  /**
   * Close the source modal
   */
  public function closeSourceModal(): void
  {
    $this->isSourceModalOpen = false;
    $this->sourceDefinitionId = null;
    $this->sourceYear = null;
    $this->sourceText = null;
    $this->sourceDefinitionName = null;
    $this->isMetricSource = false;
  }

  /**
   * Save the source for a definition and year
   *
   * @throws \Brick\Math\Exception\DivisionByZeroException
   * @throws \Brick\Math\Exception\NumberFormatException
   * @throws \Brick\Math\Exception\RoundingNecessaryException
   * @throws RuntimeException
   */
  public function saveSource(): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));
        $this->closeSourceModal();

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      if ($this->sourceDefinitionId === null || $this->sourceYear === null) {
        $this->closeSourceModal();

        return;
      }

      if ($this->isMetricSource) {
        $result = $this->metricValueService->saveSource(
          $this->sourceDefinitionId,
          $this->sourceText ?? "",
          $this->sourceYear,
        );

        if ($result["success"]) {
          // Update local data
          $this->metricSources[$this->sourceDefinitionId][$this->sourceYear] = $this->sourceText;
        }
      } else {
        $result = $this->valueService->saveSource(
          $this->sourceDefinitionId,
          $this->sourceText ?? "",
          $this->sourceYear,
        );

        if ($result["success"]) {
          // Update local data
          $this->sources[$this->sourceDefinitionId][$this->sourceYear] = $this->sourceText;
        }
      }

      if ($result["success"]) {
        $this->dispatch("notify", [
          "message" => __("data.messages.source_updated"),
          "type" => "success",
        ]);
      } else {
        $this->dispatch("notify", [
          "message" => $result["message"],
          "type" => "error",
        ]);
      }

      $this->closeSourceModal();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
      $this->closeSourceModal();
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.source_save_failed"));
      $this->closeSourceModal();
    }
  }

  /**
   * Update definition name
   */
  public function updateDefinitionName(int $definitionId): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.definition_not_found"),
          "type" => "error",
        ]);

        return;
      }

      $customName = $this->definitionNames[$definitionId] ?? "";
      $dataUnitId = $this->getUnitIdFromForm($definitionId);
      $scopeId = $this->getScopeIdFromForm($definitionId);
      $categoryId = $this->getCategoryIdFromForm($definitionId);
      $groupingId = $this->getGroupingIdFromForm($definitionId);

      if ($scopeId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.scope_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($categoryId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.category_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($groupingId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.grouping_missing"),
          "type" => "error",
        ]);

        return;
      }

      $this->definitionService->updateDefinition(
        $definitionId,
        $customName,
        $scopeId,
        $categoryId,
        $groupingId,
        $dataUnitId,
        $definition->emission_factor_compound_unit_id ?? null,
      );

      $this->dispatch("notify", [
        "message" => __("data.messages.name_updated"),
        "type" => "success",
      ]);

      // Reload all data to ensure consistency
      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      $this->addErrorWithFlash("global", __("data.errors.definition_not_found_or_unauthorized"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.name_update_failed"));
    }
  }

  /**
   * Update definition unit
   */
  public function updateDefinitionUnit(int $definitionId): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.definition_not_found"),
          "type" => "error",
        ]);

        return;
      }

      // Verify the definition belongs to the current company
      if ($definition->company_id !== $company->id) {
        $this->dispatch("notify", [
          "message" => __("data.errors.can_only_edit_own"),
          "type" => "error",
        ]);

        return;
      }

      $customName = $this->definitionNames[$definitionId] ?? "";
      $dataUnitId = $this->getUnitIdFromForm($definitionId);
      $scopeId = $this->getScopeIdFromForm($definitionId);
      $categoryId = $this->getCategoryIdFromForm($definitionId);
      $groupingId = $this->getGroupingIdFromForm($definitionId);

      if ($scopeId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.scope_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($categoryId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.category_missing"),
          "type" => "error",
        ]);

        return;
      }

      if ($groupingId === null) {
        $this->dispatch("notify", [
          "message" => __("data.errors.grouping_missing"),
          "type" => "error",
        ]);

        return;
      }

      $this->definitionService->updateDefinition(
        $definitionId,
        $customName,
        $scopeId,
        $categoryId,
        $groupingId,
        $dataUnitId,
        $definition->emission_factor_compound_unit_id ?? null,
      );

      $this->dispatch("notify", [
        "message" => __("data.messages.unit_updated"),
        "type" => "success",
      ]);

      // Reload all data to ensure consistency
      $this->reloadAllData();
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      $this->addErrorWithFlash("global", __("data.errors.definition_not_found_or_unauthorized"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.unit_update_failed"));
    }
  }

  /**
   * Add a new row immediately
   *
   * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
   * @throws Error
   * @throws Exception
   */
  public function addNewRow(int $scopeId, int $groupingId, int $categoryId): void
  {
    try {
      if ($this->currentYear === null) {
        $this->addErrorWithFlash("global", __("data.errors.action_not_available"));

        return;
      }

      $company = $this->companyContextService->getCurrentCompany();
      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      // Use the template-aware method
      $createdCount = $this->definitionService->createFromCategoryTemplates(
        $categoryId,
        $scopeId,
        $groupingId,
        $this->currentYear,
      );

      // Reload all data to get properly loaded relationships
      $this->reloadAllData();

      $message =
        $createdCount > 1
          ? __("data.messages.definitions_added")
          : __("data.messages.definition_added");

      $this->dispatch("notify", [
        "message" => $message,
        "type" => "success",
      ]);
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.row_add_failed"));
    }
  }

  /**
   * Delete a company-specific definition (and linked definitions if applicable)
   *
   * @param  int  $definitionId  The definition ID to delete
   */
  public function deleteDefinition(int $definitionId): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $deletedCount = $this->definitionService->deleteDefinition($definitionId);

      // Clear linked definitions tracking before reloading
      $this->linkedDefinitionIds = [];
      $this->linkedDefinitionNames = [];

      // Reload all data to get properly updated state
      $this->reloadAllData();

      $message =
        $deletedCount > 1
          ? __("data.messages.linked_definitions_deleted", [
            "count" => $deletedCount,
          ])
          : __("data.messages.definition_deleted");

      $this->dispatch("notify", [
        "message" => $message,
        "type" => "success",
      ]);
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_edit_permission"));
    } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
      $this->addErrorWithFlash("global", __("data.errors.definition_not_found"));
    } catch (Exception $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.row_delete_failed"));
    }
  }

  /**
   * Get help text for a definition and year
   */
  public function getHelpText(int $definitionId, int $year): ?string
  {
    return $this->helpTexts[$definitionId][$year] ?? null;
  }

  /**
   * Open the delete definition modal
   */
  public function openDeleteModal(int $definitionId): void
  {
    try {
      $company = $this->companyContextService->getCurrentCompany();

      if ($company === null) {
        $this->addErrorWithFlash("global", __("data.errors.no_company_selected"));

        return;
      }

      Gate::authorize(Abilities::EDIT_COMPANY_DATA, $company);

      $definition = CalculationDefinition::find($definitionId);

      if ($definition === null) {
        $this->addErrorWithFlash("global", __("data.errors.definition_not_found"));

        return;
      }

      // Verify the definition belongs to the current company
      if ($definition->company_id !== $company->id) {
        $this->addErrorWithFlash("global", __("data.errors.can_only_delete_own"));

        return;
      }

      $this->definitionToDeleteId = $definitionId;
      $this->definitionToDeleteName =
        $this->definitionNames[$definitionId] ?? __("data.errors.unknown_definition");

      // Check for linked definitions
      $linkedDefinitions = $this->definitionService->getLinkedDefinitions($definitionId);
      $this->linkedDefinitionIds = [];
      $this->linkedDefinitionNames = [];

      if ($linkedDefinitions->count() > 1) {
        // Has linked definitions - collect their IDs and names (excluding the main one)
        foreach ($linkedDefinitions as $linkedDef) {
          if ($linkedDef->id !== $definitionId) {
            $this->linkedDefinitionIds[] = $linkedDef->id;
            // Get the display name from our stored names
            $linkedName =
              $this->definitionNames[$linkedDef->id] ??
              ($linkedDef->custom_name ??
                ($linkedDef->translate()->data_name ?? __("data.errors.unknown_definition")));
            $this->linkedDefinitionNames[] = $linkedName;
          }
        }
      }

      $this->showDeleteModal = true;
    } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
      $this->addErrorWithFlash("global", __("data.errors.no_delete_permission"));
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      $this->addErrorWithFlash("global", __("data.errors.modal_open_failed"));
    }
  }

  /**
   * Close the delete definition modal
   */
  public function closeDeleteModal(): void
  {
    $this->showDeleteModal = false;
    $this->definitionToDeleteId = null;
    $this->definitionToDeleteName = "";
    $this->linkedDefinitionIds = [];
    $this->linkedDefinitionNames = [];
  }

  /**
   * Confirm and delete the definition
   */
  public function confirmDeleteDefinition(): void
  {
    if ($this->definitionToDeleteId === null) {
      $this->closeDeleteModal();

      return;
    }

    $this->deleteDefinition($this->definitionToDeleteId);
    $this->closeDeleteModal();
  }

  /**
   * Render the component
   *
   * @throws RuntimeException
   */
  #[Layout("components.layouts.app")]
  public function render(): View
  {
    $company = $this->companyContextService->getCurrentCompany();

    return view("livewire.data-page", [
      "company" => $company,
      "metricDefinitions" => $this->metricDefinitions,
      "availableScopes" => $this->availableScopes,
      "scopedGroupedCategorizedDefinitions" => $this->scopedGroupedCategorizedDefinitions,
      "scopeCategories" => $this->scopeCategories,
      "scopeGroupings" => $this->scopeGroupings,
      "allYears" => $this->allYears,
      "tableYears" => $this->tableYears,
      "currentYear" => $this->currentYear,
      "units" => $this->units,
      "categories" => $this->categories,
      "groupings" => $this->groupings,
    ]);
  }

  /**
   * Get years with pivot for a definition
   *
   * @return Collection<int, \App\Models\Year&object{pivot: CalculationDefinitionYear}>
   */
  private function getYearsWithPivot(CalculationDefinition $definition): Collection
  {
    /** @phpstan-ignore-next-line return.type */
    return $definition->years;
  }

  /**
   * Get a grouping key for flat array storage
   */
  private function getGroupingKey(int $scopeId, int $groupingId): string
  {
    return $scopeId . "." . $groupingId;
  }

  /**
   * Clear all temporary data for a grouping
   */
  private function clearGroupingData(int $scopeId, int $groupingId): void
  {
    $key = $this->getGroupingKey($scopeId, $groupingId);

    unset($this->hiddenGroupings[$key]);
    unset($this->selectOptionIds[$key]);
    unset($this->customReasons[$key]);
  }

  /**
   * Reload all data to prevent lazy loading issues
   *
   * @throws RuntimeException
   */
  private function reloadAllData(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $this->loadMetricDefinitions();
    $this->loadMetricValues();
    $this->loadDefinitions();
    $this->loadValues();
    $this->initializeDefinitionFields();
    $this->loadUnitsData();
    $this->loadCategoriesData();
    $this->loadGroupingsData();
  }

  /**
   * Load metric definitions for the current year
   *
   * @throws RuntimeException
   */
  private function loadMetricDefinitions(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $metrics = $this->metricDefinitionService->getDefinitionsForYear($this->currentYear);

    $this->metricDefinitions = [];
    foreach ($metrics as $definition) {
      $yearModel = $this->yearService->getYearByValue($this->currentYear);
      $helpText = "";

      if ($yearModel !== null) {
        $yearPivot = $definition
          ->years()
          ->where("year_id", $yearModel->id)
          ->withPivot("id")
          ->first();

        if ($yearPivot !== null && $yearPivot->pivot !== null) {
          $metricDefYear = MetricDefinitionYear::with("translation")->find($yearPivot->pivot->id);
          if ($metricDefYear !== null) {
            $helpText = $metricDefYear->translate()->help_text ?? "";
          }
        }
      }

      $dto = MetricDefinitionDto::fromModel(
        $definition,
        $definition->translate()->name ?? "",
        $definition->unit?->translate()->symbol ?? "",
        $helpText,
      );

      $this->metricDefinitions[] = $dto;
      $this->metricHelpTexts[$dto->id] = $dto->helpText;
    }
  }

  /**
   * Load metric values for all definitions and years
   *
   * @throws RuntimeException
   */
  private function loadMetricValues(): void
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null || count($this->metricDefinitions) === 0) {
      return;
    }

    // Convert DTOs back to a collection of IDs for the service
    $metricIds = array_map(fn($dto) => $dto->id, $this->metricDefinitions);
    $metricsCollection = MetricDefinition::whereIn("id", $metricIds)->get();

    $valueData = $this->metricValueService->loadValuesForYears(
      $metricsCollection,
      $this->tableYears,
    );

    $this->metricValues = $valueData["values"];
    $this->metricSources = $valueData["sources"];
  }

  /**
   * Initialize editable fields for all definitions
   */
  private function initializeDefinitionFields(): void
  {
    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;
      if (!isset($this->scopedGroupedCategorizedDefinitions[$scopeId])) {
        continue;
      }

      foreach ($this->scopedGroupedCategorizedDefinitions[$scopeId] as $groupingId => $categories) {
        foreach ($categories as $categoryId => $definitions) {
          foreach ($definitions as $definitionDto) {
            $this->definitionNames[$definitionDto->id] =
              $definitionDto->customName ?? $definitionDto->dataName;
            $this->definitionUnitIds[$definitionDto->id] = $definitionDto->dataUnitId;
            $this->definitionScopeIds[$definitionDto->id] = $definitionDto->scopeId;
            $this->definitionCategoryIds[$definitionDto->id] = $definitionDto->categoryId;
            $this->definitionGroupingIds[$definitionDto->id] = $definitionDto->groupingId;
            $this->helpTexts[$definitionDto->id] = $definitionDto->helpTexts;
            $this->definitionLinkIds[$definitionDto->id] = $definitionDto->linkId ?? null;
          }
        }
      }
    }
  }

  /**
   * Load all definitions sorted by scope, grouping, and category
   *
   * @throws RuntimeException
   */
  private function loadDefinitions(): void
  {
    if ($this->currentYear === null) {
      return;
    }

    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    // Get all available scopes and convert to DTOs
    $scopes = $this->definitionService->getAvailableScopes($this->currentYear);
    $this->availableScopes = [];
    foreach ($scopes as $scope) {
      $this->availableScopes[] = ScopeDto::fromModel($scope);
    }

    // Initialize the nested array
    $this->scopedGroupedCategorizedDefinitions = [];
    $this->scopeCategories = [];
    $this->scopeGroupings = [];

    // Collect ALL definitions first to pre-load pivot translations
    $allDefinitions = collect();
    $allPivotIds = collect();

    // First pass: collect all definitions and pivot IDs
    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;

      // Get definitions for this scope
      $definitions = $this->definitionService->getDefinitions($this->currentYear, $scopeId);

      // Filter to only visible definitions on data page (handle null as false/visible)
      $definitions = $definitions->filter(function ($definition) {
        return !($definition->hide_from_data_page ?? false);
      });

      $allDefinitions = $allDefinitions->merge($definitions);

      // Collect pivot IDs from each definition's years
      foreach ($definitions as $definition) {
        $yearsWithPivot = $this->getYearsWithPivot($definition);
        $pivotIds = $yearsWithPivot->map(function ($year) {
          return $year->pivot->id;
        });
        $allPivotIds = $allPivotIds->merge($pivotIds);
      }
    }

    // Load ALL pivot translations in ONE query
    $pivotTranslations = new Collection();
    if ($allPivotIds->isNotEmpty()) {
      $pivotTranslations = \App\Models\CalculationDefinitionYearTranslation::whereIn(
        "calculation_definition_year_id",
        $allPivotIds->unique()->values(),
      )
        ->where("locale", app()->getLocale())
        ->get()
        ->keyBy("calculation_definition_year_id");
    }

    // Second pass: build DTOs with pre-loaded translations
    foreach ($this->availableScopes as $scopeDto) {
      $scopeId = $scopeDto->id;

      // Initialize arrays for this scope
      $this->scopedGroupedCategorizedDefinitions[$scopeId] = [];
      $this->scopeCategories[$scopeId] = [];
      $this->scopeGroupings[$scopeId] = [];

      // Get definitions for this scope with all necessary relationships
      $definitions = $this->definitionService->getDefinitions($this->currentYear, $scopeId);

      // Filter to only visible definitions on data page (handle null as false/visible)
      $definitions = $definitions->filter(function ($definition) {
        return !($definition->hide_from_data_page ?? false);
      });

      // Group definitions by grouping and then by category
      $groupedCategorizedDefinitions = $definitions
        ->groupBy("grouping_id")
        ->map(function ($groupDefinitions) {
          return $groupDefinitions->groupBy("category_id");
        });

      // Store grouping-categorized definitions as DTOs
      foreach ($groupedCategorizedDefinitions as $groupingId => $categorizedDefs) {
        $groupingIdInt = (int) $groupingId;
        $this->scopedGroupedCategorizedDefinitions[$scopeId][$groupingIdInt] = [];

        foreach ($categorizedDefs as $categoryId => $defs) {
          $categoryIdInt = (int) $categoryId;
          $defsArray = [];
          foreach ($defs as $definition) {
            $yearsWithPivot = $this->getYearsWithPivot($definition);

            // Set translations on pivots before creating DTO
            $yearsWithPivot->each(function ($year) use ($pivotTranslations) {
              $year->pivot->setRelation(
                "translation",
                $pivotTranslations[$year->pivot->id] ?? null,
              );
            });

            $defsArray[] = CalculationDefinitionDto::fromModel($definition, $yearsWithPivot);
          }
          $this->scopedGroupedCategorizedDefinitions[$scopeId][$groupingIdInt][
            $categoryIdInt
          ] = $defsArray;
        }
      }

      // Get all categories for this scope as DTOs
      $categories = $this->definitionService->getCategoriesForScope($scopeId);
      foreach ($categories as $category) {
        $this->scopeCategories[$scopeId][] = CategoryDto::fromModel(
          $category,
          $category->translate()->title ?? "",
          $category->translate()->description ?? "",
        );
      }

      // Get all groupings for this scope as DTOs
      $groupings = $this->definitionService->getGroupingsForScope($scopeId);
      foreach ($groupings as $grouping) {
        $this->scopeGroupings[$scopeId][] = GroupingDto::fromModel(
          $grouping,
          $grouping->translate()->title ?? "",
        );
      }
    }
  }

  /**
   * Convert form unit ID to integer
   */
  private function getUnitIdFromForm(int $definitionId): int
  {
    if (isset($this->definitionUnitIds[$definitionId])) {
      $formValue = $this->definitionUnitIds[$definitionId];

      return (int) $formValue;
    }

    // Default to first unit if value is missing or invalid
    return $this->getDefaultUnitId();
  }

  /**
   * Get scope from form value
   */
  private function getScopeIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionScopeIds[$definitionId])) {
      $formValue = $this->definitionScopeIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get category from form value
   */
  private function getCategoryIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionCategoryIds[$definitionId])) {
      $formValue = $this->definitionCategoryIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get grouping from form value
   */
  private function getGroupingIdFromForm(int $definitionId): ?int
  {
    if (isset($this->definitionGroupingIds[$definitionId])) {
      $formValue = $this->definitionGroupingIds[$definitionId];

      return (int) $formValue;
    }

    return null;
  }

  /**
   * Get a default unit ID to use
   * If no units exist, creates a default unit
   */
  private function getDefaultUnitId(): int
  {
    if (count($this->units) > 0) {
      return $this->units[0]->id;
    }

    $unit = Unit::first();

    if ($unit === null) {
      // If no units exist, create a default one
      $unit = Unit::create([]);
      $unit->translateOrNew()->name = __("data.labels.no_unit");
      $unit->translateOrNew()->symbol = "-";
      $unit->save();
    }

    return $unit->id;
  }

  /**
   * Load units data
   */
  private function loadUnitsData(): void
  {
    $units = Unit::with("translation")->orderByTranslation("name")->get();
    $this->units = [];
    foreach ($units as $unit) {
      $this->units[] = UnitDto::fromModel(
        $unit,
        $unit->translate()->name ?? "",
        $unit->translate()->symbol ?? "",
      );
    }
  }

  /**
   * Load categories data
   */
  private function loadCategoriesData(): void
  {
    $categories = Category::with("translation")->orderByTranslation("title")->get();
    $this->categories = [];
    foreach ($categories as $category) {
      $this->categories[] = CategoryDto::fromModel(
        $category,
        $category->translate()->title ?? "",
        $category->translate()->description ?? "",
      );
    }
  }

  /**
   * Load groupings data
   */
  private function loadGroupingsData(): void
  {
    $groupings = Grouping::with("translation")->orderByTranslation("title")->get();
    $this->groupings = [];
    foreach ($groupings as $grouping) {
      $this->groupings[] = GroupingDto::fromModel($grouping, $grouping->translate()->title ?? "");
    }
  }

  /**
   * Load available years with data
   *
   * @throws RuntimeException
   */
  private function loadYears(): void
  {
    $this->allYears = $this->yearService->getAllYears();
  }

  /**
   * Load hiding reason options from SelectOption table
   *
   * @throws InvalidArgumentException
   */
  private function loadHidingReasonOptions(): void
  {
    // Service returns SelectOption DTOs, convert them to wireable SelectOptionDto
    $options = $this->selectOptionService->getSelectOptions(
      SelectOptionType::GROUPING_HIDING_REASON,
    );

    $this->hidingReasonOptions = [];
    foreach ($options as $option) {
      $this->hidingReasonOptions[] = SelectOptionDto::fromSelectOption($option);
    }
  }

  /**
   * Load grouping states from database
   */
  private function loadGroupingStates(): void
  {
    try {
      if ($this->currentYear === null) {
        return;
      }

      $company = $this->companyContextService->getCurrentCompany();
      if ($company === null) {
        return;
      }

      $states = $this->groupingStateService->loadGroupingStates($company, $this->currentYear);
      $this->hiddenGroupings = $states["hiddenGroupings"];
      $this->selectOptionIds = $states["selectOptionIds"];
      $this->customReasons = $states["customReasons"];
    } catch (RuntimeException $e) {
      $this->safeReport($e);
      // fall back to empty arrays on error
      $this->hiddenGroupings = [];
      $this->selectOptionIds = [];
      $this->customReasons = [];
    }
  }

  /**
   * Load all data values for all definitions and years
   *
   * @throws RuntimeException
   */
  private function loadValues(): void
  {
    $company = $this->companyContextService->getCurrentCompany();

    if ($company === null) {
      return;
    }

    $allDefinitions = new Collection();

    // Collect all definition IDs from DTOs
    $definitionIds = [];
    foreach ($this->scopedGroupedCategorizedDefinitions as $scopeDefs) {
      foreach ($scopeDefs as $groupingDefs) {
        foreach ($groupingDefs as $categoryDefs) {
          foreach ($categoryDefs as $dto) {
            $definitionIds[] = $dto->id;
          }
        }
      }
    }

    if (count($definitionIds) === 0) {
      return;
    }

    // Load models by IDs
    $allDefinitions = CalculationDefinition::whereIn("id", $definitionIds)->get();

    $valueData = $this->valueService->loadValuesForYears($allDefinitions, $this->tableYears);

    $this->values = $valueData["values"];
    $this->sources = $valueData["sources"];
  }

  /**
   * Safe error reporting
   */
  private function safeReport(Throwable $e): void
  {
    try {
      report($e);
    } catch (Throwable $reportException) {
      Log::emergency("Failed to report exception", [
        "exception" => get_class($e),
        "message" => $e->getMessage(),
        "reportException" => get_class($reportException),
        "reportExceptionMessage" => $reportException->getMessage(),
      ]);
    }
  }

  /**
   * Add error with flash notification
   */
  private function addErrorWithFlash(string $field, string $message): void
  {
    $this->addError($field, $message);
    $this->dispatch("notify", [
      "message" => $message,
      "type" => "error",
    ]);
  }
}
