<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Settings\InstructionsSettings;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Livewire\Component;
use RuntimeException;

final class InstructionsPage extends Component
{
  public string $content = "";

  /**
   * @throws RuntimeException
   */
  public function mount(): void
  {
    $settings = App::make(InstructionsSettings::class);
    $locale = App::getLocale();

    $this->content = match ($locale) {
      "en" => $settings->content_en,
      "sv" => $settings->content_sv,
      default => $settings->content_fi,
    };
  }

  public function render(): View
  {
    return view("livewire.instructions-page", [
      "content" => $this->content,
    ]);
  }
}
