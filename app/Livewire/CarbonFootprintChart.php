<?php

declare(strict_types=1);

namespace App\Livewire;

use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;

final class CarbonFootprint<PERSON>hart extends ChartWidget
{
  /** @var int[] */
  public array $years = [];

  /** @var array<int, array<int, string>> */
  public array $scopeResults = [];

  protected static ?string $maxHeight = "400px";

  protected function getData(): array
  {
    $datasets = [];
    $scopeColors = [
      1 => "#E8F1F9", // sky-100
      2 => "#FD9180", // red-300
      3 => "#BB95EF", // indigo-300
    ];

    $scopeHoverColors = [
      1 => "#CBDFF1", // sky-200
      2 => "#F66B55", // red-400
      3 => "#9D70DB", // indigo-400
    ];

    // Create a dataset for each scope
    foreach ($this->scopeResults as $scopeId => $scopeData) {
      $yearData = [];

      // For each year, get the total for this scope
      foreach ($this->years as $year) {
        $yearData[] = isset($scopeData[$year]) ? $scopeData[$year] : "0.0";
      }

      $datasets[] = [
        "label" => "Scope {$scopeId}",
        "data" => $yearData,
        "backgroundColor" => $scopeColors[$scopeId] ?? "#6B7280",
        "hoverBackgroundColor" => $scopeHoverColors[$scopeId] ?? "#4B5563",
      ];
    }

    return [
      "datasets" => $datasets,
      "labels" => $this->years,
    ];
  }

  protected function getOptions(): RawJs
  {
    $blue = "#002663"; // blue-900
    $neutral = "#D8D8D8"; // neutral-200

    return RawJs::make(
      <<<JS
      {
          responsive: true,
          plugins: {
              tooltip: {
                  callbacks: {
                      label: function(context) {
                          const label = context?.dataset?.label || '';
                          const value = context?.parsed?.y;
                          if (value > 0) {
                              return label;
                          } else {
                              return null;
                          }
                      },
                      title: function() {
                          return '';
                      }
                  },
                  displayColors: false,
                  backgroundColor: 'white',
                  borderColor: '$blue',
                  borderWidth: 1,
                  cornerRadius: 3,
                  titleColor: '$blue',
                  bodyColor: '$blue',
                  position: 'nearest',
                  yAlign: 'bottom',
              },
              legend: {
                  labels: {
                      boxWidth: 25,
                      boxHeight: 25,
                      padding: 27,
                      font: {
                          size: 18
                      },
                      color: '$blue'
                  }
              }
          },
          minBarLength: 2,
          maxBarThickness: 100,
          elements: {
              bar: {
                  borderWidth: 0,
              }
          },
          layout: {
            padding: {
              left: -16
            }
          },
          scales: {
              x: {
                  stacked: true,
                  ticks: {
                      color: '$blue',
                      font: {
                          size: 14,
                          weight: 600
                      }
                  },
                  grid: {
                      display: false
                  }
              },
              y: {
                  stacked: true,
                  ticks: {
                      color: '$blue',
                      font: {
                          size: 14
                      },
                      padding: 16,
                  },
                  grid: {
                      color: function(context) {
                          return context?.index === 0 ? '$blue' : '$neutral';
                      },
                  }
              }
          },
          devicePixelRatio: 2
      }
      JS
      ,
    );
  }

  protected function getType(): string
  {
    return "bar";
  }
}
