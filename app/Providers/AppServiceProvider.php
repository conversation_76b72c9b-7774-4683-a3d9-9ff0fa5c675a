<?php

declare(strict_types=1);

namespace App\Providers;

use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Sleep;
use Illuminate\Validation\Rules\Password;
use RuntimeException;

final class AppServiceProvider extends ServiceProvider
{
  /**
   * Register any application services.
   */
  public function register(): void
  {
    if (
      $this->app->environment("local") &&
      class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)
    ) {
      $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
      $this->app->register(TelescopeServiceProvider::class);
    }
  }

  /**
   * Bootstrap any application services.
   *
   * @throws RuntimeException
   */
  public function boot(): void
  {
    Model::shouldBeStrict();
    Date::use(CarbonImmutable::class);
    DB::prohibitDestructiveCommands(App::isProduction());
    URL::forceHttps();
    Http::preventStrayRequests(App::runningUnitTests());
    Password::defaults(fn(): Password => Password::min(12)->max(255)->uncompromised());
    Sleep::fake(App::runningUnitTests());
    Vite::useAggressivePrefetching();
  }
}
