<?php

declare(strict_types=1);

namespace App\Providers;

use App\Enums\Abilities;
use App\Models\Company;
use App\Models\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use RuntimeException;

final class AuthServiceProvider extends ServiceProvider
{
  /**
   * @throws RuntimeException
   */
  public function boot(): void
  {
    Gate::define(Abilities::VIEW_COMPANY, function (User $user, Company $company) {
      if ($user->hasPermissionTo("view any company")) {
        return true;
      }

      return $user->companies()->where("companies.id", $company->id)->exists();
    });

    Gate::define(Abilities::VIEW_ANY_COMPANY, function (User $user) {
      return $user->hasPermissionTo("view any company");
    });

    Gate::define(Abilities::EDIT_COMPANY, function (User $user, Company $company) {
      if ($user->hasPermissionTo("edit any company")) {
        return true;
      }

      return $user->companies()->where("companies.id", $company->id)->exists();
    });

    Gate::define(Abilities::MANAGE_COMPANY_USERS, function (User $user, Company $company) {
      if ($user->hasPermissionTo("manage any company users")) {
        return true;
      }

      $pivot = $user->companies()->where("companies.id", $company->id)->first()?->pivot;

      return $pivot !== null && $pivot->is_primary;
    });

    Gate::define(Abilities::CREATE_COMPANY, function (User $user) {
      return $user->hasPermissionTo("create companies");
    });

    Gate::define(Abilities::DELETE_COMPANY_USER, function (
      User $currentUser,
      Company $company,
      User $userToDelete,
    ) {
      if ($currentUser->hasPermissionTo("delete any company user")) {
        return true;
      }

      $pivot = $currentUser->companies()->where("companies.id", $company->id)->first()?->pivot;

      return $pivot !== null &&
        $pivot->is_primary &&
        $userToDelete->companies()->where("companies.id", $company->id)->exists() &&
        $currentUser->id !== $userToDelete->id;
    });

    Gate::define(Abilities::VIEW_COMPANY_DATA, function (User $user, Company $company) {
      if ($user->hasPermissionTo("view any company data")) {
        return true;
      }

      return $user->companies()->where("companies.id", $company->id)->exists();
    });

    Gate::define(Abilities::EDIT_COMPANY_DATA, function (User $user, Company $company) {
      if ($user->hasPermissionTo("edit any company data")) {
        return true;
      }

      return $user->companies()->where("companies.id", $company->id)->exists();
    });

    Gate::define(Abilities::VIEW_COMPANY_AUDIT_LOGS, function (User $user, Company $company) {
      if ($user->hasPermissionTo("view any company audit logs")) {
        return true;
      }

      $pivot = $user->companies()->where("companies.id", $company->id)->first()?->pivot;

      return $pivot !== null && $pivot->is_primary;
    });
  }
}
