<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Filament\Plugins\AstrotomicTranslatable\FilamentAstrotomicTranslatablePlugin;
use App\Filament\Widgets\AuditLogBreakdownWidget;
use App\Filament\Widgets\AuditLogOverviewWidget;
use App\Filament\Widgets\AuditLogStatsWidget;
use App\Filament\Widgets\AuditLogWidget;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

final class AdminPanelProvider extends PanelProvider
{
  public function panel(Panel $panel): Panel
  {
    return $panel
      ->default()
      ->id("admin")
      ->path("/{_locale}/admin")
      ->colors([
        "primary" => Color::Blue,
      ])
      ->databaseTransactions()
      ->discoverResources(in: app_path("Filament/Resources"), for: "App\\Filament\\Resources")
      ->discoverPages(in: app_path("Filament/Pages"), for: "App\\Filament\\Pages")
      ->pages([Pages\Dashboard::class])
      ->discoverWidgets(in: app_path("Filament/Widgets"), for: "App\\Filament\\Widgets")
      ->widgets([
        AuditLogOverviewWidget::class,
        AuditLogStatsWidget::class,
        AuditLogBreakdownWidget::class,
        AuditLogWidget::class,
      ])
      ->middleware([
        EncryptCookies::class,
        AddQueuedCookiesToResponse::class,
        StartSession::class,
        AuthenticateSession::class,
        ShareErrorsFromSession::class,
        VerifyCsrfToken::class,
        SubstituteBindings::class,
        DisableBladeIconComponents::class,
        DispatchServingFilamentEvent::class,
      ])
      ->authMiddleware([Authenticate::class])
      ->plugin(FilamentAstrotomicTranslatablePlugin::make());
  }
}
