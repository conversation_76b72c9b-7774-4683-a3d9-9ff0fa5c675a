{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --config vite.lib.config.js", "dev": "vite", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint --fix", "format:check": "prettier --check .", "format": "prettier --write ."}, "devDependencies": {"@prettier/plugin-php": "^0.22.4", "@shufo/prettier-plugin-blade": "^1.15.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@total-typescript/ts-reset": "^0.6.1", "@types/alpinejs": "^3.13.11", "autoprefixer": "^10.4.21", "axios": "^1.8.2", "concurrently": "^9.0.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "globals": "^16.1.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.2.4"}}