{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --config vite.lib.config.js", "dev": "vite", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint --fix", "format:check": "prettier --check .", "format": "prettier --write ."}, "devDependencies": {"@prettier/plugin-php": "^0.24.0", "@shufo/prettier-plugin-blade": "^1.16.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@total-typescript/ts-reset": "^0.6.1", "@types/alpinejs": "^3.13.11", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "concurrently": "^9.2.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "laravel-vite-plugin": "^2.0.0", "postcss": "^8.5.6", "postcss-nesting": "^13.0.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "typescript-eslint": "^8.40.0", "vite": "^7.1.3"}, "engines": {"node": "24.6.0"}}